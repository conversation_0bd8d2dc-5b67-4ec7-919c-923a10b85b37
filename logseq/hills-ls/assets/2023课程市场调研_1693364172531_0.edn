{:highlights [{:id #uuid "65c5f003-b390-4147-ae84-f767003ad643",
               :page 1,
               :position {:bounding {:x1 113.3203125,
                                     :y1 174.1953125,
                                     :x2 420.64434814453125,
                                     :y2 210.1953125,
                                     :width 952.32,
                                     :height 1347.072},
                          :rects ({:x1 113.3203125,
                                   :y1 174.1953125,
                                   :x2 420.64434814453125,
                                   :y2 210.1953125,
                                   :width 952.32,
                                   :height 1347.072}),
                          :page 1},
               :content {:text "⼀、⽬标⼈群活跃平台调研"},
               :properties {:color "red"}}
              {:id #uuid "65c5f01a-211a-4d4f-83d2-8b02ca9d6cfa",
               :page 1,
               :position {:bounding {:x1 113.3203125,
                                     :y1 242.609375,
                                     :x2 606.1997680664062,
                                     :y2 267.109375,
                                     :width 952.32,
                                     :height 1347.072},
                          :rects ({:x1 113.3203125,
                                   :y1 242.609375,
                                   :x2 606.1997680664062,
                                   :y2 267.109375,
                                   :width 952.32,
                                   :height 1347.072}),
                          :page 1},
               :content {:text "调研对象：全国全国⽼年教育公共平台、⽼年⼤学、美篇、糖⾖"},
               :properties {:color "red"}}
              {:id #uuid "65c5f029-1338-4763-818b-53c0e638d5d3",
               :page 1,
               :position {:bounding {:x1 113.3203125,
                                     :y1 313.5,
                                     :x2 338.8437805175781,
                                     :y2 340.5,
                                     :width 952.32,
                                     :height 1347.072},
                          :rects ({:x1 113.3203125,
                                   :y1 313.5,
                                   :x2 338.8437805175781,
                                   :y2 340.5,
                                   :width 952.32,
                                   :height 1347.072}
                                  {:x1 123.890625,
                                   :y1 317.5,
                                   :x2 149.5234375,
                                   :y2 336.703125,
                                   :width 952.32,
                                   :height 1347.072}),
                          :page 1},
               :content {:text "1、 全国⽼年教育公共平台"},
               :properties {:color "red"}}
              {:id #uuid "65c5f041-7e2c-4eb1-9ba1-c299c4980792",
               :page 1,
               :position {:bounding {:x1 121.984375,
                                     :y1 813.6328125,
                                     :x2 597.1998901367188,
                                     :y2 838.1328125,
                                     :width 952.32,
                                     :height 1347.072},
                          :rects ({:x1 121.984375,
                                   :y1 813.6328125,
                                   :x2 597.1998901367188,
                                   :y2 838.1328125,
                                   :width 952.32,
                                   :height 1347.072}),
                          :page 1},
               :content {:text "从整体热度看，舞蹈课题热度最⾼，其次为中医保健类课题。"},
               :properties {:color "red"}}
              {:id #uuid "65c5f079-001f-48a0-a1b5-121cfb371843",
               :page 1,
               :position {:bounding {:x1 113.3203125,
                                     :y1 880.4453125,
                                     :x2 826.0418090820312,
                                     :y2 935.65625,
                                     :width 952.32,
                                     :height 1347.072},
                          :rects ({:x1 121.984375,
                                   :y1 880.4453125,
                                   :x2 826.0418090820312,
                                   :y2 904.9453125,
                                   :width 952.32,
                                   :height 1347.072}
                                  {:x1 297.9765625,
                                   :y1 883.9453125,
                                   :x2 826.0418090820312,
                                   :y2 901.609375,
                                   :width 952.32,
                                   :height 1347.072}
                                  {:x1 113.3203125,
                                   :y1 911.15625,
                                   :x2 518.2008056640625,
                                   :y2 935.65625,
                                   :width 952.32,
                                   :height 1347.072}),
                          :page 1},
               :content {:text "从平均播放数据看，以退休⽣涯规划课题的平均播放量最⾼，次⾼峰为舞蹈、书法和摄影这类兴趣课题，以及数字素养，即电⼦产品教学类课题"},
               :properties {:color "red"}}
              {:id #uuid "65c5f09e-1231-4291-ac25-b9ad13562c02",
               :page 1,
               :position {:bounding {:x1 92,
                                     :y1 362,
                                     :x2 744,
                                     :y2 715,
                                     :width 833.9999999999999,
                                     :height 1179.7064516129028},
                          :rects (),
                          :page 1},
               :content {:text "[:span]", :image 1707471006361},
               :properties {:color "red"}}
              {:id #uuid "65c5f0c4-5ae4-4312-b317-6ae42897d894",
               :page 2,
               :position {:bounding {:x1 130,
                                     :y1 123.5,
                                     :x2 1058,
                                     :y2 651.5,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 2},
               :content {:text "[:span]", :image 1707471044144},
               :properties {:color "red"}}
              {:id #uuid "65c5f0cf-bb50-4b6f-ac72-22e8fccbfffe",
               :page 2,
               :position {:bounding {:x1 135,
                                     :y1 656.5,
                                     :x2 1046,
                                     :y2 1527.5,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 2},
               :content {:text "[:span]", :image 1707471055429},
               :properties {:color "red"}}
              {:id #uuid "65c5f0fc-879f-43f9-99a4-c62db281c4d9",
               :page 3,
               :position {:bounding {:x1 131,
                                     :y1 122.5,
                                     :x2 1047,
                                     :y2 239.5,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 3},
               :content {:text "[:span]", :image 1707471099843},
               :properties {:color "red"}}
              {:id #uuid "65c5f107-fbd4-40e7-b28a-f37c8370af25",
               :page 3,
               :position {:bounding {:x1 150.79359436035156,
                                     :y1 238.3046875,
                                     :x2 1013.3897705078125,
                                     :y2 269.3046875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 150.79359436035156,
                                   :y1 238.3046875,
                                   :x2 1013.3897705078125,
                                   :y2 269.3046875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 215.8125,
                                   :y1 242.8046875,
                                   :x2 223.171875,
                                   :y2 264.8828125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text " TOP20 中，热度占⽐最⼤的依旧是【舞蹈】课题。其中朝鲜舞、古典舞等⺠族舞最受关注"},
               :properties {:color "red"}}
              {:id #uuid "65c5f112-14a1-4974-98f5-619a25e7a378",
               :page 3,
               :position {:bounding {:x1 141.65625,
                                     :y1 282.59375,
                                     :x2 1032.************,
                                     :y2 352.3203125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 282.59375,
                                   :x2 1032.************,
                                   :y2 313.59375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 321.3203125,
                                   :x2 977.7620849609375,
                                   :y2 352.3203125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text "中医养⽣类课题中，除了太极、⼋段锦等常⻅养⽣运动，养⽣理论《⻩帝内经》排名更前，推测除了中医保健⽅法，该年龄层⽤户对探索中医历史、⽞学渊源也有⼀定的猎奇⼼态"},
               :properties {:color "red"}}
              {:id #uuid "65c5f117-8378-47e7-b349-2b79da817991",
               :page 3,
               :position {:bounding {:x1 152.484375,
                                     :y1 366.109375,
                                     :x2 988.5902099609375,
                                     :y2 397.109375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 366.109375,
                                   :x2 988.5902099609375,
                                   :y2 397.109375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text "除了兴趣娱乐与养⽣保健，中⽼年⼈群对退休规划与⼼理健康维护也有⾮常强烈的需求"},
               :properties {:color "red"}}
              {:id #uuid "65c5f11e-8b17-4489-8ce5-1ac7cfb9e5c1",
               :page 3,
               :position {:bounding {:x1 150.79359436035156,
                                     :y1 411.234375,
                                     :x2 925.3875122070312,
                                     :y2 442.234375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 150.79359436035156,
                                   :y1 411.234375,
                                   :x2 925.3875122070312,
                                   :y2 442.234375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 215.8125,
                                   :y1 415.734375,
                                   :x2 223.171875,
                                   :y2 437.8125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text " TOP20 兴趣类关键词：舞蹈、唱歌、插花、书法、茶⽂化、花艺、葫芦丝、⼆胡"},
               :properties {:color "red"}}
              {:id #uuid "65c5f12a-2d56-418c-89c0-14fa0352f282",
               :page 3,
               :position {:bounding {:x1 141.65625,
                                     :y1 500.8203125,
                                     :x2 279.5546875,
                                     :y2 534.3203125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 500.8203125,
                                   :x2 279.5546875,
                                   :y2 534.3203125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 154.8671875,
                                   :y1 505.8203125,
                                   :x2 186.90625,
                                   :y2 529.8203125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text "2、 ⽼年⼤学"},
               :properties {:color "red"}}
              {:id #uuid "65c5f132-a920-496b-8f76-02df938eb704",
               :page 3,
               :position {:bounding {:x1 141.65625,
                                     :y1 561.9375,
                                     :x2 823.7561645507812,
                                     :y2 628.46875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 561.9375,
                                   :x2 594.************,
                                   :y2 592.9375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 229.625,
                                   :y1 566.4375,
                                   :x2 594.************,
                                   :y2 588.515625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 597.46875,
                                   :x2 823.7561645507812,
                                   :y2 628.46875,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text "源⽹址：https://lndx.edu.cn/recommendCourse（⽼年⼤学与全国⽼年教育公共平台有部分课程重合，应为共⽤平台）"},
               :properties {:color "red"}}
              {:id #uuid "65c5f142-3674-4238-b9c6-847aec554729",
               :page 1,
               :position {:bounding {:x1 141.65625,
                                     :y1 452.9921875,
                                     :x2 559.8795776367188,
                                     :y2 483.9921875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 452.9921875,
                                   :x2 559.8795776367188,
                                   :y2 483.9921875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 229.625,
                                   :y1 457.4921875,
                                   :x2 559.8795776367188,
                                   :y2 479.5703125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 1},
               :content {:text "源⽹址：https://lndx.edu.cn/generalCourses"},
               :properties {:color "red"}}
              {:id #uuid "65c5f166-0c4c-442e-80d9-97930f2d05b6",
               :page 3,
               :position {:bounding {:x1 132,
                                     :y1 702,
                                     :x2 1065,
                                     :y2 1218,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 3},
               :content {:text "[:span]", :image 1707471206392},
               :properties {:color "red"}}
              {:id #uuid "65c5f16a-b550-452b-befb-377d82214f1e",
               :page 3,
               :position {:bounding {:x1 141.65625,
                                     :y1 1227.5625,
                                     :x2 1032.************,
                                     :y2 1297.453125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 1227.5625,
                                   :x2 1032.************,
                                   :y2 1258.5625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 570.4375,
                                   :y1 1232.0625,
                                   :x2 1032.************,
                                   :y2 1254.140625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 1266.453125,
                                   :x2 163.7421875,
                                   :y2 1297.453125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text "⽼年⼤学⽤户不熟悉智能电⼦产品的使⽤，对电脑应⽤、⼿机应⽤的学习需求远⾼于其他课题"},
               :properties {:color "red"}}
              {:id #uuid "65c5f16f-8e93-4954-93b0-07a7dd7322ce",
               :page 3,
               :position {:bounding {:x1 141.65625,
                                     :y1 1311.078125,
                                     :x2 1032.************,
                                     :y2 1380.9765625,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 1311.078125,
                                   :x2 1032.************,
                                   :y2 1342.078125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 1349.9765625,
                                   :x2 647.75146484375,
                                   :y2 1380.9765625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 3},
               :content {:text "除了电⼦产品使⽤⽅法，⽼年⼤学的⽤户更关⼼居家健康、⻝品营养、中医养⽣、健康运动等，健康类课题。兴趣类课题在⽼年⼤学中体量较少"},
               :properties {:color "red"}}
              {:id #uuid "65c5f17d-433a-4687-ade6-d601ab0bc2ee",
               :page 4,
               :position {:bounding {:x1 139,
                                     :y1 127,
                                     :x2 1058,
                                     :y2 749,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 4},
               :content {:text "[:span]", :image 1707471228858},
               :properties {:color "red"}}
              {:id #uuid "65c5f186-7173-42dd-944e-cf419ff683e7",
               :page 4,
               :position {:bounding {:x1 139,
                                     :y1 756,
                                     :x2 1052,
                                     :y2 1535,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 4},
               :content {:text "[:span]", :image 1707471238558},
               :properties {:color "red"}}
              {:id #uuid "65c5f194-180e-42c5-8e48-87c702999866",
               :page 5,
               :position {:bounding {:x1 136,
                                     :y1 129,
                                     :x2 1049,
                                     :y2 417,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 5},
               :content {:text "[:span]", :image 1707471252060},
               :properties {:color "red"}}
              {:id #uuid "65c5f197-3c17-4692-8878-31cea2d8adba",
               :page 5,
               :position {:bounding {:x1 141.65625,
                                     :y1 417.8046875,
                                     :x2 1035.46875,
                                     :y2 487.1953125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 156.87867736816406,
                                   :y1 417.8046875,
                                   :x2 1035.46875,
                                   :y2 448.8046875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 215.8125,
                                   :y1 422.3046875,
                                   :x2 1035.46875,
                                   :y2 444.3828125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 456.1953125,
                                   :x2 229.66270446777344,
                                   :y2 487.1953125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 460.6953125,
                                   :x2 185.640625,
                                   :y2 482.7734375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 5},
               :content {:text "TOP20 健康类关键词：⼆⼗四节⽓、⻝品安全、拍打操、⻣质疏松、经络调理、营养师、爱眼、按⼿"},
               :properties {:color "red"}}
              {:id #uuid "65c5f1a3-cc6a-49ee-a01e-a73a0bf58333",
               :page 5,
               :position {:bounding {:x1 140,
                                     :y1 685,
                                     :x2 1054,
                                     :y2 1153,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 5},
               :content {:text "[:span]", :image 1707471267158},
               :properties {:color "red"}}
              {:id #uuid "65c5f1a8-3a9f-40c3-897e-8557bdc823a2",
               :page 5,
               :position {:bounding {:x1 141.65625,
                                     :y1 546.453125,
                                     :x2 231.5546875,
                                     :y2 579.953125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 546.453125,
                                   :x2 231.5546875,
                                   :y2 579.953125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 154.8671875,
                                   :y1 551.453125,
                                   :x2 186.90625,
                                   :y2 575.453125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 5},
               :content {:text "3、 美篇"},
               :properties {:color "red"}}
              {:id #uuid "65c5f1ab-6b2a-44d2-80ad-d6a9f5ce4303",
               :page 5,
               :position {:bounding {:x1 141.65625,
                                     :y1 607.3984375,
                                     :x2 317.4010009765625,
                                     :y2 638.3984375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 607.3984375,
                                   :x2 317.4010009765625,
                                   :y2 638.3984375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 229.625,
                                   :y1 611.8984375,
                                   :x2 293.65625,
                                   :y2 633.9765625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 5},
               :content {:text "数据源：美篇 APP"},
               :properties {:color "red"}}
              {:id #uuid "65c5f1d2-5825-4ae8-94de-3dc05d3164d7",
               :page 5,
               :position {:bounding {:x1 152.484375,
                                     :y1 1165.765625,
                                     :x2 1032.************,
                                     :y2 1196.765625,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 1165.765625,
                                   :x2 1032.************,
                                   :y2 1196.765625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 262.4765625,
                                   :y1 1170.265625,
                                   :x2 966.5220947265625,
                                   :y2 1192.34375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 5},
               :content {:text "美篇主要玩法是以照⽚分享美好⽣活，因此【摄影圈】与【⽣活⽅式】两个圈⼦活跃度最⾼"},
               :properties {:color "red"}}
              {:id #uuid "65c5f1d9-1a40-4cca-9f9e-a4c5b2fa7ba9",
               :page 5,
               :position {:bounding {:x1 141.65625,
                                     :y1 1210.3828125,
                                     :x2 1032.5845947265625,
                                     :y2 1280.28125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 1210.3828125,
                                   :x2 1032.5845947265625,
                                   :y2 1241.3828125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 174.390625,
                                   :y1 1214.8828125,
                                   :x2 1032.5845947265625,
                                   :y2 1236.9609375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 1249.28125,
                                   :x2 647.7330322265625,
                                   :y2 1280.28125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 1253.78125,
                                   :x2 603.7109375,
                                   :y2 1275.859375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 5},
               :content {:text "从交友、年代回忆、我爱我家，可看出⽬标⽤户对唠嗑家⻓⾥短有强烈需求，想要提升⽤户活跃与留存，必须为他们提供⼀个能⾃由唠嗑的场景"},
               :properties {:color "red"}}
              {:id #uuid "65c5f1df-0ec7-43aa-b11e-a9d6bc0e3a55",
               :page 5,
               :position {:bounding {:x1 152.484375,
                                     :y1 1293.8984375,
                                     :x2 636.5625,
                                     :y2 1324.8984375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 1293.8984375,
                                   :x2 636.5625,
                                   :y2 1324.8984375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 306.40625,
                                   :y1 1298.3984375,
                                   :x2 614.546875,
                                   :y2 1320.4765625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 5},
               :content {:text "除了常⻅兴趣，发现新课题【美⻝圈】、【园艺圈】"},
               :properties {:color "red"}}
              {:id #uuid "65c5f1ee-593e-45ce-9530-61309304c28f",
               :page 6,
               :position {:bounding {:x1 138.5,
                                     :y1 128,
                                     :x2 1054.5,
                                     :y2 653,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 6},
               :content {:text "[:span]", :image 1707471342224},
               :properties {:color "red"}}
              {:id #uuid "65c5f1f9-ea21-4040-a55e-a80b29827c5e",
               :page 6,
               :position {:bounding {:x1 141.5,
                                     :y1 658,
                                     :x2 1043.5,
                                     :y2 1359,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 6},
               :content {:text "[:span]", :image 1707471353675},
               :properties {:color "red"}}
              {:id #uuid "65c5f1fe-e163-47cd-bcfe-33e1ad064b7a",
               :page 6,
               :position {:bounding {:x1 152.484375,
                                     :y1 1362.09375,
                                     :x2 548.5271606445312,
                                     :y2 1393.09375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 1362.09375,
                                   :x2 548.5271606445312,
                                   :y2 1393.09375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 218.4375,
                                   :y1 1366.59375,
                                   :x2 460.5078125,
                                   :y2 1388.671875,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 6},
               :content {:text "摄影类⽬关键词：花卉、旅游、⼭⽔⻛光"},
               :properties {:color "red"}}
              {:id #uuid "65c5f202-04d9-4067-9376-6f08f7d1d67b",
               :page 6,
               :position {:bounding {:x1 152.484375,
                                     :y1 1406.71875,
                                     :x2 614.5220947265625,
                                     :y2 1437.71875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 1406.71875,
                                   :x2 614.5220947265625,
                                   :y2 1437.71875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 174.390625,
                                   :y1 1411.21875,
                                   :x2 570.5924072265625,
                                   :y2 1433.296875,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 6},
               :content {:text "新类⽬关键词：古诗词、传统⽂化、美⻝、写作"},
               :properties {:color "red"}}
              {:id #uuid "65c5f216-9107-436f-af55-41dd16b8103a",
               :page 7,
               :position {:bounding {:x1 141.65625,
                                     :y1 136.609375,
                                     :x2 231.5546875,
                                     :y2 170.109375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 136.609375,
                                   :x2 231.5546875,
                                   :y2 170.109375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 154.8671875,
                                   :y1 141.609375,
                                   :x2 186.90625,
                                   :y2 165.609375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 7},
               :content {:text "4、 糖⾖"},
               :properties {:color "red"}}
              {:id #uuid "65c5f218-554a-4dab-aa2e-b40265d88e36",
               :page 7,
               :position {:bounding {:x1 141.65625,
                                     :y1 197.5546875,
                                     :x2 317.4010009765625,
                                     :y2 228.5546875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 197.5546875,
                                   :x2 317.4010009765625,
                                   :y2 228.5546875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 229.625,
                                   :y1 202.0546875,
                                   :x2 293.65625,
                                   :y2 224.1328125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 7},
               :content {:text "数据源：糖⾖ APP"},
               :properties {:color "red"}}
              {:id #uuid "65c5f221-4668-400f-8bcd-440f66c509d5",
               :page 7,
               :position {:bounding {:x1 139.5,
                                     :y1 270,
                                     :x2 1052.5,
                                     :y2 735,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 7},
               :content {:text "[:span]", :image 1707471392741},
               :properties {:color "red"}}
              {:id #uuid "65c5f225-713b-49d0-a04f-d5c1bf8af9db",
               :page 7,
               :position {:bounding {:x1 152.484375,
                                     :y1 736.046875,
                                     :x2 768.578125,
                                     :y2 767.046875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 736.046875,
                                   :x2 768.578125,
                                   :y2 767.046875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 240.453125,
                                   :y1 740.546875,
                                   :x2 746.5546875,
                                   :y2 762.625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 7},
               :content {:text "【形体】舞热度⾸当其冲，⽤户练习舞蹈的最⼤需求是形体仪态"},
               :properties {:color "red"}}
              {:id #uuid "65c5f229-9194-474c-9642-1345fb755efb",
               :page 7,
               :position {:bounding {:x1 152.484375,
                                     :y1 780.671875,
                                     :x2 988.4959106445312,
                                     :y2 811.671875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 780.671875,
                                   :x2 988.4959106445312,
                                   :y2 811.671875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 416.3984375,
                                   :y1 785.171875,
                                   :x2 900.5101928710938,
                                   :y2 807.25,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 7},
               :content {:text "瘦肚⼦舞、拍打操、颈椎操等功能型舞蹈，更贴合兴趣岛⽤户画像，有⼀定的参考价值"},
               :properties {:color "red"}}
              {:id #uuid "65c5f22e-1a5a-41ca-a6a7-d539531abe3e",
               :page 7,
               :position {:bounding {:x1 141.65625,
                                     :y1 870.078125,
                                     :x2 1043.6796875,
                                     :y2 939.9765625,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 870.078125,
                                   :x2 1043.6796875,
                                   :y2 901.078125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 163.5546875,
                                   :y1 874.578125,
                                   :x2 1043.6796875,
                                   :y2 896.65625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 908.9765625,
                                   :x2 185.74082946777344,
                                   :y2 939.9765625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 7},
               :content {:text "以上为课程类⽬排⾏，下⽅则是课程标题关键词，有⼀定的出⼊，可能需要数据⽀撑再进⼀步筛选"},
               :properties {:color "red"}}
              {:id #uuid "65c5f236-2a1a-4421-ae07-cf2bcdfb0de5",
               :page 7,
               :position {:bounding {:x1 164.5,
                                     :y1 958,
                                     :x2 577.5,
                                     :y2 1205,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 7},
               :content {:text "[:span]", :image 1707471413755},
               :properties {:color "red"}}
              {:id #uuid "65c5f240-c696-42e1-8a8a-2a462b0f2790",
               :page 8,
               :position {:bounding {:x1 136.5,
                                     :y1 122,
                                     :x2 1059.5,
                                     :y2 696,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 8},
               :content {:text "[:span]", :image 1707471423754},
               :properties {:color "red"}}
              {:id #uuid "65c5f248-594c-4758-b21f-d6c0995af6a3",
               :page 8,
               :position {:bounding {:x1 139.5,
                                     :y1 696,
                                     :x2 1052.5,
                                     :y2 1525,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 8},
               :content {:text "[:span]", :image 1707471432307},
               :properties {:color "red"}}
              {:id #uuid "65c5f250-1b05-45e1-bb02-038aae2e1021",
               :page 9,
               :position {:bounding {:x1 137.5,
                                     :y1 126,
                                     :x2 1047.5,
                                     :y2 532,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 9},
               :content {:text "[:span]", :image 1707471440241},
               :properties {:color "red"}}
              {:id #uuid "65c5f255-7f8e-45e8-b542-f7ecf01b801e",
               :page 9,
               :position {:bounding {:x1 152.484375,
                                     :y1 533.984375,
                                     :x2 979.************,
                                     :y2 564.984375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 533.984375,
                                   :x2 979.************,
                                   :y2 564.984375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 196.4140625,
                                   :y1 538.484375,
                                   :x2 913.9296875,
                                   :y2 560.5625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "⽤户明显偏向步伐简单，容易记忆的舞种：16 步、32 步、⻤步舞、中三舞、步⼦舞等"},
               :properties {:color "red"}}
              {:id #uuid "65c5f259-aa80-4f4b-9f1c-61177893fbb3",
               :page 9,
               :position {:bounding {:x1 152.484375,
                                     :y1 578.609375,
                                     :x2 878.5703125,
                                     :y2 609.609375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 578.609375,
                                   :x2 878.5703125,
                                   :y2 609.609375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 196.4140625,
                                   :y1 583.109375,
                                   :x2 856.5546875,
                                   :y2 605.1875,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "课程主要学习⽬标为成品舞蹈，节奏欢快，容易复制，可直接⽤于学习演出"},
               :properties {:color "red"}}
              {:id #uuid "65c5f25e-c96e-4dca-9eb2-46d41f682397",
               :page 9,
               :position {:bounding {:x1 141.65625,
                                     :y1 659.765625,
                                     :x2 185.74082946777344,
                                     :y2 690.765625,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 659.765625,
                                   :x2 185.74082946777344,
                                   :y2 690.765625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "⼩结"},
               :properties {:color "red"}}
              {:id #uuid "65c5f262-572b-4f29-a7b3-ba6f15fa37a7",
               :page 9,
               :position {:bounding {:x1 141.65625,
                                     :y1 716.0078125,
                                     :x2 1033.2633056640625,
                                     :y2 785.90625,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 716.0078125,
                                   :x2 1033.2633056640625,
                                   :y2 747.0078125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 153.203125,
                                   :y1 720.5078125,
                                   :x2 1033.2633056640625,
                                   :y2 742.5859375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 754.90625,
                                   :x2 197.3125,
                                   :y2 785.90625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "1、适合研发⼤课的课题：功能型舞蹈、表演型舞蹈、唱歌、⼆胡、葫芦丝、书法、茶艺、花艺"},
               :properties {:color "red"}}
              {:id #uuid "65c5f26a-c838-4836-a818-2884e667ebf7",
               :page 9,
               :position {:bounding {:x1 141.65625,
                                     :y1 799.53125,
                                     :x2 1042.4007568359375,
                                     :y2 952.4375,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 799.53125,
                                   :x2 1042.4007568359375,
                                   :y2 830.53125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 153.203125,
                                   :y1 804.03125,
                                   :x2 1042.4007568359375,
                                   :y2 826.109375,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 838.421875,
                                   :x2 736.3203125,
                                   :y2 869.421875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 842.921875,
                                   :x2 736.3203125,
                                   :y2 865,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 883.046875,
                                   :x2 1042.375,
                                   :y2 914.046875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 887.546875,
                                   :x2 1042.375,
                                   :y2 909.625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 921.4375,
                                   :x2 197.3125,
                                   :y2 952.4375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "2、适合引进/研发的【免费】⼩课课题：兴趣岛⼯具使⽤教程、退休规划（可接⼊兴趣、养⽣规划等内容）、智能⼿机教程、中⽼年⼼理、⽼年痴呆防治适合引发/研发的【付费】⼩课课题：美⻝烘焙、园艺、宠物、古诗词、经典⽂学、历史故事"},
               :properties {:color "red"}}
              {:id #uuid "65c5f278-65a3-4490-815a-cd663256ac6a",
               :page 9,
               :position {:bounding {:x1 141.65625,
                                     :y1 966.5625,
                                     :x2 945.234375,
                                     :y2 997.5625,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 966.5625,
                                   :x2 945.234375,
                                   :y2 997.5625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 153.203125,
                                   :y1 971.0625,
                                   :x2 923.3359375,
                                   :y2 993.140625,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "3、适合开拓的圈⼦类型：家⻓⾥短唠嗑圈、⽣活⽅式圈、园艺圈、旅游圈、美⻝圈"},
               :properties {:color "red"}}
              {:id #uuid "65c5f27d-bc84-4dbd-913e-e6a639f00014",
               :page 9,
               :position {:bounding {:x1 141.65625,
                                     :y1 1056.1640625,
                                     :x2 545.7324829101562,
                                     :y2 1101.1640625,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 1056.1640625,
                                   :x2 545.7324829101562,
                                   :y2 1101.1640625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 205.6953125,
                                   :y1 1062.6640625,
                                   :x2 216.40625,
                                   :y2 1094.8203125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "⼆、 主流课程平台调研分析"},
               :properties {:color "red"}}
              {:id #uuid "65c5f283-a12d-4316-91fc-deaa8ecdc9ac",
               :page 9,
               :position {:bounding {:x1 141.65625,
                                     :y1 1171.828125,
                                     :x2 231.5546875,
                                     :y2 1205.328125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 1171.828125,
                                   :x2 231.5546875,
                                   :y2 1205.328125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 154.8671875,
                                   :y1 1176.828125,
                                   :x2 186.90625,
                                   :y2 1200.828125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "1、 学浪"},
               :properties {:color "red"}}
              {:id #uuid "65c5f287-679c-4286-8e87-6e4a6d125aef",
               :page 9,
               :position {:bounding {:x1 141.65625,
                                     :y1 1232.9453125,
                                     :x2 317.4010009765625,
                                     :y2 1263.9453125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 1232.9453125,
                                   :x2 317.4010009765625,
                                   :y2 1263.9453125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 229.625,
                                   :y1 1237.4453125,
                                   :x2 293.65625,
                                   :y2 1259.5234375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 9},
               :content {:text "数据源：学浪 APP"},
               :properties {:color "red"}}
              {:id #uuid "65c5f28f-ed71-4dee-a01c-22ee6be11e91",
               :page 10,
               :position {:bounding {:x1 134.5,
                                     :y1 120,
                                     :x2 1053.5,
                                     :y2 649,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 10},
               :content {:text "[:span]", :image 1707471503489},
               :properties {:color "red"}}
              {:id #uuid "65c5f296-0898-42e1-9802-c1f1e6af75a4",
               :page 10,
               :position {:bounding {:x1 152.484375,
                                     :y1 650.6796875,
                                     :x2 878.5703125,
                                     :y2 681.6796875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 152.484375,
                                   :y1 650.6796875,
                                   :x2 878.5703125,
                                   :y2 681.6796875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 174.390625,
                                   :y1 655.1796875,
                                   :x2 856.5299072265625,
                                   :y2 677.2578125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 10},
               :content {:text "⾳乐舞蹈类课程数、整体播放量最⾼，但平均课程播放量较低。推测原因："},
               :properties {:color "red"}}
              {:id #uuid "65c5f29c-800e-4c18-a07b-2d36c837e1e6",
               :page 10,
               :position {:bounding {:x1 141.65625,
                                     :y1 695.296875,
                                     :x2 945.234375,
                                     :y2 726.296875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 695.296875,
                                   :x2 945.234375,
                                   :y2 726.296875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 153.203125,
                                   :y1 699.796875,
                                   :x2 923.2445678710938,
                                   :y2 721.875,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 10},
               :content {:text "1）⾮刚需课程，⽤户在直播间冲动消费买单居多，后续⽆⼈辅导因此播放量受影响"},
               :properties {:color "red"}}
              {:id #uuid "65c5f2a0-e79a-4306-978a-b3e320815781",
               :page 10,
               :position {:bounding {:x1 141.65625,
                                     :y1 740.421875,
                                     :x2 747.2734375,
                                     :y2 771.421875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 740.421875,
                                   :x2 747.2734375,
                                   :y2 771.421875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 153.203125,
                                   :y1 744.921875,
                                   :x2 725.2836303710938,
                                   :y2 767,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 10},
               :content {:text "2）抖⾳较多⾳乐舞蹈类内容⽣产者，导致该类⽬课程数量较多"},
               :properties {:color "red"}}
              {:id #uuid "65c5f2a4-ccfd-454a-9992-284fe2c32f46",
               :page 10,
               :position {:bounding {:x1 141.65625,
                                     :y1 785.046875,
                                     :x2 1031.65625,
                                     :y2 854.9453125,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 151.63038635253906,
                                   :y1 785.046875,
                                   :x2 1031.65625,
                                   :y2 816.046875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 198.671875,
                                   :y1 789.546875,
                                   :x2 1031.65625,
                                   :y2 811.625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 823.9453125,
                                   :x2 185.640625,
                                   :y2 854.9453125,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 141.65625,
                                   :y1 828.4453125,
                                   :x2 163.7421875,
                                   :y2 850.5234375,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 10},
               :content {:text " TOP10 品类中，50%为职业技能类课程，如商业管理、英语、⾏业技能等，⽤户年龄层相对年轻"},
               :properties {:color "red"}}
              {:id #uuid "65c5f2ad-76c0-4b73-948e-f29ab2e5ea12",
               :page 10,
               :position {:bounding {:x1 136.5,
                                     :y1 936,
                                     :x2 1055.5,
                                     :y2 1471,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 10},
               :content {:text "[:span]", :image 1707471533405},
               :properties {:color "red"}}
              {:id #uuid "65c5f2ba-018b-4195-a7b9-cd1670d1677a",
               :page 11,
               :position {:bounding {:x1 131.5,
                                     :y1 123,
                                     :x2 1053.5,
                                     :y2 645,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 11},
               :content {:text "[:span]", :image 1707471546039},
               :properties {:color "red"}}
              {:id #uuid "65c5f2cb-4f7c-415b-b4e8-da5376b73b1f",
               :page 11,
               :position {:bounding {:x1 136.5,
                                     :y1 680,
                                     :x2 1058.5,
                                     :y2 1526,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 11},
               :content {:text "[:span]", :image 1707471563139},
               :properties {:color "red"}}
              {:id #uuid "65c5f2de-a706-4f4f-b04f-04698f9e5e58",
               :page 12,
               :position {:bounding {:x1 131.5,
                                     :y1 123,
                                     :x2 1054.5,
                                     :y2 239,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 12},
               :content {:text "[:span]", :image 1707471582637},
               :properties {:color "red"}}
              {:id #uuid "65c5f2e1-8d08-4784-922d-7d1f1a5f1f7b",
               :page 12,
               :position {:bounding {:x1 141.65625,
                                     :y1 272.9921875,
                                     :x2 185.74082946777344,
                                     :y2 303.9921875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 272.9921875,
                                   :x2 185.74082946777344,
                                   :y2 303.9921875,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 12},
               :content {:text "⼩结"},
               :properties {:color "red"}}
              {:id #uuid "65c5f2ec-49a6-4a7d-9560-e872d02ef5bf",
               :page 12,
               :position {:bounding {:x1 141.65625,
                                     :y1 329.0625,
                                     :x2 1011.2799072265625,
                                     :y2 389.8671875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 329.0625,
                                   :x2 1011.2799072265625,
                                   :y2 360.0625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 153.203125,
                                   :y1 333.5625,
                                   :x2 1011.2799072265625,
                                   :y2 355.640625,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 358.8671875,
                                   :x2 923.3359375,
                                   :y2 389.8671875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 175.2265625,
                                   :y1 363.3671875,
                                   :x2 901.3125,
                                   :y2 385.4453125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 12},
               :content {:text "1、学浪⽤户群体较泛，⼈群偏年轻化，销售⽅式主要为直播销售。抖⾳⾳乐舞蹈类主播较多，因此会影响该类⽬的课程数和销量。但从⼤课题⽅向来看，关键词如下："},
               :properties {:color "red"}}
              {:id #uuid "65c5f2f3-632d-4b77-9f40-35d0ee02461b",
               :page 12,
               :position {:bounding {:x1 181.5,
                                     :y1 412,
                                     :x2 934.5,
                                     :y2 839,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects (),
                          :page 12},
               :content {:text "[:span]", :image 1707471603004},
               :properties {:color "red"}}
              {:id #uuid "65c5f2f8-78d7-4dd1-90c9-6c116192d622",
               :page 12,
               :position {:bounding {:x1 141.65625,
                                     :y1 857.1171875,
                                     :x2 934.28125,
                                     :y2 888.1171875,
                                     :width 1190.4,
                                     :height 1683.84},
                          :rects ({:x1 141.65625,
                                   :y1 857.1171875,
                                   :x2 934.28125,
                                   :y2 888.1171875,
                                   :width 1190.4,
                                   :height 1683.84}
                                  {:x1 153.203125,
                                   :y1 861.6171875,
                                   :x2 912.265625,
                                   :y2 883.6953125,
                                   :width 1190.4,
                                   :height 1683.84}),
                          :page 12},
               :content {:text "2、新课题参考：绘画、爵⼠舞、记忆训练、声⾳训练（普通话、⼝才）、⼈际关系"},
               :properties {:color "red"}}],
 :extra {:page 12}}
