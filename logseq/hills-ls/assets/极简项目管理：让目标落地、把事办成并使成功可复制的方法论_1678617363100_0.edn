{:highlights [{:id #uuid "64258858-253b-48d5-8e29-9d55d1a78bea",
               :page 14,
               :position {:bounding {:x1 115.1640625,
                                     :y1 89.0234375,
                                     :x2 501.8361511230469,
                                     :y2 119.5234375,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 115.1640625,
                                   :y1 89.0234375,
                                   :x2 501.8361511230469,
                                   :y2 119.5234375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 158.125,
                                   :y1 94.0234375,
                                   :x2 186.78077697753906,
                                   :y2 115.5078125,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 14},
               :content {:text "前言 创业公司也适用的极简项目管理法"},
               :properties {:color "yellow"}}
              {:id #uuid "64258863-ce0a-4fe7-a427-41376ca731e8",
               :page 14,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 535.0750122070312,
                                     :y2 235.171875,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 110.890625,
                                   :y1 137.3125,
                                   :x2 526.7634887695312,
                                   :y2 160.3125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 162.265625,
                                   :x2 485.1762390136719,
                                   :y2 185.265625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 165.765625,
                                   :x2 485.1762390136719,
                                   :y2 182.3984375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 187.21875,
                                   :x2 535.0750122070312,
                                   :y2 210.21875,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 190.71875,
                                   :x2 535.0750122070312,
                                   :y2 207.3515625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 212.171875,
                                   :x2 343.7804260253906,
                                   :y2 235.171875,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 14},
               :content {:text "我们处在一个VUCA的时代，易变性（Volatility）、不确定性（Uncertainty）、复杂性（Complexity）和模糊性（Ambiguity）给我们的工作带来了很多困扰。变化是VUCA的根源，VUCA时代唯一不变的就是变化。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258886-6b0e-4cc6-9f86-ce7a1f009d96",
               :page 14,
               :position {:bounding {:x1 0,
                                     :y1 68.5,
                                     :x2 526.7686767578125,
                                     :y2 401.5234375,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 110.890625,
                                   :y1 253.7578125,
                                   :x2 526.7686767578125,
                                   :y2 276.7578125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 278.7109375,
                                   :x2 526.7626953125,
                                   :y2 301.7109375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 282.2109375,
                                   :x2 526.7626953125,
                                   :y2 298.84375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 303.6640625,
                                   :x2 518.4421997070312,
                                   :y2 326.6640625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 307.1640625,
                                   :x2 518.4421997070312,
                                   :y2 323.796875,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 328.6171875,
                                   :x2 526.7636108398438,
                                   :y2 351.6171875,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 332.1171875,
                                   :x2 526.7636108398438,
                                   :y2 348.75,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 353.5703125,
                                   :x2 526.7626953125,
                                   :y2 376.5703125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 357.0703125,
                                   :x2 526.7626953125,
                                   :y2 373.703125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.6171875,
                                   :y1 378.5234375,
                                   :x2 127.52225494384766,
                                   :y2 401.5234375,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 14},
               :content {:text "项目管理本质上是一种基于战略方向，组织开拓创新和应对变化的学问，其目的在于调配各方资源，让它们在短时间内形成合力，完成突破性的商业目标。按照汤姆·彼得斯（Tom Peters）的说法，项目管理已经站到了“管理舞台的中央”，卓越的项目管理能力已成为企业的竞争力，而且是一种核心竞争力。"},
               :properties {:color "yellow"}}
              {:id #uuid "642588b1-c714-4743-9031-6fe9c32c0c3b",
               :page 15,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 627.2626953125,
                                     :y2 294.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 88.9140625,
                                   :x2 607.427978515625,
                                   :y2 116.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 118.609375,
                                   :x2 627.2626342773438,
                                   :y2 146.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 122.609375,
                                   :x2 627.2626342773438,
                                   :y2 142.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 148.3125,
                                   :x2 607.4558715820312,
                                   :y2 175.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 152.3125,
                                   :x2 607.4558715820312,
                                   :y2 172.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 178.0078125,
                                   :x2 607.************,
                                   :y2 205.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 182.0078125,
                                   :x2 607.************,
                                   :y2 201.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 207.7109375,
                                   :x2 627.2626953125,
                                   :y2 235.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 211.7109375,
                                   :x2 627.2626953125,
                                   :y2 231.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 237.4140625,
                                   :x2 627.2626953125,
                                   :y2 264.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 241.4140625,
                                   :x2 627.2626953125,
                                   :y2 261.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 267.109375,
                                   :x2 270.6838684082031,
                                   :y2 294.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 15},
               :content {:text "国内经验主义盛行，然而事实证明在项目管理这个行当中，过度相信经验，效果并不好。经验仅代表过去，依托于传统要素和经验的管理方法越来越难以适应当下的行业发展需求。更有甚者，这些成功的经验成了进一步前进的桎梏。因此，切记不要过分迷信自己的经验，经过众人验证的科学方法不同于个人经验，无论个人经验怎样有效，都无法代替被众人验证过的科学方法。"},
               :properties {:color "yellow"}}
              {:id #uuid "642588b6-4388-4414-804f-7850e70eeef1",
               :page 15,
               :position {:bounding {:x1 0,
                                     :y1 96.5,
                                     :x2 627.2626953125,
                                     :y2 403.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 316.609375,
                                   :x2 627.2348022460938,
                                   :y2 344.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 346.3125,
                                   :x2 627.2626953125,
                                   :y2 373.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 350.3125,
                                   :x2 627.2626953125,
                                   :y2 370.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 376.0078125,
                                   :x2 250.87696838378906,
                                   :y2 403.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 15},
               :content {:text "未来几十年，经济发展速度很可能会下降，机会也不再遍地都是，这就更需要企业用科学的方法把成本做低、把速度做快、把质量做好。"},
               :properties {:color "yellow"}}
              {:id #uuid "642588c9-a570-40c0-8826-a75aed04ee19",
               :page 16,
               :position {:bounding {:x1 131.984375,
                                     :y1 475.0078125,
                                     :x2 270.6560363769531,
                                     :y2 502.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 475.0078125,
                                   :x2 270.6560363769531,
                                   :y2 502.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 16},
               :content {:text "本书共三部分："},
               :properties {:color "yellow"}}
              {:id #uuid "642588cc-75cc-402d-87c0-6dc8ec8d4df5",
               :page 16,
               :position {:bounding {:x1 0,
                                     :y1 166.5,
                                     :x2 627.2348022460938,
                                     :y2 581.7109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 524.5078125,
                                   :x2 627.2348022460938,
                                   :y2 552.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 554.2109375,
                                   :x2 191.44068908691406,
                                   :y2 581.7109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 16},
               :content {:text "第一部分介绍项目管理的基础知识，为后续的学习和交流打好基础。"},
               :properties {:color "yellow"}}
              {:id #uuid "642588d0-2664-44c6-869f-6a31b452d81b",
               :page 16,
               :position {:bounding {:x1 0,
                                     :y1 194.5,
                                     :x2 627.2626953125,
                                     :y2 690.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 603.7109375,
                                   :x2 627.2348022460938,
                                   :y2 631.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 633.4140625,
                                   :x2 627.2626953125,
                                   :y2 660.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 637.4140625,
                                   :x2 627.2626953125,
                                   :y2 657.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 663.109375,
                                   :x2 587.6437377929688,
                                   :y2 690.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 16},
               :content {:text "第二部分介绍实施极简项目管理的五大过程，这部分也是全书的重点。为方便项目的实施和落地，这些过程可以被分解为19个步骤，即极简项目管理地图，也称为“三字经”。"},
               :properties {:color "yellow"}}
              {:id #uuid "642588d9-8bba-4f7d-872a-7144a2ee6577",
               :page 16,
               :position {:bounding {:x1 0,
                                     :y1 236.5,
                                     :x2 627.2626953125,
                                     :y2 799.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 250.5,
                                   :x2 0,
                                   :y2 267.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 712.609375,
                                   :x2 627.2348022460938,
                                   :y2 740.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 742.3125,
                                   :x2 627.2626953125,
                                   :y2 769.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 746.3125,
                                   :x2 627.2626953125,
                                   :y2 766.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 772.0078125,
                                   :x2 468.7840576171875,
                                   :y2 799.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 16},
               :content {:text "第三部分着眼于项目管理者的成长问题。项目管理者可以说是项目的灵魂人物，一方面要选择合适的人管理项目，另一方面要把合适的人培养好，二者都很重要。"},
               :properties {:color "yellow"}}
              {:id #uuid "6425895b-af26-47fb-ba30-d39bfed5002e",
               :page 19,
               :position {:bounding {:x1 226.5703125,
                                     :y1 106.9140625,
                                     :x2 507.8586730957031,
                                     :y2 142.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 226.5703125,
                                   :y1 106.9140625,
                                   :x2 507.8586730957031,
                                   :y2 142.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 328.84375,
                                   :y1 111.9140625,
                                   :x2 362.95233154296875,
                                   :y2 137.484375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 19},
               :content {:text "第一部分 认识项目管理"},
               :properties {:color "yellow"}}
              {:id #uuid "6425895d-7c13-4994-bf7d-9b8e14d6600e",
               :page 19,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 627.23486328125,
                                     :y2 220.8125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 163.609375,
                                   :x2 627.23486328125,
                                   :y2 191.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 193.3125,
                                   :x2 171.63380432128906,
                                   :y2 220.8125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 19},
               :content {:text "极简项目管理是让目标落地、把事办成并使成功可复制的方法论。"},
               :properties {:color "yellow"}}
              {:id #uuid "*************-4d2d-a054-260c9051a9c3",
               :page 20,
               :position {:bounding {:x1 92.3203125,
                                     :y1 110.578125,
                                     :x2 383.144287109375,
                                     :y2 146.078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.3203125,
                                   :y1 110.578125,
                                   :x2 383.144287109375,
                                   :y2 146.078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 155.53125,
                                   :y1 116.078125,
                                   :x2 189.6379852294922,
                                   :y2 141.3671875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 20},
               :content {:text "第1章 极简项目管理基础"},
               :properties {:color "yellow"}}
              {:id #uuid "6425896d-af53-46b0-9d98-fc611d3ad5fa",
               :page 20,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 642.082763671875,
                                     :y2 244.5,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 168.046875,
                                   :x2 620.9933471679688,
                                   :y2 195.546875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 465.8515625,
                                   :y1 217,
                                   :x2 642.082763671875,
                                   :y2 244.5,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 20},
               :content {:text "用户根本不知道自己需要什么，直到你把它摆在他面前。——史蒂夫·乔布斯"},
               :properties {:color "yellow"}}
              {:id #uuid "642589cd-0080-4f03-82b3-2070e7a21849",
               :page 20,
               :position {:bounding {:x1 0,
                                     :y1 208.5,
                                     :x2 640.57568359375,
                                     :y2 714.4296875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 341.9208984375,
                                   :y1 657.5546875,
                                   :x2 640.57568359375,
                                   :y2 685.0546875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 686.9296875,
                                   :x2 550.2444458007812,
                                   :y2 714.4296875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 20},
               :content {:text "我确实说不清我想要的东西是什么样子的，但我能说清的是你给我的东西不是我想要的"},
               :properties {:color "yellow"}}
              {:id #uuid "642589de-c62f-497a-b74b-579d2e3acb65",
               :page 22,
               :position {:bounding {:x1 92.390625,
                                     :y1 115.015625,
                                     :x2 437.63018798828125,
                                     :y2 150.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 115.015625,
                                   :x2 139.2578125,
                                   :y2 150.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 156.3125,
                                   :y1 115.015625,
                                   :x2 437.63018798828125,
                                   :y2 150.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 130.7421875,
                                   :y1 120.015625,
                                   :x2 139.2578125,
                                   :y2 145.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 22},
               :content {:text "1.1 项目是独特的一次性事业"},
               :properties {:color "yellow"}}
              {:id #uuid "642589e5-90d7-4e76-b78e-ab3334927aff",
               :page 22,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 642.1198120117188,
                                     :y2 284.7109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 178.0078125,
                                   :x2 607.427978515625,
                                   :y2 205.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 207.7109375,
                                   :x2 132.01220703125,
                                   :y2 235.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 211.7109375,
                                   :x2 132.01220703125,
                                   :y2 231.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 424.203125,
                                   :y1 257.2109375,
                                   :x2 642.1198120117188,
                                   :y2 284.7109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 22},
               :content {:text "如果你从不犯错，这意味着你从来没有尝试过任何新事物。——阿尔伯特·爱因斯坦"},
               :properties {:color "yellow"}}
              {:id #uuid "64258a1e-93bb-4532-a2e7-1f1c9190c4fb",
               :page 23,
               :position {:bounding {:x1 0,
                                     :y1 110.5,
                                     :x2 627.2626953125,
                                     :y2 514.2109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 211.2197723388672,
                                   :y1 425.5078125,
                                   :x2 627.2348480224609,
                                   :y2 453.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 455.2109375,
                                   :x2 627.2626953125,
                                   :y2 482.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 459.2109375,
                                   :x2 627.2626953125,
                                   :y2 479.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 486.7109375,
                                   :x2 389.54083251953125,
                                   :y2 514.2109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 23},
               :content {:text "通过开展持续的活动来生产同样的产品或提供重复的服务的工作就是运营，而为创造独特的产品、服务或成果而进行的临时性的工作则是项目。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258a6c-4af7-45d4-86dd-1dc0c7ba1243",
               :page 24,
               :position {:bounding {:x1 92.3203125,
                                     :y1 123.0390625,
                                     :x2 534.8871459960938,
                                     :y2 158.5390625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.3203125,
                                   :y1 123.0390625,
                                   :x2 534.8871459960938,
                                   :y2 158.5390625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 155.53125,
                                   :y1 128.5390625,
                                   :x2 189.6379852294922,
                                   :y2 153.828125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 24},
               :content {:text "1.1.1 项目开始时总存在说不清的成分"},
               :properties {:color "yellow"}}
              {:id #uuid "64258a8b-e2b9-4bc8-a20a-a56fe505652a",
               :page 25,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 640.5780639648438,
                                     :y2 175.078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 539.05419921875,
                                   :y1 88.8359375,
                                   :x2 640.5780639648438,
                                   :y2 116.3359375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 118.2109375,
                                   :x2 640.5725708007812,
                                   :y2 145.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 122.2109375,
                                   :x2 640.5725708007812,
                                   :y2 141.7890625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 147.578125,
                                   :x2 260.1842041015625,
                                   :y2 175.078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 25},
               :content {:text "人类总会犯一个错误：人总是用之前见过的东西，来描述一个未曾出现过的东西（见图1-2）。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ab2-ab1b-4563-a6b5-d1a03fce4113",
               :page 26,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 640.5796508789062,
                                     :y2 321.9296875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 88.8359375,
                                   :x2 640.5796508789062,
                                   :y2 116.3359375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 118.2109375,
                                   :x2 640.5725708007812,
                                   :y2 145.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 122.2109375,
                                   :x2 640.5725708007812,
                                   :y2 141.7890625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 147.578125,
                                   :x2 640.5725708007812,
                                   :y2 175.078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 151.578125,
                                   :x2 640.5725708007812,
                                   :y2 171.15625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 176.953125,
                                   :x2 640.57080078125,
                                   :y2 204.453125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 180.953125,
                                   :x2 640.57080078125,
                                   :y2 200.53125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 206.3203125,
                                   :x2 640.5725708007812,
                                   :y2 233.8203125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 210.3203125,
                                   :x2 640.5725708007812,
                                   :y2 229.8984375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 235.6875,
                                   :x2 640.5725708007812,
                                   :y2 263.1875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 239.6875,
                                   :x2 640.5725708007812,
                                   :y2 259.265625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 265.0625,
                                   :x2 640.5725708007812,
                                   :y2 292.5625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 269.0625,
                                   :x2 640.5725708007812,
                                   :y2 288.640625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 294.4296875,
                                   :x2 483.9273681640625,
                                   :y2 321.9296875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 26},
               :content {:text "可见，项目在开始时总存在说不清的成分，这就会导致一个问题——频繁的变更。说白了，这种频繁的变更是由项目工作本身的性质决定的。很多人不懂这个道理，总是追着客户，要求客户说清楚之后再干。这些人会让客户签字——你不是想要“马”吗？那你签个字我再干。到最后，他们发现签字并没有改变频繁变更的状况，反而为后续与客户意见不合造成了隐患。我并不反对签字，因为不签字客户要求的变更会更随意，但我反对把签字当成与客户意见不合，进而“扯皮”的依据。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ac5-5a88-4552-b275-b3e34992d210",
               :page 26,
               :position {:bounding {:x1 0,
                                     :y1 110.5,
                                     :x2 640.5796508789062,
                                     :y2 429.625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 343.3828125,
                                   :x2 640.5796508789062,
                                   :y2 370.8828125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 372.75,
                                   :x2 640.57568359375,
                                   :y2 400.25,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 376.75,
                                   :x2 640.57568359375,
                                   :y2 396.328125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 402.125,
                                   :x2 248.9670867919922,
                                   :y2 429.625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 26},
               :content {:text "现在，如果再看到有人逼着客户在项目开始时把所有问题都说清楚，你也可以怼他一句：“孩子没出生之前，你能把孩子长什么样说清吗？”"},
               :properties {:color "yellow"}}
              {:id #uuid "64258acf-81fe-41d2-9063-4d509f52762e",
               :page 27,
               :position {:bounding {:x1 92.390625,
                                     :y1 124.015625,
                                     :x2 591.065673828125,
                                     :y2 159.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 124.015625,
                                   :x2 591.065673828125,
                                   :y2 159.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 156.3125,
                                   :y1 129.015625,
                                   :x2 190.4210968017578,
                                   :y2 154.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 27},
               :content {:text "1.1.2 项目是一个业务过程，而非技术过程"},
               :properties {:color "yellow"}}
              {:id #uuid "64258b3b-4299-44a2-890b-60d853b07465",
               :page 27,
               :position {:bounding {:x1 0,
                                     :y1 124.5,
                                     :x2 627.2626953125,
                                     :y2 559.2109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 389.5130615234375,
                                   :y1 472.3125,
                                   :x2 627.23486328125,
                                   :y2 499.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 502.0078125,
                                   :x2 627.2626953125,
                                   :y2 529.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 506.0078125,
                                   :x2 627.2626953125,
                                   :y2 525.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 531.7109375,
                                   :x2 151.82693481445312,
                                   :y2 559.2109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 27},
               :content {:text "建议你要求客户说明他们为什么会提出这些要求。通过不断地提问，你最终会知道问题的根源。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258b6b-3bb7-432c-8908-6b9ebb21e445",
               :page 27,
               :position {:bounding {:x1 0,
                                     :y1 152.5,
                                     :x2 627.2626953125,
                                     :y2 796.8125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 180.5,
                                   :x2 0,
                                   :y2 197.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 250.5,
                                   :x2 0,
                                   :y2 267.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 151.81912231445312,
                                   :y1 531.7109375,
                                   :x2 627.2626953125,
                                   :y2 559.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 561.4140625,
                                   :x2 607.454833984375,
                                   :y2 588.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 565.4140625,
                                   :x2 607.454833984375,
                                   :y2 585.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 591.109375,
                                   :x2 627.2626953125,
                                   :y2 618.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 595.109375,
                                   :x2 627.2626953125,
                                   :y2 614.90625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 620.8125,
                                   :x2 627.2626953125,
                                   :y2 648.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 624.8125,
                                   :x2 627.2626953125,
                                   :y2 644.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 650.5078125,
                                   :x2 627.2626953125,
                                   :y2 678.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 654.5078125,
                                   :x2 627.2626953125,
                                   :y2 674.3046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 680.2109375,
                                   :x2 627.2626342773438,
                                   :y2 707.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 684.2109375,
                                   :x2 627.2626342773438,
                                   :y2 704.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 709.9140625,
                                   :x2 627.2615356445312,
                                   :y2 737.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 713.9140625,
                                   :x2 627.2615356445312,
                                   :y2 733.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 739.609375,
                                   :x2 607.4558715820312,
                                   :y2 767.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 743.609375,
                                   :x2 607.4558715820312,
                                   :y2 763.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 769.3125,
                                   :x2 429.1624450683594,
                                   :y2 796.8125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 27},
               :content {:text "实际上，针对业务的解决方案才是客户的真实需求。具体而言，面对客户所说的“想要一匹跑得更快的马”这个问题，我们从开始就不要盯着马本身提问，而是应该去问客户业务问题。这些问题可以是：你要马干什么呀？想解决什么问题呀？你要的这匹马要在什么情况下使用呢？使用时会遇到什么困难呀？总之，围绕使用的背景、环境、目的等业务问题进行提问，而不是围绕“对马详细描述”的技术问题进行提问。在这里，我要提醒你，遇到问题以后不要试图仅仅解决问题本身，还要去解决问题所在环境的问题。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ba7-aa9e-4bef-8def-a8b73a1002b6",
               :page 28,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 627.2682495117188,
                                     :y2 235.2109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 88.9140625,
                                   :x2 627.23486328125,
                                   :y2 116.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 118.609375,
                                   :x2 627.2682495117188,
                                   :y2 146.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 122.609375,
                                   :x2 627.2682495117188,
                                   :y2 142.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 148.3125,
                                   :x2 607.4512939453125,
                                   :y2 175.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 152.3125,
                                   :x2 607.4512939453125,
                                   :y2 172.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 178.0078125,
                                   :x2 627.2660522460938,
                                   :y2 205.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 182.0078125,
                                   :x2 627.2660522460938,
                                   :y2 201.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 207.7109375,
                                   :x2 567.8341064453125,
                                   :y2 235.2109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 28},
               :content {:text "事实上，系统工程的一个基本原理就是超越系统本身解决问题，即N维系统产生的问题只有在N+1维系统中才能解决。用我们老祖宗的话来说，叫“不识庐山真面目，只缘身在此山中”。在替别人分析问题时，我们能抽丝剥茧，分析得头头是道。当自己遇到同样的问题时，却总是很困惑和痛苦。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258bfe-bad8-4cbe-b93d-7dc5ebd98aa7",
               :page 28,
               :position {:bounding {:x1 0,
                                     :y1 82.5,
                                     :x2 627.2627563476562,
                                     :y2 471.0078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 324.7109375,
                                   :x2 627.2347412109375,
                                   :y2 352.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 354.4140625,
                                   :x2 627.2627563476562,
                                   :y2 381.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 358.4140625,
                                   :x2 627.2627563476562,
                                   :y2 378.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 384.109375,
                                   :x2 627.2626953125,
                                   :y2 411.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 388.109375,
                                   :x2 627.2626953125,
                                   :y2 407.90625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 413.8125,
                                   :x2 627.2615356445312,
                                   :y2 441.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 417.8125,
                                   :x2 627.2615356445312,
                                   :y2 437.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 443.5078125,
                                   :x2 567.8341674804688,
                                   :y2 471.0078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 28},
               :content {:text "请务必记住：项目是一个业务过程，而不是技术过程。这是重要的项目思维！项目管理者也应该是一个业务层面的管理者。项目的业务思维方式，能帮助你快速理解客户的痛点，明白客户“真正的需求”，在此基础上你再给出专业的反馈，并提供解决方案。这时候，你还应该具备业务建模能力。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c18-d135-400b-a039-986f8cdc148d",
               :page 28,
               :position {:bounding {:x1 0,
                                     :y1 152.5,
                                     :x2 627.2626342773438,
                                     :y2 579.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 493.0078125,
                                   :x2 627.2348022460938,
                                   :y2 520.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 522.7109375,
                                   :x2 627.2626342773438,
                                   :y2 550.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 526.7109375,
                                   :x2 627.2626342773438,
                                   :y2 546.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 552.4140625,
                                   :x2 468.7840576171875,
                                   :y2 579.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 28},
               :content {:text "说到建模，房地产行业给我们提供了先例：客户买房子之前是先要看看样板房和模型的，什么都看不到你敢买房子吗？答案一定是否定的。除非，你不是自己住！"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c20-bb81-46bd-b1bc-5b00021543c9",
               :page 28,
               :position {:bounding {:x1 0,
                                     :y1 194.5,
                                     :x2 627.2626953125,
                                     :y2 777.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 250.5,
                                   :x2 0,
                                   :y2 267.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 601.9140625,
                                   :x2 627.23486328125,
                                   :y2 629.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 631.609375,
                                   :x2 627.2626953125,
                                   :y2 659.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 635.609375,
                                   :x2 627.2626953125,
                                   :y2 655.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 661.3125,
                                   :x2 627.2626953125,
                                   :y2 688.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 665.3125,
                                   :x2 627.2626953125,
                                   :y2 685.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 691.0078125,
                                   :x2 627.2626953125,
                                   :y2 718.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 695.0078125,
                                   :x2 627.2626953125,
                                   :y2 714.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 720.7109375,
                                   :x2 627.2626953125,
                                   :y2 748.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 724.7109375,
                                   :x2 627.2626953125,
                                   :y2 744.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 750.4140625,
                                   :x2 250.87696838378906,
                                   :y2 777.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 28},
               :content {:text "可见，要能把需求确认落地，实施者必须有很好的业务建模能力，并以咨询方式展开，也就是要推出自己的方案，快速地给客户展示合理的样例。一方面，这样可以较好地引导客户提出合理的需求，把他们的思路控制在可执行的范围内。另一方面，通过逆向反馈，推动客户确认需求，可以较大程度地避免理解上的偏差。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c2e-7c69-4ebb-bfff-f47e146386eb",
               :page 28,
               :position {:bounding {:x1 -0.000003814697265625,
                                     :y1 278.5,
                                     :x2 734.6663780212402,
                                     :y2 1910,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 278.5,
                                   :x2 0,
                                   :y2 295.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 294,
                                   :x2 734,
                                   :y2 294,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.98020935058594,
                                   :y1 799.9078369140625,
                                   :x2 627.2340545654297,
                                   :y2 827.4078369140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.000003814697265625,
                                   :y1 828.6593627929688,
                                   :x2 734.6663780212402,
                                   :y2 950.3859252929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 960,
                                   :x2 734,
                                   :y2 1910,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 28},
               :content {:text "多年的经验告诉我，对于项目，一定要给客户看到“样板房”，如样品、模型、照片、软件的基本界面等，否则很可能会出问题！"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c38-c2c5-430c-b4c6-c5b4792d3527",
               :page 29,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 627.2652587890625,
                                     :y2 255.0078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 138.4140625,
                                   :x2 627.23486328125,
                                   :y2 165.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 168.109375,
                                   :x2 627.2626953125,
                                   :y2 195.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 172.109375,
                                   :x2 627.2626953125,
                                   :y2 191.90625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 197.8125,
                                   :x2 627.2652587890625,
                                   :y2 225.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 201.8125,
                                   :x2 627.2652587890625,
                                   :y2 221.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 227.5078125,
                                   :x2 231.06227111816406,
                                   :y2 255.0078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 29},
               :content {:text "回到福特汽车这个例子。如果问清楚客户要这匹马的目的是更快的话，问题就简单了——在脚下装两个滑轮行不行？不行的话再装两个翅膀，还不行就装发动机......反正比找跑得更快的马强多了。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c40-3aea-4f7c-ad4c-fc45f53b7c36",
               :page 30,
               :position {:bounding {:x1 92.390625,
                                     :y1 124.015625,
                                     :x2 539.9148559570312,
                                     :y2 159.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 124.015625,
                                   :x2 539.9148559570312,
                                   :y2 159.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 156.3125,
                                   :y1 129.015625,
                                   :x2 190.4210968017578,
                                   :y2 154.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 30},
               :content {:text "1.1.3 拥抱变更：无关人品，项目使然"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c56-712c-4bc8-ada9-57c606bbf170",
               :page 30,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 627.2347412109375,
                                     :y2 252.3125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 195.109375,
                                   :x2 627.2347412109375,
                                   :y2 222.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 224.8125,
                                   :x2 607.************,
                                   :y2 252.3125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 30},
               :content {:text "客户提出改动，是在帮我们还是在害我们呢？实际上，客户每一次提出新的要求，都是在帮我们逐步逼近事实真相。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c5c-df55-49a3-8199-5844127c6e66",
               :page 30,
               :position {:bounding {:x1 0,
                                     :y1 40.5,
                                     :x2 627.2626953125,
                                     :y2 480.0078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 274.3125,
                                   :x2 627.2417602539062,
                                   :y2 301.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 304.0078125,
                                   :x2 627.2626342773438,
                                   :y2 331.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 308.0078125,
                                   :x2 627.2626342773438,
                                   :y2 327.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 333.7109375,
                                   :x2 627.2626953125,
                                   :y2 361.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 337.7109375,
                                   :x2 627.2626953125,
                                   :y2 357.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 363.4140625,
                                   :x2 627.2626953125,
                                   :y2 390.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 367.4140625,
                                   :x2 627.2626953125,
                                   :y2 387.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 393.109375,
                                   :x2 607.4557495117188,
                                   :y2 420.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 397.109375,
                                   :x2 607.4557495117188,
                                   :y2 416.90625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 422.8125,
                                   :x2 627.2615966796875,
                                   :y2 450.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 426.8125,
                                   :x2 627.2615966796875,
                                   :y2 446.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 452.5078125,
                                   :x2 508.4056701660156,
                                   :y2 480.0078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 30},
               :content {:text "但是，很多项目从业者的结论是“你在害我”——为什么不早说？人喜欢改变但又害怕被改变，面对这种变更，他们总感觉之前的工作被否定了——辛辛苦苦的努力，你一句话就让我的努力都白费了！这样，每经历一次变更，项目从业者的内心就会受到一次折磨，变更的次数多了，情绪就变坏了。于是，他们开始学习各种各样的“套路”，这使得迎合这种需求的培训、文章、书籍等开始在市场上大行其道！"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c7d-fcc8-438a-9811-b40c1348735f",
               :page 30,
               :position {:bounding {:x1 0,
                                     :y1 138.5,
                                     :x2 627.2626953125,
                                     :y2 618.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 502.0078125,
                                   :x2 627.23486328125,
                                   :y2 529.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 531.7109375,
                                   :x2 607.4547729492188,
                                   :y2 559.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 535.7109375,
                                   :x2 607.4547729492188,
                                   :y2 555.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 561.4140625,
                                   :x2 627.2626953125,
                                   :y2 588.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 565.4140625,
                                   :x2 627.2626953125,
                                   :y2 585.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 591.109375,
                                   :x2 151.82691955566406,
                                   :y2 618.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 30},
               :content {:text "很多人在遇到变更时，首先想到的是客户多么不可理喻，还时不时问一句“改是可以，但这是不是最后一次”。问题是，在没有看到最终结果之前，客户怎么知道这是不是最后一次呢？"},
               :properties {:color "yellow"}}
              {:id #uuid "64258c94-1a47-4729-b58d-0fa5e5388e8f",
               :page 30,
               :position {:bounding {:x1 -0.00000762939453125,
                                     :y1 194.5,
                                     :x2 734.6686325073242,
                                     :y2 1910,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 250.5,
                                   :x2 0,
                                   :y2 267.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 266,
                                   :x2 734,
                                   :y2 266,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.97796630859375,
                                   :y1 640.6031494140625,
                                   :x2 627.2354125976562,
                                   :y2 668.1031494140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.00000762939453125,
                                   :y1 669.3624877929688,
                                   :x2 734.6605758666992,
                                   :y2 699.0499877929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.00000762939453125,
                                   :y1 699.0578002929688,
                                   :x2 734.6644821166992,
                                   :y2 728.7453002929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.00000762939453125,
                                   :y1 728.7609252929688,
                                   :x2 734.668327331543,
                                   :y2 758.4484252929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.00000762939453125,
                                   :y1 758.4640502929688,
                                   :x2 734.6686325073242,
                                   :y2 788.1515502929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.000003814697265625,
                                   :y1 788.1593627929688,
                                   :x2 734.6683921813965,
                                   :y2 950.3859252929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 958.5,
                                   :x2 0,
                                   :y2 975.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 960,
                                   :x2 734,
                                   :y2 1910,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 30},
               :content {:text "在“客户是上帝”的口号下，一方面项目经理要靠客户赚钱，另一方面内心又接受不了频繁的变更方案，所以就很痛苦。“客户虐我千百遍，我待客户如初恋”的说法就足以说明问题。在无法改变现实的情况下，他们只能忍着，忍的时间长了就开始“虐”自己。实际上，“虐”这个字的负面情绪特别重，一说出口就意味着这份工作无法带来幸福感了，因为这很容易给客户经理一种心理暗示——这工作真苦！张口就说负面词汇，这是很多职场人焦虑的重要原因之一。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ca3-c1ed-4179-9cb4-829f2d2ca256",
               :page 31,
               :position {:bounding {:x1 0,
                                     :y1 26.5,
                                     :x2 627.2626953125,
                                     :y2 255.0078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 168.109375,
                                   :x2 627.2348022460938,
                                   :y2 195.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 197.8125,
                                   :x2 627.2626953125,
                                   :y2 225.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 201.8125,
                                   :x2 627.2626953125,
                                   :y2 221.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 227.5078125,
                                   :x2 250.87696838378906,
                                   :y2 255.0078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 31},
               :content {:text "更严重的是，有的人在面对变更时，不是去消灭新需求，而是去消灭提出新需求的人。于是，因为变更导致的冲突、打架事件层出不穷。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258cac-3cd5-4403-86ee-08230eb807bb",
               :page 31,
               :position {:bounding {:x1 0,
                                     :y1 68.5,
                                     :x2 627.2626953125,
                                     :y2 393.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 277.0078125,
                                   :x2 627.2348022460938,
                                   :y2 304.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 306.7109375,
                                   :x2 627.2626953125,
                                   :y2 334.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 310.7109375,
                                   :x2 627.2626953125,
                                   :y2 330.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 336.4140625,
                                   :x2 627.2626953125,
                                   :y2 363.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 340.4140625,
                                   :x2 627.2626953125,
                                   :y2 360.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 366.109375,
                                   :x2 290.4907531738281,
                                   :y2 393.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 31},
               :content {:text "实际上，只要理解项目的不确定性，就很容易明白绝大多数变更不是人的问题，而是项目属性所决定的。在频繁变更这件事上，无论谁做项目都不可避免，因此唯一需要调整的就是我们面对变更的态度。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258cc2-29ac-427a-8e45-b624adf08ad3",
               :page 31,
               :position {:bounding {:x1 0,
                                     :y1 124.5,
                                     :x2 627.2626953125,
                                     :y2 591.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 180.5,
                                   :x2 0,
                                   :y2 197.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 415.609375,
                                   :x2 627.2347412109375,
                                   :y2 443.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 445.3125,
                                   :x2 627.2626953125,
                                   :y2 472.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 449.3125,
                                   :x2 627.2626953125,
                                   :y2 469.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 475.0078125,
                                   :x2 627.2615966796875,
                                   :y2 502.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 479.0078125,
                                   :x2 627.2615966796875,
                                   :y2 498.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 504.7109375,
                                   :x2 607.************,
                                   :y2 532.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 508.7109375,
                                   :x2 607.************,
                                   :y2 528.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 534.4140625,
                                   :x2 627.2615966796875,
                                   :y2 561.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 538.4140625,
                                   :x2 627.2615966796875,
                                   :y2 558.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 564.109375,
                                   :x2 132.01220703125,
                                   :y2 591.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 31},
               :content {:text "时刻记住，变更来源于项目属性，与人无关。这会让我们在真正遇到变更时更能够保持冷静的心态和敏锐的洞察力。而且，“客户在帮我们逐步逼近事实真相”这个结论是积极向上的，也往往能让我们自己的情绪好很多。如果你正遇到一个“不可理喻的变更”，你可以马上试试这种思维模式的神奇作用。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258cc8-1c38-4c4a-8f4a-088f5113dd11",
               :page 31,
               :position {:bounding {:x1 0,
                                     :y1 208.5,
                                     :x2 627.2347412109375,
                                     :y2 700.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 613.609375,
                                   :x2 627.2347412109375,
                                   :y2 641.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 643.3125,
                                   :x2 587.6444702148438,
                                   :y2 670.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 647.3125,
                                   :x2 587.6444702148438,
                                   :y2 667.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 673.0078125,
                                   :x2 151.82278442382812,
                                   :y2 700.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 31},
               :content {:text "所以，我们今后在与人沟通变更时，应注意改变态度，少一些抱怨和吐槽，而要说：“谢谢你让我离真相又近了一步。”"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ccd-fa9a-42ed-886a-ef7602536a9a",
               :page 31,
               :position {:bounding {:x1 0,
                                     :y1 250.5,
                                     :x2 627.2347412109375,
                                     :y2 779.7109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 250.5,
                                   :x2 0,
                                   :y2 267.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 722.5078125,
                                   :x2 627.2347412109375,
                                   :y2 750.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 752.2109375,
                                   :x2 567.8319702148438,
                                   :y2 779.7109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 31},
               :content {:text "关于变更，请务必记住：无关人品，项目使然！一句话：在项目中拥抱变更（注意是“拥抱”而非“接受”）。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258cd5-aa0d-4f13-afce-28ae4ed44efc",
               :page 32,
               :position {:bounding {:x1 92.390625,
                                     :y1 115.015625,
                                     :x2 284.18560791015625,
                                     :y2 150.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 115.015625,
                                   :x2 284.18560791015625,
                                   :y2 150.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 130.7421875,
                                   :y1 120.015625,
                                   :x2 164.8507843017578,
                                   :y2 145.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 32},
               :content {:text "1.2 项目的特点"},
               :properties {:color "yellow"}}
              {:id #uuid "64258cdc-840f-4d4e-b9fb-4f93efd2d2b4",
               :page 32,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 642.095703125,
                                     :y2 344.109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 178.0078125,
                                   :x2 627.23486328125,
                                   :y2 205.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 207.7109375,
                                   :x2 627.2626342773438,
                                   :y2 235.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 211.7109375,
                                   :x2 627.2626342773438,
                                   :y2 231.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 237.4140625,
                                   :x2 627.2627563476562,
                                   :y2 264.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 241.4140625,
                                   :x2 627.2627563476562,
                                   :y2 261.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 267.109375,
                                   :x2 389.54083251953125,
                                   :y2 294.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 271.109375,
                                   :x2 389.54083251953125,
                                   :y2 290.90625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 483.609375,
                                   :y1 316.609375,
                                   :x2 642.095703125,
                                   :y2 344.109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 32},
               :content {:text "如果你现在还没有参加项目，那么你多半是在做一些费力不讨好而且枯燥无味的重复性工作。但是，如果你参加了一个项目，日子就更难过了，你会觉得干一件费力不讨好且枯燥无味的重复性工作是多么令人神往。——项目管理谚语"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ce6-86e5-404f-9e17-611fe16acf9d",
               :page 32,
               :position {:bounding {:x1 0,
                                     :y1 82.5,
                                     :x2 627.2626342773438,
                                     :y2 482.7109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 366.109375,
                                   :x2 627.2348022460938,
                                   :y2 393.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 395.8125,
                                   :x2 627.2626342773438,
                                   :y2 423.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 399.8125,
                                   :x2 627.2626342773438,
                                   :y2 419.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 425.5078125,
                                   :x2 627.2626342773438,
                                   :y2 453.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 429.5078125,
                                   :x2 627.2626342773438,
                                   :y2 449.3046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 455.2109375,
                                   :x2 231.06227111816406,
                                   :y2 482.7109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 32},
               :content {:text "项目在本质上是独特的、临时的非重复性工作，要求使用有限的资源，在有限的时间内为特定的人（或组织）完成某种特定目标（产品、服务或成果）。项目的定义非常简洁，但是含义非常深刻。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ced-bdc0-4b04-b088-676396b09afe",
               :page 32,
               :position {:bounding {:x1 0,
                                     :y1 138.5,
                                     :x2 627.2348022460938,
                                     :y2 561.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 504.7109375,
                                   :x2 627.2348022460938,
                                   :y2 532.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 534.4140625,
                                   :x2 369.7329406738281,
                                   :y2 561.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 32},
               :content {:text "首先，定义明确了项目的目的是给出产品、服务或成果，对于结果强调了其“独特性”；"},
               :properties {:color "yellow"}}
              {:id #uuid "64258cf1-3bef-430c-898d-aa02cfcd9be3",
               :page 32,
               :position {:bounding {:x1 131.984375,
                                     :y1 583.9140625,
                                     :x2 508.3768310546875,
                                     :y2 611.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 583.9140625,
                                   :x2 508.3768310546875,
                                   :y2 611.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 32},
               :content {:text "其次，对于工作时限强调了其“临时性”。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258cf6-cd8f-46c5-9846-08be97cb8f67",
               :page 32,
               :position {:bounding {:x1 0,
                                     :y1 180.5,
                                     :x2 627.2627563476562,
                                     :y2 809.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 180.5,
                                   :x2 0,
                                   :y2 197.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 633.4140625,
                                   :x2 627.2348022460938,
                                   :y2 660.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 663.109375,
                                   :x2 627.2627563476562,
                                   :y2 690.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 667.109375,
                                   :x2 627.2627563476562,
                                   :y2 686.90625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 692.8125,
                                   :x2 627.2615966796875,
                                   :y2 720.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 696.8125,
                                   :x2 627.2615966796875,
                                   :y2 716.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 722.5078125,
                                   :x2 627.2626953125,
                                   :y2 750.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 726.5078125,
                                   :x2 627.2626953125,
                                   :y2 746.3046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 752.2109375,
                                   :x2 627.2615356445312,
                                   :y2 779.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 756.2109375,
                                   :x2 627.2615356445312,
                                   :y2 776.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 781.9140625,
                                   :x2 132.01220703125,
                                   :y2 809.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 32},
               :content {:text "独特性说明项目所创造的成果存在与众不同的地方，即成果的不重复性。同样，为创造独特成果而开展工作的过程不仅具有临时性，也具有不重复性。这种“不重复性”会给人们在认识项目的过程中带来很多不确定因素，这就是项目的风险来源。该如何认识“复杂、不确定”的项目呢？答案就是渐进明细。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d0b-f807-4669-8869-937f91dff567",
               :page 33,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 627.2626953125,
                                     :y2 175.8125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 88.9140625,
                                   :x2 607.4279174804688,
                                   :y2 116.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 118.609375,
                                   :x2 627.2626953125,
                                   :y2 146.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 122.609375,
                                   :x2 627.2626953125,
                                   :y2 142.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 148.3125,
                                   :x2 290.4907531738281,
                                   :y2 175.8125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 33},
               :content {:text "独特性、临时性和渐进明细性，是项目最显著的三大特性，其中独特性和临时性是最基本的特性，渐进明细性是在这个基础上衍生出来的。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d2b-b810-4434-8175-8b5500186d56",
               :page 34,
               :position {:bounding {:x1 92.390625,
                                     :y1 124.015625,
                                     :x2 258.5973205566406,
                                     :y2 159.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 124.015625,
                                   :x2 258.5973205566406,
                                   :y2 159.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 156.3125,
                                   :y1 129.015625,
                                   :x2 190.4210968017578,
                                   :y2 154.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 34},
               :content {:text "1.2.1 独特性"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d2e-f5f0-4122-9791-34b3240a32be",
               :page 34,
               :position {:bounding {:x1 131.984375,
                                     :y1 195.109375,
                                     :x2 488.5704040527344,
                                     :y2 222.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 195.109375,
                                   :x2 488.5704040527344,
                                   :y2 222.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 34},
               :content {:text "1.独特性意味着项目成果的“不重复性”"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d33-6616-4cb7-8224-4ccc7d68bf6e",
               :page 34,
               :position {:bounding {:x1 0,
                                     :y1 26.5,
                                     :x2 627.2626953125,
                                     :y2 331.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 244.609375,
                                   :x2 627.2347412109375,
                                   :y2 272.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 274.3125,
                                   :x2 627.2626953125,
                                   :y2 301.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 278.3125,
                                   :x2 627.2626953125,
                                   :y2 298.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 304.0078125,
                                   :x2 468.78302001953125,
                                   :y2 331.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 34},
               :content {:text "每个项目创造的可交付成果（产品、服务或成果）都是独特的。当前的项目与以前的项目相比，会或多或少存在不一样的地方，也就是具有一定的“不重复性”。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d40-e7cc-4a55-bdac-840d8e510333",
               :page 34,
               :position {:bounding {:x1 0,
                                     :y1 68.5,
                                     :x2 627.2626953125,
                                     :y2 529.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 353.5078125,
                                   :x2 627.234619140625,
                                   :y2 381.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 383.2109375,
                                   :x2 627.2626953125,
                                   :y2 410.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 387.2109375,
                                   :x2 627.2626953125,
                                   :y2 407.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 412.9140625,
                                   :x2 627.2626953125,
                                   :y2 440.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 416.9140625,
                                   :x2 627.2626953125,
                                   :y2 436.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 442.609375,
                                   :x2 627.2626953125,
                                   :y2 470.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 446.609375,
                                   :x2 627.2626953125,
                                   :y2 466.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 472.3125,
                                   :x2 627.2625732421875,
                                   :y2 499.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 476.3125,
                                   :x2 627.2625732421875,
                                   :y2 496.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 502.0078125,
                                   :x2 389.54083251953125,
                                   :y2 529.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 34},
               :content {:text "核电在国内发展得很快，在广东、浙江、山东、广西、福建、辽宁都有相关项目，而且进展都比较顺利。但同样的项目在湖南桃花江和湖北咸宁的实施就遇到了困难。项目遭到当地百姓的强烈反对，施工进程也因此一拖再拖。虽然项目得到了相关部门的批准，人力、物力、财力、技术也不是问题，但终究还是不得不面对被搁置的局面！"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d49-8aa6-4aa0-822e-085fe4713326",
               :page 34,
               :position {:bounding {:x1 0,
                                     :y1 152.5,
                                     :x2 627.2627563476562,
                                     :y2 786.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 180.5,
                                   :x2 0,
                                   :y2 197.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 551.5078125,
                                   :x2 627.23486328125,
                                   :y2 579.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 581.2109375,
                                   :x2 627.2627563476562,
                                   :y2 608.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 585.2109375,
                                   :x2 627.2627563476562,
                                   :y2 605.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 610.9140625,
                                   :x2 627.2626953125,
                                   :y2 638.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 614.9140625,
                                   :x2 627.2626953125,
                                   :y2 634.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 640.609375,
                                   :x2 627.2626953125,
                                   :y2 668.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 644.609375,
                                   :x2 627.2626953125,
                                   :y2 664.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 670.3125,
                                   :x2 607.************,
                                   :y2 697.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 674.3125,
                                   :x2 607.************,
                                   :y2 694.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 700.0078125,
                                   :x2 627.2626953125,
                                   :y2 727.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 704.0078125,
                                   :x2 627.2626953125,
                                   :y2 723.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 729.7109375,
                                   :x2 627.2626953125,
                                   :y2 757.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 733.7109375,
                                   :x2 627.2626953125,
                                   :y2 753.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 759.4140625,
                                   :x2 429.16241455078125,
                                   :y2 786.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 34},
               :content {:text "如果是创新性很强的项目，将完全没有可以参考的以往的项目。这种创新带来的独特性意味着项目中有新的知识有待认知。知识仅是对现实世界的近似描述，人不可能掌握任何事物的全部知识。不能认知和掌握的事物会显得比较复杂，对于复杂的事物，人们无法一开始就掌握完备的认识，需要循序渐进，而没有掌握完备的知识，往往就会犯错误。已经发生的错误称作问题，可能发生的错误称作风险，我们应该通过完善的程序来管理这些错误（问题和风险）。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d53-01d8-4658-948e-54b84358177c",
               :page 34,
               :position {:bounding {:x1 131.984375,
                                     :y1 808.9140625,
                                     :x2 290.4682922363281,
                                     :y2 836.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 808.9140625,
                                   :x2 290.4682922363281,
                                   :y2 836.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 34},
               :content {:text "2.独特性是相对的"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d58-f50f-49ec-8dc5-e8c20834f569",
               :page 35,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 627.2627563476562,
                                     :y2 294.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 88.9140625,
                                   :x2 627.2348022460938,
                                   :y2 116.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 118.609375,
                                   :x2 627.2627563476562,
                                   :y2 146.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 122.609375,
                                   :x2 627.2627563476562,
                                   :y2 142.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 148.3125,
                                   :x2 627.2626953125,
                                   :y2 175.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 152.3125,
                                   :x2 627.2626953125,
                                   :y2 172.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 178.0078125,
                                   :x2 627.2626953125,
                                   :y2 205.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 182.0078125,
                                   :x2 627.2626953125,
                                   :y2 201.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 207.7109375,
                                   :x2 627.2626953125,
                                   :y2 235.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 211.7109375,
                                   :x2 627.2626953125,
                                   :y2 231.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 237.4140625,
                                   :x2 627.2626953125,
                                   :y2 264.9140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 241.4140625,
                                   :x2 627.2626953125,
                                   :y2 261.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 267.109375,
                                   :x2 409.35552978515625,
                                   :y2 294.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 35},
               :content {:text "一个项目是否独特是相对的。将其与以前的项目相比，或多或少会存在某些相似性，如两座大楼建设项目或两个手机开发项目之间肯定有很多相似的地方。如果每个项目都是完全独特的，就不可能存在适用于大多数项目的知识，项目管理方法也就不具任何意义了。正是这种相对的独特性，使项目管理的应用范围得以广泛扩展。而许多工作都具有相对的独特性，也就因此具备当作项目来做的可能性。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d5c-2705-4924-a1e2-12e22b64e88a",
               :page 35,
               :position {:bounding {:x1 131.984375,
                                     :y1 366.109375,
                                     :x2 429.1399230957031,
                                     :y2 393.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 366.109375,
                                   :x2 429.1399230957031,
                                   :y2 393.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 35},
               :content {:text "3.独特性提升竞争力，增加挑战性"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d60-a0bf-416d-a6a2-f2f4ceb1b895",
               :page 35,
               :position {:bounding {:x1 0,
                                     :y1 124.5,
                                     :x2 627.2627563476562,
                                     :y2 532.2109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 415.609375,
                                   :x2 607.427978515625,
                                   :y2 443.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 445.3125,
                                   :x2 627.2626953125,
                                   :y2 472.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 449.3125,
                                   :x2 627.2626953125,
                                   :y2 469.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 475.0078125,
                                   :x2 627.2627563476562,
                                   :y2 502.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 479.0078125,
                                   :x2 627.2627563476562,
                                   :y2 498.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 504.7109375,
                                   :x2 330.1123352050781,
                                   :y2 532.2109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 35},
               :content {:text "正是因为项目的独特性使得项目的成果具备了某种竞争力，否则只需要重复以前的工作就够了。当然独特性也提升了项目工作的挑战性，项目工作中的一个重要部分就是化解复杂性，使之更明确、更可控。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d64-7abc-4552-86fc-fc058d6b6a3e",
               :page 36,
               :position {:bounding {:x1 92.390625,
                                     :y1 124.015625,
                                     :x2 258.5973205566406,
                                     :y2 159.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 124.015625,
                                   :x2 258.5973205566406,
                                   :y2 159.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 156.3125,
                                   :y1 129.015625,
                                   :x2 190.4210968017578,
                                   :y2 154.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "1.2.2 临时性"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d66-706a-414f-976a-315fe29b5e5e",
               :page 36,
               :position {:bounding {:x1 131.984375,
                                     :y1 195.109375,
                                     :x2 468.7564392089844,
                                     :y2 222.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 195.109375,
                                   :x2 468.7564392089844,
                                   :y2 222.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "1.临时性是指项目有明确的起点与终点"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d8e-8042-4feb-ac04-c5769e541677",
               :page 36,
               :position {:bounding {:x1 0,
                                     :y1 26.5,
                                     :x2 627.2627563476562,
                                     :y2 331.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 244.609375,
                                   :x2 627.23486328125,
                                   :y2 272.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 274.3125,
                                   :x2 627.2627563476562,
                                   :y2 301.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 278.3125,
                                   :x2 627.2627563476562,
                                   :y2 298.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 304.0078125,
                                   :x2 132.01220703125,
                                   :y2 331.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "商业机会是稍纵即逝的。快速变化的环境会使过去赚钱的产品很快变成明日黄花，未及时交付的项目成果将失去商业价值。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d93-758c-4de3-bc58-c55e2a25648d",
               :page 36,
               :position {:bounding {:x1 0,
                                     :y1 68.5,
                                     :x2 627.2627563476562,
                                     :y2 499.8125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 353.5078125,
                                   :x2 627.23486328125,
                                   :y2 381.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 383.2109375,
                                   :x2 627.2627563476562,
                                   :y2 410.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 387.2109375,
                                   :x2 627.2627563476562,
                                   :y2 407.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 412.9140625,
                                   :x2 627.2626953125,
                                   :y2 440.4140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 416.9140625,
                                   :x2 627.2626953125,
                                   :y2 436.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 442.609375,
                                   :x2 607.************,
                                   :y2 470.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 446.609375,
                                   :x2 607.************,
                                   :y2 466.40625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 472.3125,
                                   :x2 132.01220703125,
                                   :y2 499.8125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "临时性是变化的结果。商业环境的变化会产生新的需求，从而催生新项目的出现，项目就是为满足新产生的需求而启动的。需求是基于人的，它可能是对原来需求的完善，也可能是全新的。当需求得到满足或者需求不再存在时，项目就会结束。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258d97-19f1-44c9-a41e-4b36aaf2d452",
               :page 36,
               :position {:bounding {:x1 131.984375,
                                     :y1 521.8125,
                                     :x2 468.76153564453125,
                                     :y2 549.3125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 521.8125,
                                   :x2 468.76153564453125,
                                   :y2 549.3125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "2.临时性并不意味着项目的持续时间短"},
               :properties {:color "yellow"}}
              {:id #uuid "64258db1-b6ff-46e2-a0bb-619db8ae5544",
               :page 36,
               :position {:bounding {:x1 0,
                                     :y1 152.5,
                                     :x2 627.2626953125,
                                     :y2 658.2109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 571.3125,
                                   :x2 627.2348022460938,
                                   :y2 598.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 601.0078125,
                                   :x2 627.2626953125,
                                   :y2 628.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 605.0078125,
                                   :x2 627.2626953125,
                                   :y2 624.8046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 630.7109375,
                                   :x2 567.8341674804688,
                                   :y2 658.2109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "任何具有明确开始和结束时间的工作都是临时的，不会无限期延续下去。临时性与项目持续时间的长短没有关系，历时一个月的项目是临时的，历时十年的项目也是临时的。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258dbc-0ebe-45c4-97d3-7b39904602e8",
               :page 36,
               :position {:bounding {:x1 131.984375,
                                     :y1 680.2109375,
                                     :x2 468.76153564453125,
                                     :y2 707.7109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 680.2109375,
                                   :x2 468.76153564453125,
                                   :y2 707.7109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "3.临时性会造成项目管理者的权限不足"},
               :properties {:color "yellow"}}
              {:id #uuid "64258dc2-bb79-4e6d-9db6-6069b4d9d7c5",
               :page 36,
               :position {:bounding {:x1 -0.000003814697265625,
                                     :y1 208.5,
                                     :x2 734.6683921813965,
                                     :y2 1910,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 252,
                                   :x2 734,
                                   :y2 252,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.980224609375,
                                   :y1 729.7047119140625,
                                   :x2 627.234130859375,
                                   :y2 757.2047119140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.000003814697265625,
                                   :y1 758.4640502929688,
                                   :x2 734.6663780212402,
                                   :y2 788.1515502929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.000003814697265625,
                                   :y1 788.1593627929688,
                                   :x2 734.664363861084,
                                   :y2 817.8468627929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.000003814697265625,
                                   :y1 817.8624877929688,
                                   :x2 734.6683921813965,
                                   :y2 950.3859252929688,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 958.5,
                                   :x2 0,
                                   :y2 975.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 960,
                                   :x2 734,
                                   :y2 1910,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 36},
               :content {:text "因为项目的临时性，在组建项目团队时可能会遇到“招不到合适成员”的情况。大多数情况下，项目经理可能会在管理团队成员时感到“权力不足”。在常见的矩阵组织结构中，项目经理和团队成员之间是单次博弈，而职能经理和团队成员之间是多次博弈，职能经理决定成员的工资、奖金和晋升，而非项目经理。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258dc7-90a5-49bc-969d-28f38ec27564",
               :page 37,
               :position {:bounding {:x1 0,
                                     :y1 26.5,
                                     :x2 627.2348022460938,
                                     :y2 255.0078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 168.109375,
                                   :x2 627.2348022460938,
                                   :y2 195.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 197.8125,
                                   :x2 607.************,
                                   :y2 225.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 201.8125,
                                   :x2 607.************,
                                   :y2 221.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 227.5078125,
                                   :x2 468.7840576171875,
                                   :y2 255.0078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "项目的临时性决定了项目团队的临时性，团队通常需要随着项目的完成而解散。团队解散后，团队成员需要重新找工作。这也是项目带给团队成员的挑战之一。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258dcf-be23-4149-bf58-196cb9e61518",
               :page 37,
               :position {:bounding {:x1 131.984375,
                                     :y1 277.0078125,
                                     :x2 468.76153564453125,
                                     :y2 304.5078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 277.0078125,
                                   :x2 468.76153564453125,
                                   :y2 304.5078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "4.临时性的项目不意味着成果的临时性"},
               :properties {:color "yellow"}}
              {:id #uuid "64258dd2-37bb-40b1-ac28-c02a71beb307",
               :page 37,
               :position {:bounding {:x1 0,
                                     :y1 82.5,
                                     :x2 627.2626342773438,
                                     :y2 413.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 326.5078125,
                                   :x2 607.427978515625,
                                   :y2 354.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 356.2109375,
                                   :x2 627.2626342773438,
                                   :y2 383.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 360.2109375,
                                   :x2 627.2626342773438,
                                   :y2 380.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 385.9140625,
                                   :x2 211.25538635253906,
                                   :y2 413.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "临时性的项目所创造的成果往往具有可持续的长期生命力，并持续地对环境造成影响。例如，都江堰水利工程至今还在发挥作用。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258dd9-d824-4e23-acdc-37044cf6f0e6",
               :page 37,
               :position {:bounding {:x1 131.984375,
                                     :y1 435.4140625,
                                     :x2 349.************,
                                     :y2 462.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 435.4140625,
                                   :x2 349.************,
                                   :y2 462.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "5.项目可因多种原因结束"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ddd-f499-4b79-b878-8d86ab3d13ae",
               :page 37,
               :position {:bounding {:x1 131.984375,
                                     :y1 484.9140625,
                                     :x2 567.8062744140625,
                                     :y2 512.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 484.9140625,
                                   :x2 567.8062744140625,
                                   :y2 512.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "当满足以下一种或多种情况时，项目即宣告结束："},
               :properties {:color "yellow"}}
              {:id #uuid "64258de4-4726-4f7d-bb5a-20ae4dcc2aea",
               :page 37,
               :position {:bounding {:x1 142.5480499267578,
                                     :y1 534.4140625,
                                     :x2 290.4644775390625,
                                     :y2 561.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 142.5480499267578,
                                   :y1 534.4140625,
                                   :x2 290.4644775390625,
                                   :y2 561.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "达成项目目标；"},
               :properties {:color "yellow"}}
              {:id #uuid "64258de7-0103-40df-bd4e-85880b502a18",
               :page 37,
               :position {:bounding {:x1 142.35940551757812,
                                     :y1 583.9140625,
                                     :x2 349.90106201171875,
                                     :y2 611.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 142.35940551757812,
                                   :y1 583.9140625,
                                   :x2 349.90106201171875,
                                   :y2 611.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "不会或不能达到目标；"},
               :properties {:color "yellow"}}
              {:id #uuid "64258de9-ed2c-4c98-8f15-9127e66d0447",
               :page 37,
               :position {:bounding {:x1 142.20729064941406,
                                     :y1 633.4140625,
                                     :x2 448.95106506347656,
                                     :y2 660.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 142.20729064941406,
                                   :y1 633.4140625,
                                   :x2 448.95106506347656,
                                   :y2 660.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "项目资金缺乏或没有可分配资金；"},
               :properties {:color "yellow"}}
              {:id #uuid "64258dec-2a3a-4cb2-b4f0-4c303085ede1",
               :page 37,
               :position {:bounding {:x1 142.4090576171875,
                                     :y1 682.9140625,
                                     :x2 330.0861511230469,
                                     :y2 710.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 142.4090576171875,
                                   :y1 682.9140625,
                                   :x2 330.0861511230469,
                                   :y2 710.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "项目需求不复存在；"},
               :properties {:color "yellow"}}
              {:id #uuid "64258df6-db1b-4a9b-a15f-53ec58ffc4bf",
               :page 37,
               :position {:bounding {:x1 142.22930908203125,
                                     :y1 732.4140625,
                                     :x2 429.13616943359375,
                                     :y2 759.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 142.22930908203125,
                                   :y1 732.4140625,
                                   :x2 429.13616943359375,
                                   :y2 759.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "无法获得所需人力或物力资源；"},
               :properties {:color "yellow"}}
              {:id #uuid "64258df9-ff33-44aa-837b-a12176b9d5df",
               :page 37,
               :position {:bounding {:x1 142.20729064941406,
                                     :y1 781.9140625,
                                     :x2 448.9510040283203,
                                     :y2 809.4140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 142.20729064941406,
                                   :y1 781.9140625,
                                   :x2 448.9510040283203,
                                   :y2 809.4140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 37},
               :content {:text "出于法律或其他原因而终止项目。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e00-7c82-4b74-9e55-b166334e04c3",
               :page 38,
               :position {:bounding {:x1 92.3203125,
                                     :y1 123.0390625,
                                     :x2 307.2767028808594,
                                     :y2 158.5390625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.3203125,
                                   :y1 123.0390625,
                                   :x2 307.2767028808594,
                                   :y2 158.5390625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 155.53125,
                                   :y1 128.5390625,
                                   :x2 189.6379852294922,
                                   :y2 153.828125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 38},
               :content {:text "1.2.3 渐进明细性"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e03-8ebc-4fa6-a3d4-79faaf0e1071",
               :page 38,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 640.5796508789062,
                                     :y2 309.46875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 193.859375,
                                   :x2 640.5796508789062,
                                   :y2 221.359375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 223.234375,
                                   :x2 630.7860107421875,
                                   :y2 250.734375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 227.234375,
                                   :x2 630.7860107421875,
                                   :y2 246.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 252.6015625,
                                   :x2 640.5725708007812,
                                   :y2 280.1015625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 256.6015625,
                                   :x2 640.5725708007812,
                                   :y2 276.1796875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 281.96875,
                                   :x2 444.7720947265625,
                                   :y2 309.46875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 38},
               :content {:text "渐进明细性是指逐渐细化，意味着项目是在连续积累中分步骤实现的，即逐步明确项目的细节特征（见图1-3）。由于项目在实施过程中可能发生变化，因此应该在整个项目生命周期中反复开展计划工作，对工作进行逐步修正。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e0e-3495-4359-b6dd-765b315f5499",
               :page 38,
               :position {:bounding {:x1 86.5,
                                     :y1 326.5,
                                     :x2 643.5,
                                     :y2 612.5,
                                     :width 734.4,
                                     :height 950.4},
                          :rects (),
                          :page 38},
               :content {:text "[:span]", :image 1680182798261},
               :properties {:color "yellow"}}
              {:id #uuid "64258e14-400d-49c4-a03d-96efa5f90e8d",
               :page 38,
               :position {:bounding {:x1 131.484375,
                                     :y1 628.1875,
                                     :x2 405.61126708984375,
                                     :y2 655.6875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.484375,
                                   :y1 628.1875,
                                   :x2 405.61126708984375,
                                   :y2 655.6875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 38},
               :content {:text "1.项目的许多方面需要渐进明细"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e19-0fe9-424c-a79d-b7cebcc33e7d",
               :page 38,
               :position {:bounding {:x1 131.484375,
                                     :y1 677.140625,
                                     :x2 464.************,
                                     :y2 704.640625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.484375,
                                   :y1 677.140625,
                                   :x2 464.************,
                                   :y2 704.640625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 38},
               :content {:text "在项目中，需要渐进明细的方面包括："},
               :properties {:color "yellow"}}
              {:id #uuid "64258e1d-28bf-49d2-be67-d79cfc599053",
               :page 38,
               :position {:bounding {:x1 0,
                                     :y1 110.5,
                                     :x2 640.5806884765625,
                                     :y2 782.9609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 726.0859375,
                                   :x2 640.5806884765625,
                                   :y2 753.5859375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 755.4609375,
                                   :x2 405.6072998046875,
                                   :y2 782.9609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 38},
               :content {:text "·项目目标。开始只有方向性的大目标，然后逐渐细化出具体的、可测量的、可实现的小目标。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e24-aecf-4077-871e-c16fe1c1f8c2",
               :page 38,
               :position {:bounding {:x1 -0.00385284423828125,
                                     :y1 138.5,
                                     :x2 734.3931198120117,
                                     :y2 1910,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 154,
                                   :x2 734,
                                   :y2 154,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.48355102539062,
                                   :y1 804.4085083007812,
                                   :x2 640.5798950195312,
                                   :y2 831.9085083007812,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.00385284423828125,
                                   :y1 832.8861694335938,
                                   :x2 734.3931198120117,
                                   :y2 950.3861694335938,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 960,
                                   :x2 734,
                                   :y2 1910,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 38},
               :content {:text "·项目范围。开始只有粗略的范围说明书，然后细化出工作分解结构（work breakdown structure，WBS）和工作分解结构词典。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e27-3633-4f31-b077-bb137682a5b7",
               :page 39,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 640.5806884765625,
                                     :y2 194.65625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 137.7890625,
                                   :x2 640.5806884765625,
                                   :y2 165.2890625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 167.15625,
                                   :x2 248.96380615234375,
                                   :y2 194.65625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "·项目计划。开始只有控制性的计划，然后逐渐明细，制订具体的实施计划。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e2e-87e7-43cb-8aa9-e4ed07116478",
               :page 39,
               :position {:bounding {:x1 131.484375,
                                     :y1 216.109375,
                                     :x2 366.45635986328125,
                                     :y2 243.609375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.484375,
                                   :y1 216.109375,
                                   :x2 366.45635986328125,
                                   :y2 243.609375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "2.渐进明细不同于范围蔓延"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e36-7ad2-48b2-be71-c323fc07cb5e",
               :page 39,
               :position {:bounding {:x1 0,
                                     :y1 54.5,
                                     :x2 640.5796508789062,
                                     :y2 410.046875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 265.0625,
                                   :x2 640.5796508789062,
                                   :y2 292.5625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 294.4296875,
                                   :x2 640.5725708007812,
                                   :y2 321.9296875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 298.4296875,
                                   :x2 640.5725708007812,
                                   :y2 318.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 323.8046875,
                                   :x2 640.5725708007812,
                                   :y2 351.3046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 327.8046875,
                                   :x2 640.5725708007812,
                                   :y2 347.3828125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 353.171875,
                                   :x2 620.9940185546875,
                                   :y2 380.671875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 357.171875,
                                   :x2 620.9940185546875,
                                   :y2 376.75,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 382.546875,
                                   :x2 542.6723022460938,
                                   :y2 410.046875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "在去商场前，甲计划买两套运动衣，可是到了商场后，他发现运动鞋促销，于是就买了一双——这是范围蔓延。在到达商场前，甲只考虑需要买运动衣，没有确定款式、色彩、价位，到商场后，看到了越来越多的商品后，甲慢慢对要买的运动衣的款式、色彩、价位有了明确的认识——这是渐进明细。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258e48-c4b7-48a6-90fd-b343fba291b8",
               :page 39,
               :position {:bounding {:x1 0,
                                     :y1 124.5,
                                     :x2 640.5796508789062,
                                     :y2 547.109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 431.4921875,
                                   :x2 640.5796508789062,
                                   :y2 458.9921875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 460.8671875,
                                   :x2 640.5725708007812,
                                   :y2 488.3671875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 464.8671875,
                                   :x2 640.5725708007812,
                                   :y2 484.4453125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 490.234375,
                                   :x2 640.5725708007812,
                                   :y2 517.734375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 494.234375,
                                   :x2 640.5725708007812,
                                   :y2 513.8125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 519.609375,
                                   :x2 170.64205932617188,
                                   :y2 547.109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "项目的渐进明细，一定要在适当的范围定义下进行，也就是要在项目的边界内进行，以避免渐进明细演变成范围蔓延。渐进明细与范围蔓延根本不是一回事，前者是必须做的，后者是必须避免的。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ec5-3185-474c-816f-81d3ac4a2f5a",
               :page 39,
               :position {:bounding {:x1 0,
                                     :y1 180.5,
                                     :x2 640.5796508789062,
                                     :y2 684.171875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 180.5,
                                   :x2 0,
                                   :y2 197.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 568.5546875,
                                   :x2 640.5796508789062,
                                   :y2 596.0546875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 597.9296875,
                                   :x2 640.5725708007812,
                                   :y2 625.4296875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 601.9296875,
                                   :x2 640.5725708007812,
                                   :y2 621.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 627.296875,
                                   :x2 640.5725708007812,
                                   :y2 654.796875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 631.296875,
                                   :x2 640.5725708007812,
                                   :y2 650.875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 656.671875,
                                   :x2 151.06358337402344,
                                   :y2 684.171875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "渐进明细是正常的，项目范围不可能在开始的时候就非常清晰，需要不断地补充、细化、完善，这是客观规律。范围蔓延是不正常的、危险的、失控的，应该在项目实施过程中控制好这个问题。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ec9-ccfa-4546-b878-b15bb6d0e4a2",
               :page 39,
               :position {:bounding {:x1 131.484375,
                                     :y1 705.6171875,
                                     :x2 288.1346435546875,
                                     :y2 733.1171875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.484375,
                                   :y1 705.6171875,
                                   :x2 288.1346435546875,
                                   :y2 733.1171875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "3.渐进明细的方式"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ecb-cf8f-4105-baaf-a63dffffa260",
               :page 39,
               :position {:bounding {:x1 131.484375,
                                     :y1 754.5703125,
                                     :x2 386.0359191894531,
                                     :y2 782.0703125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.484375,
                                   :y1 754.5703125,
                                   :x2 386.0359191894531,
                                   :y2 782.0703125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "实现渐进明细的方式有两种："},
               :properties {:color "yellow"}}
              {:id #uuid "64258ece-cbc5-4f40-8af2-d37f204222ef",
               :page 39,
               :position {:bounding {:x1 0,
                                     :y1 264.5,
                                     :x2 640.5806884765625,
                                     :y2 860.390625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 264.5,
                                   :x2 0,
                                   :y2 281.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 803.5234375,
                                   :x2 640.5806884765625,
                                   :y2 831.0234375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 832.890625,
                                   :x2 620.9940185546875,
                                   :y2 860.390625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 39},
               :content {:text "·化大为小，逐步推进。将项目划分成几个阶段，在不同的阶段执行不同的项目活动，最终分阶段完成项目的所有活动。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ed1-441f-4a78-8f1e-97f4cc3137fc",
               :page 40,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 640.5806884765625,
                                     :y2 175.078125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 88.8359375,
                                   :x2 640.5806884765625,
                                   :y2 116.3359375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 118.2109375,
                                   :x2 640.5725708007812,
                                   :y2 145.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 122.2109375,
                                   :x2 640.5725708007812,
                                   :y2 141.7890625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 147.578125,
                                   :x2 209.8068389892578,
                                   :y2 175.078125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 40},
               :content {:text "·剥洋葱式，逐层深入。首先解决当前能够解决的问题，再逐层深入，每个层次上以不同的完整程度进行项目活动，最后彻底完成项目。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258ed6-**************-aac99ca9ad6e",
               :page 40,
               :position {:bounding {:x1 0,
                                     :y1 40.5,
                                     :x2 640.5796508789062,
                                     :y2 370.8828125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 196.53125,
                                   :x2 640.5796508789062,
                                   :y2 224.03125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 225.8984375,
                                   :x2 620.9940185546875,
                                   :y2 253.3984375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 229.8984375,
                                   :x2 620.9940185546875,
                                   :y2 249.4765625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 255.2734375,
                                   :x2 640.5725708007812,
                                   :y2 282.7734375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 259.2734375,
                                   :x2 640.5725708007812,
                                   :y2 278.8515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 284.640625,
                                   :x2 640.5725708007812,
                                   :y2 312.140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 288.640625,
                                   :x2 640.5725708007812,
                                   :y2 308.21875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 314.015625,
                                   :x2 640.57568359375,
                                   :y2 341.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 318.015625,
                                   :x2 640.57568359375,
                                   :y2 337.59375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 343.3828125,
                                   :x2 151.0675811767578,
                                   :y2 370.8828125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 40},
               :content {:text "从项目的临时性、独特性和渐进明细性这三种特性方面讲，任何一项工作，如果你更看重它的临时性、独特性和渐进明细性，它就是项目；如果你更看重它的重复性，与其他工作的相似性，且一开始就能明确大部分细节，它就是运营。从这个意义上讲，组织中的许多工作都可以被看作项目，可以进行“项目化管理”。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258efb-cee4-4352-beaa-6235a8940ee0",
               :page 41,
               :position {:bounding {:x1 92.390625,
                                     :y1 115.015625,
                                     :x2 437.63018798828125,
                                     :y2 150.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 115.015625,
                                   :x2 139.2578125,
                                   :y2 150.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 156.3125,
                                   :y1 115.015625,
                                   :x2 437.63018798828125,
                                   :y2 150.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 130.7421875,
                                   :y1 120.015625,
                                   :x2 139.2578125,
                                   :y2 145.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 41},
               :content {:text "1.3 项目的价值在于驱动变革"},
               :properties {:color "yellow"}}
              {:id #uuid "64258f01-a2e7-409f-9cef-4f3d03596ec5",
               :page 41,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 642.06787109375,
                                     :y2 284.7109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 178.0078125,
                                   :x2 607.427978515625,
                                   :y2 205.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 207.7109375,
                                   :x2 132.01220703125,
                                   :y2 235.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 211.7109375,
                                   :x2 132.01220703125,
                                   :y2 231.5078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 523.203125,
                                   :y1 257.2109375,
                                   :x2 642.06787109375,
                                   :y2 284.7109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 41},
               :content {:text "如果你给孩子一把锤子，那么整个世界在他眼里都是钉子。——哲学谚语"},
               :properties {:color "yellow"}}
              {:id #uuid "64258f04-7994-478e-b72c-ab65857a5d65",
               :page 41,
               :position {:bounding {:x1 0,
                                     :y1 54.5,
                                     :x2 627.2348022460938,
                                     :y2 363.9140625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 306.7109375,
                                   :x2 627.2348022460938,
                                   :y2 334.2109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 336.4140625,
                                   :x2 250.87696838378906,
                                   :y2 363.9140625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 41},
               :content {:text "做项目管理一定要关注企业的宏观环境，由宏观看微观，再从微观看宏观。"},
               :properties {:color "yellow"}}
              {:id #uuid "64258f0d-dcde-4237-bafc-237bc5ac8489",
               :page 42,
               :position {:bounding {:x1 92.3203125,
                                     :y1 123.0390625,
                                     :x2 433.7252197265625,
                                     :y2 158.5390625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.3203125,
                                   :y1 123.0390625,
                                   :x2 433.7252197265625,
                                   :y2 158.5390625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 155.53125,
                                   :y1 128.5390625,
                                   :x2 189.6379852294922,
                                   :y2 153.828125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 42},
               :content {:text "1.3.1 组织的工作类型与框架"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd0c6-b51e-4db7-864e-a713398b964c",
               :page 42,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 529.9476318359375,
                                     :y2 210.40625,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 110.4609375,
                                   :y1 162.7265625,
                                   :x2 529.9476318359375,
                                   :y2 185.7265625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 187.40625,
                                   :x2 135.1400146484375,
                                   :y2 210.40625,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 42},
               :content {:text "在组织中，我们通常将组织的日常工作分为3种类型（见图1-4）。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd0ce-7878-46b4-85be-7a4262d1b843",
               :page 42,
               :position {:bounding {:x1 0,
                                     :y1 40.5,
                                     :x2 529.94775390625,
                                     :y2 276.203125,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 110.4609375,
                                   :y1 228.53125,
                                   :x2 529.94775390625,
                                   :y2 251.53125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 253.203125,
                                   :x2 126.91486358642578,
                                   :y2 276.203125,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 42},
               :content {:text "（1）战略规划类工作，主要是给企业定发展方向和长期目标的。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd0d9-ed15-4c86-8cca-5fbf20d2bb1f",
               :page 42,
               :position {:bounding {:x1 110.4609375,
                                     :y1 294.328125,
                                     :x2 513.4942626953125,
                                     :y2 317.328125,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 110.4609375,
                                   :y1 294.328125,
                                   :x2 513.4942626953125,
                                   :y2 317.328125,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 42},
               :content {:text "（2）日常运营类工作，帮助企业维持稳定和创造收入。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd0dc-2ab1-430d-9916-c3939217003d",
               :page 42,
               :position {:bounding {:x1 0,
                                     :y1 82.5,
                                     :x2 529.9475708007812,
                                     :y2 383.1328125,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 110.4609375,
                                   :y1 335.4609375,
                                   :x2 529.9475708007812,
                                   :y2 358.4609375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 360.1328125,
                                   :x2 126.91486358642578,
                                   :y2 383.1328125,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 42},
               :content {:text "（3）项目类工作，帮助企业建立新的竞争优势或更科学的机制。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd0e4-f7d5-4b94-9eea-99d6c6c07ab6",
               :page 42,
               :position {:bounding {:x1 35,
                                     :y1 393,
                                     :x2 572,
                                     :y2 726,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects (),
                          :page 42},
               :content {:text "[:span]", :image 1687998692337},
               :properties {:color "yellow"}}
              {:id #uuid "649cd10d-6d32-466c-8c2e-fcff193ecf04",
               :page 43,
               :position {:bounding {:x1 222.1328125,
                                     :y1 74.5,
                                     :x2 394.8690490722656,
                                     :y2 97.5,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 222.1328125,
                                   :y1 74.5,
                                   :x2 394.8690490722656,
                                   :y2 97.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 263.2578125,
                                   :y1 78,
                                   :x2 285.4639892578125,
                                   :y2 94.453125,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 43},
               :content {:text "图1-4 组织的工作框架"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd118-3879-44a6-8687-a2cb62acbba6",
               :page 43,
               :position {:bounding {:x1 110.4609375,
                                     :y1 181.421875,
                                     :x2 242.0677947998047,
                                     :y2 204.421875,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 110.4609375,
                                   :y1 181.421875,
                                   :x2 242.0677947998047,
                                   :y2 204.421875,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 43},
               :content {:text "1.战略规划类工作"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd11b-6413-41d3-8084-b35144fed20a",
               :page 43,
               :position {:bounding {:x1 0,
                                     :y1 54.5,
                                     :x2 538.1778564453125,
                                     :y2 319.578125,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 110.4609375,
                                   :y1 222.546875,
                                   :x2 521.7239379882812,
                                   :y2 245.546875,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 247.2265625,
                                   :x2 538.1778564453125,
                                   :y2 270.2265625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 250.7265625,
                                   :x2 538.1778564453125,
                                   :y2 267.1796875,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 271.8984375,
                                   :x2 538.1714477539062,
                                   :y2 294.8984375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 275.3984375,
                                   :x2 538.1714477539062,
                                   :y2 291.8515625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 296.578125,
                                   :x2 472.3735046386719,
                                   :y2 319.578125,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 43},
               :content {:text "“战略”这个词来源于古希腊语，最初用于军事，意思是“指挥官的艺术”。水平高的指挥官有一个共性，就是他们对于战争发展前景的预判能力比一般人强很多。所以，战略更多强调的是对于长期目标定位的准确性以及实现目标的方法。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd11e-385b-42b5-83f8-8ee35121b10a",
               :page 43,
               :position {:bounding {:x1 0,
                                     :y1 110.5,
                                     :x2 538.17138671875,
                                     :y2 484.078125,
                                     :width 617,
                                     :height 798.4705882352941},
                          :rects ({:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 0,
                                   :y1 166.5,
                                   :x2 0,
                                   :y2 183.5,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 110.4609375,
                                   :y1 337.703125,
                                   :x2 538.1708374023438,
                                   :y2 360.703125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 362.375,
                                   :x2 538.17138671875,
                                   :y2 385.375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 365.875,
                                   :x2 538.17138671875,
                                   :y2 382.328125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 387.0546875,
                                   :x2 538.17138671875,
                                   :y2 410.0546875,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 390.5546875,
                                   :x2 538.17138671875,
                                   :y2 407.0078125,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 411.7265625,
                                   :x2 521.7257690429688,
                                   :y2 434.7265625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 415.2265625,
                                   :x2 521.7257690429688,
                                   :y2 431.6796875,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 436.3984375,
                                   :x2 538.17138671875,
                                   :y2 459.3984375,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 439.8984375,
                                   :x2 538.17138671875,
                                   :y2 456.3515625,
                                   :width 617,
                                   :height 798.4705882352941}
                                  {:x1 77.5625,
                                   :y1 461.078125,
                                   :x2 521.725830078125,
                                   :y2 484.078125,
                                   :width 617,
                                   :height 798.4705882352941}),
                          :page 43},
               :content {:text "战略至少应包括两个方面，一是现状，即我们现在在哪儿，处于什么状态。二是愿景，就是我们去哪儿。从现状到愿景的路径规划就是所谓的战略规划。因此，战略通常要分几步走，不可能一步到位。企业越大，越需要全体人员达成对未来愿景的共识、统一行动方向，因此也就越需要规划未来的发展路径，这样大家才能方向明确、步调一致，同心协力地推动企业的发展。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd17d-fb9d-4081-89b7-01730bb79caf",
               :page 43,
               :position {:bounding {:x1 131.484375,
                                     :y1 597.9296875,
                                     :x2 405.61328125,
                                     :y2 625.4296875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.484375,
                                   :y1 597.9296875,
                                   :x2 405.61328125,
                                   :y2 625.4296875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 43},
               :content {:text "2.日常运营类工作和项目类工作"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd186-9974-4486-bc80-fa0550be920a",
               :page 43,
               :position {:bounding {:x1 0,
                                     :y1 208.5,
                                     :x2 640.5796508789062,
                                     :y2 850.6015625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 208.5,
                                   :x2 0,
                                   :y2 225.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 222.5,
                                   :x2 0,
                                   :y2 239.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 236.5,
                                   :x2 0,
                                   :y2 253.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 250.5,
                                   :x2 0,
                                   :y2 267.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 264.5,
                                   :x2 0,
                                   :y2 281.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 278.5,
                                   :x2 0,
                                   :y2 295.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 646.875,
                                   :x2 640.5796508789062,
                                   :y2 674.375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 676.25,
                                   :x2 620.9940185546875,
                                   :y2 703.75,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 680.25,
                                   :x2 620.9940185546875,
                                   :y2 699.828125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 705.6171875,
                                   :x2 620.9940185546875,
                                   :y2 733.1171875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 709.6171875,
                                   :x2 620.9940185546875,
                                   :y2 729.1953125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 734.9921875,
                                   :x2 640.5725708007812,
                                   :y2 762.4921875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 738.9921875,
                                   :x2 640.5725708007812,
                                   :y2 758.5703125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 764.359375,
                                   :x2 640.5725708007812,
                                   :y2 791.859375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 768.359375,
                                   :x2 640.5725708007812,
                                   :y2 787.9375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 793.734375,
                                   :x2 640.5725708007812,
                                   :y2 821.234375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 797.734375,
                                   :x2 640.5725708007812,
                                   :y2 817.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 823.1015625,
                                   :x2 327.2855529785156,
                                   :y2 850.6015625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 43},
               :content {:text "战略再好也必须落地才行，战略一般要通过开展两种类型的工作得以实现，一类是运营（重复性工作），另一类就是项目（非重复性工作）。运营以追求效率为目的，赚钱的事要好好干，这对组织很重要。而由于外界环境在不断地变化，组织的机制和工作方式也需要随之改变，应进行内部调整，以适应环境，也就是必须做项目。实际上，项目的本质是改变业务类工作，以非重复性劳动为主要特征。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd218-b2e6-45f1-85e7-aa88c753796c",
               :page 44,
               :position {:bounding {:x1 0,
                                     :y1 -1.5,
                                     :x2 640.5796508789062,
                                     :y2 263.1875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 -1.5,
                                   :x2 0,
                                   :y2 15.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 88.8359375,
                                   :x2 640.5796508789062,
                                   :y2 116.3359375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 118.2109375,
                                   :x2 640.5725708007812,
                                   :y2 145.7109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 122.2109375,
                                   :x2 640.5725708007812,
                                   :y2 141.7890625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 147.578125,
                                   :x2 640.5725708007812,
                                   :y2 175.078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 151.578125,
                                   :x2 640.5725708007812,
                                   :y2 171.15625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 176.953125,
                                   :x2 640.5725708007812,
                                   :y2 204.453125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 180.953125,
                                   :x2 640.5725708007812,
                                   :y2 200.53125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 206.3203125,
                                   :x2 640.5725708007812,
                                   :y2 233.8203125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 210.3203125,
                                   :x2 640.5725708007812,
                                   :y2 229.8984375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 235.6875,
                                   :x2 248.96380615234375,
                                   :y2 263.1875,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 44},
               :content {:text "流程和制度本质上是组织的最佳实践，是把自己或前人的经验教训总结到文档上，固化下来，以避免走弯路或犯不该犯的错误。但是，最佳实践也不是永远适用的，随着环境的改变，原来的最佳实践可能不再适用，这就需要找到新的最佳实践，用新的制度流程去代替旧的制度流程。当然，这个转变和迭代过程也应该是循序渐进的。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd22d-7c2a-4a9e-92af-af5451f13afe",
               :page 44,
               :position {:bounding {:x1 0,
                                     :y1 82.5,
                                     :x2 640.5796508789062,
                                     :y2 400.25,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 284.640625,
                                   :x2 640.5796508789062,
                                   :y2 312.140625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 314.015625,
                                   :x2 640.5725708007812,
                                   :y2 341.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 318.015625,
                                   :x2 640.5725708007812,
                                   :y2 337.59375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 343.3828125,
                                   :x2 640.5725708007812,
                                   :y2 370.8828125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 347.3828125,
                                   :x2 640.5725708007812,
                                   :y2 366.9609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 372.75,
                                   :x2 170.64205932617188,
                                   :y2 400.25,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 44},
               :content {:text "相应地，整个过程需要有人专门负责，这个人必须不断审视组织已经常态化的工作机制，评估它们与当前环境和未来发展趋势的适用程度，然后在此基础上进行调整，这就是我们所说的项目管理。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd235-f25e-43ab-bd12-8eed9c9f4a5a",
               :page 44,
               :position {:bounding {:x1 0,
                                     :y1 138.5,
                                     :x2 640.5725708007812,
                                     :y2 507.9453125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 138.5,
                                   :x2 0,
                                   :y2 155.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 152.5,
                                   :x2 0,
                                   :y2 169.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 421.703125,
                                   :x2 620.9933471679688,
                                   :y2 449.203125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 451.078125,
                                   :x2 640.5725708007812,
                                   :y2 478.578125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 455.078125,
                                   :x2 640.5725708007812,
                                   :y2 474.65625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 480.4453125,
                                   :x2 542.6723022460938,
                                   :y2 507.9453125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 44},
               :content {:text "当确定了新的最佳实践后，要把它固化到日常运营工作中去，这就要建立新的流程制度，这些新的流程制度一旦被组织成员运用到工作中，就会成为组织自身能力的一部分。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd261-fd2b-4691-8cf4-28b4959fb242",
               :page 44,
               :position {:bounding {:x1 0,
                                     :y1 180.5,
                                     :x2 640.5725708007812,
                                     :y2 615.640625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 180.5,
                                   :x2 0,
                                   :y2 197.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 194.5,
                                   :x2 0,
                                   :y2 211.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 529.3984375,
                                   :x2 630.7852783203125,
                                   :y2 556.8984375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 558.765625,
                                   :x2 640.5725708007812,
                                   :y2 586.265625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 562.765625,
                                   :x2 640.5725708007812,
                                   :y2 582.34375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 588.140625,
                                   :x2 151.06358337402344,
                                   :y2 615.640625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 44},
               :content {:text "可见，项目的价值在于驱动变革（见图1-5），运营能够维持组织在一定水平上持续运行，而项目可以实现组织运营水平的提升。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd26e-8c45-4303-8893-2b35ef07d821",
               :page 45,
               :position {:bounding {:x1 92,
                                     :y1 80,
                                     :x2 657,
                                     :y2 418,
                                     :width 734.4,
                                     :height 950.4},
                          :rects (),
                          :page 45},
               :content {:text "[:span]", :image 1687999086294},
               :properties {:color "yellow"}}
              {:id #uuid "649cd277-20c5-4008-93f3-84c461d33e7a",
               :page 45,
               :position {:bounding {:x1 225.2421875,
                                     :y1 433.2734375,
                                     :x2 509.16021728515625,
                                     :y2 460.7734375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 225.2421875,
                                   :y1 433.2734375,
                                   :x2 280.7109375,
                                   :y2 460.7734375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 293.7734375,
                                   :y1 433.2734375,
                                   :x2 509.16021728515625,
                                   :y2 460.7734375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 274.1875,
                                   :y1 437.2734375,
                                   :x2 280.7109375,
                                   :y2 456.8515625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 45},
               :content {:text "图1-5 项目的价值在于驱动变革"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd32d-2167-4608-a686-3e07d8210339",
               :page 45,
               :position {:bounding {:x1 0,
                                     :y1 40.5,
                                     :x2 640.5725708007812,
                                     :y2 707.3046875,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 82.5,
                                   :x2 0,
                                   :y2 99.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 96.5,
                                   :x2 0,
                                   :y2 113.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 562.328125,
                                   :x2 591.629638671875,
                                   :y2 589.828125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 591.6953125,
                                   :x2 640.5725708007812,
                                   :y2 619.1953125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 595.6953125,
                                   :x2 640.5725708007812,
                                   :y2 615.2734375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 621.0703125,
                                   :x2 640.5725708007812,
                                   :y2 648.5703125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 625.0703125,
                                   :x2 640.5725708007812,
                                   :y2 644.6484375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 650.4375,
                                   :x2 630.787353515625,
                                   :y2 677.9375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 654.4375,
                                   :x2 630.787353515625,
                                   :y2 674.015625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 679.8046875,
                                   :x2 464.3505859375,
                                   :y2 707.3046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 683.8046875,
                                   :x2 464.3505859375,
                                   :y2 703.3828125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 45},
               :content {:text "为满足市场需要，A公司决定研发一款新手机（一个项目），新手机定型后转入批量生产（运营）。新手机获得了客户的喜爱，需要提高产能（一个新项目）。随着市场的变化和竞争对手的发展，该款手机已经不再满足客户的需求，A公司决定对这款手机进行升级换代（又一个新项目）。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd3de-3720-4f54-b3e3-357d9d23a4ca",
               :page 45,
               :position {:bounding {:x1 -0.00385284423828125,
                                     :y1 110.5,
                                     :x2 734.************,
                                     :y2 1910,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 110.5,
                                   :x2 0,
                                   :y2 127.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 124.5,
                                   :x2 0,
                                   :y2 141.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.47996520996094,
                                   :y1 728.7522583007812,
                                   :x2 611.2023773193359,
                                   :y2 756.2522583007812,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.00385284423828125,
                                   :y1 757.2377319335938,
                                   :x2 734.************,
                                   :y2 786.5971069335938,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 -0.00385284423828125,
                                   :y1 786.6049194335938,
                                   :x2 734.************,
                                   :y2 950.3861694335938,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 958.5,
                                   :x2 0,
                                   :y2 975.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 960,
                                   :x2 734,
                                   :y2 1910,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 45},
               :content {:text "项目和运营的常见关系如图1-6所示。随着相关工作的完成，可交付成果和知识在项目与运营间转移。当项目开始时，资源从运营转移到项目；在项目接近结束时，资源则从项目转移到运营。因此，运营部门与项目团队之间通常都会进行大量互动，为实现项目目标而协同工作。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd3f4-60d9-488b-956f-025cc52004b3",
               :page 46,
               :position {:bounding {:x1 0,
                                     :y1 26.5,
                                     :x2 640.5796508789062,
                                     :y2 282.7734375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 26.5,
                                   :x2 0,
                                   :y2 43.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 40.5,
                                   :x2 0,
                                   :y2 57.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.484375,
                                   :y1 167.15625,
                                   :x2 640.5796508789062,
                                   :y2 194.65625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 196.53125,
                                   :x2 640.5725708007812,
                                   :y2 224.03125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 200.53125,
                                   :x2 640.5725708007812,
                                   :y2 220.109375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 225.8984375,
                                   :x2 640.5725708007812,
                                   :y2 253.3984375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 229.8984375,
                                   :x2 640.5725708007812,
                                   :y2 249.4765625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.3203125,
                                   :y1 255.2734375,
                                   :x2 425.193603515625,
                                   :y2 282.7734375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 46},
               :content {:text "运营实现组织的持续稳定，项目实现组织的持续发展。一静一动，共同支撑起组织的战略落地工作，确保组织可以不断地朝着战略规划的方向前进，边运转边调整，既能保持组织的健康运转，又具备依据环境调整自身的能力。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd40e-9237-4953-978d-cda91a89753a",
               :page 46,
               :position {:bounding {:x1 83,
                                     :y1 307,
                                     :x2 656,
                                     :y2 544,
                                     :width 734.4,
                                     :height 950.4},
                          :rects (),
                          :page 46},
               :content {:text "[:span]", :image 1687999502479},
               :properties {:color "yellow"}}
              {:id #uuid "649cd412-5494-427d-b7f6-cc1731f73199",
               :page 46,
               :position {:bounding {:x1 225.2421875,
                                     :y1 561.4375,
                                     :x2 509.16021728515625,
                                     :y2 588.9375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 225.2421875,
                                   :y1 561.4375,
                                   :x2 509.16021728515625,
                                   :y2 588.9375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 274.1875,
                                   :y1 565.4375,
                                   :x2 300.6009216308594,
                                   :y2 585.015625,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 46},
               :content {:text "图1-6 项目与运营的关系和互动"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd502-0184-49a4-aca9-a7b657e50385",
               :page 47,
               :position {:bounding {:x1 92.390625,
                                     :y1 124.015625,
                                     :x2 565.4863891601562,
                                     :y2 159.515625,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 92.390625,
                                   :y1 124.015625,
                                   :x2 164.828125,
                                   :y2 159.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 181.875,
                                   :y1 124.015625,
                                   :x2 565.4863891601562,
                                   :y2 159.515625,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 156.3125,
                                   :y1 129.015625,
                                   :x2 164.828125,
                                   :y2 154.5859375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 47},
               :content {:text "1.3.2 你说的项目管理可能不是项目管理"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd507-12c7-48b6-9c8f-3a19e0223448",
               :page 47,
               :position {:bounding {:x1 0,
                                     :y1 12.5,
                                     :x2 627.2348022460938,
                                     :y2 252.3125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 12.5,
                                   :x2 0,
                                   :y2 29.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 195.109375,
                                   :x2 627.2348022460938,
                                   :y2 222.609375,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 224.8125,
                                   :x2 587.6410522460938,
                                   :y2 252.3125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 47},
               :content {:text "作为支撑组织战略落地的半边天，项目管理类工作又可以分为三个层次：项目组合管理、项目集管理和项目管理。"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd50e-8586-46f1-ab40-a328e9adc02c",
               :page 47,
               :position {:bounding {:x1 131.984375,
                                     :y1 274.3125,
                                     :x2 270.65631103515625,
                                     :y2 301.8125,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 131.984375,
                                   :y1 274.3125,
                                   :x2 270.65631103515625,
                                   :y2 301.8125,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 47},
               :content {:text "1.项目组合管理"},
               :properties {:color "yellow"}}
              {:id #uuid "649cd512-b327-40f9-aeda-f170a77c2cbf",
               :page 47,
               :position {:bounding {:x1 0,
                                     :y1 54.5,
                                     :x2 627.2626953125,
                                     :y2 410.7109375,
                                     :width 734.4,
                                     :height 950.4},
                          :rects ({:x1 0,
                                   :y1 54.5,
                                   :x2 0,
                                   :y2 71.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 0,
                                   :y1 68.5,
                                   :x2 0,
                                   :y2 85.5,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 131.984375,
                                   :y1 323.8125,
                                   :x2 627.2348022460938,
                                   :y2 351.3125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 353.5078125,
                                   :x2 627.2626953125,
                                   :y2 381.0078125,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 357.5078125,
                                   :x2 627.2626953125,
                                   :y2 377.3046875,
                                   :width 734.4,
                                   :height 950.4}
                                  {:x1 92.390625,
                                   :y1 383.2109375,
                                   :x2 310.3054504394531,
                                   :y2 410.7109375,
                                   :width 734.4,
                                   :height 950.4}),
                          :page 47},
               :content {:text "项目组合管理位于最上面的第一层，衔接战略规划和项目管理。组合的概念来源于投资领域，人们在投资时会通过组合不同的股票来降低风险。"},
               :properties {:color "yellow"}}],
 :extra {:page 47}}
