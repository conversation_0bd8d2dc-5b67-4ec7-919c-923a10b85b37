- **[[Morning-Page]]**
- [[Society]]
- [[Family]]
- [[Myself]]
	- 13:32 - 14:14（42分）
	  collapsed:: true
		- #自习室
			- #回忆
				- 还是 [[苹果小妹]]靠谱，原来就是电池鼓包了，换了个新电池，原装电源，啥事没有，这台2015的 [[<PERSON> Mac]] 还能再战5年！
				  id:: 6347e31a-a964-4c4c-b019-83f9f62e3467
					- #then
						- DONE 退掉两个盗版 [[充电器]] 
						  id:: 6347d3db-fe6c-4d80-a536-0f1f77104f63
						  SCHEDULED: <2022-10-14 Fri .+1d>
							- #step
								- DONE 1个 [[拒收]]
								  collapsed:: true
									- DONE 快递退回
									- DONE 沟通退款
									- DONE 60到账 
									  id:: 634ba22a-3688-4808-b59a-69ea402144e8
									  SCHEDULED: <2022-10-17 Mon .+1d>
								- DONE 1个退回去
								  collapsed:: true
								  :LOGBOOK:
								  CLOCK: [2022-10-13 Thu 17:28:58]--[2022-10-13 Thu 17:32:58] =>  00:04:00
								  :END:
									- DONE 约 [[菜鸟]]上面取件，费用5元 
									  SCHEDULED: <2022-10-14 Fri .+1d>
									- DONE 快递拿走
									- DONE 给卖家发快递单号
									- DONE 60到账
			- #continue
				- ((634658fd-d5ba-49bd-bfcc-9c035f4d1e64))
				- ((63423a17-e303-4704-a00d-cd519d18b7e8))
					- #回忆
					  collapsed:: true
						- 去 [[建设银行]]查了，必须要到本行办理，而且还特麻烦，需要提供各种证明，算了，这个不要线上功能了，纯当柜台转账卡使用
						  id:: 634f70bf-1c64-48dc-8186-39a8ffeb53c0
				- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
				- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a)) 
				  ```calc
				  570+30.98+12+30.56+14
				  ```
				- ((6306282f-6ee9-447f-abda-1b78da19648b))
			- #回忆
				- ((6353a251-268b-4d21-a10d-e5e664f14e47)) -> 1点睡，上床被 [[王露]] 搞了几下（推开、帮他脱裤子都要推开），失眠到3点，早上9点30起床；哇睡不着的那几下，真的是各种负面情绪、想法，那个气啊，下了好几次床，抽烟、看视频，都不管用，喝 [[超级元素晚安瓶]]才睡下。
		- #log
			- 13:53 - 14:00
				- #continue
					- {{embed ((63463092-a5ef-436c-950a-783fc3a86193))}}
			- 14:00 - 14:14
				- #闪念
					- DONE [workflow]([[workflow]]) ：优化[[logseq]] [[logseq-css]] #logseq-css #recycle
					  id:: 634850c7-edf1-416b-83b4-adee9a13fe94
					  :LOGBOOK:
					  CLOCK: [2022-10-13 Thu 14:00:41]
					  CLOCK: [2022-10-13 Thu 14:00:45]--[2022-10-13 Thu 14:13:49] =>  00:13:04
					  :END:
						- DONE 把右侧栏的顶部导航隐藏了
						  id:: 634850c7-4b77-4b19-b98f-f5d2bb0f37b4
						- DONE 统一透明度 0.5
						  id:: 634850c7-d733-4140-95e2-a96fe7977ca7
			- 14:14
				- #break
					- 取咖啡
	- 15:28 - 18:40（3小时12分）
	  collapsed:: true
		- #闪念
			- DONE [[采耳]] 归来
			- CANCELED [[bug]] to [[Discord]]
			  id:: 634850c7-510c-45a0-be4a-132fd19520bc
			  collapsed:: true
				- 说下目前我使用比较别扭体验，当一个block加上了TODO（显示计时）、DEADLINE、或其他插件，如 wordcount ：
				  id:: 634850c7-743c-469b-9ae8-4b43813ababe
				  在块引用它时，计时、日期等信息会被换行显示，但很多时候我只是想引用块的内容而已，我不想换行。这会导致我的上下文排版很凌乱。
				  而在顶部面包屑导航的显示，也有同样的问题。导航的内容不再是一行到底，而是被计时、日期这些数据分割开。（这情况也包括底部的linked reference)
				  另一个问题，当我选择embed一个块，并在这个块下再缩进一些块，当我聚焦编辑时，顶部导航会显示整个embed的内容，这导致我不敢在包含embed的块下面缩进添加新的block。像references那样，在顶部面包屑导航时，embed也只显示第一行会不会更好？
				  最后一个问题，还是关于面包屑导航，如果导航里有［［标签］］，点击标签还是会回到当前聚焦的上层内容，而不是跳转到标签页面。但导航里有block reference，点击则立即跳转，这个体验也很不好。我不得不修改css，在每个导航后面加个图标，确保自己不会点错。
				  >Let me say that I am currently using a rather awkward experience, when a block is added with TODO (display timing), DEADLINE, or other plugins, such as wordcount :
				  When a block is referencing it, information such as timing, date, etc. will be displayed on a new line, but many times I just want to quote the content of the block, and I don't want to wrap it.  This causes my contextual typography to be messy.
				  And the display of breadcrumb navigation at the top has the same problem.  The content of navigation is no longer a line to the end, but is divided by data such as timing and date.  (This case also includes the linked reference at the bottom)
				  Another problem, when I select a block to embed, and indent some more blocks under this block, when I focus on editing, the top navigation shows the content of the entire embed, which causes me to be afraid to indent below the block containing the embed  Add new blocks.  Wouldn't it be better to embed to only show the first row when breadcrumb navigation at the top, like in references?
				  The last question is about breadcrumb navigation. If there is [[tab]] in the navigation, clicking on the tab will still return to the currently focused upper content instead of jumping to the tab [[page]].  However, there is a block reference in the navigation, and the click will jump immediately, which is also a very bad experience.  I had to modify the css to add an icon after each nav to make sure I didn't click the wrong one.
					- #link
						- ((6364c6e4-059c-4ac7-ad49-284b01dde1ac)) ((6364c6e4-11ee-471e-84f9-d5d0b5a13825)) ((6364dd44-d6e1-4555-a5a0-7d2b2a5d4c20)) ((6364e71a-7008-4c8d-bcad-376f7b88ffd3))
		- #闪念
			- DONE 小[[字体]]和 # 一样 #Logseq
			  id:: 634850c7-3adb-445e-b933-331c8c726bc0
			  :LOGBOOK:
			  CLOCK: [2022-10-13 Thu 15:32:42]--[2022-10-13 Thu 15:50:27] =>  00:17:45
			  :END:
			- DONE 看下chrome 顶部导航样式 #Logseq
			  id:: 6368aff6-3cbf-4690-8f79-ea4a3a82d3ab
				- DONE 找不到 `/block reference` 之类的样式
				  id:: 634cef11-6b15-4dc1-8ec6-0f9155d47b0c
				  :LOGBOOK:
				  CLOCK: [2022-10-17 Mon 18:09:32]--[2022-10-17 Mon 18:36:42] =>  00:27:10
				  :END:
				- DONE 找到底部 `now` 之类的样式
				  id:: 6347e31a-b953-435b-abbc-5478beb8e5fd
		- #log
			- 16:29 - 17:11
				- #break
					- #闪念
						- CANCELED 测试 [[GoogleDrive]] -> ((6329522b-06fc-4aef-91f5-c0ab1bebd9a8)) -> ((632b1f29-4560-4a35-838c-e45e7c770d5c))
						  id:: 651ae2ca-32c8-40d4-acb8-038baa414755
						  :LOGBOOK:
						  CLOCK: [2022-10-13 Thu 16:39:41]
						  :END:
			- 17:12 - 17:27
				- #摘录
					- CANCELED [双向链接时代的快速无压记录](cubox://card?id=********************************)
					- CANCELED [拿到offer后翻车了](cubox://card?id=********************************) #中年
					- CANCELED [小红书看上「老红书」](cubox://card?id=********************************)
					- CANCELED [被误读的强迫症，和那些不安的人们](cubox://card?id=********************************)
					- CANCELED [为什么日本援交少女突然学起了中文？-虎嗅网](cubox://card?id=********************************)
					- CANCELED [国庆最新爆款“巨型珍珠奶茶”：珍珠放大5倍，10天卖出10万杯！](cubox://card?id=********************************)
			- 17:28 - 17:34
				- #continue
					- {{embed ((6347d3db-fe6c-4d80-a536-0f1f77104f63))}}
			- 18:07 - 18:09
				- #continue
					- ((6347e31a-b953-435b-abbc-5478beb8e5fd))
						- #回忆
							- 我太狠了，直接杀到`awesome ui`一个个翻源代码，又看到config.edn 里面提到now的设置，是一个关于query的查询，终于被我找到了，啊哈哈哈哈
			- 18:09 - 18:32
				- #continue
					- {{embed ((63463092-a5ef-436c-950a-783fc3a86193))}}
			- 18:39
				- #break
					- #闪念
						- DONE 关心 [[王露]] [[面试]]情况
						  id:: 6347eaec-41e2-40c9-8574-1411315917cb
						  :LOGBOOK:
						  CLOCK: [2022-10-13 Thu 18:39:44]--[2022-10-13 Thu 22:06:14] =>  03:26:30
						  :END:
	- 19:29 - 21:59（2小时20分）
	  collapsed:: true
		- #家
			- #闪念
				- DONE 清理 [[Filco]] [[键盘]]
				  :LOGBOOK:
				  CLOCK: [2022-10-13 Thu 19:31:14]
				  CLOCK: [2022-10-13 Thu 19:31:17]--[2022-10-13 Thu 21:58:36] =>  02:27:19
				  :END:
		- #log
			- 21:58 - 21:59
				- #continue
					- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
					  :LOGBOOK:
					  CLOCK: [2022-10-13 Thu 21:59:03]--[2022-10-13 Thu 21:59:03] =>  00:00:00
					  :END:
	- 22:31 - 23:52 （1小时21分）
	  collapsed:: true
		- #回忆
			- 终于是我自己的时间了。
		- #review
			- ((6347e322-e559-418e-b8f4-f1b89991659b))
				- #回忆
					- [[裸机]]的16寸 [[Apple Macbook Pro]] 是无敌的，不要再想air了 -> ((6347e31a-a964-4c4c-b019-83f9f62e3467))
		- #continue
			- {{embed ((63463092-a5ef-436c-950a-783fc3a86193))}}
		- #闪念
			- CANCELED [workflow]([[workflow]])：[[logseq]]调整下h1-h7行高
			  id:: 634850c7-0b32-4a37-aac1-33069e3927ac
			  :LOGBOOK:
			  CLOCK: [2022-10-13 Thu 23:48:25]
			  CLOCK: [2022-10-13 Thu 23:48:31]
			  :END:
		- #log
			- 23:16 - 23:45
				- #continue
					- ((651bc21c-3131-406f-9608-99fda127b5e3))
					  :LOGBOOK:
					  CLOCK: [2022-10-13 Thu 23:16:27]
					  CLOCK: [2022-10-13 Thu 23:16:44]--[2022-10-13 Thu 23:45:01] =>  00:28:17
					  :END:
- [[Comments]]
  collapsed:: true
	- #质疑
		- 还是搞不定。 -> ((651ae2ca-32c8-40d4-acb8-038baa414755))
	- #质疑
		- 拉倒吧，就这样了。 -> ((634850c7-0b32-4a37-aac1-33069e3927ac))