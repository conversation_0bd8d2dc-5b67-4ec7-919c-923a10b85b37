- **[[Morning-Page]]**
  collapsed:: true
- [[Society]]
- [[Family]]
- [[Myself]]
	- 14:55 - 17:13（2小时45分）
	  collapsed:: true
		- #闪念
			- DONE 和 [[Charliie]] [[大神]] 一起搞定 [[logseq]] [[<PERSON> Mac]] & [[Andorid]] [[git]] [[SYNC]]  #Discord #ending
			  id:: 634bab0f-6125-4d13-aa7d-2b6bf051f37c
			  link:: [Logseq-Git-Sync-101](https://github.com/CharlesChiuGit/Logseq-Git-Sync-101) [Youtube](https://www.youtube.com/watch?v=c2HrdSOoVD8&t=596s)
			  collapsed:: true
				- #recycle
					- ((634ba228-0c2f-4487-a9ae-22c13a143dd3))
					- ((632b1f29-4560-4a35-838c-e45e7c770d5c))
					- ((6329522b-06fc-4aef-91f5-c0ab1bebd9a8))
				- #step  
				  :LOGBOOK:
				  CLOCK: [2022-10-16 Sun 16:44:08]
				  :END:
					- DONE [[Apple Mac]] <-> [[Github]]
					  collapsed:: true
						- #step
							- DONE [[Github]]创建私人项目， [[命名]]
							- DONE [[Apple Mac]] 创建同名文件夹（不是 [[iCloud]] 文件夹）
							- DONE 文件夹右键选中进入 [[Terminal]]，执行 [[Github]] [[Repositiores]] 创建成功的提示
							  link:: [mac右键进入终端](https://blog.csdn.net/quantum7/article/details/123843617?utm_medium=distribute.pc_aggpage_search_result.none-task-blog-2~aggregatepage~first_rank_ecpm_v1~rank_v31_ecpm-1-123843617-null-null.pc_agg_new_rank&utm_term=mac%20%E5%8F%B3%E9%94%AE%E6%89%93%E5%BC%80%E7%BB%88%E7%AB%AF&spm=1000.2123.3001.4430)
							  :LOGBOOK:
							  CLOCK: [2022-10-16 Sun 15:40:17]--[2022-10-16 Sun 15:40:20] =>  00:00:03
							  :END:
							  ``` 
							  echo "# Loggy" >> README.md
							  git init
							  git add README.md
							  git commit -m "first commit"
							  git branch -M main
							  git remote add origin https://github.com/{username}/{reponame}.git
							  git push -u origin main
							  ```
								- #if
									- DONE 最后一步出现报错
										- #案例
										  ``` 
										  xcrun: error: invalid active developer path (/Library/Developer/CommandLineTools), missing xcrun at: /Library/Developer/CommandLineTools/usr/bin/xcrun
										  ```
										- #then
											- DONE 在 [[Terminal]] 里检查安装 [[XCODE]]： 
											  ``` 
											  xcode-select --install
											  ``` 
											  :LOGBOOK:
											  CLOCK: [2022-10-16 Sun 16:44:36]--[2022-10-16 Sun 16:44:37] =>  00:00:01
											  :END:
											  link:: https://blog.csdn.net/mifangdebaise/article/details/102722144
											- DONE remote: Support for password authentication was removed on August 13, 2021.
											  :LOGBOOK:
											  CLOCK: [2022-10-16 Sun 16:44:37]--[2022-10-16 Sun 16:44:43] =>  00:00:06
											  :END:
											  remote: Please see https://docs.github.com/en/get-started/getting-started-with-git/about-remote-repositories#cloning-with-https-urls for information on currently recommended modes of authentication.
											  fatal: Authentication failed for 'https://github.com/hillsmao/Loggy.git/'
												- DONE 需要设定 [[密钥]]（不是 [[Github]]登录密码 ）
												  link:: [About remote repositories](https://docs.github.com/en/get-started/getting-started-with-git/about-remote-repositories#cloning-with-https-urls%20for%20information)
													- #做选择
														- DONE 个人登录凭证，在 [[Github]]上设置一个密钥，供某个终端使用（不推荐）
														  id:: 634bb0c7-1978-415d-857b-515761475065
														  link:: [Personal access tokens](https://github.com/settings/tokens)
														- DONE 安装 Credential Manager，相当于装一个软件和 [[Github]]登录，这个软件就是安全凭证，供一台设备使用。 [[Apple Mac]] or [[Winodws]] 
														  link:: [GitCredentialManager](https://github.com/GitCredentialManager/git-credential-manager) [Download](https://github.com/GitCredentialManager/git-credential-manager/releases/tag/v2.0.785)
															- #回忆
																- 目前只在 [[Apple Mac]] 成功，移动端不知道行不行。
																- [[logseq]] 自动同步必须要这个，因为个人登录凭证只给 [[Terminal]]使用，logseq上又没有填写密钥的地方
																	- #link
																		- ((634bb0c7-1978-415d-857b-515761475065))
														- DONE [[SSH Key]] -> [[Charliie]] 推荐：终端生成[[密钥]] -> [[Github]] 上登记即可， [[Andorid]] 会用到
														  id:: 634bb276-2f63-49d7-8c54-0b88a9452e80
										- #then
											- DONE 进入logseq，添加图谱，调试 [[Github]] 同步
												- git pull
												- git commit -m “logseq by terminal”
												- git push
												- #做选择
													- DONE 安装 [[GitHub Desktop]]， [[GUI]]图形界面更加直观显示是否同步成功，适合新手
												- #if
													- DONE 用了 [[梯子]]
														- #then
															- DONE 给 [[Terminal]]设置代理
															  link:: [git 终端代理](https://blog.csdn.net/xiaojingfirst/article/details/115413510)
																- #回忆
																	- 一开始我以为是要给terminal开启代理，走了好长一段弯路，后来发现，只要 git config 里面开启代理就可以了——这个对国内新手用户蛮重要，也可以提示一下
								- DONE [[教程]] 里不明觉厉，但比较重要的步骤
									- DONE [[Apple Mac]] 打开 [[隐藏文件]] -> shift+cmd+.
										- Open the folder, there should have a hidden folder named  `.git` .
										- Copy&paste the  `post-commit`  and  `pre-commit`  in  `.git/hooks` .
										- Right click in  `.git/hooks`  and click  `New Terminal at folder` , type
										  
										  ```
										  chmod +x ./pre-commit && chmod +x ./post-commit
										  ```
										- #if
											- DONE 这时候如果有旧的 [[logseq]]，复制进新的 [[Github]]项目文件夹
												- #做选择
													- DONE 用 [[GitHub Desktop]] git push
													  :LOGBOOK:
													  CLOCK: [2022-10-16 Sun 16:45:35]--[2022-10-16 Sun 16:45:35] =>  00:00:00
													  :END:
													- DONE 用 [[Terminal]] git push
													  :LOGBOOK:
													  CLOCK: [2022-10-16 Sun 16:45:35]--[2022-10-16 Sun 16:45:35] =>  00:00:00
													  :END:
												- #then
												  :LOGBOOK:
												  CLOCK: [2022-10-16 Sun 16:45:35]
												  :END:
													- DONE 等待一会，大功告成
								- DONE [[Apple Mac]] [[logseq]] <-> [[Github]] 互通之后，logseq后台开启 git，间隔60秒
									- #做选择
										- DONE 安装 [[git]] 插件，手动同步
											- #回忆
												- 我都要。
									- #then
										- DONE 双向测试，同步即成功。
											- #回忆
												- 几乎都是秒 [[同步]] ，而且 [[细粒度]] 。那种打开电脑、打开软件就是最新感觉，太赞了。用 [[iCloud]] 同步，几乎每次都是 [[小心翼翼]] 的，等待icloud自动同步。
												  id:: 634cbac2-46df-48e0-bb84-e09ae275cfae
													- #continue
														- 13:33 #[[Discord]]
															- 你知道这个 [[git]] [[SYNC]] 的方法，我真的放在心里有4个月了！无数次想 [[搞定]] ，最后都 [[放弃]] 。昨晚真是幸运，遇到原作者，还陪我肝一个通宵，太太有福了 哈哈
															  id:: 634cbac2-0fbc-4695-b94e-dd033414c5f9
																- 啥别不多说了，我恢复精力过来给你点star，多多益善
													- #link
														- ((634ba267-6c5c-4971-b1b6-308ae326f3d7))
														- ((6321cf53-e10c-4853-8cc6-2efa9284e420))
					- DONE [[Andorid]] <-> [[Github]]
					  collapsed:: true
						- DONE 看教程，安装 [[Termux]] [[F-Droid]]
							- #if
								- DONE [[文档]] [[Termux]] 两边都找不到对应文件
									- cd 文件夹名称，进入
									- cd 退到根目录
									- ls 列出所有文件
									- ls -a 显示所有文件包括隐藏文件
										- ls -a ~/.shortcuts
									- rm 删除
										- rm -r LogseqGit
										  id:: 634bbc2f-bd2b-42de-ac73-4f24e08f8d46
										- cd .. && rm -rf LogseqGit
									- mv 移动
										- mv *-graph ~/.shortcuts
									- #回忆
										- 一定是把项目文件放在了 [[Termux]] 程序根目录里，termux和 [[文档]]是互通 [[Storage]]的。
											- #then
												- DONE 我最后选择把项目文件放在 sotrage/downlod下
													- 而且啃爹的是文档下的download叫：Download
														- termux叫：downloads
															- 他们是一个文件夹， [[诡异]]。
						- DONE 安装 [[SSH Key]] 相关套件
							- apt update && apt upgrade
								- pkg install openssh
									- #回忆
										- 中間可能會確認你要不要執行這個動作 按 `y` 然後enter
									- #if
										- DONE 已经有同名的项目文件夹，要删掉，否则克隆不下来
											- ((634bbc2f-bd2b-42de-ac73-4f24e08f8d46))
								- ssh-keygen
							- #then
								- DONE 生成 [[密钥]]
									- ls -a
										- DONE 看一下有沒有 `.ssh`  folder
											- DONE cd 进去
												- DONE 應該會有兩個檔案 id_rsa和id_rsa.pub
													- cat id_rsa.pub
														- DONE 然後把顯示出來的 [[密钥]] 一字不漏複製起來
								- DONE [[Apple Mac]] [[Github]] -> 登记 [[密钥]] -> settings -> ssh and gpg keys
									- #回忆
										- ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDiGgxzk19f7Tod6vfavW2hZs7Ey8l2Gc3Z/jw1iqARDXuIr3R08UUNdPsqTY23RW7Xl0q7fVRHEpzIn876mcbodAGFwovkJc+HnK2B6di/KmpXk6CRkoLFiFfbW4XmB679EwH5wnchefwQUlIV0QTISNuPPbe05lIqdSIHELiju2xEbHT3ZrNer2iVnRaudT1DSz2SDbSxBj5klwzoB4Cd3fEfw3tBv5JY34FD+t3T9yiNKvf+jiZWVA5FvkpWHXS3Yx+R1ht4LQJRJBQAPkxYUGWFJbko8By8seTYlL3snzd+NtQ4KnyxHbtxoSeEeygFoZ1EeepP+H8KaGNOuMMI0VHDmzf+IPhU8wENl/W8IhY5UFq0CSSxZKAkuxBHqIMGAQlUjfJwBTCT5o9j9AXG7bkEjelLPff4s3lHDNpog6yGCE/NdSIpVUuTPk8ZnW/gv+MHCeoCDjTKyzV9gYbC8tl/nwmdfZRrNdLE8wKXyHMhmM3aO1yADduextf+/0M= u0_a587@localhost
							- #link
								- ((634bb276-2f63-49d7-8c54-0b88a9452e80))
						- DONE [[Termux]] [[Clone]]
							- [[Termux]] [[Git重要命令]]
								- git config --global user.name "hillsmao"
								- git config --global user.email "<EMAIL>"
								- git config --global --add safe.directory /storage/emulated/0/Download/loggit
								- git remote set-<NAME_EMAIL>:hillsmao/loggit.git
								- git remote -v
								- <NAME_EMAIL>:hillsmao/loggit.git ~/storage/downloads
								- DONE [[微信收藏]]
									- cd storage/downloads/loggit
									- git pull
									- git add -A && git commit -m "sync from android" && git push
									- git push
							- #回忆
								- 又涉及到 [[梯子]]问题，折腾很久很久，其实就是github访问不稳定，关了vpn就好了。
								  link:: https://idreamshen.github.io/posts/github-connection-closed/
									- CANCELED source bin/source-ssh-agent
										- git push
									- CANCELED ssh-add ~/.ssh/id_rsa
								- 还有 widget 可以不用
								- 最后就是同步报错产生分支：我这边发现的原因是，只要我不重复 pull，就没问题。就是说当我需要在手机编辑时，先pull一次，然后接下来所有的编辑，都只需要push，就可以，如果再pull，就会出现问题。
									- git config pull.rebase true
					- DONE 还需要搞定备份 [[GoogleDrive]]
					  collapsed:: true
					  id:: 63bff1b2-8b6e-4ffe-bdd7-8a9e2058c99b
						- #then
							- CANCELED [[iCloud]]本地文件自动化复制 
							  SCHEDULED: <2022-10-22 Sat .+1d>
						- #回忆
							- 不要整个项目备份！带有 [[git]] 直接 给 [[GoogleDrive]] 无限生成新文件！！ -> 子文件夹一个个备份吧
				- #回忆
					- 昨天23点开始到现在 16:48 ，只睡了1.5小时； [[肝]]了18小时！！！！
					  id:: 634c0d5d-2a03-4851-a23c-9d83fb261aae
						- 感谢 [[Charliie]]，你是我既 [[OneStutteringMind]] [[Conor White-Sullivan]] 第三位对我有重大帮助的人！
						  id:: 634bd106-0b8a-493f-93a6-5c586bc52852
							- 全程手把手指导，光截图就有53张
							  id:: 634cbac2-3f6e-4139-a51b-df8de433afeb
							  collapsed:: true
								- #截图
									- ![2022-10-16-21-24-11.jpeg](../assets/2022-10-16-21-24-11.jpeg){:width 245}
				- #log
				  collapsed:: true
					- 16:52 - 17:13
					  collapsed:: true
						- #闪念
						  collapsed:: true
							- DONE 测试 [[logseq]] [[SYNC]]
							  collapsed:: true
							  :LOGBOOK:
							  CLOCK: [2022-10-16 Sun 16:53:03]
							  CLOCK: [2022-10-16 Sun 16:53:05]--[2022-10-16 Sun 17:12:53] =>  00:19:48
							  :END:
								- DONE [[Apple Mac]] -> [[Github]] = ok
								- DONE [[Github]] -> [[Andorid]] = ok
								  :LOGBOOK:
								  CLOCK: [2022-10-16 Sun 16:55:47]
								  CLOCK: [2022-10-16 Sun 16:55:53]--[2022-10-16 Sun 17:04:24] =>  00:08:31
								  :END:
								- DONE [[Andorid]] -> [[Github]] = ok
								  :LOGBOOK:
								  CLOCK: [2022-10-16 Sun 17:11:27]--[2022-10-16 Sun 17:12:46] =>  00:01:19
								  :END:
	- 17:24 - 17:32（8分）
	  collapsed:: true
		- #回忆
			- [[岳父]] [[岳母]] [[老家]] [[机场]] [[封口]]，机票要改期了。
				- #continue
					- {{embed ((634ba22a-72fb-470e-9d66-a858b4150b48))}}
		- #log
			- 17:28 - 17:32
				- #continue
				  collapsed:: true
					- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
					- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
						- DONE [[黄埔公园]] [[草坪]]
				- #回忆
				  collapsed:: true
					- ((6353a251-268b-4d21-a10d-e5e664f14e47)) -> 哈哈哈哈， [[一把年纪]] ， [[通宵]] ！！！！！！！！啊哈哈哈哈！而且10点睡，11点40起床， [[状态]] 还是在线的，啊哈哈哈，所以说，做喜欢的事情并绝对的 [[投入]] ，有多重要！ [[SYNC]]的 [[心结]] 给老子解了，哦hoho！！！真的放在心里有4个月了！无数次想 [[搞定]] ，最后都 [[放弃]] 。昨晚真是 [[幸运]] ，遇到原 [[作者]] [[Charliie]] ，还陪我 [[肝]] 一个通宵，太太 [[有福]] 了 哈哈 -> ((634bab0f-6125-4d13-aa7d-2b6bf051f37c)) -> ((634ba7dc-6a88-4ae4-aec5-ef67166ebbbd))
					  id:: 634bd106-fe4c-498a-9c00-b764ed749d28
	- 17:39 - 18:07（27分）
	  collapsed:: true
		- #continue
			- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
			  :LOGBOOK:
			  CLOCK: [2022-10-16 Sun 17:40:17]--[2022-10-16 Sun 18:07:35] =>  00:27:18
			  :END:
		- #log
			- 17:40 - 17:44
				- #闪念
					- DONE 给 [[大神]] [[Charliie]] 刷 [[Github]]star 
					  SCHEDULED: <2022-10-17 Mon .+1d>
					  id:: 634c0d5d-c2a4-4839-a999-fba132105734
					  collapsed:: true
					  :LOGBOOK:
					  CLOCK: [2022-10-17 Mon 11:07:09]--[2022-10-17 Mon 11:27:25] =>  00:20:16
					  :END:
						- #step
							- DONE 上 [[淘宝]] [[闲鱼]] 找下
								- #回忆
									- 价格大概 1-2块1颗星
							- DONE [[知乎]]上查一下 [[Github]] [[刷星]]有什么风险
								- [[答案]]：被发现后的道德风险。不公开刷问题不大。
								- #if
									- DONE 风险可知可控
										- #then
											- DONE 冲多少？思考下
												- #深思
													- [[Charliie]] 在 [[Github]] 上有 6个项目
														- [Logseq-Git-Sync-101](https://github.com/CharlesChiuGit/Logseq-Git-Sync-101) 236 star
														  id:: 634cc6bb-4132-42d3-b9db-ef623a239066
														- [Logseq-Hugo-Template](https://github.com/CharlesChiuGit/Logseq-Hugo-Template) 22 star
														  id:: 634cc733-fc76-499b-be8d-a11168ee4c16
														- [nvimdots](https://github.com/CharlesChiuGit/nvimdots) 7 star
														  id:: 634cc6ed-14d5-4255-b6c4-dd70f98869e9
														- [CharlesChiuGit.github.io](https://github.com/CharlesChiuGit/CharlesChiuGit.github.io) 1 star
														  id:: 634cc728-f8ca-40fe-be80-6a200c95ab02
														- [Lab-env-management](https://github.com/CharlesChiuGit/Lab-env-management) 2 star
														- [dotfiles](https://github.com/CharlesChiuGit/dotfiles) 2 star
														  id:: 634cc736-3663-4a4e-9e84-7a5802edd909
														- 对比
															- [[logseq]]是1.7w star
															- [[lumacss]]是79 star
															- #then
																- DONE 刷100个吧
																	- ((634cc733-fc76-499b-be8d-a11168ee4c16)) +30
																	- ((634cc6ed-14d5-4255-b6c4-dd70f98869e9)) + 25
																	- ((634cc728-f8ca-40fe-be80-6a200c95ab02)) + 15
																	- ((634cc728-f8ca-40fe-be80-6a200c95ab02)) + 15
																	- ((634cc736-3663-4a4e-9e84-7a5802edd909)) + 15
																- DONE [[闲鱼]]找到 [[性价比]]好的卖家
																- DONE 下单
																- DONE 看结果 
																  id:: 634cbac2-5ac4-4a78-a04b-825f86e41a72
																  SCHEDULED: <2022-10-17 Mon 16:00>
						- #[[回忆]]
							- 再次感谢 [[Charliie]]让我终于用上了 [[Andorid]] [[logseq]]，使用 [[Discord]]这么久以来第一次获得极大的 [[即时帮助]] ，更重要的是极大的 [[正反馈]] -> ((634c0d5d-2a03-4851-a23c-9d83fb261aae)) -> ((634bd106-fe4c-498a-9c00-b764ed749d28))
							  id:: 634cbac2-fb05-4370-8a69-e878a00e69e3
			- 17:48 - 18:07
				- #review
					- {{embed ((634bd106-fe4c-498a-9c00-b764ed749d28))}}
				- #闪念
				  collapsed:: true
					- CANCELED 尝试去掉 [[logseq]] [块引用]([[block-ref]]) 背景 -> ((6471e8cb-536f-4474-bead-aef923a7ac74))
					  id:: 651afa36-6f32-44c3-a37e-a992c5159c4b
					  :LOGBOOK:
					  CLOCK: [2022-10-16 Sun 23:01:51]
					  :END:
				- #log
					- 17:59 - 18:02
						- #[[闪念]]
							- 人不能[[逃避]][[困难]]。
							  id:: 634cbac2-cd01-4b9f-b7b6-00d70afe97d7
	- 21:09 - 21:53（44分）
	  collapsed:: true
		- #continue
			- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
			  :LOGBOOK:
			  CLOCK: [2022-10-16 Sun 21:10:31]--[2022-10-16 Sun 21:53:24] =>  00:42:53
			  :END:
				- #[[回忆]]
					- [[毛懋]] & [老妈]([[丁满冬]])[[照片]]
					  collapsed:: true
						- ![2022-10-16-21-14-12.jpeg](../assets/2022-10-16-21-14-12.jpeg){:width 245}
						- ![2022-10-16-21-15-09.jpeg](../assets/2022-10-16-21-15-09.jpeg){:width 245}
		- #log
			- 21:20 - 21:24
				- #continue
					- {{embed ((634bd106-0b8a-493f-93a6-5c586bc52852))}}
			- 21:25 - 21:27
				- #闪念
					- DONE 查下 [[Conor White-Sullivan]] 在 [[移动端]]的 [workflow]([[workflow]]) 
					  SCHEDULED: <2022-10-17 Mon .+1d>
					  id:: 634c0d5d-e5b3-49dd-8b15-b9282996b737
					  :LOGBOOK:
					  CLOCK: [2022-10-16 Sun 22:10:23]--[2022-10-16 Sun 23:04:07] =>  00:53:44
					  * State "DONE" from "DOING" [2022-10-16 Sun 23:04]
					  :END:
	- 21:53 - 23:09（1小时16分）
	  collapsed:: true
		- #continue
			- ((651aa258-198e-4b8e-af29-1b768aeb7a1a))
			  :LOGBOOK:
			  CLOCK: [2022-10-16 Sun 22:08:42]--[2022-10-16 Sun 23:10:14] =>  01:01:32
			  :END:
		- #log
			- 22:10 - 23:04（55分）
				- #continue
					- ((634c0d5d-e5b3-49dd-8b15-b9282996b737))
					  id:: 634d6701-7680-4f34-afe5-956b4eb968eb
						- #摘录
						  collapsed:: true
							- [[Twitter]] [[Conor White-Sullivan]]：关键词 [[Phone]] #任务管理
							  link:: [twitter](https://twitter.com/conaw/status/1565598781470433287?s=61&t=l44FWFYUS7OYvGP-ZNUr2Q) [twitter](https://twitter.com/conaw/status/1565598829700739072?s=61&t=l44FWFYUS7OYvGP-ZNUr2Q)
								- 我基本上从不通过 [[手机]] 进行认真的 [[思考]] 工作（去年我做过的一次是使用 Apple Notes）
								  但今晚我决定复习一个躺在床上的候选人的 In-Roam 练习，并向他们展示如何使用 [block refs]([[block-ref]]) 可能是 [[工作环境]] 中最重要的 [[特征]]
									- 这个例子是我们到达 [块引用]([[block-ref]]) 的[[完美]]例子
									  id:: 6471e8cb-536f-4474-bead-aef923a7ac74
										- 他们在第一个月做的一些任务，他们希望在第二个月和第三个月继续
											- 通过使用 refs 而不是（续），您无需阅读整个文档即可知道这一点，+ 部分会随着时间的推移而保持在一起
							- [[Conor White-Sullivan]] [[aha moment]][[啊哈时刻]]
							  link:: [twitter](https://twitter.com/conaw/status/1299475748097384448?s=61&t=YjvDTHFZlnPUO1LpwQIbAA)
							  #习惯 #RoamResearch #双链笔记
								- 啊哈时刻往往是
								  id:: 634c211a-dfcf-4c93-a55e-bd24836ac2c7
								  1. 尝试 [[学习]] 新的和具有 [[挑战性]] 的东西，Roam 1000x 中的笔记对于 [[探索]] 材料很有用
								  2. 感觉 [[生活]] [[混乱]] [[不堪重负]] ，Roam [[自下而上]] 的 [[组织]] 正在为 [[任务]] 、 [[项目]] 、 [[错误的想法]] 和拼凑在一起而 [[平静]] 下来
								  3. 开发您的 [[Magum Opus]] - 消除 [[重大项目]] 或大统一 [[理论]] ，Roam 帮助您对过去的工作产生 [[复合兴趣]] ，并在您知道它们如何组合在一起之前就开始工作的能力。
								  4. 在你的 [[生活]] 中“ [[做科学]] ”： [[日记]] 、 [[日常问题]] 、 [[跟踪习惯]] 、 [[跟踪时间]] 、 [[注意力]] 、 [[情绪]] 、 [[输入]] （ [[药物]] 、 [[食物]] 、 [[睡眠]] 、 [[媒体]] ）和 [[输出]] （ [[做了什么]] ，你 [[创造了什么]] ）和 [[发现模式]] 告知您想要 [[培养]] 的生活选择和 [[系统]] 。
									- #日常英语
										- 代表作 #card
											- Magum Opus
							- TODO [[Conor White-Sullivan]]强烈推荐这位博主的 [[RoamResearch]] 使用心得分享
							  link:: [twitter](https://twitter.com/conaw/status/1299480030653763584?s=61&t=S2ZxT3YM5tQKwUaelRy0ag) [twitter](https://twitter.com/kwharrison13/status/1299519854169346050?s=61&t=Dyq8M7xS40KDgZKpqCc2XQ)
								- [[Conor White-Sullivan]] ：与@kwharrison13交谈到目前为止，他的 10 分钟漫游演示是我最喜欢的演示，因为他是一个如此简单的人。**你可以在漫游中养成简单的 [[习惯]] ，它及时赋予你 [[简单]] 的 [[超能力]] ，这是我最自豪的。**
								  id:: 634c211a-2413-4451-ad14-bfd9833abe00
								  一直在等待他更广泛地发布它。
									- @kwharrison13：在过去的 3 个月里，我进行了超过 25 次漫游之旅。不是因为我擅长使用 Roam，而是因为我不擅长它。
									  我在这里向您展示 2 / 10 可以用像漫游这样强大的东西做什么。 #RoamTour
				- #log
					- 22:13
					  collapsed:: true
						- #闪念
							- DONE 发个 [[Twitter]] 感谢下 [[Charliie]] 
							  SCHEDULED: <2022-10-18 Tue .+1d>
							  id:: 634cbac2-a8cf-41a0-a2a4-2930487b9c7b
							  :LOGBOOK:
							  CLOCK: [2022-10-17 Mon 11:34:24]--[2022-10-17 Mon 12:02:45] =>  00:28:21
							  * State "DONE" from "DOING" [2022-10-17 Mon 12:02]
							  :END:
								- #output
								  collapsed:: true
									- 周六-周日，终于搞定了@logseq mac <-> andriod 的sync。一把年纪了，居然干了个通宵，关键是还有一位大神 @charliie —— [Logseq-Git-Sync-101](https://github.com/CharlesChiuGit/Logseq-Git-Sync-101) 的作者全程陪着我，完全手把手的指导，光是软件截图就有53张！我真是太有福了！SYNC真是心心念念太久了，每次想搞，都苦于文科生不懂这些命令，导致中途脑子烧穿，不得不放弃。现在手机与电脑同步，作为一个虔诚的时间记录者，仿佛拥有了真正的完整的24小时。
								- #recycle
								  collapsed:: true
									- ((634cbac2-46df-48e0-bb84-e09ae275cfae))
									- ((634bd106-fe4c-498a-9c00-b764ed749d28))
									- ((634bd106-0b8a-493f-93a6-5c586bc52852))
									- ((634c0d5d-2a03-4851-a23c-9d83fb261aae))
									- ((634cbac2-3f6e-4139-a51b-df8de433afeb))
									- ((634cbac2-fb05-4370-8a69-e878a00e69e3))
					- 22:16
						- #闪念
							- CANCELED [[logseq]] [[Mobile]] [[优化]] 
							  SCHEDULED: <2022-10-31 Mon .+1d>
							  id:: 634c211a-75c1-4f51-8416-1bbf2166c7f5
							  :LOGBOOK:
							  CLOCK: [2022-10-16 Sun 23:23:58]--[2022-10-16 Sun 23:32:26] =>  00:08:28
							  :END:
								- #必要条件
									- DONE 看下[[logseq]] [[Config]] [[Mobile]]有什么可以设置的
									  :LOGBOOK:
									  CLOCK: [2022-10-16 Sun 23:24:02]--[2022-10-16 Sun 23:32:24] =>  00:08:22
									  :END:
										- `:outliner/block-title-collapse-enabled?` false -> true
										  id:: 634cbac2-8398-4143-87f1-703e0595c1c8
										- `:ref/linked-references-collapsed-threshold` 50 -> 20
										- CANCELED 看下 [[Mobile]] 底部功能栏的 [[定制]]
									- CANCELED 看下 [[Custom]] [[logseq-css]] 关于 [[Mobile]]
									  id:: 635d3f86-c82e-48a2-8dfa-388197d33460
					- 22:21 - 22:23
						- #闪念
							- DONE 陪[[毛懋]]一会 [*](((63f4f1b2-416f-40a1-a075-e2b8ad3677b8)))
							  :LOGBOOK:
							  CLOCK: [2022-10-16 Sun 22:21:53]
							  CLOCK: [2022-10-16 Sun 22:21:58]--[2022-10-16 Sun 22:23:36] =>  00:01:38
							  :END:
			- 23:06 - 23:09
			  collapsed:: true
				- #continue
					- {{embed ((634c0d5d-c2a4-4839-a999-fba132105734))}}
	- 23:14 - 23:22（8分）
	  collapsed:: true
		- #continue
			- ((634bab0f-6125-4d13-aa7d-2b6bf051f37c))
				- #肯定
					- 搞定了 [[移动端]] 与 [[Apple Mac]]同步 [[logseq]]后，我终于可以放开手脚，进入 [[心流]]至境，成为一名 [[虔诚]]的 [[时间记录]]者！畅游在 [[念头]]的 [[精神世界]]里，我不需要 [[正念练习]]，我就在 [[正念]]中！
						- #link
							- ((634c211a-dfcf-4c93-a55e-bd24836ac2c7))
							- ((634c211a-2413-4451-ad14-bfd9833abe00))
							- ((6314bdc0-dc0d-41c3-9d3b-f0d6ae9f6fb3))
							- ((634ba242-c6b9-493f-bfce-2f70e5d55775))
							- ((62fca069-8c40-4248-bcdd-8dee6f4bdd71))
	- 23:22 - 23:33（11分）
	  collapsed:: true
		- #continue
			- {{embed ((634c211a-75c1-4f51-8416-1bbf2166c7f5))}}
	- 23:33 - 01:18（1小时45分）
	  collapsed:: true
		- #continue
			- ((651aa257-c7fc-43ca-8e45-b129364eabf0)) ((651ad660-4be8-4004-b82a-9ca1f84cea3a))
			  :LOGBOOK:
			  CLOCK: [2022-10-16 Sun 23:34:45]--[2022-10-17 Mon 01:00:18] =>  01:25:33
			  :END:
				- #and
					- ((651bc21c-3131-406f-9608-99fda127b5e3))
					  :LOGBOOK:
					  CLOCK: [2022-10-16 Sun 23:36:00]
					  CLOCK: [2022-10-16 Sun 23:36:18]--[2022-10-17 Mon 01:00:21] =>  01:24:03
					  :END:
		- #log
			- 01:00 - 01:18
				- #break
					- #continue
						- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
						  :LOGBOOK:
						  CLOCK: [2022-10-17 Mon 01:02:08]--[2022-10-17 Mon 01:18:07] =>  00:15:59
						  :END:
						- ((651aa257-6b75-4549-82e2-69f005661986))
- [[Comments]]
  collapsed:: true
	- #回忆
		- 看了下，现在的效果就已经很不错。 -> ((651afa36-6f32-44c3-a37e-a992c5159c4b))
	- #[[回忆]]
		- 想起昨天还是前天和 [[王露]]说：人要清楚的知道，做什么事是可以让自己 [[状态]] 到 [[八分]] 的。 [[投入]] 去做，设定一个 [[时间单元]] ，带着8分的状态迅速 [[切换]] 到 [[主线任务]] ，这样即使主线的状态只有3-4，也可以被带到5-6，每次 [[攻克]] 一点点，因此即使你主线你并[[不擅长]] ，并 [[不喜欢]] ，特别 [[困难]] ，也可以通过这个方法让自己 [[保持好状态]] 。 [[退一万步]] 讲，你知道你 [[最好的状态]] 在哪里，并且 [[心向往之]] ，那就好好利用这一点 #状态切换 -> ((634bd106-fe4c-498a-9c00-b764ed749d28)) -> ((634ba7dc-6a88-4ae4-aec5-ef67166ebbbd))
		  id:: 6368aff4-b518-4490-af00-e297338f461e