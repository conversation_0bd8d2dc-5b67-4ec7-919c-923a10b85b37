# 00:15 - 01:19（1小时4分）
collapsed:: true
	- {{embed ((634d6701-69a8-4b2b-a8cb-474a0273718e))}}
	- ((634d2f52-02d4-4d65-8f04-ae3db6955be7))
		- #回忆
			- 搞不定，搞不定
- # 02:21 - 11:29（9小时8分）
  collapsed:: true
	- #bed
		- [[闪念]]
			- 在无限中看见有限。
	- #log
		- 09:28 - 09:36
		  collapsed:: true
			- #回忆
				- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 睡了7个小时，妈的手表数据怎么只有5小时， [[深度睡眠时长]]才1小时，但4点20到5点06 [[深度睡眠]]连续了46分钟
		- 09:36 - 09:39
		  collapsed:: true
			- DONE 参与 [老妈]([[丁满冬]]) 和 [[王露]] 的[[家庭会议]]
			  id:: 634e0e53-7969-438e-9895-abfc1c8b416e
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 09:36:30]
			  CLOCK: [2022-10-18 Tue 09:37:12]--[2022-10-18 Tue 10:30:52] =>  00:53:40
			  :END:
				-
					- [[回忆]]
					  collapsed:: true
						- 连续两天睡醒自己下床走出来找人。这几天感觉长大了！ #突然间长大了 #毛懋 #成长
		- 09:39 - 09:44
		  collapsed:: true
			- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 09:40:40]--[2022-10-18 Tue 09:44:40] =>  00:04:00
			  :END:
		- 09:44 - 09:49
		  collapsed:: true
			- [[回忆]]
			  collapsed:: true
				- 停水了
				  collapsed:: true
					- DONE [[美团]]买桶装水
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 09:45:29]
					  CLOCK: [2022-10-18 Tue 09:46:44]--[2022-10-18 Tue 09:48:48] =>  00:02:04
					  :END:
		- 09:49 - 10:02
		  collapsed:: true
			- ((651aa253-ccfc-43d3-87d3-e27b046b3ceb))
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 09:49:47]
			  CLOCK: [2022-10-18 Tue 09:50:03]--[2022-10-18 Tue 10:02:05] =>  00:12:02
			  :END:
		- 09:50 - 09:58
		  collapsed:: true
			- DONE [workflow]([[workflow]])
			  collapsed:: true
				- CANCELED 减少 [常用功能标签]([[特殊标签]])
				  id:: 634f70bc-8ca5-46e6-8183-d1ed5b97de17
				- CANCELED 思考 [[时间记录]]进阶用法
				  id:: 63523a62-fcf3-4e9b-9522-c2ce22c0b9c6
					- [[回忆]]
						- 现在[[Andorid]]的用法，比较像 [[Conor White-Sullivan]]
							- 就 [[输入]]的时候无限缩进、聚焦，整理的时候再整理成 [[log]]，这个方法尤其适合移动端。
		- 10:02 - 10:21
		  collapsed:: true
			- DONE 去 [[自习室]] 
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 10:02:41]--[2022-10-18 Tue 10:44:41] =>  00:42:00
			  :END:
				- 10:07 - 10:10
				  collapsed:: true
					- ((651aa256-3898-464e-bd91-6752c7aed81e))
				- 10:10 - 10:21
				  collapsed:: true
					- DONE 听 [[王露]]说昨晚她和 [[毛懋]]都 [[崩溃]]的故事
					  id:: 634e18c0-5b2e-46fc-abf9-b03cabafe044
		- 10:21 - 10:40
		  collapsed:: true
			- ((634e18c0-5b2e-46fc-abf9-b03cabafe044))
			  collapsed:: true
				- #深思
					- [[王露]]说昨天 [[毛懋]] 崩溃了两次，一次 [[喝牛奶]] ，被奶奶阻止，老婆也跟着阻止，儿子打老婆，老婆发火了，儿子感到震惊，自己一个人默默的去玩玩具了。二次是接着去洗澡，儿子要带一个铁皮 [[挖掘机]]，老婆不让，儿子再次崩溃，哈哈哈。 #想对老婆说的话
					  id:: 634f70bc-e198-4058-a005-62b8ad89c6c5
						- 好的效果是，晚上睡觉特别老实。
							- 老婆也很 [[崩溃]]，被 [老妈]([[丁满冬]])像监考老师一样，监视她有没有睡前给儿子喝牛奶。
								- 早上醒来就听到她和妈妈在聊天 ((634e0e53-7969-438e-9895-abfc1c8b416e))
									- 我觉得 [[王露]] 无论是对 [[毛懋]] 还是 [老妈]([[丁满冬]]) ，都做的很好。最起码，面对 [[冲突]] [[矛盾]]都是正面面对，正面解决。至于解决的艺术、方式、方法，巧妙性 … 用老婆的原话说：
										- 你能自如的应对外部矛盾，自如的应答、应对、交流，前提是你有一颗完整的内心。
											- 我们都在构建自己的内心秩序中，都在建立更高的 [[自我认可]]之中，还强求什么呢？
												- 任何的 [[交流沟通]] [[情绪沟通]] 对目前的我们来说，都是强求，都是不合理的要求。我们能正面面对，已经很不错了。
													- 想起了 [[王志安]]，说他50岁才是做 [[新闻]]最好的年纪，因为这个年纪下，自己信的，不会再改变，有自己清晰的 [[判断逻辑]] 和 [[表达逻辑]] 。
														- 又想起 [[王露]]说 [[小阿舅]]，他们这些 [[官场]]的人早早的就搞懂了生存法则，把自己的内心秩序封闭起来，任凭外界玲珑八面，人说人话，鬼说鬼话，不同的领导说不同的话，而这些都无法触及、动摇早已经坚固封锁的内心。 —— 我们还做不到这样哦。
														  id:: 634e63a2-53cf-4cbc-9121-a24ae070dfb5
		- 10:40 - 11:27
			- #break
			- 10:48 - 11:12
				- #闪念
				- DONE 研究下 [[Morning-Page]]
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 10:50:44]
				  CLOCK: [2022-10-18 Tue 10:50:46]--[2022-10-18 Tue 11:02:33] =>  00:11:47
				  :END:
					- #肯定
						- 来自 [[Conor White-Sullivan]] - moring
							- [[Morning-Page]] [[自动写作]]就是与你的个人恶魔交流。
					- #回忆
						- 没有头绪。
					- 11:02 - 11:12
						- #深思
							- 暂时按这个规则来：
								- 保持大的 [[时间单元]] 不变。
									- 分记录和整理两种情况
										- 记录：无限向下打时间戳
										- 整理：回到 log 形式
			- 11:12 - 11:14
				- :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 10:51:34]--[2022-10-18 Tue 10:51:45] =>  00:00:11
				  CLOCK: [2022-10-18 Tue 11:10:04]--[2022-10-18 Tue 11:14:50] =>  00:04:46
				  :END:
			- 11:14 - 11:27
				- {{embed ((632ada1d-7cfc-450c-ad9f-ce0c679b6ece))}}
				- 11:23 - 11:27
					- #break
						- 抽烟
- # 11:32 - 11:39（7分钟）
  collapsed:: true
	- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a)) 
	  :LOGBOOK:
	  CLOCK: [2022-10-18 Tue 11:32:48]--[2022-10-18 Tue 18:38:59] =>  07:06:11
	  :END:
	  ```calc
	  40+11+11
	  ```
	- ((651aa256-9459-42af-8400-536aadc21970))
	  :LOGBOOK:
	  CLOCK: [2022-10-18 Tue 11:34:01]--[2022-10-18 Tue 11:39:39] =>  00:05:38
	  :END:
	- :LOGBOOK:
	  CLOCK: [2022-10-18 Tue 11:32:59]--[2022-10-18 Tue 18:39:01] =>  07:06:02
	  :END:
		- ((6306282f-6ee9-447f-abda-1b78da19648b))
	- #log
		- 11:39
			- #break
- # 12:06 - 18:18（6小时12分）
  collapsed:: true
	- 午饭
	  :LOGBOOK:
	  CLOCK: [2022-10-18 Tue 12:22:25]--[2022-10-18 Tue 12:46:10] =>  00:23:45
	  :END:
	- #log
		- 12:46 - 14:45
		  collapsed:: true
			- DONE [[黄埔公园]] [[散步]]
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 12:48:17]--[2022-10-18 Tue 14:49:03] =>  02:00:46
			  :END:
				- 13:16 - 13:51
					- DONE [[阿叔猪扒包]]
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 13:23:24]--[2022-10-18 Tue 13:51:53] =>  00:28:29
					  :END:
						- #[[回忆]]
							- TODO 是时候给[儿子]([[毛懋]])儿子练习 [[延迟满足]]了
							  id:: 634f70bc-eb9f-4421-a7c3-5a1d0dcb0114
							- DONE 和 [[王露]] 聊 [老妈]([[丁满冬]]) 的 [[矛盾]] 与 [[自我]] 的关系
							  id:: 634e4c0b-3995-4fdb-884c-668863f6d2d2
							  :LOGBOOK:
							  CLOCK: [2022-10-18 Tue 16:58:32]--[2022-10-18 Tue 17:32:29] =>  00:33:57
							  :END:
				- 13:51 - 13:54
					- DONE [[核酸]]
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 13:52:17]
					  CLOCK: [2022-10-18 Tue 13:52:34]--[2022-10-18 Tue 13:54:41] =>  00:02:07
					  :END:
						- #回忆
							- DONE 和 [[王露]] 聊 处理 [[想法]] 的 [[workflow]]
							  id:: 634e63a2-c261-4031-8631-35a5c2fbb65b
							  :LOGBOOK:
							  CLOCK: [2022-10-18 Tue 17:35:07]--[2022-10-18 Tue 18:18:02] =>  00:42:55
							  :END:
		- 14:45 - 16:22
		  collapsed:: true
			- DONE [workflow]([[workflow]]) & [[break]] #logseq-css
			  id:: 635e9c1d-0a54-4bfa-90df-4fb17edef06e
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 14:53:53]--[2022-10-18 Tue 16:15:00] =>  01:21:07
			  :END:
				- #Logseq
					- DONE 移动端首页底部栏目名称和日期样式与“[[page]]”样式重叠了，尝试修改下。
					  id:: 635e9c1d-8abd-4999-9e77-fa68e82aef3b
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 15:02:28]
					  CLOCK: [2022-10-18 Tue 15:02:33]
					  CLOCK: [2022-10-18 Tue 15:02:38]--[2022-10-18 Tue 15:32:57] =>  00:30:19
					  :END:
					- DONE 调整 `[workflow]([[工作流]])` `[[namespace]]`
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 15:33:07]
					  CLOCK: [2022-10-18 Tue 15:33:14]
					  :END:
						- #深思
							- 工作流的部分标签不要直接用标签，而是用[[namespace]]方便查找。
								- `[[回收♻️]]` 是个例外
								- `[[Favorites/habit/卡片回顾60分钟：打标签、嵌套关系（上下文）、时间戳、功能标签]]` -> `[[Favorites/workflow/回顾]]`
								- `[[Favorites/workflow/补录]]`
								- `[[Favorites/workflow/重置]]`
								- `[[Favorites/workflow/改进]]`
								- `[[Favorites/workflow/回收♻️]]`
								- #回忆
									- 绕不开整个 journals 不能存在fav类的[[namespace]]标签，不方便使用。
					- DONE 新增 `[[改进]]` -> `[[Favorites/workflow/回顾/改进]]`
					  id:: 634e63a2-b3e8-42aa-8e44-8da977a33379
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 16:09:47]
					  CLOCK: [2022-10-18 Tue 16:09:50]--[2022-10-18 Tue 16:12:02] =>  00:02:12
					  :END:
					- DONE 进一步缩短左侧栏目宽度
					  id:: 635e9c1d-d83e-4263-a0d3-2e95488449e1
		- 16:22 - 16:58
		  collapsed:: true
			- DONE [workflow]([[workflow]])
				- CANCELED 添加 `[[黄埔公园]] [[阿叔猪扒包]]` -> `[[Favorites/state/place]]` #namespace  #recycle
				  id:: 635eb4cb-5401-474d-9638-58e06b8db78f
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 16:22:53]
				  CLOCK: [2022-10-18 Tue 16:22:56]--[2022-10-18 Tue 16:33:14] =>  00:10:18
				  :END:
					- `[[Favorites/state/place/黄埔公园]]`
					- `[[Favorites/state/place/阿叔猪扒包]]`
				- DONE [[re-index]] [[Andorid]] [[logseq]]
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 16:32:51]
				  CLOCK: [2022-10-18 Tue 16:33:07]--[2022-10-18 Tue 16:45:27] =>  00:12:20
				  :END:
				- DONE 把小尖头的透明度再调低一点 #logseq-css
				  id:: 635e9c1d-d445-419a-a604-be1707c28a63
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 16:45:30]
				  CLOCK: [2022-10-18 Tue 16:45:35]--[2022-10-18 Tue 16:58:14] =>  00:12:39
				  :END:
		- 16:58 - 17:32
		  collapsed:: true
			- ((634e4c0b-3995-4fdb-884c-668863f6d2d2))
			  id:: 634e63a2-d2d3-4855-b26d-05c5e645b236
				- #回忆
					- 看懂 [老妈]([[丁满冬]])的 [[处事方式]] ，就知道怎么更好的相处。
						- 我是硬的； [[王露]] 是软的， [[软妹子]]一枚，那么 [老妈]([[丁满冬]])就是强硬的 [[规矩]]派。
							- 在老妈用一生去可以训练这种处事方式，重复又重复，所以，她对万事万物的理解，都基于她固定的处事方式。
								- 这是她最自然、最舒服的。即便她知道这个世界还有多种和她不一样的理解方式，她也认同各人有各人的风格，但一旦问题触及到她个人或她在乎的对象，她会立刻回到自己的处事方式来理解问题，例如：老婆的软碰上孙子的教育，老婆的软碰上 [[家庭关系]] 的相处。
									- 得出的答案也是充满偏见：老婆的软是错的。孩子的教育方式她不认可。
						- 所以知道这一点，就明白，要在孩子教育、家庭关系的处理能力上，获得老妈的认可，除非你和她是同一个处事方式，否则，就是自寻死路，找死，自讨没趣，你永远无法获得 [老妈]([[丁满冬]])的真心认可。
							- 在她的眼里，用她的标准衡量，你所有“做的不对”的地方都被放大，被记住，在她心里反复摩擦，如鲠在喉，叠加过去的种种“不对”，一次又一次的确定，你的处事方式不对，要改。
								- 而做的好地方，她都看不到，或觉得这很正常。
									- 想想都觉得 [[委屈]] 。但这就是 [[常识]] ，一个老人只会用她自己 [[顽固不变]] 的 [[处事方式]] 去 [[理解事物]] 。
						- 所以*2，不要去追求别人的认可，自己认可自己就好。
							- 老妈是一个100级的战士，而你只是刚出新手村的法师，那又如何？
								- 战士无法指导法师如何升级，你只有走你自己的路。
									- 而眼下的环境，只不过你身处在100级的战士所呆的环境，你不适应，害怕而已。这不是自我。这是对环境的恐惧。
										- 你要做的就是，看见自己，看清自己，接纳自己，认可自己。至始至终，只有一件事，和自己更好的相处。
											- 你有你自己的道路。任何人无法给你提供建议和帮助，你也无须掉进任何人的升级路径里。
												- 你只是和他们共处一室罢了。
													- 捡捡果子，蹭蹭经验，你要相信，恐惧不会让你更好，也不是真的活不下去。总有100级的战士愿意去哪都带着你（比如我），也总有100级的战士嫌弃你，觉得你不应该出现在这里。
														- 无须理会，你死不掉，只要你不把外界的干扰当回事。
															- 你的世界，其实只有你自己。
																- 你所有的行为，都是自我之下产生的，只是很多时候，因为没有看清自己，产生了很多的精神上的冲突罢了。
																	- 你需要练习的就是多多看见自己，多多看到念头，多多的转念一想。
																		- 转念一想，其实就是正念时刻，就是身心合一的时刻。抓住这种timeing，和自己和解、相处、接纳、认可。
																			- 所以昨晚上儿子喝牛奶的冲突。无论前因是什么，是什么促使你产生了冲突后的行为，“行为”是不会改变的，你还是会耐下心好好的安慰儿子，会和他交心，会和他讲道理，纵使你心里其实对那个“因”很不舒服，但你还是按自我的方式去和儿子沟通。
																				- 但转念一想，这何尝不是件好事呢？
																					- 你做到了驾驭自己的情绪。
																						- 你有新的机会教育儿子延迟满足。
																							- 你有新的机会和儿子走心的交流
																								- 你还升级了自己的处事方式 -> 软中带硬。
																	- 所以行为是自我的，精神没有在自我之中而已。只要转念一想，就能身心合一。
																		- 你已经做的很好了，因为你就是用自我在行动啊！
																			- 谁的自我不是[[完美]]的呢？
																			  id:: 63823eb3-09c6-42eb-b0f4-e5bff8e96cdd
																				- 当然自我是可以升级的，但妈妈的100级用了一生来升级呀！慌啥！接纳自己！你就是100分的你！
						- 面对事物的所有的处理方式，都没有对错好坏，只有适不适合自己，自我能不能接受。
							- 但只有一条是有对错好坏的，就是必须让自己丢进积极的一面，要知道自己8分的状态怎样才能触发，怎样才能保持。自己活在自己的世界里，状态为什么不能调整到8分？换而言之，在负面状态之下，人只会被带进情绪黑洞，活在虚无的世界里，这样不好。
						- 所以想起 [[李嘉诚]] 的 [[名言]] ： [[建立自我，追求无我]] ，真的是非常高级的理念和使命。
						  id:: 634f70bc-0e62-4fbf-8709-d18c3ca7fb52
							- 自我可能要用一生的时间寻找和建立，建立之后，面对这个世界，就是无我了，因为任何的冲突矛盾都影响不了你。
								- #link
									- ((634e63a2-53cf-4cbc-9121-a24ae070dfb5))
						- 再分享两点：
							- 感到 [[恐惧]] [[害怕]] [[焦虑]] [[悲伤]] [[委屈]] 时：你要知道，你只是被一个100级的男人带到了100级的生存环境而已。但你死不了，这个环境就是个游戏，不会让你死，最多让你“感觉”到窒息。
								- 而且，你的精神世界，你的行为模式，是干净的，里面只有你一人，要相信这一点。
								  id:: 634f70bc-afeb-43a8-b7bc-ceffed18bf84
									- 这是你的 [[宝藏]] 啊，宝贝，多摸摸它。
									  id:: 634f70bc-0456-45ca-b4b2-d12438d73a17
							- 第二点，你所有的行为思考带来经验（包括蹭来的），都是为自己升下一级做准备而已。眼下的你，就是这个级别的满分。这是游戏的设定，也是真实世界的设定。接纳自己本来也就是不需要花费精力的，是常识而已。你的世界里只有你一人，哪来的不[[完美]]呢？只是很多人混淆了自己的精神世界与真实世界的关系，二者是独立的，有联通，但平行。
							  id:: 634f70bc-6965-490c-af0f-edc11833cb6e
								- 所以你在新手村攒的经验，肯定没有100级环境里攒的经验快，认同这一点，也会让自己开心很多，开心最重要，开心能打开你进去自己的精神世界的大门，去看见自己，活在自己最好的状态里。
			-
			-
		- 17:34 - 18:18
		  collapsed:: true
			- ((634e63a2-c261-4031-8631-35a5c2fbb65b))
				- #回忆
					- 说起儿子晚上不肯睡觉， [[王露]]说是因为儿子的睡前 [[todo-list]] 太多了，而且越来越多：买了新的玩具了，开发了新的玩法了，学到了新的东西了，他每天睡前都要把这些都做一遍、摸一遍 #想对老婆说的话
						- #肯定
							- 怎么办？这里把话题切到了 [[任务管理]] -> ((634e63a2-f58f-4394-a806-d3c2cd291fd7))
								- #深思
									- 有必要提醒老婆，养成记录、思考、规划、分解执行任务的习惯。
										- 比如当我想到这个问题的时候，它就是个任务，然后我就会记录它，分解他，当我在再一次想到的时候，打开它，思考，因此想到了用新的习惯替换目前儿子的睡前行为，那就是准备口述 [[睡前故事]]，这需要准备和功力，用 [[毛懋]] 认知里的事物编成可以讲到他入睡的故事。这比去修正儿子的睡前 [[todo-list]]要好，用有趣的 [[睡前故事]] 让儿子彻底忘记睡前 [[todo-list]]这件事 —— 培养了一个新的习惯替换了旧的习惯，还可以加上仪式，讲故事前把玩具放在床头码齐，讲的要睡着了，又惦记，就跟这些玩具说再见～～～
											- 但我要说的是， [老婆]([[王露]])你自己要能 [[触发]] “管理、解决问题”的 [[系统]] 。而不是由我来触发，你每次碰到解决不了的问题，就会放一边，放在脑子里。不是有没有解决的能力，而是没有“管理”问题的习惯。
												- 本质上，这是管理“想法”的习惯，和logseq的使用一脉相承，软件是工具，本质是让自己养成管理想法的习惯。
													- 脑子里产生的想法，叫灵感，它不具体。想法多了，占用大脑内存，就会导致脑子转不动。最痛苦的事莫过于回忆某个过去的某一个想法。
														- 所以我们要清理“想法”，怎么清理？把它变具体。
															- 想法只可能有两个路径，要么变成具体的思考，要么变成任务。
																- 思考就是：深思、回忆、肯定、质疑这些，总之，你的脑子思考了一遍这个想法，并把它记录下来了，这就从你大脑里摘出来了。
																- 任务就是：起初是个主题式的人物，它需要被分解，不必刻意去做，先记录主题，打上todo，想到的时候，思考下怎么分解，这就是做规划。
																	- 规划了也不一定要立即去做，继续放着，下次想到了再规划的更具体，一直到每一个步骤，都清晰可以做，设定个时间去完成它，或就让它放着。
																		- 所以本质上，任务也就是思考的一种形式，一个带有todo标签的思考而已。
											- 养成这种习惯的好处是：
												- 不困于想法，苦于执行（思想的巨人，行动的矮子）
												- 大脑轻松，越做越轻松，直到今天的想法，今天处理了，非常之当下
												- 整个人变的更加自信和高执行，想到就去做，因为你养成了这个习惯，就拥有了解决问题的元能力（分解问题），未来考虑任何问题，就不会任由它悬而未决，而是面对它、规划它、分解它、执行它。
											- 还有一点是：面对任务，就不再是 [[todo-list]]，它只是想法的一种，你会轻松很多，焦点不是把任务做了，而是把问题想清楚，回到思考层面，大脑越转越灵光。
				- 17:35 - 17:50
				  collapsed:: true
					- #break
				- 17:50 - 17:53
				  collapsed:: true
					- #continue
						- {{embed ((634e63a2-d2d3-4855-b26d-05c5e645b236))}}
		-
- # 18:18 - 18:38（20分）
  collapsed:: true
	- {{embed ((634e63a7-6690-473a-b7f2-3be752e38d5a))}}
	- {{embed ((632ada1d-7cfc-450c-ad9f-ce0c679b6ece))}}
- # 20:07 - 23:46（3小时39分）
  collapsed:: true
	- DONE [[日常英语]]
	  collapsed:: true
	  :LOGBOOK:
	  CLOCK: [2022-10-18 Tue 20:08:09]--[2022-10-18 Tue 20:17:30] =>  00:09:21
	  :END:
		- [[山洞]] #card
			- cave
		- [[完美]] #card
			- perfect
		- [[特别的]] #card
			- special
		- [[兄弟姐妹]] #card
			- sibiling
		- [[分支]] #card
			- branch
		- [[先决条件]] #card
			- prerequisite
		- [[偏僻的]]
			- remote
		- [[放弃]] #card
			- discard
		- [[齿轮]] #card
			- gear
	- #log
		- 20:11 - 20:14
		  collapsed:: true
			- 被 [儿子]([[毛懋]]) cue
		- 20:17 - 20:24
		  collapsed:: true
			- #深思
			  collapsed:: true
				- CANCELED 我要不要整理 [workflow]([[workflow]])？
				  id:: 634f70bc-160e-442c-9b6d-b6161997875e
				  collapsed:: true
					- #深思
						- [workflow]([[workflow]]) 有点像 [[选题]] ，创建时用到，后面的 [[page-embed]] 都不需要
						  id:: 634f70bc-162d-47ee-a210-827c809daa95
							- 因此，embed前面只需要时间戳就可以
								- 20:20 - 20:23
									- CANCELED [workflow]([[workflow]]) -> 去掉 [[page-embed]]前面出时间戳以外的缩进。
									  :LOGBOOK:
									  CLOCK: [2022-10-18 Tue 20:20:18]--[2022-10-18 Tue 20:21:39] =>  00:01:21
									  :END:
										- #回忆
											- 并没有workflow下的embed，看来我还是很自然的。
												- 看了一圈，其它也没有必要去改。
													- 因为 workflow 系列的标签都带有含义，在上下文里多少传递了当初添加时的信息。
		- 20:24 - 20:35
		  collapsed:: true
			- DONE 和 [[王露]] 下楼 [[扔垃圾]]
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 20:24:17]
			  CLOCK: [2022-10-18 Tue 20:24:19]--[2022-10-18 Tue 20:34:57] =>  00:10:38
			  :END:
			- 20:24 - 20:26
			  collapsed:: true
				- #回忆
					- [[今日份快乐]]：路上，再次被王露 [[肯定]]，说今天的这番话，有 [[被接纳]] 的感觉，因为被自己最亲密的关系接纳，自己也进而 [[接纳]]了自己。
					  id:: 634f70bc-1295-44d6-a2bd-896cf2b6246d
						- #link
							- ((634f70bc-e198-4058-a005-62b8ad89c6c5))
			- 20:26
			  collapsed:: true
				- DONE [workflow]([[workflow]]) -> [[logseq-css]] ：在 [[CANCELED]]  前面加 ♻️
				  id:: 634e9b91-3a55-478a-8f14-71dbd9b3508f
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 20:36:49]--[2022-10-18 Tue 21:46:38] =>  01:09:49
				  :END:
		- 20:35 - 21:46
			- ((634e9b91-3a55-478a-8f14-71dbd9b3508f))
			  id:: 634e9da8-6aca-4ab8-bf19-63e73ffbe4b3
			- 20:40 - 21:29
				- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
		- 21:46 - 22:23
			- DONE [workflow]([[workflow]]) ：去掉 [[CANCELED]]  前面的 [[recycle]]
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 21:47:43]--[2022-10-18 Tue 22:22:58] =>  00:35:15
			  :END:
				- #link
				  collapsed:: true
					- ((634e9b91-3a55-478a-8f14-71dbd9b3508f))
			- 22:17 - 22:20
				- #continue
					- collapsed:: true
						- #回忆
						  collapsed:: true
							- 昨晚在 [[知乎]] 上找到了。
							  link:: [💅 Logseq自助修改 —— CSS指导手册 - Ws在知乎的文章 - 知乎](https://zhuanlan.zhihu.com/p/548640615)
		- 22:23 - 22:27
			- DONE [[日常英语]]
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 22:23:45]--[2022-10-18 Tue 22:25:06] =>  00:01:21
			  :END:
				- 蚊子 #card
				  collapsed:: true
					- mosquito
				- 苍蝇 #card
				  collapsed:: true
					- fly
				- 壳 #card
				  collapsed:: true
				  card-last-interval:: 0.04
				  card-repeats:: 1
				  card-ease-factor:: 2.6
				  card-next-schedule:: 2022-11-18T04:20:28.698Z
				  card-last-reviewed:: 2022-11-18T04:20:28.699Z
				  card-last-score:: 5
					- shell
			- CANCELED [workflow]([[workflow]])： [[日常英语]] -> [[日常英语]] #namespace  #recycle
			  id:: 635eb4ca-6655-42bf-a0eb-73f663f69b78
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 22:25:19]
			  CLOCK: [2022-10-18 Tue 22:25:30]--[2022-10-18 Tue 22:26:40] =>  00:01:10
			  :END:
			- CANCELED [workflow]([[workflow]])： [[habit]] -> [[habit]] #namespace  #recycle
			  id:: 635eb4ca-5a4d-4706-932f-6ca0db05e196
			  :LOGBOOK:
			  CLOCK: [2022-10-18 Tue 22:26:38]
			  CLOCK: [2022-10-18 Tue 22:26:46]--[2022-10-18 Tue 22:27:37] =>  00:00:51
			  :END:
		- 22:27 - 23:05
			- #review
				- DONE ` [[改进]] `
				  id:: 634f70bc-2036-4418-9044-0fa3221412bb
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 22:28:03]
				  CLOCK: [2022-10-18 Tue 22:28:08]--[2022-10-18 Tue 23:02:12] =>  00:34:04
				  :END:
					- DONE 取消 并删除页面
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 22:47:09]
					  CLOCK: [2022-10-18 Tue 22:47:12]--[2022-10-18 Tue 22:47:43] =>  00:00:31
					  :END:
						- {{embed ((634e63a2-b3e8-42aa-8e44-8da977a33379))}}
			- 22:56 - 23:02
			  collapsed:: true
				- ((634e63a6-ceb6-426b-a884-bdb01d518e4c))
				  collapsed:: true
					- #闪念
					  collapsed:: true
						- 好像这个 [[习惯]]又没啥用，因为这几天的状态就是各种回顾卡片（包括工作流），所以检视 [[habit]] 的执行状态，特别少的习惯有两种可能：执行不到位；或者执行的太到位，以至于不需要标记它。
		- 23:07 - 23:27
			- #闪念
				- CANCELED [[data]] `[[Favorites/future-you]]`
				  id:: 635123cb-6079-4cf3-aab4-36a872807aa9
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 23:07:51]
				  CLOCK: [2022-10-18 Tue 23:08:04]--[2022-10-18 Tue 23:27:14] =>  00:19:10
				  :END:
					- DONE `Favorites/data/余额💰️` -> [[余额]] #pass
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 23:10:19]
					  CLOCK: [2022-10-18 Tue 23:10:20]--[2022-10-18 Tue 23:13:00] =>  00:02:40
					  :END:
					- DONE `Favorites/data/模版` -> [[模板]]
					  :LOGBOOK:
					  CLOCK: [2022-10-18 Tue 23:12:42]
					  :END:
		- 23:28 - 23:31
			- ((634ec0d6-ef1f-4f59-82a7-c5dfa3a7a0e5))
		- 23:31 - 23:33
			- #break
		- 23:33 - 23:40
			- DONE [[continue]]
			  collapsed:: true
				- {{embed ((634e63a7-6690-473a-b7f2-3be752e38d5a))}}
		- 23:41 - 23:46
		  collapsed:: true
			- {{embed ((634ec0d6-3ac8-4cf7-837e-4f648d0f3d2d))}}
- # 23:50 - 00:14（24分）
  collapsed:: true
	- #闪念
		- CANCELED [[habit]] [[status]] #recycle -> 
		  id:: 634f70bc-3741-40d5-9c2e-67474901f3c2
	- #log
		- 23:54 - 00:06
			- #闪念
				- DONE 改下 tabs [[logseq-css]]
				  id:: 635d3f85-44ce-4d13-bc05-799e26b56e30
				  :LOGBOOK:
				  CLOCK: [2022-10-18 Tue 23:54:06]--[2022-10-19 Wed 00:06:47] =>  00:12:41
				  :END:
		- 00:06 - 00:13
			- #摘录
				- DONE 查找 [[失效]] 块引用 #无效 #失效 #命令 #queries #block-ref #[[block-embed]] #queries
				  SCHEDULED: <2022-10-31 Mon ++1w>
				  id:: 634ecb69-37a3-4b2b-8fb7-c3e98d323a28
				  collapsed:: true
				  :LOGBOOK:
				  CLOCK: [2022-10-19 Wed 00:07:44]--[2022-10-19 Wed 00:13:22] =>  00:05:38
				  :END:
					- DONE [[Discord]] 上给的查询命令
					  id:: 6357e8db-753a-45c7-8b55-c1d3a3ab8966
					  :LOGBOOK:
					  CLOCK: [2022-10-19 Wed 00:08:54]
					  CLOCK: [2022-10-19 Wed 00:08:56]
					  CLOCK: [2022-10-19 Wed 00:09:01]--[2022-10-19 Wed 00:12:54] =>  00:03:53
					  :END:
						- id:: 63f30ad9-50df-4f2d-bc1f-4129c5f215bb
						  ``` clojure
						  #+BEGIN_QUERY 
						    {:title [:b "Broken References"]
						     :query [:find (pull ?b [*])
						             :in $ ?matcher
						             :where
						             [(re-pattern ?matcher) ?regex]
						             [?b :block/content ?c]
						             [(re-find ?regex ?c)]
						             [(missing? $ ?b :block/refs)]]
						     :inputs [ "\\([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\)"]
						     }
						  #+END_QUERY
						  ```
					- DONE 另一位大佬给的代码
					  id:: 63f30ad9-1b83-4c5f-b510-517e50294734
					  :LOGBOOK:
					  CLOCK: [2022-10-25 Tue 21:01:53]
					  CLOCK: [2022-10-25 Tue 21:01:57]--[2022-10-25 Tue 21:09:15] =>  00:07:18
					  :END:
						- id:: 63f30ad9-574a-41be-9343-be38dedd4c8d
						  ``` clojure
						  **Broken References**
						  #+BEGIN_QUERY 
						    {:title [:h2 "Broken References"]
						     :query [:find (pull ?b [*])
						             :in $ ?matcher
						             :where
						             [?b :block/content ?c]
						             [(re-pattern ?matcher) ?regex]
						             [(re-find ?regex ?c)]
						             [?b :block/refs ?ref]
						             [(missing? $ ?ref :block/content)]
						             [(missing? $ ?ref :block/name)]
						             ]
						     :inputs [ "\\(\\([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\)\\)"]
						     }
						  #+END_QUERY
						  ```
		- 00:14
			- #break
- [[Comments]]
  collapsed:: true
	- #回忆
		- 居然没有失效的？真的假的。 -> ((6357e8db-753a-45c7-8b55-c1d3a3ab8966))
		  id:: 634f637b-6ff2-4016-955c-f0fb86794f20
			- 假的
			  id:: 6357e8db-4e15-4c02-9b8b-ff7319536892
				- {{embed ((634ecaa8-eece-4e76-85cc-03c4b4f54c9c))}}
				-