# 00:16 - 00:58（42分）
collapsed:: true
	- CANCELED [workflow]([[workflow]])： `[[图]]` 、 `[[zettel]]` #namespace  #recycle
	  id:: 635015ed-06db-4bce-819b-98d588b1d630
	  collapsed:: true
	  :LOGBOOK:
	  CLOCK: [2022-10-19 Wed 00:17:15]
	  CLOCK: [2022-10-19 Wed 00:17:19]--[2022-10-19 Wed 00:57:42] =>  00:40:23
	  :END:
		- #step
			- CANCELED `[[图]]` -> `[[图]]` #namespace
			  id:: 635eb4ca-36a6-4e80-b808-fa46c87e8a42
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 00:20:48]--[2022-10-19 Wed 00:32:35] =>  00:11:47
			  :END:
			- CANCELED alias `[[图]]` 下面的标签 #namespace
			  id:: 635eb4ca-387e-416e-9453-8b5a7633810a
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 00:20:24]--[2022-10-19 Wed 00:32:36] =>  00:12:12
			  :END:
			- CANCELED `[[zettel]]` -> [[zettel]] #namespace
			  id:: 635eb4ca-36b8-4d89-9c8f-ab1a55b134b9
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 00:32:47]
			  CLOCK: [2022-10-19 Wed 00:32:53]--[2022-10-19 Wed 00:34:13] =>  00:01:20
			  :END:
			- CANCELED [[闪念]] [[摘录]] [[回忆]] [[肯定]] [[质疑]] [[深思]] -> #namespace  #recycle
			  id:: 635eb4ca-e6b9-42d3-b59e-794516043809
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 00:34:59]--[2022-10-19 Wed 00:42:42] =>  00:07:43
			  :END:
				- [[深思]]
				- [[质疑]]
				- [[肯定]]
				- [[回忆]]
				- [[摘录]]
				- [[闪念]]
		- #link
			- ((634f70bc-160e-442c-9b6d-b6161997875e))
			  id:: 635015ed-c812-4546-9d0e-bd0285696565
	- #log
		- 00:43 - 00:58
		  collapsed:: true
			- DONE 移除property中的 [[link]]
			  id:: 634edb5d-1f15-49fa-8cd4-73ceb8f9cdb3
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 00:49:25]--[2022-10-19 Wed 00:57:46] =>  00:08:21
			  :END:
				- #回忆
					- 居然没清干净
						- DONE 用了下query查询
						  link:: https://docs.logseq.com/#/page/634674ab-c340-4fba-8888-672a71517f20
			- #闪念
				- CANCELED [[复盘]] -> fav #pass -> ((634f70bc-160e-442c-9b6d-b6161997875e))
				  id:: 635015ed-41da-4cf1-8b19-330d650b9dcd
					- [[选题]]
					- [[结构]]
					- [[草稿]]
					- [[初稿]]
					- #recycle
					- [[定稿]]
					- [[标题]]
					- `[[Favorites/选题🎙]]`
					- `[[Favorites/稿子💰]]`
					- [[策划]]
					- [[大纲]]
			- 00:49 - 00:53
				- {{embed ((634ecb69-37a3-4b2b-8fb7-c3e98d323a28))}}
- # 01:01 - 02:32（1小时31分）
  collapsed:: true
	- {{embed ((634edb5d-1f15-49fa-8cd4-73ceb8f9cdb3))}}
	- #log
		- 01:06 - 02:03
		  collapsed:: true
			- ((651aa257-c7fc-43ca-8e45-b129364eabf0))
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 01:07:18]--[2022-10-19 Wed 02:03:11] =>  00:55:53
			  :END:
			- ((651ad660-4be8-4004-b82a-9ca1f84cea3a))
				- [[照片]][[parking lot]] [[ladder fire truck]]
					- ![2022-10-19-02-09-24.jpeg](../assets/2022-10-19-02-09-24.jpeg){:width 245}
			- 01:11 - 01:14
				- [:div.color-level {:style {:padding-left 5}} [:h2 "Logseq Graph Stats 📊"][:ul [:li "pages: 5,303"]][:h3 "Text"][:ul [:li "Blocks: 22,640"][:li "Words: 443,474"][:li "Characters: 221,502"][:li "Emoji: 2,391"]][:h3 "Code"][:ul [:li "Codeblocks: 87"]][:h3 "References"][:ul [:li "Interconnections (refs): 5,671"][:li "Block References: 970"][:li "Orphans: 235"][:li "External links: 133"]][:h3 "Task management"][:ul [:li "Tasks: 789"][:li "Finished tasks (DONE): 1,161"]][:h3 "Queries"][:ul [:li "Number of simple queries: 0"][:li "Number of advanced queries: 1"]][:h3 "Media"][:ul [:li "Videos: 0"][:li "Assets: 59"]]]
			- 01:39 - 01:41
				- #回忆
					- [老婆]([[王露]])我是真心 [[肯定]]你的，如果没有你，我们家现在已经 [[喝西北风]]了。 #想对老婆说的话
		- 02:03 - 02:32
		  collapsed:: true
			- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 02:04:52]--[2022-10-19 Wed 02:33:04] =>  00:28:12
			  :END:
			- ((651aa257-6b75-4549-82e2-69f005661986))
			- 02:12 - 02:13
				- DONE [[日常英语]]
					- 云梯消防车 #card
						- [[ladder fire truck]]
- # 08:47 - 10:04（1小时17分）
  collapsed:: true
	- 醒了
	- #log
		- 09:08 - 09:10
			- [[回忆]]
				- [儿子]([[毛懋]]) 扇了[老婆]([[王露]])三个 [[巴掌]] ，我帮妈妈打回去，大哭，不准他抱妈妈，必须要 [[道歉]]
				  id:: 634fff89-aa56-4a72-8c6e-677fd74eb94a
					- DONE 为什么打人？
					  id:: 634f5b0d-774d-443f-8ea6-9d6ba7d4d652
						- [[王露]]说是早上 [[毛懋]] 醒来还想粘一会， [[奶奶]]开门进来了，打断了；然后老婆给他脱了裤子，又不高兴，再穿裤子更不高兴了。
						  id:: 635015ed-131d-44d7-882d-93e9e261aeb8
							- #深思
								- 这也是好事，感觉 [[2-3岁育儿]] 就不能像上一年那样了，要做 [[规矩]] 了。…… 这有点 [[措手不及]]
								  id:: 635015ed-1e23-4bb3-8513-501b261a967a
									- 夸要夸到点上
									- [[孩子]] 的 [[坏习惯]] 要 [[及时应对]] 。对，及时应对，不一定是 [[阻止]] ，但要 [[及时应对]] ，否则很容易养成坏习惯，关键是孩子也不懂什么 [[道理]] ，这就浪费了 [[宝贵]] 的 [[教育机会]] 、 [[教育窗口]] [[教育时机]]。
		- 09:11 - 09:29
			- ((651aa253-ccfc-43d3-87d3-e27b046b3ceb))
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 09:12:20]--[2022-10-19 Wed 09:29:28] =>  00:17:08
			  :END:
			- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
		- 09:29 - 09:45
			- :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 09:30:20]--[2022-10-19 Wed 09:45:19] =>  00:14:59
			  :END:
			- ((634e1b47-d967-484f-9f0d-0e44198478f3))
		- 09:46 - 10:04
			- DONE [[自习室]]
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 09:46:29]--[2022-10-19 Wed 10:04:48] =>  00:18:19
			  :END:
- # 10:09 - 10:31（22分）
  collapsed:: true
	- #闪念
		- DONE [[睡眠时长记录]] 
		  SCHEDULED: <2024-02-03 Sat .+1d>
		  id:: 6353a251-268b-4d21-a10d-e5e664f14e47
		  :LOGBOOK:
		  * State "DONE" from "TODO" [2022-11-02 Wed 11:57]
		  * State "DONE" from "TODO" [2022-11-03 Thu 11:39]
		  * State "DONE" from "TODO" [2022-11-04 Fri 11:44]
		  * State "DONE" from "TODO" [2022-11-08 Tue 11:04]
		  * State "DONE" from "TODO" [2022-11-09 Wed 14:18]
		  * State "DONE" from "TODO" [2022-11-11 Fri 22:51]
		  * State "DONE" from "TODO" [2022-11-12 Sat 11:17]
		  * State "DONE" from "TODO" [2023-10-08 Sun 07:49]
		  * State "DONE" from "TODO" [2023-10-09 Mon 11:48]
		  * State "DONE" from "TODO" [2023-10-10 Tue 19:43]
		  * State "DONE" from "TODO" [2023-10-11 Wed 10:23]
		  * State "DONE" from "TODO" [2023-10-12 Thu 12:49]
		  * State "DONE" from "TODO" [2023-10-13 Fri 20:21]
		  * State "DONE" from "TODO" [2023-10-14 Sat 12:43]
		  * State "DONE" from "TODO" [2023-10-19 Thu 19:45]
		  * State "DONE" from "TODO" [2023-10-22 Sun 13:17]
		  * State "DONE" from "TODO" [2023-10-23 Mon 15:07]
		  * State "DONE" from "TODO" [2023-10-26 Thu 15:06]
		  * State "DONE" from "TODO" [2023-10-29 Sun 19:54]
		  * State "DONE" from "TODO" [2023-10-30 Mon 14:39]
		  * State "DONE" from "TODO" [2023-11-06 Mon 13:50]
		  * State "DONE" from "TODO" [2023-11-07 Tue 09:38]
		  * State "DONE" from "TODO" [2023-11-08 Wed 00:33]
		  * State "DONE" from "TODO" [2023-11-13 Mon 11:55]
		  * State "DONE" from "TODO" [2023-11-22 Wed 11:45]
		  :END:
	- #log
		- 10:11 - 10:15
			- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a)) 
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 10:11:33]
			  :END:
			  ```calc
			  5+18+38+44+5
			  ```
			- ((651aa256-9459-42af-8400-536aadc21970))
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 10:11:47]--[2022-10-19 Wed 10:15:05] =>  00:03:18
			  :END:
		- 10:15 - 10:23
			- :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 10:15:28]--[2022-10-19 Wed 21:53:45] =>  11:38:17
			  :END:
				- ((632ada1d-7cfc-450c-ad9f-ce0c679b6ece))
				- ((634f5b0a-fd4b-4a07-85d2-fce8a1cbc733))
				- ((6306282f-6ee9-447f-abda-1b78da19648b))
		- 10:23 - 10:30
			- #continue
				- {{embed ((634f5b0d-774d-443f-8ea6-9d6ba7d4d652))}}
		- 10:31
			- #break
- # 10:41 - 15:13（4小时32分）
  collapsed:: true
	- DONE 录制视频 -> 证明查询空block的命令无效 
	  query-table:: false
		- #link
			- ((634ecb69-37a3-4b2b-8fb7-c3e98d323a28))
	- #log
		- 10:47 - 11:36
			- DONE [workflow]([[workflow]])：测试 [[logseq]]8.9 有没有修复样式问题。 #logseq-css
			  id:: 635e9c1c-b0e5-43f1-b0ab-f55850b80437
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 10:47:22]
			  CLOCK: [2022-10-19 Wed 10:47:27]--[2022-10-19 Wed 11:36:13] =>  00:48:46
			  :END:
				- DONE 顺便整理下 [[logseq-css]]
				  id:: 635d3f85-9cd4-4603-a71b-62fff5a96fcf
				  :LOGBOOK:
				  CLOCK: [2022-10-19 Wed 10:47:51]
				  CLOCK: [2022-10-19 Wed 10:47:54]--[2022-10-19 Wed 11:36:14] =>  00:48:20
				  :END:
		- 11:40 - 12:01
			- #review
				- {{embed ((634f5b0a-fd4b-4a07-85d2-fce8a1cbc733))}}
		- 12:01 - 15:13
			- #break
				- 午饭
				- DONE 送 [[王露]]去 [[地铁站]]
					- #link
						- ((634f70c1-ea3a-48ab-b473-2eebc474c6ab))
				- DONE 给自己买了杯 [[喜事柠檬]]，坐着放空一会会。
			- 13:29 - 13:48
				- DONE [[continue]]
				  :LOGBOOK:
				  CLOCK: [2022-10-19 Wed 13:29:17]--[2022-10-19 Wed 13:40:36] =>  00:11:19
				  :END:
					- {{embed ((634f70c1-0690-43ac-b557-6454a38814bb))}}
			- 13:48 - 13:57
				- DONE [[iPad]] 2022 宣传视频
				  link:: [iPad 和 iPad Pro 联手上新](https://www.bilibili.com/video/BV1gD4y1r7gq?share_source=copy_web)
			- 13:58 - 14:02
				- #深思
					- 人虽然只需要自己肯定自己，自己认可自己，话虽如此，但还是需要获得别人的肯定的，这是一种外界的正反馈。尤其是亲密关系的肯定、认可、接纳，因为亲密的关系认可了你之后，会触发你进一步的认可自己。
						- #link
							- ((634f70bc-1295-44d6-a2bd-896cf2b6246d))
			- 14:02 - 14:05
				- #摘录
					- CANCELED [不痴迷增长的创业者：风口之外的另一种选择](cubox://card?id=********************************)
					- TODO [[Conor White-Sullivan]] [[Twitter]] [[思考]] [[RoamResearch]] 
					  link:: [twitter](https://twitter.com/conaw/status/1200646481423863808?s=61&t=FUM_GbwWpCebUN_xjHiSZA)
			- 14:12 - 14:15
				- DONE [[闪念]] ： [[经营贷]] [[资金]] 安排  #网贷 #债务 #ending
				  id:: 636a8889-c0d4-40c4-90e7-fb4b3c940cad
				  collapsed:: true
				  SCHEDULED: <2022-11-11 Fri>
					- #定量  
					  collapsed:: true
						- DONE [[毛义明]] 实际到手：572852
						  collapsed:: true
							- [[支付宝]]：0
							  id:: 636d1652-0831-421d-9f47-81ddd4aaa8c4
							- [[工商银行]]：12000
							  id:: 636d16aa-cb7d-4aab-b0e6-fdfa63559dbe
								- `抹掉了769.87`
							- [[招商银行]]：560852
							  id:: 636d16eb-5dce-4555-9b4c-4dfa2436aee5
						- [[王露]] [[短期]] 债务：415383.46
						  id:: 636d17a1-a58a-4870-b085-232c6d247666
						  collapsed:: true
							- [[王露]] [[短期]] 债务：527440.46（含[[微粒贷]]）
							  collapsed:: true
								- ```calc
								  54500+38038.27+112057+91753.62+91685.57+55861+35000+34545+14000
								  54500+38038.27+91753.62+91685.57+55861+35000+34545+14000
								  ```
							- #借款
							  collapsed:: true
								- DONE [[王艺]]： 54500
								  id:: 636c7cfd-a48d-498d-976b-33c3adb05a2e
									- #银行卡
										- 王艺，6217856200037624272，中国银行台州市分行
							- #网贷
								- DONE [[美团]]（10号）：91753.62
								  id:: 636c7cfd-4cbb-4a9e-a3f6-6a11aceb4520
								- DONE [[招联金融]]（15号）：91685.57
								  id:: 636c7cfd-9093-4720-b7a5-c94f320d604a
								- DONE [[网商贷]]（18号）：38038.27
								  id:: 636c7cfd-4afc-451d-8d7d-48f980e918a3
								- DONE [[微粒贷]]（28号）：112057 
								  id:: 636c7cfd-93a2-485e-bafc-8ebe01e4f39c
								  SCHEDULED: <2023-11-24 Fri>
							- #信用卡
								- DONE [[花呗]]：14000
								  id:: 636c7cfd-4757-4830-8605-f5c9eaeec2e3
								- DONE [[平安银行]]（1号）：13453.91
								  id:: 636c7cfd-46ff-4bfa-9c7c-e142e65fbae5
								- DONE [[广发银行]]（17号）：39913.53
								  id:: 636c7cfd-24e9-47c0-b1de-f001885c50ed
								- DONE [[招商银行]] （23号）：57555.23
								  id:: 636c7cfd-c3bd-4f1c-a3e5-fb463020c6b7
						- 3个月 [[工商银行]] [[贷款]] [[利息]] ：30112.5
						  id:: 636d19b2-cb23-4f5c-94e0-55271845466c
						  collapsed:: true
							- ```calc
							  10037.5*3
							  ```
						- ((636c7cfd-93a2-485e-bafc-8ebe01e4f39c)) 半年：49240.5
						  id:: 636d17f2-be13-4de4-83cd-83f16e42e097
						  ```calc
						  8206.75*6
						  ```
						- 6个月 [[家庭]] 基本 [[生活费]]：72000
						  id:: 636d1a09-f616-4067-9f86-85ca85b891f6
						  ```calc
						  (2000+2500+1500+6000)*6
						  ```
							- [[育儿]]：2000
							- [[买菜]]：2500
							- [[水电煤]] [[物业]] [[宽带]]：1500
							- [[个人消费]]：3000*2
						- [[招商银行]] 单日 [[转账]] [[限额]] ：50000
						  id:: 636cc4b9-d04c-46de-8912-81724852489d
						  collapsed:: true
					- #必要条件
					  collapsed:: true
						- DONE [[资金]] 分配
							- #if
							  collapsed:: true
								- DONE 扣除 [短期债务](((636d17a1-a58a-4870-b085-232c6d247666))) ，余额无法覆盖 [生活费](((636d19b2-cb23-4f5c-94e0-55271845466c))) 和 [利息](((636d1a09-f616-4067-9f86-85ca85b891f6)))
									- #做选择
										- DONE 保留一个利息最低/金额最高的 [[网贷]] ： ((636c7cfd-93a2-485e-bafc-8ebe01e4f39c))
										- CANCELED 全部网贷都按月还
										- CANCELED 硬还，活不下去套 [[信用卡]]
							- #then
								- DONE [[王露]] 预计存款：127356.04
								  id:: 636e1a27-cd9e-4171-bbe4-a68517a42a92
									- ```calc
									  572852-415383.46-30112.5
									  ```
								- DONE [[毛义明]] 预计存款：30112.5
								- DONE [[还款]] 计划：[415383.46](((636d17a1-a58a-4870-b085-232c6d247666)))
								  id:: 636c7cfd-a92d-4b0d-bf85-f39111bbb814
					- ((636c7cfd-a92d-4b0d-bf85-f39111bbb814))
					  id:: 636d24e4-4547-4fc0-b0ad-fe2a7e692c14
						- #变量
						  collapsed:: true
							- [[工商银行]] [[转账]] 有可能异常。
							  id:: 636cc9ab-0a37-4f04-a3d1-925ccdbc7bc3
							- [[毛义明]] 转 [[王露]] 被监控。
						- #step
						  id:: 636e1a27-e55e-4234-a690-7fe94a5f883a
						  collapsed:: true
							- 10号
							  collapsed:: true
								- DONE [[垫资公司]] [[汇款]] -> ((636c7cfd-a48d-498d-976b-33c3adb05a2e)) 
								  id:: 636c9385-d18c-4140-91a3-f55b22ee1f92
								  collapsed:: true
								  SCHEDULED: <2022-11-10 Thu>
									- #if
										- DONE 和 [[杨行]] 沟通后，可行
											- #截图
												- ![WechatIMG241.jpeg](../assets/WechatIMG241_1668060378971_0.jpeg){:width 245}
											- #then
												- #step
													- DONE [[王露]] 跟 [[王艺]] 要 [[汇款账号]] 和 最终 [[还款]] 金额
													- DONE [[垫资公司]] 完成汇款
														- #截图
															- ![WechatIMG1468.jpeg](../assets/WechatIMG1468_1668072033403_0.jpeg){:width 245}
													- DONE [[王艺]] 确认到账
								- DONE [[线上转账]]
								  collapsed:: true
									- #[[毛义明]]
										- DONE [[招商银行]] [[转账]] [[工商银行]]：50000
										- DONE [[工商银行]] [[充值]] [[支付宝]]：12000
										- DONE [[工商银行]] [[充值]] [[支付宝]]：5000
										- DONE [[工商银行]] [[充值]] [[支付宝]]：4000
										  id:: 636cfb80-d5ed-4a4e-b136-0e96749f15fa
										- DONE [[工商银行]] [[充值]] [[支付宝]]：28800
										- DONE [[支付宝]] [[转账]] [[杨行]]：21000
									- #杨行
										- [[支付宝]] [[转账]] [[王露]]：21000
								- DONE [[余额]] 更新 #pass
								  collapsed:: true
									- ((636d1652-0831-421d-9f47-81ddd4aaa8c4)) 28800
									  id:: 636cc6ea-c4c0-46f2-9634-aa87f01a9aaa
									- ((636d16aa-cb7d-4aab-b0e6-fdfa63559dbe)) 12200
									  id:: 636cc709-3ad5-4a72-bc32-bcb8220b98f8
									- ((636d16eb-5dce-4555-9b4c-4dfa2436aee5)) 456352
									  id:: 636cc733-b43d-40aa-a027-98aefe6ff1f7
								- ((636c7cfd-a92d-4b0d-bf85-f39111bbb814)) 335104.61
								  id:: 636cfbf3-1de6-4218-8f5d-834c2009ed7c
								  collapsed:: true
									- ```calc
									  415383.46-14000-54500-6470.71-5308.14
									  ```
									- #已还
									  collapsed:: true
										- ((636c7cfd-a48d-498d-976b-33c3adb05a2e))
										- ((636c7cfd-4757-4830-8605-f5c9eaeec2e3)) 10700.54
										  id:: 636cce97-0332-47b9-8cf7-fd4aec2ede7e
										- ((636c7cfd-4cbb-4a9e-a3f6-6a11aceb4520)) 6470.71
										  id:: 636cfc4d-e59a-4828-b76a-31faeef50a83
										- ((636c7cfd-4afc-451d-8d7d-48f980e918a3)) 5308.14
										  id:: 636cfc77-1179-4815-a1a4-92dd5c1dc043
									- #待还
									  id:: 636cfc04-07b8-4223-ae38-60a23dde1a4c
									  collapsed:: true
										- #支付宝
											- ((636c7cfd-9093-4720-b7a5-c94f320d604a)) 91717.43
											  id:: 636d0160-ae32-480e-a994-74e1c5f318e6
											- ((636cfc77-1179-4815-a1a4-92dd5c1dc043)) 32772.52
											  id:: 636cfed9-51c5-4d63-b288-1d97c2a34c94
											- ```calc
											  91717.43+32772.52
											  ```
										- #转账
											- ((636cfc4d-e59a-4828-b76a-31faeef50a83)) 85316.28
											  id:: 636cfedf-70b6-47c4-8c9f-7d4e389ac9ac
											- ((636c7cfd-46ff-4bfa-9c7c-e142e65fbae5))
											  id:: 636cfea6-2e32-4bfb-ac53-3e2727750c3d
											- ((636c7cfd-24e9-47c0-b1de-f001885c50ed))
											  id:: 636cfeb2-e1c7-4fcd-8f4c-9489288ffcb2
											- ((636c7cfd-c3bd-4f1c-a3e5-fb463020c6b7))
											  id:: 636cfeba-b047-42e1-9792-7ae5a6eadfa5
									- #后还
										- ((636cce97-0332-47b9-8cf7-fd4aec2ede7e)) 3438.9
										  id:: 636e1a27-299a-414d-a64b-42e756fdde58
								- DONE 拍下银行卡号
								  collapsed:: true
									- #银行卡
										- [[王露]] [[广发银行]] [[信用卡]]：5201521646782160
										  id:: 636d2a2a-bc35-4448-a209-7a5f4f74fb44
										  collapsed:: true
											- #证照
												- ![WechatIMG250.jpeg](../assets/WechatIMG250_1668098799166_0.jpeg){:width 245}
												- ![WechatIMG249.jpeg](../assets/WechatIMG249_1668098806910_0.jpeg){:width 245}
										- [[王露]] [[招商银行]] [[信用卡]]：6225768791791831
										  id:: 636d2a4c-fe42-49dd-ac9e-3e7758d3e05a
										  collapsed:: true
											- #证照
												- ![WechatIMG248.jpeg](../assets/WechatIMG248_1668098934459_0.jpeg){:width 245}
												- ![WechatIMG247.jpeg](../assets/WechatIMG247_1668098943311_0.jpeg){:width 245}
										- [[王露]] [[平安银行]] [[信用卡]]：4835368202675165
										  id:: 636d2a5a-ba15-439e-a72c-e381a4781e3f
										  collapsed:: true
											- #证照
												- ![WechatIMG246.jpeg](../assets/WechatIMG246_1668098993213_0.jpeg){:width 245}
												- ![WechatIMG245.jpeg](../assets/WechatIMG245_1668098999819_0.jpeg){:width 245}
										- [[王露]] [[中信银行]]：6217680905901307
										  id:: 636dcc41-32f0-4c39-909b-7d8ad214da32
										  collapsed:: true
											- #证照
												- ![WechatIMG5170.jpeg](../assets/WechatIMG5170_1668140127277_0.jpeg){:width 245}
										- [[毛义明]] [[工商银行]]：6212253602062201506
										  id:: 636d2a65-3b1e-48bc-a62c-9025137e8f42
										  collapsed:: true
											- #证照
												- ![WechatIMG251.jpeg](../assets/WechatIMG251_1668098743849_0.jpeg){:width 245}
												- ![WechatIMG252.jpeg](../assets/WechatIMG252_1668098752076_0.jpeg){:width 245}
										- [[杨行]] [[平安银行]] 广州黄埔大道支行：6230580000084533483 #pass
										  id:: 636dcf7d-8f35-4614-936c-53086140be7a
								- DONE 准备电子 [[身份证]]
								  collapsed:: true
									- #毛义明 #证照
										- ![CF8CABBE-2DF3-41DB-B94D-7C5EDE518446_1_105_c.jpeg](../assets/CF8CABBE-2DF3-41DB-B94D-7C5EDE518446_1_105_c_1668099582777_0.jpeg){:width 245}
										- ![3A5879C5-2219-4554-B916-CBB7BFE2F1E9_1_105_c.jpeg](../assets/3A5879C5-2219-4554-B916-CBB7BFE2F1E9_1_105_c_1668099615656_0.jpeg){:width 245}
									- #王露 #证照
										- ![截屏2022-11-11 01.05.45.png](../assets/截屏2022-11-11_01.05.45_1668099979741_0.png){:width 245}
										- ![截屏2022-11-11 01.06.06.png](../assets/截屏2022-11-11_01.06.06_1668099996960_0.png){:width 245}
							- 11号
							  id:: 636dbb30-c343-495c-976e-1acfa5770eb1
							  collapsed:: true
								- #必要条件
								  collapsed:: true
									- DONE 查看 [[美团]] [[还款]] [[账号]] #pass
										- 账户：美团借钱还款待结算资金专户；账号：62310000010901 ；[[开户银行]]：平安银行股份有限公司深圳分行营业部
										  id:: 636dbdf7-ed5d-4c5e-a9e3-ea64297965b1
										  collapsed:: true
											- #截图
												- ![WechatIMG254.jpeg](../assets/WechatIMG254_1668136412313_0.jpeg){:width 245}
									- DONE 电话就近 [[招商银行]] 是否可以前往转账
									  collapsed:: true
										- #答案
											- [[科学城支行]]，需提供 [[hour/24]] [[核酸证明]]
									- ((636c7cfd-ae3e-4457-beef-14e85ff728d4))
								- DONE [[线上转账]]
								  collapsed:: true
									- DONE [[招商银行]] [[充值]] [[支付宝]]：50000
									- DONE [[工商银行]] [[充值]] [[支付宝]]：12200
									- DONE [[支付宝]] 转账 [[杨行]]：91000
									- DONE [[杨行]] 转账 [[王露]] 支付宝：91000
								- ((636cfbf3-1de6-4218-8f5d-834c2009ed7c)) 271275.03
								  id:: 636e1a27-71be-4974-9e2d-9c861c3bacde
								  collapsed:: true
									- #已还
										- ((636cfed9-51c5-4d63-b288-1d97c2a34c94)) 33047.81
										- ((636d0160-ae32-480e-a994-74e1c5f318e6))  30781.77
										  id:: 636dc08f-b533-4f45-b485-bc9bc88d1413
									- #待还
										- ((636dc08f-b533-4f45-b485-bc9bc88d1413)) 60935.66
										  id:: 636dbb30-3ec2-40ef-997d-464cda37d190
										- ((636cfc4d-e59a-4828-b76a-31faeef50a83)) 85316.28
										  id:: 636e1a27-f74c-496e-a908-651ea8cf4d24
										- {{embed((636c7cfd-46ff-4bfa-9c7c-e142e65fbae5))}}
										- {{embed ((636c7cfd-24e9-47c0-b1de-f001885c50ed))}}
										- {{embed((636c7cfd-c3bd-4f1c-a3e5-fb463020c6b7))}}
								- DONE 核对当天 [[汇款]] [[转账]] 金额与账号
								  collapsed:: true
									- #[[回忆]]
									  collapsed:: true
										- 15:06 纠结于直接转中信还是分别转各个还款账号， [老婆]([[王露]])给了个好想法，信用卡附近有还款点的可以取现！这样我对老婆转账瞬间只剩一下一个，而且不是中信，也不是储蓄卡！[老婆]([[王露]])真棒！果然是毛家黄蓉啊 哈哈哈 #今日份快乐
									- #必要条件
									  collapsed:: true
										- CANCELED 询问柜台转账影不影响手机转账额度？
										  collapsed:: true
											- #回忆
											  collapsed:: true
												- [[杨行]] 说不影响
									- #柜台汇款
									  id:: 636ded69-ff21-4edc-a632-0cd1c5ce8d88
									  collapsed:: true
										- DONE 查询 [[招商银行]] 是否有异常
										  collapsed:: true
											- #then
											  collapsed:: true
												- DONE [招商：6225768791791831](((636d2a4c-fe42-49dd-ac9e-3e7758d3e05a))) -> [57556](((636cfeba-b047-42e1-9792-7ae5a6eadfa5)))
												  id:: 636dbb30-0d29-432d-818b-3d60d4e4e0ec
												  collapsed:: true
													- #then
													  collapsed:: true
														- ((636c7cfd-c3bd-4f1c-a3e5-fb463020c6b7)) 57556
														  id:: 636e202e-c68b-4812-9f09-b1de56d83b14
														  collapsed:: true
												- DONE [杨行平安：6230580000084533483](((636dcf7d-8f35-4614-936c-53086140be7a))) -> [34000](((636dcd69-9275-4eab-bf34-ad26597c482d)))
												  collapsed:: true
													- #then #[[线上转账]]
													  collapsed:: true
														- DONE [[招商银行]] 转账 [[杨行]] [[平安银行]] ：34000
														  id:: 636dcd69-9275-4eab-bf34-ad26597c482d
														  collapsed:: true
														- DONE [[杨行]] [[支付宝]] [[转账]] [[王露]]：34000
														  collapsed:: true
															- #then
															  collapsed:: true
																- ((636dbb30-3ec2-40ef-997d-464cda37d190)) 61055.79
																  id:: 636e1a27-de62-45d3-a4d2-607455a0ad36
																  collapsed:: true
									- #柜台取现
									  id:: 636deed6-9025-4295-a213-92ea82afbed1
									  collapsed:: true
										- DONE [[招商银行]] [[取现]] ：140000
										  id:: 636fbc01-597b-47c1-ba3d-ff95e88f4778
										  collapsed:: true
											- collapsed:: true
											  ```calc
											  85316.28+39914+13443
											  ```
											- #回忆
											  collapsed:: true
												- 好久好久没有摸过这么大笔钱了，抱回 [[中信银行]] 存钱的时候，不停的闻 [[钱味]]。
												  collapsed:: true
													- #照片
													  collapsed:: true
														- ![WechatIMG274.jpeg](../assets/WechatIMG274_1668268791010_0.jpeg){:width 245}
														- ![WechatIMG275.jpeg](../assets/WechatIMG275_1668268802695_0.jpeg){:width 245}
														- ![WechatIMG276.jpeg](../assets/WechatIMG276_1668268817188_0.jpeg){:width 245}
											- #then
											  collapsed:: true
												- ((636e13ca-0343-4a5f-a96b-a4bff42fe8fe))
												- ((636e1a29-85dd-4eca-86ac-1de5bf092fef))
											- #link
											  collapsed:: true
												- ((636e5754-30a3-46bb-92c8-a213841d5c06))
												- ((636e5754-cd87-45bf-a224-060bbb1692c7))
									- #ATM存款
									  id:: 636ded7a-8196-4405-8dd3-a43a9d6deb36
									  collapsed:: true
										- DONE [中信：6217680905901307](((636dcc41-32f0-4c39-909b-7d8ad214da32))) -> [85400](((636dbb30-6347-45ba-81d3-5aeeb2752eab)))
										  id:: 636e1a27-44d3-44ef-887b-7bf2eedc82b7
										  collapsed:: true
											- ```calc
											  140000-85400
											  39914+13454
											  54600-53368
											  ```
											- #then #柜台汇款
												- DONE [美团借钱还款待结算资金专户：62310000010901](((636dbdf7-ed5d-4c5e-a9e3-ea64297965b1))) -> [85316.28](((636cfedf-70b6-47c4-8c9f-7d4e389ac9ac))) @ [[王露]]
												  id:: 636dbb30-6347-45ba-81d3-5aeeb2752eab
												  collapsed:: true
										- DONE [广发：5201521646782160](((636d2a2a-bc35-4448-a209-7a5f4f74fb44))) -> [39200](((636cfeb2-e1c7-4fcd-8f4c-9489288ffcb2)))
										  id:: 636d30d7-e23c-47f9-8ccf-d0bc2fad3aea
										- DONE [平安：4835368202675165](((636d2a5a-ba15-439e-a72c-e381a4781e3f))) -> [13500](((636cfea6-2e32-4bfb-ac53-3e2727750c3d)))
										  id:: 636dbb30-dee3-45cd-9b43-50d96da893cc
								- DONE [[转账]]
								  SCHEDULED: <2022-11-11 Fri>
								  id:: 636df174-ac03-4746-a2d4-fc9652997d7e
								  collapsed:: true
								  :LOGBOOK:
								  CLOCK: [2022-11-11 Fri 15:39:44]--[2022-11-11 Fri 17:56:25] =>  02:16:41
								  :END:
									- #step
										- {{embed ((636ded69-ff21-4edc-a632-0cd1c5ce8d88))}}
										- {{embed ((636deed6-9025-4295-a213-92ea82afbed1))}}
										- {{embed ((636ded7a-8196-4405-8dd3-a43a9d6deb36))}}
									- #link
										-
								- ((636e1a27-71be-4974-9e2d-9c861c3bacde)) 150974.52 -> 98274.52
								  collapsed:: true
									- #少还 #预估
										- ```calc
										  150974.52-13500-39200
										  152663.24-1688.72-85316.28-13453.91-39913.53
										  271275.03-61055.79-57556-1668.72-13500-39200
										  ```
									- #已还
										- ((636e1a27-de62-45d3-a4d2-607455a0ad36))
										- ((636e202e-c68b-4812-9f09-b1de56d83b14))
										- ((636e1a27-299a-414d-a64b-42e756fdde58)) 1668.72
										  id:: 636e2402-e36b-4473-af4e-c151f8b3b0e7
										- ((636c7cfd-46ff-4bfa-9c7c-e142e65fbae5)) 13500
										  id:: 636e5754-26be-4627-bc50-cdf126b3b935
										- ((636c7cfd-24e9-47c0-b1de-f001885c50ed)) 39200
										  id:: 636e5754-a131-43c7-b8f4-6ceb8ca1cfe2
									- #待还
										- ((636cfc4d-e59a-4828-b76a-31faeef50a83)) 85316.28
									- #后还
										- ((636e2402-e36b-4473-af4e-c151f8b3b0e7)) 1770.18
										- ((636e202e-c68b-4812-9f09-b1de56d83b14)) 720.33
										- ((636e5754-a131-43c7-b8f4-6ceb8ca1cfe2)) 700
										- ((636e5754-26be-4627-bc50-cdf126b3b935)) 50
								- DONE [[余额]] 更新 #pass
								  id:: 636d3489-de62-4724-9ff9-27896656bf24
								  collapsed:: true
									- ((636cc6ea-c4c0-46f2-9634-aa87f01a9aaa)) 0
									- ((636cc709-3ad5-4a72-bc32-bcb8220b98f8)) 0
									- ((636cc733-b43d-40aa-a027-98aefe6ff1f7)) 174746
										- [减掉工商利息30112.5，剩余](((636d19b2-cb23-4f5c-94e0-55271845466c)))：144633 （[之前预估 127356.04](((636e1a27-cd9e-4171-bbe4-a68517a42a92)))）
											- ```calc
											  174746-30112.5
											  144633.5-127356.04
											  17277.46-12290
											  ```
											- 这4987.46，包含花呗 1770.18 ，取现的50，取钱多出来的1232，大概是这样吧
							- 12号
							  collapsed:: true
								- ((636dbb30-6347-45ba-81d3-5aeeb2752eab))
								- DONE ((636d3489-de62-4724-9ff9-27896656bf24)) 转给 [[王露]] [[中信银行]]
			- 15:09 - 15:13
				- ((634fa679-c417-4178-89cc-db896b26f1f9))
				  id:: 635015ed-91a7-4047-bafd-6f8c911266d4
					- #回忆
						- [[王露]]说去到 [[银行卡]] 所在支行，解释了流水，其它没什么，支行提交了解冻申请，但是[[上级银行]]拒绝，据说被什么列入监控，解冻不了，只能先 [[赎房]] ，等拿 [[房本]] 的时候再提交恢复 [[线上支付]] 申请。
						  id:: 635015ed-9abd-4cac-8dee-0cb237887e33
							- [[fuck]]，那我的 [[建设银行]]不也是了？
							- #link
								- ((634f70bf-1c64-48dc-8186-39a8ffeb53c0))
				- {{embed ((634f5b0a-fd4b-4a07-85d2-fce8a1cbc733))}}
- # 15:14 - 18:39（3小时25分）
  collapsed:: true
	- {{embed ((632ada1d-7cfc-450c-ad9f-ce0c679b6ece))}}
	- #log
		- 15:36 - 16:09
			- DONE [[大沙东]] [[地铁站]] -> [[五羊邨]]
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 15:38:34]--[2022-10-19 Wed 16:09:22] =>  00:30:48
			  :END:
			- 15:38 - 15:54
				- {{embed ((634f70c1-0690-43ac-b557-6454a38814bb))}}
				- {{embed ((634f5b0a-fd4b-4a07-85d2-fce8a1cbc733))}}
		- 16:27 - 16:31
			- #回忆
				- 去[[梦开始的地方]]打卡：9年前在广州的 [[落脚点]]。2013年初我找的房，然后[[王露]]过来，再然后 [[杨天吏]]过来。
				  id:: 634fc009-00df-4c8d-80a7-0f6ca7e0ecbb
				  :LOGBOOK:
				  CLOCK: [2022-10-19 Wed 18:38:14]--[2022-10-19 Wed 19:10:13] =>  00:31:59
				  :END:
		- 17:21 - 18:35
			- ((634ec0d6-b89b-4843-b010-689c90355b0a))
			  id:: 634fff89-59f9-4c95-ac9f-97af639fbe3e
				- [[回忆]]
					- [[垫资]] 的人查了，29号（包含）之前都可以。
					  id:: 634fff89-221b-40d2-826b-be52fb66125d
						- #recycle
						  id:: 634fff89-387f-4732-a083-2042796bb1b3
							- ((634f70c1-0690-43ac-b557-6454a38814bb))
- # 18:35 - 22:17（3小时42分）
  collapsed:: true
	- ((634fc009-00df-4c8d-80a7-0f6ca7e0ecbb))
	  id:: 634ff80a-777a-479e-b3e4-a39775eac27d
		- #照片
		  collapsed:: true
			- ![2022-10-19-19-01-21.jpeg](../assets/2022-10-19-19-01-21.jpeg){:width 245}
			- ![2022-10-19-19-08-17.jpeg](../assets/2022-10-19-19-08-17.jpeg){:width 245}
			- ![2022-10-19-19-08-46.jpeg](../assets/2022-10-19-19-08-46.jpeg){:width 245}
			- ![2022-10-19-19-09-26.jpeg](../assets/2022-10-19-19-09-26.jpeg){:width 245}
			- ![2022-10-19-21-39-06.jpeg](../assets/2022-10-19-21-39-06.jpeg){:width 245}
			- ![2022-10-19-21-39-37.jpeg](../assets/2022-10-19-21-39-37.jpeg){:width 245}
			- ![2022-10-19-21-39-51.jpeg](../assets/2022-10-19-21-39-51.jpeg){:width 245}
	- #log
		- 19:10 - 19:20
		  collapsed:: true
			- DONE [[嗨椒小面]]
			  id:: 634fff89-6478-49af-8dbc-deb1d27f8eed
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 19:10:54]
			  CLOCK: [2022-10-19 Wed 19:11:37]--[2022-10-19 Wed 19:20:21] =>  00:08:44
			  :END:
				- #[[回忆]]
				  collapsed:: true
					- 想到 [老婆]([[王露]])以后不用再惦记每个月的还款，我还是很开心的。
					  id:: 634ff80a-8ba7-4003-80e2-8a2953c0ea94
					  collapsed:: true
						- #link
						  collapsed:: true
							- ((6329521a-69b7-4ebd-bf33-eb5fbca5afe4))
		- 19:20 - 20:06
		  collapsed:: true
			- #回忆
			  collapsed:: true
				- DONE [[散步]] 走在 [[五羊邨]] 到 [[猎德]]，途经 [[南天广场]] [[国馆]] [[高德置地]] [[花城汇]]，9年前一起走过的路，说了同样的话：站在世界的中心，奋斗都更有了____ ；只不过9年前，说的是“欲望”，现在说的是力量。心境完全不同。现在只想踏踏实实的和 [[王露]]在 [[广州]] [[扎根]]，打好每个基础。
				  collapsed:: true
				  id:: 635015ed-b46c-486e-8bcb-0301281e6227
				  :LOGBOOK:
				  CLOCK: [2022-10-19 Wed 19:21:07]
				  CLOCK: [2022-10-19 Wed 19:21:52]--[2022-10-19 Wed 20:06:05] =>  00:44:13
				  :END:
					- #link
					  collapsed:: true
						- ((634fc009-00df-4c8d-80a7-0f6ca7e0ecbb))
		- 20:06 - 20:44
		  collapsed:: true
			- DONE [[猎德]] [[地铁站]]-> [[大沙东]]
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 20:06:43]
			  CLOCK: [2022-10-19 Wed 20:07:06]--[2022-10-19 Wed 20:44:34] =>  00:37:28
			  :END:
		- 20:44 - 21:31
		  collapsed:: true
			- 想到[[向鹏飞]]的 [[债务]]，当时是多么的劝 [[王艺]]，现在我们却都同样的处境，甚至更甚，但 [[王露]]却付出了我们曾经反对的东西。更甚者，这个种子其实在 [[超级元素]]的过程中，一次次的请求老婆 [[网贷]]，那个时候，我已经踏破自己的底线了，王露也因为我，踏破了底线。
			  collapsed:: true
				- #link
				  collapsed:: true
					- ((634ff80a-8ba7-4003-80e2-8a2953c0ea94))
					- ((6329521a-69b7-4ebd-bf33-eb5fbca5afe4))
			- 21:03
			  collapsed:: true
				- 到 [[家]]
		- 21:31 - 22:17
		  collapsed:: true
			- DONE [[continue]]
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 21:31:35]--[2022-10-19 Wed 22:17:12] =>  00:45:37
			  :END:
				- 21:47 - 21:52
				  collapsed:: true
					- ((634fff89-aa56-4a72-8c6e-677fd74eb94a))
					  collapsed:: true
						- #回忆
							- 回到家后， [儿子]([[毛懋]])特别“讨好”，哈哈哈， [老婆]([[王露]])买了的 [[栗子]]，他拿过来给我吃，给 [[妈妈]]吃，全程都在对我和老婆好…… 看来早上还是有效果的。
							  id:: 635015ed-33b2-4d1c-8892-ade42cef7750
								- 后来奶奶说，她跟孙子说：你打人是不是连爸爸妈妈都生气啦。耨耨说是。奶奶还说他今天想打的时候，说：耨娜不打了。 T_T -> 我怎么听着有点心酸呢。
								  id:: 635015ed-2a30-4367-befd-bf88ca5452b3
									- 想到 ((634fff89-6478-49af-8dbc-deb1d27f8eed)) 的时候，和 [老婆]([[王露]])说，当 [儿子]([[毛懋]]) 产生了 [[过激行为]]的时候，就要 [[干预]]了，例如打人这件事，打大腿、打手，都没问题，扇脸，就过激了。
									  collapsed:: true
									  id:: 635015ed-5306-427b-a621-c2fecb2450ac
										- 孩子小的时候，哪怕是现在，可能就是无意识的，但是过激之后，他现在这个年纪 [[2-3岁育儿]]，如果我们处理不当，不干预，反而会让他对自己的行为有一种错误的理解。这一点更可怕。
										  id:: 635015ed-afae-4346-8020-e79d416b5533
				- 21:52 - 21:59
				  collapsed:: true
					- {{embed ((634fff89-59f9-4c95-ac9f-97af639fbe3e))}}
				- 21:59 - 22:15
				  collapsed:: true
					- {{embed ((6329521a-69b7-4ebd-bf33-eb5fbca5afe4))}}
- # 22:22 - 01:26（3小时4分）
  collapsed:: true
	- #回忆
		- 说起 ((634f70bc-493b-41b3-965a-61092e3efcc0)) [[杨行]] 居然是云南人。
	- #log
		- 22:23 - 22:25
		  collapsed:: true
			- #日常英语
			  collapsed:: true
				- 正方形 #card
				  collapsed:: true
					- square
				- 三角形 #card
				  collapsed:: true
					- triangle
		- 22:29 - 23:04
		  collapsed:: true
			- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
		- 23:05 - 23:22
		  collapsed:: true
			- ((651aa257-6ad2-43e6-8334-2c48cb73d2fa))
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 22:30:13]--[2022-10-19 Wed 23:22:08] =>  00:51:55
			  :END:
		- 23:22 - 23:29
		  collapsed:: true
			- #continue
			  collapsed:: true
				- {{embed ((634ba22a-72fb-470e-9d66-a858b4150b48))}}
			- ((651aa257-c7fc-43ca-8e45-b129364eabf0))
			- ((651ad660-4be8-4004-b82a-9ca1f84cea3a))
			-
			- ((651aa257-6b75-4549-82e2-69f005661986))
		- 23:29 - 23:39
		  collapsed:: true
			- #闪念
				- CANCELED 整理 [[Favorites]]  #recycle
				  SCHEDULED: <2022-10-21 Fri .+1d>
				  id:: 6350baf9-ec2c-464e-b9a6-a917b2f81a54
				  :LOGBOOK:
				  CLOCK: [2022-10-21 Fri 13:54:59]--[2022-10-21 Fri 14:16:47] =>  00:21:48
				  :END:
					- #recycle
						- ((635015ed-c812-4546-9d0e-bd0285696565))
						  id:: 6360eb68-2a1d-40f8-a3bb-f702e0cd02e7
					- #step
						- ((635015ed-06db-4bce-819b-98d588b1d630))
						- ((635015ed-41da-4cf1-8b19-330d650b9dcd))
						- ((634f70bc-2036-4418-9044-0fa3221412bb))
						- ((634f70bc-3741-40d5-9c2e-67474901f3c2))
						- CANCELED [[logseq]]8.9版本升级了 [[namespace]]的文件命名规则，需要手动更改。  #recycle
						  id:: 635eb4ca-5318-4037-977f-3105a5801f6b
						  collapsed:: true
						  :LOGBOOK:
						  CLOCK: [2022-10-20 Thu 10:36:54]
						  CLOCK: [2022-10-20 Thu 10:36:57]--[2022-10-20 Thu 10:43:16] =>  00:06:19
						  :END:
							- CANCELED 新的命名规则不占用`title`，可以删除空的页面啦
							  id:: 635eb4ca-e065-4462-992f-66eeaa6fe7b1
							  :LOGBOOK:
							  CLOCK: [2022-10-20 Thu 10:43:14]--[2022-10-20 Thu 11:07:00] =>  00:23:46
							  :END:
							- DONE 删除手机的克隆，重新下载
							  :LOGBOOK:
							  CLOCK: [2022-10-20 Thu 11:07:50]--[2022-10-20 Thu 11:23:41] =>  00:15:51
							  :END:
						- CANCELED [常用功能标签]([[特殊标签]]) -> fav
						  id:: 6350ee8f-bce1-449c-8d10-32ee8fb2d32b
						  collapsed:: true
						  :LOGBOOK:
						  CLOCK: [2022-10-20 Thu 14:48:42]--[2022-10-20 Thu 14:51:26] =>  00:02:44
						  CLOCK: [2022-10-20 Thu 14:51:33]--[2022-10-20 Thu 14:56:00] =>  00:04:27
						  :END:
							- [[故事]][[知识点]][[观点]][[案例]][[比喻]][[金句]]
								- [[冲突]] [[背景]] [[问题]] [[答案]]
							- `[[Favorites/zettel/摘录✂️/故事🪑]]`
							- [[知识点]]
							- [[观点]]
							- [[案例]]
							- `[[Favorites/zettel/摘录✂️/比喻💫]]`
							- `[[Favorites/zettel/摘录✂️/金句🎉]]`
							- `[[Favorites/workflow/结构🎢/冲突]]`
							- `[[Favorites/workflow/结构🎢/背景]]`
							- `[[Favorites/workflow/结构🎢/问题]]`
							- `[[Favorites/workflow/结构🎢/答案]]`
							- #link
								- ((6300f067-fdf8-4d78-930a-3da642aae2e3))
							- #recycle
								- ((634f70bc-8ca5-46e6-8183-d1ed5b97de17))
						- ((635123cb-6079-4cf3-aab4-36a872807aa9))
						- `[[TODO]]` -> `[[TODO]]` #recycle
						  id:: 6360eb68-b1cc-4d14-b119-06e2ccd7fc71
						  :LOGBOOK:
						  CLOCK: [2022-10-20 Thu 17:38:43]
						  CLOCK: [2022-10-20 Thu 17:38:50]--[2022-10-20 Thu 17:57:34] =>  00:18:44
						  :END:
							- `[[Favorites/todo/if]]`
							- `[[Favorites/todo/then]]`
							- `[[Favorites/todo/做选择]]`
							- `[[Favorites/todo/变量]]`
							- `[[Favorites/todo/必要条件]]`
							- [[DOING]]
							- [[CANCELED]]
							- [[DONE]]
							- `[[A]]` #recycle
							- `[[B]]` #recycle
							- `[[C]]`
						- CANCELED [[data]] -> `Favorites/wishlist`
						  :LOGBOOK:
						  CLOCK: [2022-10-20 Thu 17:51:32]
						  :END:
						- CANCELED `Favorites/state` -> [[status]]
						  id:: 6360eb68-d555-4af9-8032-d6f8daa1759a
						- CANCELED `Favorites/workflow` -> [workflow]([[workflow]])
						- CANCELED [[ending]] ->`[[Favorites/成果🏆]]`
						  id:: 6360eb68-7a22-4760-8584-f6feb1598378
			- #闪念
				- ` [[TODO]] ` 有点像 `[[问题]]` [*](((6350baf9-ec2c-464e-b9a6-a917b2f81a54)))
					- #link
						- ((634f70bc-162d-47ee-a210-827c809daa95))
		- 23:40 - 23:41
		  collapsed:: true
			- #recycle
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 23:41:06]
			  :END:
				- {{embed ((634c211a-75c1-4f51-8416-1bbf2166c7f5))}}
		- 23:42 - 23:54
		  collapsed:: true
			- 23:50 - 23:53
				- DONE [[continue]]
				  collapsed:: true
				  :LOGBOOK:
				  CLOCK: [2022-10-19 Wed 23:50:28]--[2022-10-19 Wed 23:53:34] =>  00:03:06
				  :END:
					- {{embed ((635015ed-91a7-4047-bafd-6f8c911266d4))}}
		- 23:55 - 01:20
		  collapsed:: true
			- DONE [[continue]]： [[毛懋]]今早上 [[打人]] 事件后续 —— 变得 [[讨好]] ，是我们 [[想要的]] 样子吗？ #[[开放式育儿]] #毛懋 #成长
			  id:: 637454a5-6a8a-41d7-8fb8-3e9e0201b7b7
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-19 Wed 23:56:20]--[2022-10-19 Wed 23:56:22] =>  00:00:02
			  CLOCK: [2022-10-19 Wed 23:56:23]--[2022-10-20 Thu 01:19:54] =>  01:23:31
			  :END:
				- #背景
					- 9点回到家以后， [儿子]([[毛懋]])一直是那种 [[乖巧]] [[懂事]]的状态：
						- 我说： [[大大斜坡]] 玩好了，我要放下来了，
						- 他说：好
						- 我说：爸爸要去洗澡了
						- 他说：好。爸爸拿下圆柱体给耨耨玩可以吗？
						- ……
						- #深思
							- 这是 [[讨好]]吗？我 [[不知道]] ，我也 [[不确定]] 。
								- 这是我们想要的样子吗？应该不是，我不希望 [儿子]([[毛懋]]) 一直是这种状态，是可以 [[反抗权威]] 的， [儿子]([[毛懋]]) 。
								  id:: 637454a5-03e8-4888-a100-4a621e8ec737
									- 洗完澡，又想起以前的那个观点：
										- ((62d4e32d-3ea8-440f-85f3-920c13e0f4a1))
										- ((634f70f2-d734-4299-bdd5-393f70d7bc41))
										- 心里一惊啊。
									- 我觉得有几点值得再次的思考：
										- 我们肯定不希望儿子的行为、处事方式来自一个 [[模板]] —— 这个世界的好坏对错，希望由他自己来提出问题、做出判断、做出选择
										  collapsed:: true
											- 如此说来， [[奶奶]]的教育模式， [[规矩派]]，好不好，好。但仅仅只是 [儿子]([[毛懋]]) 学习接收的一面。自然，[我]([[毛义明]])有我的一面。 [[王露]] 有王露的一面。
											  collapsed:: true
												- 我希望我的儿子做人做事，是有选择的。而不是没有选择，不是先强行接受规则，而不知道这套规则背后的得失利弊。相比而言，知道得失利弊后自己做出选择， [儿子]([[毛懋]])对规则的理解更清晰，习惯也更容易养成。
												  collapsed:: true
													- 现实的例子，今天早上起床的发火 —— 摁下 —— 变乖。相隔上一次，只差了1天。前天晚上也是发货—— 摁下 —— 变乖，睡得好。
													  collapsed:: true
														- 这真的好吗，我很怀疑。
										- [[摁下]]要不要做？要做，偶尔做做可以。但一定要 [[警醒]]，这坏处绝对远大于好处 —— 你的儿子会被 [[模板]] 化，一想到这一点，我内心翻涌，接受不了。“摁下”唯一的好处，就是让儿子的日子过的不要太舒服 —— 在他完全 [[放飞自我]] 之后，让他回到正常轨道。
										  collapsed:: true
											- 什么是正常轨道：在 [[自我]] 之中，能控制自己的行为。不过分的“任性妄为”是可以的。能轻微体察他人的行为和情绪，就可以了。 —— 2岁小孩呀。
											  collapsed:: true
												- 有时候我觉得整体说 [[长大了]]，也不好，他就呆在他的年纪里，不好吗？为什么要植入 [[快快长大]] 的讯号？他的进步为什么要和长大了划等号？
												  collapsed:: true
													- 进步他妈的就是进步。回到常识好不好。 [[唯手熟尔]] 和 [[长大了]] 没有半毛钱关系好不好。
													  collapsed:: true
														- 同样的，成熟了，成长了，他妈的也是一种道德绑架式的评价。[[fuck]] —— 这类词连同 [[聪明]] 一并列为禁用词 @ [[王露]]
										- 大部分成人严重性的“过激行为”，其实都可以选择忽视，这个观点我没有改变。那什么才是真正的要引起重视的 [[过激行为]] ？
										  collapsed:: true
											- 很明确的养成坏习惯或导致养成坏习惯的行为 —— 这个最简单，多观察，多思考即可
											- 很明确的感受到 [儿子]([[毛懋]])在反复摩擦你的处事底线，并且带有挑衅的意味，或者他知道这是你的底线，但就是要任性，已经把你搞毛了。这种时候，可以 [[打压]]一下 —— 让他知道，日子没有天天过的那么舒服，男孩子就不要生活在温床里。
											- 要警惕无限的 “睡前[[todo-list]]” 效应，这是王露提出的，我觉的举一反三特别好：原意是随着玩具的增多，玩法的增多，孩子的睡前仪式永远做不完，导致永远无法早睡。导致睡前仪式上瘾。
											  collapsed:: true
												- 举一反三，不要让孩子在他过激之后，产生不该有的判断，导致过激行为上瘾。
												  collapsed:: true
													- 爸爸妈妈总是会在他过激行为的时候特别关注他
													- 爸爸妈妈永远会在他过激行为之后更加爱他——尤其是打骂之后会有特别心疼，还会有礼物给他
													  collapsed:: true
														- 这个在这次巴掌事件后我给了他5辆小汽车，我给的同时，心里也很后怕。
														- btw，我真的很讨厌，买了10辆车，一次给1辆这种做法。我试了几次，我心里还是过不去。我是男人，我也男孩过，我知道小孩的感受，我以后再也不会这么做了，顶多买3套玩具，2套备用，这是提前计划好了的。但绝不会准备好给他5辆，一次给一辆，他妈的，写到这里心里又激动了。我再不会这么做了。这个家里也不允许这么做。 —— 为什么总是要人为的制造 [[不满足]] 的坏习惯呢？！！
														  collapsed:: true
															- 今天你看，为什么给5辆，因为就是 [[耨耨]]还想要啊！你有，为什么不给呢？他妈的全给了会怎么样嘛？！！！！
															  collapsed:: true
																- 但你不给，他会不满足啊！！！！！！！
																  collapsed:: true
																	- 你有就给，没有就不会。真实一点好吧。在小孩子面前玩套路，真他妈的丢人 + 可耻。
											- 关于 [[过激行为]] ，讲到这里很明白了：
											  collapsed:: true
												- 一种是因为家庭环境（模因） + 自身基因产生的：这个扪心自问，我们的基因里没有。事实上耨耨也没有
												- 一种是因为一直不满足 -> 这会导致两种情况
												  collapsed:: true
													- A：不满足 -> 频繁的过激行为发泄情绪
													- B：不满足 -> 把情绪深深的埋在心里
													- 摁下A，会导致B，两种都不是好结果
												- 一种是因为大部分情况满足 + 偶尔不满足 + 一点点的撒娇任性
												  collapsed:: true
													- 我们应该是这种情况，还是什么好说呢，引导引导引导，让孩子知道，发脾气不会得到更多的爱，反而说出自己的需求，商量、谈判，父母才会看见你，接受你，需求也能尽可能的得到满足
										- 2岁了，今年的主题就是深度探索，多学习万事万物的原理。比起恐惧孩子过激行为，应该把更多时间更多精力放在带给 [儿子]([[毛懋]]) 对于这个世界的方方面面的理解上。最后的话字最少，当然也最重要。
									- #link
										- 1
											- ((634fff89-aa56-4a72-8c6e-677fd74eb94a))
											- ((635015ed-131d-44d7-882d-93e9e261aeb8))
											- ((635015ed-1e23-4bb3-8513-501b261a967a))
											- ((635015ed-5306-427b-a621-c2fecb2450ac))
											- ((635015ed-afae-4346-8020-e79d416b5533))
											- ((635015ed-33b2-4d1c-8892-ade42cef7750))
											- ((635015ed-2a30-4367-befd-bf88ca5452b3))
										- 2
											- ((634f70bc-e198-4058-a005-62b8ad89c6c5))
										- 3
											- ((634f70d4-b6b0-457c-8eb0-587b2218dc74))
										- 3
											- ((62d4e32d-3ea8-440f-85f3-920c13e0f4a1))
											- ((634f70f2-013c-4b72-9cd9-e5a24b444f8e))
											- ((634f70f2-6882-4d3e-b338-c3bfa57ef1da))
											- ((634f70f2-2410-475e-b7b1-2e9e2212161b))
											- ((634f70f2-922a-4481-bf13-74cbe17cda81))
			- 00:07
			  collapsed:: true
				- CANCELED [workflow]([[workflow]])： fav -> `[[陪儿子玩、放电、投入高质量的爱]]` -> `[[陪儿子玩、放电、投入高质量的爱]]` #namespace  #recycle
				  id:: 635020af-852c-4356-8910-e774991e29bf
				  :LOGBOOK:
				  CLOCK: [2022-10-20 Thu 01:23:51]--[2022-10-20 Thu 01:26:06] =>  00:02:15
				  :END:
			- 00:20 - 00:36
				- #continue
					- #闪念
						- 当你掐断对方的话，阻止对方说下去，或者其他一些反抗他人的行为，这些行为都是你的 [[自我]]出发的，是你的自我对你的 [[精神世界]]的一种自我保护。 —— 即所谓的怕收到伤害、怕被拒绝、怕被否定折射出来的行为，都是在保护你自己的——精神世界。 #金句
						  id:: 635023fa-811d-4aa5-8ad3-1df2639c6704
				- #回忆
					- 9年前，从 [[南天广场]]走到花城汇，今天再次重走，一样的感受，说了一样的话，虽然心境有所不同——这只是 [[经历风雨]] 后的转变，说明一个问题：人永远只可能按自己的方式活。不管你愿不愿意、承不承认。你的精神世界指挥着你的行为，这就是自我。你内耗、怕受伤害、胆怯都改变不了这一事实。这是常识。只不过，你无法自洽是因为，面对这花花世界，你的眼睛、焦点停留在了外部，太少关心你的精神世界里的那个我了。他就缩在角落，孤零零的，可怜巴巴的指挥着你所有的行为。你缺少的不是自我，也不需要找到自我，他就在那里，你只需要的是看见他，多看看他，看清他，接纳他，认可他……和他手牵手一起面对这个世界，说好再也不分开。——毕竟，你的精神世界，有且只可能你一人进出。那些说分享自己的精神世界给你的人，都是鬼话，那些愿意把自己的精神世界分享给别人的人，也是幻想和妄念，是对自我的放弃。因为你分享出去的那个世界，必定包装、描绘过，但里面唯独没有真实的自己。
						- #link
							- ((635015ed-b46c-486e-8bcb-0301281e6227))
							- ((635023fa-811d-4aa5-8ad3-1df2639c6704))
							- ((634f70bc-0e62-4fbf-8709-d18c3ca7fb52))
							- ((634f70bc-6965-490c-af0f-edc11833cb6e))
							- ((634f70bc-afeb-43a8-b7bc-ceffed18bf84))
							- ((634f70bc-0456-45ca-b4b2-d12438d73a17))
					-
		- 01:21 - 01:26
		  collapsed:: true
			- {{embed ((635020af-852c-4356-8910-e774991e29bf))}}
- [[Comments]]
  collapsed:: true
	- #回忆
		- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 手表显示，3点04入睡，08:39醒来，比较准，睡了5小时34分。 [[深度睡眠时长]] 只有 1小时35分。目标是超过2小时，理想是达到3小时。#2小时深度睡眠 #3小时深度睡眠  -> ((634133c8-2ff6-4c3b-9aab-660a9616f531))