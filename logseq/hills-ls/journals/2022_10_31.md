# 01:17 - 01:45（28分）
id:: 63612f01-cd81-407c-9ed2-f7044bf1eb09
collapsed:: true
	- DONE [[闪念]] ： [[资源]] -> [[namespace]] -> [[Favorites]] #recycle #ending #workflow #特殊标签
	  id:: 635eb1a9-8179-40f2-8de6-6f326b68de18
	  collapsed:: true
	  :LOGBOOK:
	  CLOCK: [2022-10-31 Mon 01:17:32]
	  CLOCK: [2022-10-31 Mon 01:17:39]
	  CLOCK: [2022-10-31 Mon 01:17:52]--[2022-10-31 Mon 01:45:24] =>  00:27:32
	  :END:
		- #recycle
		  collapsed:: true
			- ((62ef5140-03ea-4c43-b000-53523feff649))
			- ((63730f81-4e33-4214-bff3-d9ddd9262f27))
			- ((63730f2c-61d0-4601-81d6-28b739780533))
			- ((63730f2b-e2c6-4625-b899-7454aa185eac))
			- ((63730f2a-2048-4601-a238-2cacd0736a70))
			- ((63730f27-2d19-44de-adf8-cc3b61626983))
			- ((636a77a0-5ce3-4957-9219-884647bec9ab))
			- ((636a779d-f00e-46a8-bf30-ebf774ef8f20))
			- ((6367e344-7944-45e8-9bfd-38b700260c5f))
			- ((6367d7cc-7e49-4c57-abb6-6087738366ca))
			- ((6367d7cc-92a9-4097-90d0-42a5db2238aa))
			- ((6367d7cc-abca-4811-84b3-c54bf144b745))
			- ((634f70bc-160e-442c-9b6d-b6161997875e))
			- ((6360eb68-7a22-4760-8584-f6feb1598378))
			- ((6360eb68-d555-4af9-8032-d6f8daa1759a))
			- ((6360eb68-b1cc-4d14-b119-06e2ccd7fc71))
			- ((635123cb-6079-4cf3-aab4-36a872807aa9))
			- ((634f70bc-3741-40d5-9c2e-67474901f3c2))
			- ((635015ed-41da-4cf1-8b19-330d650b9dcd))
			- ((6350baf9-ec2c-464e-b9a6-a917b2f81a54))
			- ((635eb4c7-7e1c-4ce1-8d51-e90acce322ee))
			- ((635eb4c8-3953-4054-ba13-34acc5792b2d))
			- ((635eb4c8-ad4a-4457-ad71-9c4a9aee688d))
			- ((635eb4c8-a952-4072-9571-42e694392e64))
			- ((635eb4c8-d213-45db-920f-cdbc4cb11ba8))
			- ((635eb4c8-55d4-4d59-a577-6061a71bc9ee))
			- ((635eb4c9-8986-4997-835e-25fef40f992c))
			- ((635eb4c9-62c6-4b32-932a-b68d7407afd7))
			- ((635eb4c9-ffaa-44c7-95c1-b05925f69f29))
			- ((635eb4c9-4072-4b7d-b5b2-1a58b8fcc2f0))
			- ((635eb4c9-1109-4d88-9b19-b38d78a702f6))
			- ((635eb4ca-e6b9-42d3-b59e-794516043809))
			- ((635015ed-06db-4bce-819b-98d588b1d630))
			- ((635eb4ca-36a6-4e80-b808-fa46c87e8a42))
			- ((635eb4ca-387e-416e-9453-8b5a7633810a))
			- ((635eb4ca-36b8-4d89-9c8f-ab1a55b134b9))
			- ((635eb4ca-e6b9-42d3-b59e-794516043809))
			- ((635eb4ca-5318-4037-977f-3105a5801f6b))
			- ((635eb4ca-e065-4462-992f-66eeaa6fe7b1))
			- ((635eb4ca-387e-416e-9453-8b5a7633810a))
			- ((635eb4ca-36a6-4e80-b808-fa46c87e8a42))
			- ((635020af-852c-4356-8910-e774991e29bf))
			- ((635eb4ca-36b8-4d89-9c8f-ab1a55b134b9))
			- ((635eb4cb-5401-474d-9638-58e06b8db78f))
			- ((635eb4cb-513e-4916-89d2-741a83867e4a))
			- ((634cbac1-1b02-4556-800a-7b9235fba9be))
			- ((635eb4cb-a39e-4f4b-8388-ef34d6fe1cf6))
			- ((635eb4cc-c771-42db-a135-fec1ad1c7955))
			- ((635eb4cc-836c-49b9-960c-166ba3616390))
			- ((63463092-a5ef-436c-950a-783fc3a86193))
			- ((635eb4ce-5e9f-4bcc-b19a-e9a14fcd3e6c))
			- ((635eb4d0-5040-4ccd-abaf-2a25db599041))
			- ((635eb4d0-bad6-42a6-a841-7a4c62b9de46))
			- ((635eb4d0-07df-4e9d-9d78-53616678ecfa))
			- ((635eb4d0-633d-4ad5-8fff-1d64ca84217d))
			- ((635eb4d0-1df8-45b6-99e3-fe351a953c30))
			- ((635eb4d0-e754-422e-8315-fb136882fc59))
			- ((635eb4d2-4a45-4996-a8d8-aa66fd3b690a))
			- ((635eb4d3-5512-4e8c-9d64-7224cfe69ce4))
			- ((634e63a6-ceb6-426b-a884-bdb01d518e4c))
			- ((635eb4dc-6ae0-47c5-aefb-20f4e15b8767))
			- ((635eb4ca-6655-42bf-a0eb-73f663f69b78))
			- ((635eb4ca-5a4d-4706-932f-6ca0db05e196))
			- CANCELED [[action]]
				- CANCELED [[洗屁股]]
			- CANCELED [[status]]
			- CANCELED [[habit]]
			- CANCELED [[data]]
			- CANCELED [[zettel]]
			- CANCELED [workflow]([[workflow]])
			- ((6360b68f-ccff-4f03-b163-86270e0de30f))
			- ((6360b68f-1ba9-4d5d-8de8-3a3dcc964d50))
			- ((634f70bc-8ca5-46e6-8183-d1ed5b97de17))
			- ((6350ee8f-bce1-449c-8d10-32ee8fb2d32b))
			- ((6360b69f-2fe2-46a8-a2f5-ba07ec37eac5))
			- ((6360b69f-1adc-4d42-9d37-c6ea7ad79f79))
			- ((63156189-ae2b-4785-ae9d-920e6d6a7edd))
			- ((6300f067-fdf8-4d78-930a-3da642aae2e3))
			- ((6302cd50-7848-45bf-b95b-6bd1e21d4a05))
			- ((6360b6a1-2a43-4a88-8667-c1e8fce0abb2))
			- ((6360b6a1-76e3-478f-86d9-867265b7a3fd))
			- ((6360b69e-2164-4030-ac79-2de49f9315cc))
			- ((6360b69e-fb2e-4383-972b-939f633827fe))
			- ((634850c7-abad-4a5e-89e9-c5bcfe6e9088))
		- #草稿
		- #step
		  collapsed:: true
			- CANCELED [[drink-water]]
			- ((62efedee-e55f-496f-8865-21cfa6fbc8c3))
			- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
			- CANCELED [[pee-time]]
			- CANCELED [[深度睡眠时长]]
			- CANCELED [[沙县]]
			- CANCELED [[散步]]
			- CANCELED [[link]]
			- CANCELED [[link]]
			- CANCELED [[Year]]
			- CANCELED [[tags]]
			- CANCELED [[rate]]
			- CANCELED [[水电煤]]
			- CANCELED [[物业]]
			- CANCELED [[宽带]]
			- CANCELED [[阿姨]]
		- #link
		  collapsed:: true
			- ((6315d0cc-e8b6-427d-860b-71abdd33faa1))
			- ((6314bdc1-2459-4a24-8ca8-24c31602c695))
			- ((6360b69f-bcfe-4faf-b679-01cc6df908a5))
- # 02:01 - 12:44（12小时43分）
  collapsed:: true
	- DONE [[上床]]
	  collapsed:: true
	  :LOGBOOK:
	  CLOCK: [2022-10-31 Mon 02:02:17]--[2022-10-31 Mon 08:42:13] =>  06:39:56
	  :END:
		- ((651aa257-6b75-4549-82e2-69f005661986))
	- #log
		- 08:35 - 08:46
		  collapsed:: true
			- :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 08:43:55]--[2022-10-31 Mon 08:52:38] =>  00:08:43
			  :END:
		- 08:46 - 08:52
		  collapsed:: true
			- ((62fa66a5-2fca-4bba-98dc-dc88140218fb))
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 08:51:25]--[2022-10-31 Mon 08:51:26] =>  00:00:01
			  :END:
		- 08:52 - 09:04
		  collapsed:: true
			- :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 08:53:46]--[2022-10-31 Mon 09:04:28] =>  00:10:42
			  :END:
		- 09:04 - 09:53
		  collapsed:: true
			- DONE 准备出门，去 [[自习室]]
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 09:31:15]--[2022-10-31 Mon 09:53:14] =>  00:21:59
			  :END:
			- 09:11 - 09:14
			  collapsed:: true
				- [[回忆]]
				  collapsed:: true
					- [儿子]([[毛懋]])饭还没吃完， [老婆]([[王露]])就起身拿他穿的衣服什么的，搞的情绪又 [[崩溃]]，本来就防着她 [[上班]]，我觉得可以专心陪他吃完饭。
			- 09:14 - 09:35
			  collapsed:: true
				- DONE [[煤气灶]]要换 [[电池]]了 
				  id:: 635f8975-5284-4d49-a1b2-7ae754087607
				  SCHEDULED: <2022-10-31 Mon .+1d>
				- DONE [workflow]([[workflow]])： [[首行]] 如果不是 [[h1]] [[字体]] ，左侧小点的位置无法与文字 [[对齐]] ，包括[[page]] property [[property]] ，这是最难受的 -> 最终可能是需要 :has 伪类更新 
				  id:: 635f2a67-0155-43a6-8842-e617c28177be
				  collapsed:: true
				  :LOGBOOK:
				  CLOCK: [2022-10-31 Mon 10:26:38]
				  :END:
				  #logseq-css  #pass
					- #step
						- DONE [[Discord]] 求助
						  id:: 63612f01-da28-4565-91c6-0c683e6001a1
							- #回忆
								- 请教一个 [[logseq-css]] 写法，现在遇到了困难，非常难受。
								  id:: 6364c6e4-059c-4ac7-ad49-284b01dde1ac
									- 先说我的目的：==聚焦写作时，让页面的第一行可以自动放大字体，并且左侧小点可以与文字保持对齐==
										- 我研究了 `h1` 的样式，发现它和正文的样式区别是：
											- 1、`h1` 本身的定制样式
											- 2、整个block（包含前面的小点） 加入了样式 `align-items:baseline` ，正是这个样式使得左侧的小点可以和 `h1` 对齐
										- 第一个好解决，我通过查找结构，因为都是嵌套关系，我只要找到页面的第一个 `block-content-inner>div>span.inline`  ，精准加上 `h1` 的样式就可以实现
										- 困扰的地方在第二点，当我在整个block上加入了 `align-items:baseline` 时，带有 `h1` 样式的block 倒是对齐了，但==没有h1样式的block，左侧小点反而对齐不了了==。
											- 例如：
												- `page` 下的 `property`
												- `#标签`
											- 我试过各种办法（在我有限的html css认知下），始终都没办法修复这一问题。
										- 附上视频，希望有能力的人能帮帮我，强迫症太难受了。
										- 附上我写的css代码
									- 11月4日 取消任务，中英论坛都无人相助
									  id:: 6364c6e4-11ee-471e-84f9-d5d0b5a13825
									- ((6364dd44-d6e1-4555-a5a0-7d2b2a5d4c20))
									  id:: 6364e6ef-3dec-4362-b6d1-555f74ab62f2
									- ((6364e71a-7008-4c8d-bcad-376f7b88ffd3))
									  id:: 6364e78e-800e-4938-93be-5d6f8837c0bf
							- #ending
							  id:: 6364e688-7bb0-48a7-84ec-ff660384a250
								- #+BEGIN_SRC css
								  .pre-block { background: var(--ls-secondary-background-color); border-radius: 4px; padding: 8px 6px;}
								  .pre-block .block-control:first-child { display: none; }
								  .pre-block .editor-inner textarea { font-size: 14px !important; margin: 0 !important; line-height: 1.75;}
								  .pre-block > div { align-items: flex-start !important;}
								  .pre-block > div > *:first-child { float: unset;}
								  .pre-block .block-content-wrapper {width: auto; flex: 1;}
								  #+END_SRC
				- CANCELED 如何提交 [[logseq-css]]主题到 [[logseq]] #recycle
				  id:: 63982e3c-3efb-4612-b661-dfa44c81b992
		- 09:54 - 10:12
		  collapsed:: true
			- #回忆
			  collapsed:: true
				- 早上看 [老婆]([[王露]]) 给 [儿子]([[毛懋]]) 喂饭开始，到我们出门，我感觉到了一种凌乱的感觉——老婆脑子里记挂了好几件事：喂饭、洗漱、收拾、安抚儿子、信用卡还款…… 自己也做不到坦然，面对儿子的夺命call，也做不到100%的耐心。
				  #育儿 #自我管理 #gtd #情绪管理 #任务管理 #workflow
				  collapsed:: true
					- 从旁观者的角度，我觉得是可以优化一下的：
					- 首先：心里的每一件事可以规划-分解，直到可以被执行的那一步。
					  collapsed:: true
						- 例如：
							- 信用卡还款：1、垫资公司用中信卡转小明；2、晚上取款 - 存民生银行
							- 要上班，安抚儿子：0、不要时不时提醒娃妈妈要上班；1、出门的前1分钟，再说；2、迅速出门；
						- 每天真的只要花30分钟认真去规划即可。
					- 其次：一次只做一件事，直到这件事被完全确认完成了
						- 例如陪孩子吃饭：
							- 你觉得娃“快”吃好了，你就想着去做其他事，但他还是坐在饭桌上，很无助
							- 你被不停的召唤，“其他事”也没办法顺利的完成，还要增加更多的词汇去强调 [儿子]([[毛懋]])去管理自己的情绪。其实大人自己也没管理好自己。
						- 这些我觉得都可以用“规划”和“只做一件事”来避开冲突，并且很好解决。
						- 还有就是，那个情绪管理，我早上起初听，真的是觉得很好的，终于可以看见自己的情绪了。但是，是不是不要一有情绪就拿这个套？——
						  id:: 635f2df7-8a88-4fce-9857-f5137e52af8b
							- 光是今天，1个半小时里，我听到了太多这个情绪对照的词汇了。但但但很多大人没做好产生的情绪啊，为啥要小孩先管理情绪呢。而且很容易又变成一种束缚型的规则，我生气了——我就是红色——我不应该有红色，我必须要粉色。
					- 最后一点，说“不”的次数真的挺多的，有时候应该想想自己的生活节奏是否稳定，我感觉“不”字如此轻松的说出口，大人心急想要纠正是很大原因（没有转念一想，转化“不”字的精力），大人为什么心急？因为心里放了很多事，缺少规划……
		- 10:13 - 10:18
		  collapsed:: true
			- DONE [[continue]]
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 10:18:17]--[2022-10-31 Mon 10:18:32] =>  00:00:15
			  :END:
				- {{embed ((635eb4d1-4274-4c31-9344-5abf5265240c))}}
		- 10:18 - 10:21
		  collapsed:: true
			- DONE [[continue]]
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 10:18:31]--[2022-10-31 Mon 10:21:17] =>  00:02:46
			  :END:
				- {{embed ((635eb4ce-664d-4d08-8ba2-d4c4da6f4d25))}}
		- 10:21 - 10:25
		  collapsed:: true
			- DONE [[continue]]
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 10:22:10]--[2022-10-31 Mon 10:25:32] =>  00:03:22
			  :END:
				- {{embed ((6329e2b4-935e-4b64-bef8-89a91bc08b0e))}}
		- 10:25 - 11:19
		  collapsed:: true
			- ((635f2a67-0155-43a6-8842-e617c28177be))
			- 10:29 - 10:36
			  collapsed:: true
				- {{embed ((635eb4d1-4274-4c31-9344-5abf5265240c))}}
			- 11:00 - 11:05
			  collapsed:: true
				- #break
		- 11:21 - 11:24
		  collapsed:: true
			- #回忆
				- [老婆]([[王露]])还是教 [[毛懋]] 认识情绪了，好棒！—— [[情绪小怪兽]]
				  id:: 635f3f3b-bed5-46e4-824d-60f787857402
					- #link
						- ((635f2df7-8a88-4fce-9857-f5137e52af8b))
		- 11:24 - 12:33
		  collapsed:: true
			- :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 11:25:09]--[2022-10-31 Mon 11:27:26] =>  00:02:17
			  :END:
				- #回忆
					- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 昨晚02:14睡的，早上8点27醒来，睡了6小时4分， [[深度睡眠时长]]有2小时18分！！！哈哈哈哈 而且还都是整段整段的 #output
						- #截图
							- ![WechatIMG217.jpeg](../assets/WechatIMG217_1667186836535_0.jpeg){:width 245}
			- :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 11:27:31]--[2022-10-31 Mon 16:56:59] =>  05:29:28
			  :END:
				- ((6358817a-f5a9-4934-9074-3f2b01f77f86))
				- ((6357e8e1-83a2-455d-be4e-282356ffc5b4))
			- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))  
			  id:: 635fead8-490f-4851-82a5-4d141958edef
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 11:29:12]--[2022-10-31 Mon 16:23:20] =>  04:54:08
			  :END:
			  ```calc
			  104+60.4
			  ```
			- 11:30 - 12:32
				- ((635f2a67-0155-43a6-8842-e617c28177be))
				- DONE [workflow]([[workflow]])修复放大首行带来的 card样式问题
				  id:: 635f449a-0602-420a-b659-c4444907ae19
				  :LOGBOOK:
				  CLOCK: [2022-10-31 Mon 11:44:29]
				  CLOCK: [2022-10-31 Mon 11:44:32]
				  :END:
				- 11:59 - 12:32
					- DONE [[continue]] ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
					  id:: 63982e3c-e4d7-4f03-9285-9f2e83dd736d
					  collapsed:: true
					  :LOGBOOK:
					  CLOCK: [2022-10-31 Mon 12:32:29]--[2022-10-31 Mon 12:32:30] =>  00:00:01
					  :END:
		- 12:33 - 12:40
		  collapsed:: true
			- CANCELED [[Discord]]-解决问题
			  id:: 636683cb-c63e-44c0-922e-e6c25958159c
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 12:33:38]
			  CLOCK: [2022-10-31 Mon 12:33:41]--[2022-10-31 Mon 12:40:29] =>  00:06:48
			  :END:
		- 12:44
		  collapsed:: true
			- #break
- # 13:45 - 14:06（21分）
  collapsed:: true
	- #continue
		- {{embed ((635bad81-93cd-4999-9d05-e394180de3ae))}}
- # 14:07 - 16:22（2小时15分）
  collapsed:: true
	- DONE [[continue]] ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
	  id:: 63982e3c-dae4-448c-96a3-c73a904c2a58
	- #log
	  collapsed:: true
		- 14:51 - 15:04
			- #continue
				- {{embed ((635eb4d1-4274-4c31-9344-5abf5265240c))}}
- # 16:42 - 16:56（14分）
  collapsed:: true
	- {{embed ((6358817a-f5a9-4934-9074-3f2b01f77f86))}}
- # 16:58 - 21:07（4小时9分）
  collapsed:: true
	- #break
		- DONE [[王露]] [[散步]]
		  :LOGBOOK:
		  CLOCK: [2022-10-31 Mon 16:58:14]
		  CLOCK: [2022-10-31 Mon 16:58:19]--[2022-10-31 Mon 18:35:40] =>  01:37:21
		  :END:
	- #log
		- 18:35 - 19:37（1小时2分）
		  collapsed:: true
			- DONE [[continue]]
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 18:42:22]--[2022-10-31 Mon 19:36:36] =>  00:54:14
			  :END:
				- {{embed ((635bad81-93cd-4999-9d05-e394180de3ae))}}
		- 21:05
			- #continue
				- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
				- #回忆
					- 接了 [老妈]([[丁满冬]])的电话，我感觉 [[爸爸]]得了 [[精神病]]了。
						- 真可怜。
- # 21:12 - 23:32（2小时20分）
  collapsed:: true
	- ((635f8975-5284-4d49-a1b2-7ae754087607))
		- DONE 再买了一箱 [[啤酒]]
	- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
	  :LOGBOOK:
	  CLOCK: [2022-10-31 Mon 21:14:00]
	  CLOCK: [2022-10-31 Mon 21:14:03]
	  CLOCK: [2022-10-31 Mon 21:15:05]
	  CLOCK: [2022-10-31 Mon 21:15:07]
	  CLOCK: [2022-10-31 Mon 21:15:18]--[2022-10-31 Mon 21:45:23] =>  00:30:05
	  :END:
	- #log
		- 21:45 - 23:32（1小时47分）
		  id:: 635fd178-f75b-41e4-99a1-97c589838b45
		  collapsed:: true
			- DONE [[continue]] ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
			  id:: 635fe66b-d472-47ee-a7a4-c23b9ca76073
			  :LOGBOOK:
			  CLOCK: [2022-10-31 Mon 23:30:31]--[2022-10-31 Mon 23:30:32] =>  00:00:01
			  :END:
- # 23:57 - 01:28（1小时31分）
  collapsed:: true
	- DONE [[continue]]
	  :LOGBOOK:
	  CLOCK: [2022-10-31 Mon 23:58:40]--[2022-10-31 Mon 23:59:42] =>  00:01:02
	  :END:
		- {{embed ((635fead8-490f-4851-82a5-4d141958edef))}}
	- #log
		- 23:59 - 00:57
			- :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 00:00:54]--[2022-11-01 Tue 00:25:19] =>  00:24:25
			  :END:
				- ((62efedee-e55f-496f-8865-21cfa6fbc8c3))
		- 01:28
			- #break