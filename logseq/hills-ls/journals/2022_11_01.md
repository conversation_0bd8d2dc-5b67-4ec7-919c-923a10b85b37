- **[[Morning-Page]]**
- [[Society]]
- [[Family]]
- [[Myself]]
  collapsed:: true
	- 01:38
	  collapsed:: true
		- #闪念
			- DONE [[家]]
			  id:: 63608c59-01dc-4504-87c2-58f93e37b848
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 01:37:50]--[2022-11-01 Tue 11:03:37] =>  09:25:47
			  :END:
		- #smoking
	- 01:45
	  collapsed:: true
		- #continue
			- ((651aa257-6b75-4549-82e2-69f005661986))
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 01:46:33]--[2022-11-01 Tue 09:23:24] =>  07:36:51
			  :END:
	- 09:23
	  collapsed:: true
		- #[[回忆]]
			- 昨天 [老婆]([[王露]])说她一下子买了3000多的东西，我说，是不有种[[穷太久]]了被[[接济]]到了的感觉，老婆说是。  #纪念日 #穷太久 #负面情绪 #难过 
			  SCHEDULED: <2024-11-01 Wed .+1y>
			  id:: 63607689-10ae-4128-88e8-8014bab90f7b
			  collapsed:: true
			  :LOGBOOK:
			  * State "DONE" from "TODO" [2022-11-01 Tue 14:05]
			  :END:
				- 早上新买的 [[简爱]] [[酸奶]]到了，好几箱，[儿子]([[毛懋]])一口气喝了两包，回想了下确实好久没喝了，又[[心酸]]，又[[欣慰]]
					- 又想起昨天下午和老婆[[散步]]，我跟 [老婆]([[王露]]) [[坦白]]了 [[iPad]]卖掉了的事情，老婆大为惊讶，我转手拿出转账记录，回想那时候，当听到老婆支付宝微信都没钱，身上一分钱都没有的时候，我心里的那种焦急、恐惧，我必须要有解决方案，必须要让老婆手里有钱，所以我就算跟老婆说出实情也没有用，老婆比我更没办法…… 现在想想都感到后怕，只能说，一切都过去了，虽然现在的钱不是现在赚的，但总算摆脱了目前日日穷日日被追债的窘境，剩下的继续加油吧。
						- 今天还是 [超级元素项目启动3周年](((635feb05-4026-4d9c-97c0-0e0036f32e31))) 3年前的今天早上8点钟，我来到了 [[华建大厦]]，和 [[王永超]]谈 [[补提液]]项目。3年了，除了产品堆在仓库以外，毫无作为。我应该感到惭愧，我应该吸收过去3年创业的种种经历，沉淀，输出，告诉自己，你是对的。
							- 所以纪念，这就是3周年纪念了，不必再和任何人提起。
								- 所以纪念，也要纪念今天、过去穷过的日子，恰逢3周年，从穷到-被接济上，超级元素3周年，一切仿佛都是冥冥中注定，一切仿佛都是你生在这个世界想有所作为、自己又不够格而必要经历的功课。
	- 09:31
	  collapsed:: true
		- #闪念
			- DONE 给 [儿子]([[毛懋]]) [[洗屁股]]
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 09:31:49]
			  CLOCK: [2022-11-01 Tue 09:31:55]--[2022-11-01 Tue 09:40:31] =>  00:08:36
			  :END:
	- 09:42
	  collapsed:: true
		- #continue
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 09:43:31]--[2022-11-01 Tue 09:55:07] =>  00:11:36
		  :END:
			- {{embed ((63607689-10ae-4128-88e8-8014bab90f7b))}}
	- 09:58
	  collapsed:: true
		- #回忆
			- ((62fa66a5-2fca-4bba-98dc-dc88140218fb)) => 50 20
	- 10:03
	  id:: 6361d335-4d8f-445f-8355-4f0f061af9fb
	  collapsed:: true
		- #continue
		  collapsed:: true
			- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 10:06:09]--[2022-11-01 Tue 11:03:14] =>  00:57:05
			  :END:
		- #log
		  collapsed:: true
			- 10:06 - 10:11
				- #回忆
					- [[咖啡]]忘记拿了，又折回去
			- 10:12 - 10:54
				- #闪念
					- DONE [[理发]]
				- #log
					- 10:16 - 10:20
						- #continue
							- ((651aa256-3898-464e-bd91-6752c7aed81e))
					- 10:21 - 10:35
						- #闪念
							- DONE [[想买]]：[[大众点评]] [[哲艺]] [[创意总监]] [[洗剪吹]]次卡，138元
							  :LOGBOOK:
							  CLOCK: [2022-11-01 Tue 10:21:35]
							  CLOCK: [2022-11-01 Tue 10:21:51]--[2022-11-01 Tue 10:27:43] =>  00:05:52
							  :END:
								- #then
									- CANCELED [[哲艺]]：现场充值2000打8折，3000打7折（110.6），后者比较划算
									  id:: 63f603cd-14f0-4576-970c-7ecd9d316572
					- 10:35 - 10:49
						- #闪念
							- DONE 研究 [[RoamResearch]] [[快捷键]]  #[[keyboard-shortcuts]] #pass 
							  id:: ************************************
							  link:: https://zhuanlan.zhihu.com/p/362414622
							  :LOGBOOK:
							  CLOCK: [2022-11-01 Tue 11:10:36]--[2022-11-01 Tue 11:12:56] =>  00:02:20
							  :END:
	- 11:04
	  id:: 63621f80-42cd-449d-8f8d-d2369816cb39
	  collapsed:: true
		- [[自习室]]
			- #[[continue]]
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 11:04:16]--[2022-11-01 Tue 11:09:37] =>  00:05:21
			  :END:
				- {{embed ((63608c59-01dc-4504-87c2-58f93e37b848))}}
	- 11:09
	  collapsed:: true
		- #continue
			- {{embed ((************************************))}}
	- 11:13
	  collapsed:: true
		- #回忆
			- ((6353a251-268b-4d21-a10d-e5e664f14e47))：昨天应该是2点多睡的，早上9点多起来，睡足了7个多小时。很棒棒
		- #continue
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 11:14:47]
		  :END:
			- ((6306282f-6ee9-447f-abda-1b78da19648b))
			- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a)) 
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 11:15:02]
			  :END:
			  ```calc
			  3+138+68
			  ```
	- 11:15
	  collapsed:: true
		- #闪念
			- ((651aa256-9459-42af-8400-536aadc21970))
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 11:27:53]--[2022-11-01 Tue 11:29:06] =>  00:01:13
			  :END:
	- 11:29
	  collapsed:: true
		- #continue
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 11:38:34]--[2022-11-01 Tue 18:59:28] =>  07:20:54
		  :END:
			- {{embed ((6306282f-6ee9-447f-abda-1b78da19648b))}}
		- #log
		  collapsed:: true
			- 11:30 - 11:38
				- #continue
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 11:30:57]--[2022-11-01 Tue 11:38:54] =>  00:07:57
				  :END:
					- {{embed ((63608c59-01dc-4504-87c2-58f93e37b848))}}
			- 11:45 - 11:55
				- #continue
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 11:52:17]--[2022-11-01 Tue 11:55:16] =>  00:02:59
				  :END:
					- {{embed ((63607689-10ae-4128-88e8-8014bab90f7b))}}
			- 11:57 - 12:06
				- #break
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 11:57:34]--[2022-11-01 Tue 12:06:12] =>  00:08:38
				  :END:
					- #smoking
			- 12:06 - 13:59
				- #continue
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 12:06:57]--[2022-11-01 Tue 12:26:30] =>  00:19:33
				  CLOCK: [2022-11-01 Tue 13:33:09]--[2022-11-01 Tue 13:59:55] =>  00:26:46
				  :END:
					- {{embed ((635eb1a9-8179-40f2-8de6-6f326b68de18))}}
				- #log
					- 12:25 - 13:28
						- [[break]] [[中饭]] 和 [[王露]]
						  :LOGBOOK:
						  CLOCK: [2022-11-01 Tue 12:26:18]--[2022-11-01 Tue 13:28:05] =>  01:01:47
						  :END:
					- 13:30 - 13:57
						- #continue
							- ((651bc212-523a-48b7-bce5-e444544ae14b))
							  :LOGBOOK:
							  CLOCK: [2022-11-01 Tue 13:30:29]
							  CLOCK: [2022-11-01 Tue 13:30:32]--[2022-11-01 Tue 13:57:07] =>  00:26:35
							  :END:
					- 13:57 - 13:57
						- #闪念
							- DONE [看]([[想看]])[[黑亚当]]
							  id:: 6360b68e-4a82-433e-a065-3aceea5f5463
							  rate:: ★★★
							  collapsed:: true
							  :LOGBOOK:
							  CLOCK: [2022-11-01 Tue 14:17:58]--[2022-11-01 Tue 16:18:36] =>  02:00:38
							  :END:
			- 14:17 - 16:24
				- #continue
					- ((6360b68e-4a82-433e-a065-3aceea5f5463))
			- 16:24 - 17:09
				- #continue
					- ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
					  id:: 63982e3c-4bf7-4379-8ee6-c77c35b0c129
					  collapsed:: true
					  :LOGBOOK:
					  CLOCK: [2022-11-01 Tue 17:06:01]--[2022-11-01 Tue 17:06:03] =>  00:00:02
					  :END:
			- 17:09 - 17:17
				- [[break]]
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 17:12:48]--[2022-11-01 Tue 17:17:29] =>  00:04:41
				  :END:
			- 17:17 - 17:20
				- #continue
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 17:17:43]--[2022-11-01 Tue 17:20:00] =>  00:02:17
				  :END:
					- {{embed ((62e73be4-a0e8-4653-b2c3-0ec2ea52397f))}}
			- 17:23 - 17:37
				- #闪念
					- DONE [workflow]([[workflow]])： [[logseq]] 清理空白页面
					  :LOGBOOK:
					  CLOCK: [2022-11-01 Tue 17:23:31]--[2022-11-01 Tue 17:37:44] =>  00:14:13
					  :END:
				- #log
					- 17:25 - 17:35
						- #continue
						  :LOGBOOK:
						  CLOCK: [2022-11-01 Tue 17:25:44]--[2022-11-01 Tue 17:35:25] =>  00:09:41
						  :END:
							- {{embed ((635eb1a9-8179-40f2-8de6-6f326b68de18))}}
			- 17:37 - 18:29
				- [[break]]
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 18:03:49]--[2022-11-01 Tue 18:29:54] =>  00:26:05
				  :END:
			- 18:30 - 18:59
				- #continue
					- ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
					  id:: 63982e3c-b746-49f2-85be-51e5570af10b
					  :LOGBOOK:
					  CLOCK: [2022-11-01 Tue 18:30:32]--[2022-11-01 Tue 18:59:53] =>  00:29:21
					  :END:
	- 19:16
	  id:: 63622097-e70d-4716-abf9-53b3263eed97
	  collapsed:: true
		- [[家]]
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 19:16:24]--[2022-11-02 Wed 01:39:18] =>  06:22:54
		  :END:
		- 晚饭
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 19:16:52]--[2022-11-01 Tue 19:38:18] =>  00:21:26
		  :END:
	- 19:38
	  collapsed:: true
		- #闪念
			- DONE [[logseq]] 新的 [[时间记录]] 方式 #ending
			  id:: 6361d335-873b-476c-b465-6d2b74356639
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 19:38:48]
			  CLOCK: [2022-11-01 Tue 19:39:19]
			  CLOCK: [2022-11-01 Tue 20:47:31]--[2022-11-01 Tue 21:03:19] =>  00:15:48
			  :END:
				- #step
					- DONE 2、以 [[journal-page]] 的第一个时间戳开始，无限向内缩进，无限聚焦，时间线也可以被追踪，有上下文
					  id:: 651c7c91-306f-414e-b651-8c195517d776
					- DONE 3、当聚焦一个主任务时产生的分心，用 log + 闭合的时间单元
					- DONE 5、第二个时间单元开始，bl+ref 时间戳到首页
					  :LOGBOOK:
					  CLOCK: [2022-11-03 Thu 14:09:52]--[2022-11-03 Thu 14:09:53] =>  00:00:01
					  :END:
				- #recycle
				  collapsed:: true
					- CANCELED 1、以 [[目的地]]为顶层时间块
					- CANCELED 4、一个时间单元里，只出现一个log
					- ((6363595f-4cf1-411e-a125-a35b4c52cdfd))
	- 20:49
	  collapsed:: true
		- #continue
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 20:50:10]
		  CLOCK: [2022-11-01 Tue 20:50:14]--[2022-11-01 Tue 20:52:23] =>  00:02:09
		  :END:
			- {{embed ((6329e2b4-935e-4b64-bef8-89a91bc08b0e))}}
	- 20:52
	  collapsed:: true
		- #continue
			- ((651bc21c-3131-406f-9608-99fda127b5e3))
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 20:53:05]
			  CLOCK: [2022-11-01 Tue 20:53:12]--[2022-11-01 Tue 21:31:27] =>  00:38:15
			  :END:
				- #故事
					- 疫情人生：10月28号 [[内蒙古工业大学]] 的 [[大学生]]从 [[呼市]] -> [[包头]]的火车上，大学生被检测出阳性，包头政府拒绝接收。
					  id:: 63611daf-dfaa-4567-a024-e0ba16e41735
		- #log
			- 20:57 - 21:04
				- #continue
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 20:58:02]--[2022-11-01 Tue 21:04:25] =>  00:06:23
				  :END:
					- {{embed ((635eb1a9-8179-40f2-8de6-6f326b68de18))}}
			- 21:05 - 21:08
				- 搞瓶 [[啤酒]]
			- 21:10 - 21:12
				- #闪念
					- DONE [workflow]([[workflow]])：看下config还有什么好设置的。
					  collapsed:: true
					  :LOGBOOK:
					  CLOCK: [2022-11-01 Tue 21:10:37]
					  CLOCK: [2022-11-01 Tue 21:10:43]--[2022-11-01 Tue 21:12:46] =>  00:02:03
					  :END:
			- 21:20 - 21:28
				- #闪念
					- TODO [想看]([[想看]])[[卡桑德拉大桥]] #灾难 #鼠疫 #火车 #疫情 #封控
					  collapsed:: true
						- #故事
						  collapsed:: true
							- 鼠疫传播到了一辆正在行驶在欧洲大陆上的火车上，于是列车上所有人都被感染了鼠疫。很快这些人就要一个接一个死去。沿途经过的所有的国家——法国、巴黎、德国都拒绝让这个火车停下来。最终各国政府就想了一个招，想让这辆火车开往一个年久失修的大桥——卡桑德拉大桥，制造一起事故，然后这些人就全死了。
							  collapsed:: true
								- #link
								  collapsed:: true
									- ((63611daf-dfaa-4567-a024-e0ba16e41735))
			- 21:28 - 21:31
				- #闪念
					- TODO [想看]([[想看]])[[铁拳男人]]
					  id:: 6365f21d-a494-4834-8b16-66ba80ff22ae
					  collapsed:: true
	- 21:31
	  id:: 6363595f-189c-4a5b-915a-81c624ef5895
	  collapsed:: true
		- #continue
			- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
			  :LOGBOOK:
			  CLOCK: [2022-11-01 Tue 21:31:34]--[2022-11-01 Tue 22:05:58] =>  00:34:24
			  :END:
	- 22:06
	  collapsed:: true
		- [[break]]
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 22:06:50]--[2022-11-01 Tue 22:49:05] =>  00:42:15
		  :END:
		- #log
			- 22:09 - 22:23
				- #闪念
					- DONE 逛逛[[logseq]] [[插件市场]]
					  collapsed:: true
					  :LOGBOOK:
					  CLOCK: [2022-11-01 Tue 22:10:10]--[2022-11-01 Tue 22:23:12] =>  00:13:02
					  :END:
			- 22:22 - 22:47
				- #闪念
					- DONE [[stone记]]
					  id:: 651bc212-523a-48b7-bce5-e444544ae14b
					  :LOGBOOK:
					  CLOCK: [2022-11-01 Tue 22:22:28]
					  CLOCK: [2022-11-01 Tue 22:22:34]--[2022-11-01 Tue 22:47:51] =>  00:25:17
					  :END:
				- #log
					- 22:26 - 22:28
						- #continue
							- ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
							  id:: 63982e3c-0824-4265-9b4e-1993908d0164
							  collapsed:: true
							  :LOGBOOK:
							  CLOCK: [2022-11-01 Tue 22:26:59]--[2022-11-01 Tue 22:28:49] =>  00:01:50
							  :END:
						- #闪念
							- CANCELED [workflow]([[workflow]])：`[[Favorites/workflow/回顾/更新]]`
							  id:: 636a779d-f00e-46a8-bf30-ebf774ef8f20
							  collapsed:: true
							  :LOGBOOK:
							  CLOCK: [2022-11-01 Tue 22:27:04]
							  CLOCK: [2022-11-01 Tue 22:27:18]--[2022-11-01 Tue 22:27:49] =>  00:00:31
							  :END:
					- 22:29 - 22:44
						- #闪念
							- DONE [[logseq]] 更新 0.8.10
							  id:: 651c7c91-1b0a-465b-9bcc-bf12641bcd68
							  :LOGBOOK:
							  CLOCK: [2022-11-01 Tue 22:29:13]
							  CLOCK: [2022-11-01 Tue 22:29:17]--[2022-11-01 Tue 22:39:17] =>  00:10:00
							  :END:
	- 22:49
	  id:: 6363595f-533c-48b2-9a29-726015cab238
	  collapsed:: true
		- #continue
		  :LOGBOOK:
		  CLOCK: [2022-11-01 Tue 22:49:40]--[2022-11-02 Wed 00:14:28] =>  01:24:48
		  :END:
			- {{embed ((63612f01-d527-4d2e-8387-a025e18ceca5))}}
		- #log
			- 23:00 - 23:03
				- #深思
				  collapsed:: true
					- 我知道为啥这段时间尽管我 [[状态很好]] ，但是还是 [[逃避]] 写东西了： [[输入]] 停了，[[卧槽]]。
					  collapsed:: true
						- 输入一停， [[思考]] 就少。
						  collapsed:: true
							- 思考少了，输入的 [[脑子]] 就 [[转不动]] ，自然想要逃避。
							  collapsed:: true
								- 有必要提醒下 [[王露]]，要 [[恢复]] [[工作状态]]，先 [[输入]]：
								  collapsed:: true
									- 逃避啃专业书、职场书？
									  collapsed:: true
										- 可以从摘录视频、文章开始。就是强迫自己的大脑转起来。
			- 23:18 - 23:20
				- #闪念
					- CANCELED [[logseq]] [[需求]] -> [[Discord]]
					  id:: 636683cb-e375-4c17-a701-a78013096262
					  collapsed:: true
					  :LOGBOOK:
					  CLOCK: [2022-11-01 Tue 23:18:17]
					  CLOCK: [2022-11-01 Tue 23:18:24]--[2022-11-01 Tue 23:20:13] =>  00:01:49
					  :END:
			- 23:28 - 23:36
				- [[break]]， [[smoking]] & [[啤酒]]
				  :LOGBOOK:
				  CLOCK: [2022-11-01 Tue 23:28:48]--[2022-11-01 Tue 23:36:52] =>  00:08:04
				  :END:
				- #log
					- 23:33 - 23:34
						- #闪念
							- CANCELED [[Apple AirPods]] pro 2代和max的区别 
							  id:: 63613c51-0db0-4494-bcc3-40193af142e1
							  collapsed:: true
							  SCHEDULED: <2022-11-01 Tue .+1d>
								- #深思
								  collapsed:: true
									- 拉倒吧，有线家里那么多，用废了再说
	- 00:11
	  collapsed:: true
		- #smoking
		  :LOGBOOK:
		  CLOCK: [2022-11-02 Wed 00:11:32]--[2022-11-02 Wed 00:15:35] =>  00:04:03
		  CLOCK: [2022-11-02 Wed 00:15:56]--[2022-11-02 Wed 00:53:53] =>  00:37:57
		  :END:
		- #log
			- 00:17 - 00:25
				- #闪念
					- DONE [workflow]([[workflow]])：了解 [[logseq]] [[官方]] [[SYNC]]
					  link:: https://blog.logseq.com/how-to-setup-and-use-logseq-sync/
					  collapsed:: true
					  :LOGBOOK:
					  CLOCK: [2022-11-02 Wed 00:18:00]
					  CLOCK: [2022-11-02 Wed 00:18:04]--[2022-11-02 Wed 00:25:24] =>  00:07:20
					  :END:
						- #回忆
						  collapsed:: true
							- 等 [[正式版]]吧，目前的 [[git]] [[SYNC]]就够用了。而且是比较页面的。官方的sync不比较页面。
			- 00:25 - 00:39
				- #continue
				  collapsed:: true
				  :LOGBOOK:
				  CLOCK: [2022-11-02 Wed 00:25:51]--[2022-11-02 Wed 00:38:49] =>  00:12:58
				  :END:
					- {{embed ((63523a62-9f24-4f62-95ba-17f930798fc4))}}
			- 00:39 - 00:42
				- #continue
					- {{embed ((63613c51-0db0-4494-bcc3-40193af142e1))}}
	- 00:54
	  collapsed:: true
		- #log
			- 00:55 - 00:59
				- #continue
				  collapsed:: true
					- {{embed ((63556fdb-374e-477d-ad1d-0f976b693921))}}
			- 01:34
				- #continue
					- ((651aa257-6b75-4549-82e2-69f005661986))
- [[Comments]]
  collapsed:: true
	- #肯定
		- 1、首页清晰，默认都是折叠的，手机移动端打开都不会太卡。而且首页就俩功能：创建楼梯；回顾scheduled；回顾 doing； -> ((6361d335-873b-476c-b465-6d2b74356639))
			- 2、点击 时间戳 ref 直接聚焦，相当于创建了一个快速楼梯
			- 3、始终沉浸在时间单元里，上下拨弄时间切线，
	- #回忆
		- 会导致检索是出现不必要的信息。 -> ((651c7c91-306f-414e-b651-8c195517d776))
		  id:: 6363595f-4cf1-411e-a125-a35b4c52cdfd
			- 搞定了！我还真是 [[完美主义]]
	- #质疑
		- 没啥感觉啊。好像输入快了点。 -> ((651c7c91-1b0a-465b-9bcc-bf12641bcd68))