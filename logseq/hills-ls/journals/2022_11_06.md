# 01:38 ((636729bd-4587-4cfb-a3a5-11a7c68c5b8d)) ((bafe79c6-eaac-4f3a-b592-2ec32aa29d26)) ((636740a3-540c-4d26-be7a-45ed27672dff)) ((636766a7-bdf3-40df-a0d7-ff122a274a0b)) ((6367c847-f826-4c90-919a-61b9a240adc8))
id:: 63669d39-0b55-466e-8171-2913c1452f42
collapsed:: true
	- #break
		- #continue
			- ((6361dae4-5718-4443-aa38-1e826fb9bcb6))
			  id:: 651c65d7-6b0d-417d-a3f7-c2786c7683be
	- 02:21
		- ((651aa257-6b75-4549-82e2-69f005661986))
		- 09:15
			- :LOGBOOK:
			  CLOCK: [2022-11-06 Sun 09:15:36]--[2022-11-06 Sun 09:55:23] =>  00:39:47
			  :END:
			- 09:53
			  id:: 636729bd-4587-4cfb-a3a5-11a7c68c5b8d
				- DONE [[核酸]] [[出门]]
				  :LOGBOOK:
				  CLOCK: [2022-11-06 Sun 10:17:42]--[2022-11-06 Sun 11:08:03] =>  00:50:21
				  :END:
					- #[[回忆]]
						- 今天被 [[黄码]]了， [[黄埔花园]] [[四面楚歌]]
					- 10:38
						- ((651aa256-3898-464e-bd91-6752c7aed81e))
				- 11:08
					- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
					  :LOGBOOK:
					  CLOCK: [2022-11-06 Sun 11:11:54]--[2022-11-06 Sun 11:29:35] =>  00:17:41
					  :END:
					- 11:29
						- DONE [[清洁]] [[电脑]]
						- ((6353a251-268b-4d21-a10d-e5e664f14e47)) [[回忆]]：5小时37分，2点46睡的，8点25醒来， [[深度睡眠时长]] 1小时46分，接近了，还不错。
						  :LOGBOOK:
						  CLOCK: [2022-11-06 Sun 11:35:42]--[2022-11-06 Sun 11:36:46] =>  00:01:04
						  :END:
						- #continue
						  :LOGBOOK:
						  CLOCK: [2022-11-06 Sun 11:36:57]--[2022-11-06 Sun 11:39:12] =>  00:02:15
						  :END:
							- {{embed ((63216038-33e3-4986-b257-eb6d23d16438))}}
						- 11:39
							- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
							  :LOGBOOK:
							  CLOCK: [2022-11-06 Sun 11:39:22]--[2022-11-06 Sun 22:47:05] =>  11:07:43
							  :END:
							  ```calc
							  3.5
							  ```
							- ((651aa256-9459-42af-8400-536aadc21970))
							  :LOGBOOK:
							  CLOCK: [2022-11-06 Sun 11:42:06]--[2022-11-06 Sun 11:56:32] =>  00:14:26
							  :END:
								- {{embed ((636729bd-185b-4508-be94-f552a6039728))}}
							- 11:56
							  id:: bafe79c6-eaac-4f3a-b592-2ec32aa29d26
								- #闪念
									- DONE [[logseq]] [[意见反馈]] #Discord #Github #Element #ending
									  id:: d5220f65-4266-40ab-b51c-73c9d67f0276
									  collapsed:: true
									  :LOGBOOK:
									  CLOCK: [2022-11-06 Sun 11:57:20]--[2022-11-06 Sun 12:42:31] =>  00:45:11
									  :END:
										- #recycle
										  collapsed:: true
											- ((634850c7-510c-45a0-be4a-132fd19520bc))
											- ((636683d7-c703-423d-a88a-326a20431e62))
											- ((636683e1-90c1-4f90-90b5-3c7183461342))
											- ((636683e1-4fc2-4739-b58c-e5d654a3ab5e))
											- ((636683e1-d4a5-4dcc-91ce-6d516e929053))
											- ((636683e1-cb58-48f1-8eb8-438b8f58b7cb))
											- ((636683e1-13a3-4f50-86b1-50e15ca0150e))
											- ((636683e1-25c5-409d-8319-4563f0b6c230))
											- ((636683e2-c929-417a-8383-fb3999d49561))
											- ((636683e4-4583-4d21-b30f-a0a5a58539f9))
											- ((636683e4-6ee8-4eb4-a954-47d00524818a))
											- ((631b6334-909c-4182-b7ca-3cfb75675189))
											- ((636683f5-a53b-468d-952d-c375fffd32c2))
											- ((636683f6-1a16-4bc3-9644-4f65b2c9a76a))
											- ((636683cb-e375-4c17-a701-a78013096262))
											- ((63612f01-da28-4565-91c6-0c683e6001a1))
											- ((636683cb-c63e-44c0-922e-e6c25958159c))
											- ((635c97be-934e-4dcc-b5fb-053e8a5172b0))
											- ((6357e8db-753a-45c7-8b55-c1d3a3ab8966))
											- ((636683e0-2921-4a3a-948e-8ff9d43e6b87))
										- #step
										  collapsed:: true
											- [[UI交互]] [[问题]]
											  collapsed:: true
												- {{embed ((634850c7-743c-469b-9ae8-4b43813ababe))}}
												- {{embed ((636683e1-ccf3-49b7-8609-98c3556fb765))}}
												- {{embed ((631ccbc5-bf3e-4e8d-b561-42c642114512))}}
												- {{embed ((636683e4-4f74-4b7b-bf02-7747aa7ff2f2))}}
											- [[功能]] [[改进]]
												- 希望能够提供右侧栏单个面板置顶的功能
												  >Hope to be able to provide the function of topping a single panel on the right side
												- 希望能够提供关闭右侧栏单个面板的[[快捷键]] #[[keyboard-shortcuts]] #recycle 
												  >Hope to be able to provide a shortcut to close a single panel on the right sidebar
												- 一个很影响体验的bug：页面内搜索，如果聚焦模式下，就搜不出结果
												- embed下面编辑，回退block 会失去编辑指针
												- 多选block是没有选择block后的反色
												- CANCELED 搜索、筛选功能增强：使用场景、优化需求
												  id:: 6367356d-24bb-4cd1-afb5-4929d3c12d15
													- 功能存在严重逻辑问题，1，为什么把上一层级的标签也放进来？；2，下一层级的不同标签，勾选某一个后，非相关block 不会隐藏 —— 也就是说，完全起不到 [筛选]([[filter]]) 的作用
													  id:: 6368afeb-7dd0-4cf0-b236-8b8808370887
													- 1、整理[[page]]下的笔记时（包含[[journal-page]]），想要快速找到某一段
														- 目前只能页面搜索，但看不到上下文
														- ==需求：提供页面内容的组合筛选==
													- 2、卡片笔记时的联想查找（想到了什么…）
														- block reference 关键词（有property的内容搜出来）
															- 全局搜索（看不到上下文）
														- ==需求：恢复 block 下的linke-ref + 筛选功能==
													- 3、[[page]]下，linked-ref 下的筛选，
														- ==需求：筛选功能逻辑增强==
													- {{embed ((63774b37-0c83-4e1e-af8b-32fd73e99f1b))}}
												- 摘录引用点击打开pdf
												- 面包屑导航包含块引用点击后直接进入
												- 图片复制黏贴拖拽上传
												- 失效块引用查询功能
												- [[快捷键]]增强：提供右侧关闭单个面板的功能 #[[keyboard-shortcuts]] #pass
												- 筛选面板里那些诡异的数字标记是啥？
												  id:: 63673af0-b21a-4d00-8528-d4bf7ef7682c
													- #示意图
														- ![截屏2022-11-06 12.40.48.png](../assets/截屏2022-11-06_12.40.48_1667709706050_0.png){:width 245}
												- {{embed ((636683e0-9753-45bf-bcaa-2b03357cb4f7))}}
												- {{embed ((636683e1-0094-4bbf-b7df-71d7209a4cf5))}}
												- {{embed ((636683e1-f313-41bb-90c4-bceb9080b1be))}}
												- 左右打开同一个block，或者左侧显示的界面包含了右侧的block，右侧折叠，左侧也折叠 -> 希望能够各自折叠
												- {{embed ((636683e1-f935-4ce3-a584-505fb4c48d8e))}}
												- {{embed ((636683e1-0148-489e-8197-f4ca6dcc82f0))}}
												- {{embed ((636683e4-a28e-4cc6-9d00-1bd3350b778b))}}
												- {{embed ((636683e2-4cfc-49a9-9069-b30b2f2bfeb2))}}
												- {{embed ((636683e2-239a-403a-89db-5c49fc1d5946))}}
												- {{embed ((636683e4-ef02-4864-b89c-f37d1a67a86a))}}
												- {{embed ((636683f6-7452-4f56-8cd2-fcc0d81b2e1d))}}
												- {{embed ((636683f6-1796-4d6e-b6f0-52f219ccb706))}}
												-
										- #回忆
										  collapsed:: true
											- {{embed ((636683e2-0fb2-47e8-a778-fefed55df613))}}
											-
											- {{embed((636683e8-be66-42a1-b9f0-d9e298f7d3c6))}}
											- {{embed ((636683f4-5e28-45ff-a2f0-e4bde3440a81))}}
								- 12:44
									- DONE [[中饭]]
									  :LOGBOOK:
									  CLOCK: [2022-11-06 Sun 12:44:40]--[2022-11-06 Sun 13:05:31] =>  00:20:51
									  :END:
									- 13:05
									  id:: 636740a3-540c-4d26-be7a-45ed27672dff
										- #continue
										  :LOGBOOK:
										  CLOCK: [2022-11-06 Sun 13:08:00]--[2022-11-06 Sun 13:40:09] =>  00:32:09
										  :END:
											- {{embed((6343e146-1b9f-472b-9606-aef5f17afcdb))}}
										- 13:40
											- DONE [[continue]] ((6364f5df-4b72-4516-845e-14f2412e19e8))
											  id:: 63982e3a-26e9-43dd-b510-f3040e86442b
											  :LOGBOOK:
											  CLOCK: [2022-11-06 Sun 15:48:54]
											  CLOCK: [2022-11-06 Sun 15:49:13]--[2022-11-06 Sun 16:17:32] =>  00:28:19
											  :END:
											- #continue
											  :LOGBOOK:
											  CLOCK: [2022-11-06 Sun 13:42:51]--[2022-11-06 Sun 16:00:14] =>  02:17:23
											  :END:
												- {{embed ((6367356d-24bb-4cd1-afb5-4929d3c12d15))}}
											- #log
												- 13:45 -14:35
													- ((651aa257-c7fc-43ca-8e45-b129364eabf0))：协助 [老婆]([[王露]]) 清理 [[卧室]] [[床底]]
											- 16:17
											  id:: 636766a7-bdf3-40df-a0d7-ff122a274a0b
												- #break
													- #闪念
														- ((651bc212-523a-48b7-bce5-e444544ae14b))
														  :LOGBOOK:
														  CLOCK: [2022-11-06 Sun 16:34:27]--[2022-11-06 Sun 17:07:41] =>  00:33:14
														  :END:
														- DONE [看]([[想看]]) [[乐透大作战]]
														  rate:: ★★★
														  :LOGBOOK:
														  CLOCK: [2022-11-06 Sun 17:07:45]
														  CLOCK: [2022-11-06 Sun 17:08:20]--[2022-11-06 Sun 19:22:59] =>  02:14:39
														  :END:
													- #书单
														- DONE [[hls__爱和自由_1669988296488_0]] 
														  SCHEDULED: <2023-02-28 Tue ++2w>
														  id:: 636a7bea-4929-4d73-b26c-5c7cc24cf8ad
														  tags:: #育儿 #[[育儿 · 家庭]] #孙瑞雪 #蒙特梭利
														  rate:: ★★★
														  year:: 2013-4-1
														  :LOGBOOK:
														  * State "DONE" from "TODO" [2022-11-26 Sat 00:19]
														  * State "DONE" from "TODO" [2022-11-28 Mon 17:10]
														  CLOCK: [2022-12-05 Mon 17:46:27]--[2022-12-06 Tue 01:45:50] =>  07:59:23
														  * State "DONE" from "TODO" [2022-12-06 Tue 11:16]
														  * State "DONE" from "TODO" [2022-12-23 Fri 11:20]
														  * State "DONE" from "TODO" [2023-01-07 Sat 13:43]
														  * State "DONE" from "TODO" [2023-01-30 Mon 12:02]
														  * State "DONE" from "TODO" [2023-02-01 Wed 00:15]
														  * State "DONE" from "TODO" [2023-02-14 Tue 01:10]
														  :END:
													- 晚饭
													- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
												- 21:59
												  id:: 6367c847-f826-4c90-919a-61b9a240adc8
													- ((651bc21c-3131-406f-9608-99fda127b5e3))
													- DONE [[continue]]
													  :LOGBOOK:
													  CLOCK: [2022-11-06 Sun 22:00:26]--[2022-11-07 Mon 00:17:16] =>  02:16:50
													  :END:
														- {{embed ((63679e20-6cfa-4e09-8c3a-5a81e4fb23c8))}}
													- #书单
														- TODO [[父母的语言：3000万词汇塑造更强大的学习型大脑]]
														  id:: 636a7bea-c9b1-4443-b95e-d9af74ad4274
														  rate:: ★★★
														  tags:: #育儿 #[[育儿 · 家庭]]
													- ((6367de12-38c0-4c5c-9b86-0db218c28d73))