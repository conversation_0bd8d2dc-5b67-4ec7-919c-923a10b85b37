# 00:17 ((e4a41a65-6d59-4f4f-a335-a6fc0d72afa3)) ((6368ff6c-e369-4c27-9c27-c3ed0a8828d5))
id:: 6367de12-38c0-4c5c-9b86-0db218c28d73
collapsed:: true
	- DONE  [[logseq]] [workflow]([[workflow]]) #ending  #使用技巧  #review
	  id:: 6369d8ee-a3ff-4469-908d-ae91060c8d2b
	  collapsed:: true
	  :LOGBOOK:
	  CLOCK: [2022-11-07 Mon 00:19:53]--[2022-11-07 Mon 00:51:01] =>  00:31:08
	  :END:
		- #recycle
		  collapsed:: true
			- ((636a889e-869b-439e-bc0a-1706fa25aeba))
			- ((63193a77-31bb-4989-9ae4-0d90ff01a4a4))
		- #回忆
		  collapsed:: true
			- ((6348c3c5-2876-4935-8263-67ad9b3de9d3))
		- #step
			- {{embed((d5220f65-4266-40ab-b51c-73c9d67f0276))}}
			- {{embed((6369d8ee-32d3-438e-9516-60e6d1a2203b))}}
			- {{embed((6361d335-873b-476c-b465-6d2b74356639))}}
			- {{embed((635eb1a9-8179-40f2-8de6-6f326b68de18))}}
			- {{embed((634bab0f-6125-4d13-aa7d-2b6bf051f37c))}}
			- ((6329e2b4-935e-4b64-bef8-89a91bc08b0e))
		- #recycle
			- CANCELED [[namespace]] -> #recycle
			  id:: 6367e344-7944-45e8-9bfd-38b700260c5f
			  :LOGBOOK:
			  CLOCK: [2022-11-07 Mon 00:40:23]
			  :END:
				- [[核酸]]
				- [[订座]]
				- [[续费]]
				- [[清洁]]
				- [[continue]]
				- [[抹药]]
		- #link
			- ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
	- 00:51
		- #break
			-
			- ((651aa257-6b75-4549-82e2-69f005661986))
		- 01:52
			- #闪念
				- DONE [workflow]([[workflow]])： [[test]] [筛选]([[filter]]) 逻辑
				  :LOGBOOK:
				  CLOCK: [2022-11-07 Mon 01:52:27]
				  CLOCK: [2022-11-07 Mon 01:52:32]--[2022-11-07 Mon 01:59:39] =>  00:07:07
				  :END:
					- #回忆
						- 优先使用，包含 + 包含 逻辑；之前 [[俞敏洪]] + [[蜡烛]] 筛选不出来，是因为没有嵌套关系。只要有嵌套，就能找出来，不慌。
				- CANCELED [筛选]([[filter]]) [[logseq-css]]
				  id:: 63982e3a-855e-4ce6-8ba2-92e36690a601
			- 02:54
				- DONE [[上床]]
				  :LOGBOOK:
				  CLOCK: [2022-11-07 Mon 02:54:52]--[2022-11-07 Mon 10:39:23] =>  07:44:31
				  :END:
				- 10:39
					- :LOGBOOK:
					  CLOCK: [2022-11-07 Mon 10:53:30]--[2022-11-07 Mon 10:55:15] =>  00:01:45
					  :END:
					- 10:53
						- #[[smoking]]
						- 10:57
							-
								- #[[回忆]]
									- [[海底捞]]的[[鱼皮]]， [[阿叔猪扒包]]的 [[依然粉]]，都是一绝。
								- #[[闪念]]
									- 吃了 [[早中饭]]，感觉一天可以不吃饭。
							- 11:16
								- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
								  :LOGBOOK:
								  CLOCK: [2022-11-07 Mon 11:17:21]--[2022-11-07 Mon 11:29:22] =>  00:12:01
								  :END:
									- #深思
										- [[logseq]] [[使用技巧]]
										  id:: 63687bf9-93dd-489c-8994-e2f58efd6a68
											- 所有的[页面]([[page]])就是一个个单独的 [筛选]([[filter]]) 页面，内容都在 [[journal-page]] 上。
											  id:: 6377add5-6ec2-46b7-8656-90dd1408cde0
												- 所有 [[标签]] 就两种： [功能标签]([[特殊标签]]) ； [[内容标签]] ；
													- 这样一来，内容标签就很重要了，要勤快打标签啊。
														- #link
															- ((651aa258-198e-4b8e-af29-1b768aeb7a1a))
								- 11:32
									- DONE [[continue]]： ((63216038-33e3-4986-b257-eb6d23d16438))
									  :LOGBOOK:
									  CLOCK: [2022-11-07 Mon 11:33:17]--[2022-11-07 Mon 11:39:06] =>  00:05:49
									  :END:
									- 11:39
										- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
										  :LOGBOOK:
										  CLOCK: [2022-11-07 Mon 11:39:43]--[2022-11-07 Mon 20:53:21] =>  09:13:38
										  :END:
										  ```calc
										  10.92+1+4+11+17
										  ```
										- ((651aa256-9459-42af-8400-536aadc21970))
										  :LOGBOOK:
										  CLOCK: [2022-11-07 Mon 11:41:15]--[2022-11-07 Mon 11:50:55] =>  00:09:40
										  :END:
										- 11:51
										  id:: e4a41a65-6d59-4f4f-a335-a6fc0d72afa3
											- :LOGBOOK:
											  CLOCK: [2022-11-07 Mon 12:01:31]
											  CLOCK: [2022-11-07 Mon 12:01:41]--[2022-11-07 Mon 12:03:05] =>  00:01:24
											  :END:
												- #回忆
													- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 3点睡的，10点40起来，最近的 [[睡眠习惯]]真是糟糕的 [[一塌糊涂]]，没有带手表，但睡眠质量应该还可以。
											- id:: 3c69f466-efa5-43b9-b5be-a66d25451729
											  collapsed:: true
											  :LOGBOOK:
											  CLOCK: [2022-11-07 Mon 11:59:39]--[2022-11-07 Mon 20:53:24] =>  08:53:45
											  :END:
												- ((6306282f-6ee9-447f-abda-1b78da19648b))
												- DONE [[轻断食]]
												  :LOGBOOK:
												  CLOCK: [2022-11-07 Mon 14:23:07]--[2022-11-07 Mon 20:52:38] =>  06:29:31
												  :END:
											- 12:17
												- #smoking
													- #回忆
														- 没喝 [[咖啡]]，好困啊。
														  id:: 63688a9f-34a6-40f9-8c54-13b13bc41159
												- #log
													- 12:33 - 12:52
														- #深思
															- [[logseq]] [[使用技巧]]： [[scheduled]] 与 [块引用]([[block-ref]]) 的关系
																- 提醒去做：例如，看书、抹药
																- 持续更新
														- #continue
															- {{embed ((6367356d-24bb-4cd1-afb5-4929d3c12d15))}}
													- 12:37 - 13:04
														- #回忆
															- :LOGBOOK:
															  CLOCK: [2022-11-07 Mon 12:52:43]--[2022-11-07 Mon 13:04:48] =>  00:12:05
															  :END:
																- #link
																	- ((63688a9f-34a6-40f9-8c54-13b13bc41159))
													- 13:06 - 15:19
														- #闪念
															- DONE [看]([[想看]])[[共助2]]
															  :LOGBOOK:
															  CLOCK: [2022-11-07 Mon 13:06:55]--[2022-11-07 Mon 15:19:38] =>  02:12:43
															  :END:
														- #log
															- 13:26 - 13:27
																- ((651aa256-3898-464e-bd91-6752c7aed81e))
															- 13:27 - 13:57
																- CANCELED 午饭 [[外卖]]
																  :LOGBOOK:
																  CLOCK: [2022-11-07 Mon 13:27:51]
																  CLOCK: [2022-11-07 Mon 13:27:57]--[2022-11-07 Mon 13:28:03] =>  00:00:06
																  :END:
															- 14:06 - 14:11
																- #smoking #pee-time 
																  :LOGBOOK:
																  CLOCK: [2022-11-07 Mon 14:06:42]
																  :END:
															- 14:11 - 14:25
																- #continue
																  :LOGBOOK:
																  CLOCK: [2022-11-07 Mon 14:12:05]--[2022-11-07 Mon 14:25:07] =>  00:13:02
																  :END:
																	- {{embed ((3c69f466-efa5-43b9-b5be-a66d25451729))}}
																	- {{embed ((63216038-33e3-4986-b257-eb6d23d16438))}}
															- 14:39 - 15:12
																- DONE 更新 - [[GitHub]]
																  :LOGBOOK:
																  CLOCK: [2022-11-07 Mon 14:39:39]
																  CLOCK: [2022-11-07 Mon 14:40:14]
																  CLOCK: [2022-11-07 Mon 14:40:17]--[2022-11-07 Mon 14:57:25] =>  00:17:08
																  :END:
																	- {{embed((d5220f65-4266-40ab-b51c-73c9d67f0276))}}
													- 15:22 - 17:47（2小时25分）
														- #smoking
														- DONE [[沙县]]
														- DONE [[王露]] [[散步]]
														  :LOGBOOK:
														  CLOCK: [2022-11-07 Mon 17:34:15]--[2022-11-07 Mon 17:34:16] =>  00:00:01
														  :END:
															- #深思
																- 又聊到怎样避免 [[业务不熟练]]，答案是用 [[业务熟练]] 的思维去做事。 -> ((6368aff4-b518-4490-af00-e297338f461e)) -> ((6364852b-4a41-49ec-8dfa-6fdb7d288ee6))
																  id:: 644e2a46-8442-48b2-9c0f-6d8cf2b82466
																	- 最重要的一条：时刻以满分状态时的自己去审视眼前的事物。如果人要抛弃妄念的话，这就是唯一需要坚持的 [[妄念]] 。 #满分状态
																		- 1分状态的你：我已经很努力了。我尽力了。我做过了。
																			- 满分状态的你：今天目标100分，只做到了99分，没达标就是没达标。明天继续，或者明天目标是99分。
																	- 2、多多的建立目标，然后规划。以满分状态的自己去建立目标，尽可能的分解目标，规划到每分每秒每个步骤。规划可以不断被修订、重置，规划也可以有A、B、C个版本，更可以多和 [[毛义明]] 这种 overthinking 的人聊聊。所谓不大没有准备的仗，所谓带着准备与人聊天，说的就是这个部分。（而不是带着结果与人聊天，这里特别给老婆提个醒）。
																	- 3、根据反馈来拿结果、调整做事方式。而不是自己认为的结果就一定是最好的。所谓的倔强，也只是用情绪脑来处理别人的意见，真正高级的是用理性脑吸收一切外部建议为我所用。
													- 17:47 - 19:08
														- DONE [[logseq]] [[使用技巧]] #ending #continue
														  id:: 6369d8ee-32d3-438e-9516-60e6d1a2203b
														  collapsed:: true
															- #回忆
															  id:: 63774b37-0c83-4e1e-af8b-32fd73e99f1b
																- 仔细想下，这个正文的父父+级别的筛选也是可以的，问题就出在todo上，这使得我要想用好目前的功能，就必须调整 todo 相关的层级关系。 —— 但我不会去改了，因为目前我这个方式应该是最舒服的， [筛选]([[filter]])本身应该要增加以下“仅在block内筛选“的功能。
														- #回忆
															- [[惠润广场]] 拉了泡巨屎
													- 19:55
														- #闪念 #recycle
															- CANCELED [[感觉超棒]] 挂靠地址 拍照
												- 20:52
												  id:: 6368ff6c-e369-4c27-9c27-c3ed0a8828d5
													- :LOGBOOK:
													  CLOCK: [2022-11-07 Mon 20:52:11]--[2022-11-07 Mon 21:09:53] =>  00:17:42
													  :END:
														- #[[回忆]]
															- 今天又是浑浑噩噩的一天。妈的我自己的资源都不够用了。先从 [[早睡]] [[早起]]开始吧。  #负面情绪 #创业
													- 21:10
														- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
														  :LOGBOOK:
														  CLOCK: [2022-11-07 Mon 21:10:24]--[2022-11-07 Mon 22:14:04] =>  01:03:40
														  :END:
														- 22:14
															- ((63612f02-a2c3-4288-b203-61ccd9706d28))
																- DONE [[贴膜]]
																  :LOGBOOK:
																  CLOCK: [2022-11-07 Mon 22:17:21]
																  CLOCK: [2022-11-07 Mon 22:17:41]--[2022-11-07 Mon 22:37:40] =>  00:19:59
																  :END:
															- 22:38
															  collapsed:: true
																-
																- 23:23
																	- ((651aa257-6b75-4549-82e2-69f005661986))
																	- ((6369b224-c428-4e78-975a-f5f67c6eee50))