# 01:23 ((636f62d6-72af-4141-9c2e-a656493fd4b3)) ((636f62d6-eca9-4cae-9788-e20ff7baf1de)) ((636f1d06-6a6a-46be-bb2b-6535530ebbf3)) ((636f6306-6ceb-4733-98e2-04c0405a9afc)) ((636f86f5-906c-4300-b26c-365bcb0db611))
id:: 636e851d-ea77-47fc-b011-7e2679381129
collapsed:: true
	- #break
	- 01:31
		- ((651aa257-6b75-4549-82e2-69f005661986))
		- 09:00
		  id:: 636f62d6-72af-4141-9c2e-a656493fd4b3
			- :LOGBOOK:
			  CLOCK: [2022-11-12 Sat 09:14:22]--[2022-11-12 Sat 09:31:52] =>  00:17:30
			  :END:
			- 09:32
				- CANCELED 去 [[黄埔花园图书馆]] 
				  :LOGBOOK:
				  CLOCK: [2022-11-12 Sat 09:32:48]
				  :END:
				- DONE 去 [[自习室]]
				- 09:53
					- DONE [[云仓]] [[费用]] 2696.15  #recycle
					  :LOGBOOK:
					  CLOCK: [2022-11-12 Sat 09:54:40]--[2022-11-12 Sat 10:06:36] =>  00:11:56
					  :END:
						- ```calc
						  2686.15-7.81
						  ```
					- DONE [[谢皓]] [[利息]] 5000
					  :LOGBOOK:
					  CLOCK: [2022-11-12 Sat 10:03:47]--[2022-11-12 Sat 10:03:55] =>  00:00:08
					  :END:
					- 10:06
						- #回忆
							- 早上 [[做梦]] ， [[梦]] 里 [[发明]] 的 [[新词]] ， [[饼人]] [[卷饼人]] ，形容啥呢？ —— 好像就是那种 [[吃惊]] ， [[错愕]] ， [[莫名其妙]] [[被搞了]] 的感觉。
						- 10:11
						  id:: 636f62d6-eca9-4cae-9788-e20ff7baf1de
							- DONE 处理 [[GitHub]] 回复，并发到 [[Discord]] 
							  link:: https://github.com/logseq/logseq/issues/7246
							  :LOGBOOK:
							  CLOCK: [2022-11-12 Sat 10:16:01]--[2022-11-12 Sat 11:05:17] =>  00:49:16
							  :END:
							  #Logseq #筛选 #意见反馈
								- #深思
								  collapsed:: true
									- 1、在我提交bug后意识到，面对文本内容，“父级+”级的筛选确实是有其价值，这点我并不想否认。但是，面对 task list ，往往需要的是精准筛选，而不是把父级的task 状态提供进来。
										- 举例说明：
										  collapsed:: true
											- `DONE` [A]
												- `TODO` [A1]
													- `TODO` A1.1
													- `TODO` A1.2
													- `TODO` A1.3
													- `DONE` A1.4
													- `DONE` A1.5
													- `DONE` A1.6
												- [B1]
												- [C1]
											- 此时我在 [A1] 页面，我想回顾还有多少个`TODO`，这时我就会用筛选功能，选择不包含`DONE`标签，结果就是——整个任务列表都消失了。
											- 当然你也可以说我不应该用 “不包含”，而是用“包含”，但现实情况远比这复杂的多。比如 [A] 上面还有一个`TODO` [A father]，这会导致 [A1] 下面的 `DONE` 隐藏不掉。
										- 2、我们在使用工具，工具也在改变我们。但并不意味着，我们在使用工具的时候，因为功能上的规则使得我们的工作难以施展，难以放开手脚去做。考虑到logseq是一个国际化的产品，面对的是多语言的环境，“父级+”级的筛选功能在文本内容面前，同样会遇到问题。
											- 举例说明：
											  collapsed:: true
												- [A tag]
													- [B tag]
														- 文本 [A tag]
														- 文本
														- 文本
													- [C tag]
													- [D tag]
													  id:: 63bff1a1-229b-4270-959f-8c589ce91c2e
												- 此时我在 [B tag] 页面，我想筛选不包含 [A tag] 的内容，结果只会是 [B tag] 下面所有的内容都被隐藏掉。这只是距离，在面对几百条内容的真实环境下，这种筛选带来的误操作甚至会意识不到，使得有价值的内容被埋没了，影响了创作，这会让人在使用筛选功能时感到不安、害怕，进而不敢放开手脚去使用工具。
											- 3、以上的观点，都来自于我的日常使用，我喜欢列主题计划，通常一个大的计划里有多个小计划，小计划里又包含几十个任务，但计划并不总是线性完成的，有时完成一个小任务，有时改变小计划的一个状态，甚至我会cancell掉整个大计划，但保留小计划里的 todo 状态。
												- 4、文本内容方面，我喜欢做书摘，通常作者的名字、书中的概念会作为关键词，反复出现在各种嵌套的block中，这也令我筛选产生了困难。
													- 5、所有这些困难，都只需要官方团队升级下筛选功能：增加一个只向下、向内筛选的选项。—— 并不需要改变原有的“父级+”级筛选，而是给用户一个选择：我不需要“父级+”级筛选，我可以取消掉它。
														- 6、第一次发表的时候言辞激烈，是因为我遇到了困难，我向官方抱歉，以上内容，重新更新，重新提交。
							- #log
								- 10:30 - 10:35
								  collapsed:: true
									- #smoking
										- #闪念
											- 如果 [[自习室]] 给每一位 [[顾客]] 发 [[问卷]]，给身边的人打分， [[小胖子]] 和 [[搬家女孩]] 我肯定给 [[差评]]。别人会给我打什么 [[评价]] 呢，估计也是差评吧哈哈哈，满身的 [[烟味]]，哈哈。
							- 11:10
								- #smoking
								- 11:15
									- collapsed:: true
									  :LOGBOOK:
									  CLOCK: [2022-11-12 Sat 11:15:39]--[2022-11-12 Sat 11:18:17] =>  00:02:38
									  :END:
										- #[[回忆]]
											- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 1:43 睡，08:46 [[自然醒]] ，睡了7个小时，[[深度睡眠时长]] 1小时54分，感觉很不错。 #2小时深度睡眠 -> ((636e5d74-caae-45e4-afb4-f8e96c2dbb89))
									- 11:09
										- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
										  id:: 636fa904-748f-4ccd-b4b3-8e738831e783
										  :LOGBOOK:
										  CLOCK: [2022-11-12 Sat 11:23:48]--[2022-11-12 Sat 22:13:28] =>  10:49:40
										  :END:
										  ```calc
										  39+9.6+10+19.5
										  ```
											- #continue
												- *昨天* 11.11 ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
												  ```calc
												  14+11+13.41+12.9+30.79+26.16+10+37.24+15.8
												  ```
										- 11:42
										  id:: 636f1d06-6a6a-46be-bb2b-6535530ebbf3
											- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
											  :LOGBOOK:
											  CLOCK: [2022-11-12 Sat 12:11:19]--[2022-11-12 Sat 12:32:36] =>  00:21:17
											  :END:
												- #回忆
													- 哈哈 [儿子]([[毛懋]]) 来 [[自习室]] 找我了，去 [[天台]] 逛了一会～
														- DONE [[大鸽饭]] 买 [[炸牛奶]] 
														  :LOGBOOK:
														  CLOCK: [2022-11-12 Sat 12:16:46]--[2022-11-12 Sat 12:32:35] =>  00:15:49
														  :END:
											- :LOGBOOK:
											  CLOCK: [2022-11-12 Sat 12:31:26]--[2022-11-12 Sat 12:32:34] =>  00:01:08
											  :END:
											- 12:32
												- #break
													- 午饭
													- DONE [[午睡]]
													- DONE 收起 [[小推车]]
													- DONE 去 [[自习室]]
												- 17:10
												  id:: 636f6306-6ceb-4733-98e2-04c0405a9afc
													- #continue
													  collapsed:: true
													  :LOGBOOK:
													  CLOCK: [2022-11-12 Sat 17:15:29]--[2022-11-12 Sat 17:31:36] =>  00:16:07
													  :END:
														- ((6353a253-d033-4d48-aa77-77492adb4c3a))
															- #闪念
																- TODO [[写作]] [[选题]] [[户外工具指南]]
																- TODO [[人生]] 新 [[目标]] ： [[越野跑]] （utmb utmf）
																- TODO 每周 [[锻炼]] 3天（ [[健身房]] ） 
																  id:: 64705b16-e355-4415-a53c-78258919faaa
																  SCHEDULED: <2023-08-01 Tue .+1d>
																- TODO 成功 [[戒烟]]
																  id:: 63f603d5-4c7c-48ce-b68a-346f98d5a776
																- TODO [[Live]] [[邮箱]]
																- DONE [[毛懋]] 新 [[一寸照]]
																- DONE 找/买手表充电器
																- TODO 整理 [[微信收藏]]
																- TODO 整理 [[抽屉收藏]]
																- DONE 整理 [[王露]] [[移动硬盘]] [[照片]]
																- TODO 找 [[声乐老师]] 练 [[嗓子]]
																- TODO 找 [[私教]] 练出8块 [[腹肌]]
																- TODO 学会 [[开车]]
																  id:: 66869fb5-352e-45ff-b2d4-1990af2172a5
																- TODO 考 [[公共营养师]] （4级）
																- TODO 学 [[拍照]]
																- TODO 学 [[唱歌]]
																- TODO 学 [[英语]]
																  id:: 6658462c-520b-44e6-b4b5-761c9860615c
																- TODO 学 [[德语]]
																- TODO 学 [[日语]]
																- TODO 学 [[写作]]
																- TODO 每个月至少两次 [[自驾]] [[周末游]]
																- TODO [[瑞典]] [[国王之路]]
																- TODO [[日本]] 2周
																- TODO [[新加坡]] 2周
																- TODO [[尼泊尔]]
																- TODO [[德国]]
																- TODO [[英国]]
																- TODO [[加拿大]]
																- TODO [[美国]]
																- TODO [[旅居]] 20个国家
																- TODO [[全家]] 人出去 [[旅游]] 一趟
																- TODO 请杭州 [[亲友]] [[吃饭]]
																- TODO 陪 [老妈]([[丁满冬]]) 做身体 [[保养]]
																- CANCELED [[还钱]]：还清 [老妈]([[丁满冬]]) 一部份钱
																  id:: 6582a396-45ca-492f-8f2e-7dfd6f395071
																- TODO 给 [老妈]([[丁满冬]]) 准备 [[600万]] [[养老金]]
																- TODO [老妈]([[丁满冬]]) 办 [[70大寿]]
																  id:: 66b35ec6-f867-4c8f-8f0b-0513cf604169
																  SCHEDULED: <2028-12-31 Thu>
																- TODO 和 [老婆]([[王露]]) 一起学 [[日语]]
																- TODO [老婆]([[王露]]) [[衣柜]] [[换新]]
																- TODO 带 [老婆]([[王露]]) 去做最贵的 [[头发]]
																- TODO 3年内 [[还清]] [[房子]] [[贷款]]
																  id:: 65eddb0b-3587-40f5-a41c-a895fb523a15
																  SCHEDULED: <2026-08-01 Sun>
																- TODO 个人第 [[二套房]] 、买到最好的 [[学区房]]
																- DONE 找 [[收纳师]] 全屋管理
																  id:: 66b35ec6-9f81-4707-8536-88a556c8f19b
																- CANCELED [[家]] 里重新 [[装修]]
																- TODO 存够 [[小孩]] 到 [[大学]] [[1000万]] [[学费]]
																- TODO 存够 [[100万]] [[学费]]
																- TODO 存够 [[300万]] [[学费]]
																- TODO 存够 [[500万]] [[学费]]
																- DONE [[冲牙器]]
																  id:: 66b35ec6-c8b8-4ba9-8f0e-cd0360962f78
																- TODO 和 [老婆]([[王露]]) 定一对 [[戒指]]
																- CANCELED [[始祖鸟]] granville 10L
																  id:: 66b35ec6-c924-4a0d-b3eb-26ced6048192
													- 17:32
														- #continue
														  collapsed:: true
														  :LOGBOOK:
														  CLOCK: [2022-11-12 Sat 17:36:16]--[2022-11-12 Sat 17:46:53] =>  00:10:37
														  :END:
															- {{embed ((63523a62-9f24-4f62-95ba-17f930798fc4))}}
														- 17:47
															- #smoking
															- 17:54
																- #continue
																  collapsed:: true
																	- {{embed ((636ca16b-86b7-4555-9c55-5b232a745968))}}
																- 18:03
																	- #continue
																	  collapsed:: true
																	  :LOGBOOK:
																	  CLOCK: [2022-11-12 Sat 18:03:38]--[2022-11-12 Sat 18:14:22] =>  00:10:44
																	  :END:
																		- {{embed ((63451f50-1a64-4721-8531-e075f231d726))}}
															- 18:14
															  id:: 636f86f5-906c-4300-b26c-365bcb0db611
																- #continue
																	- ((636a7bea-180a-42ff-b63a-a7fc4446f498))p12-33
																	  :LOGBOOK:
																	  CLOCK: [2022-11-12 Sat 18:20:30]--[2022-11-12 Sat 18:45:03] =>  00:24:33
																	  CLOCK: [2022-11-12 Sat 22:19:24]--[2022-11-13 Sun 00:42:46] =>  02:23:22
																	  :END:
																- #log
																	- 18:45 - 19:50
																	  collapsed:: true
																		- :LOGBOOK:
																		  CLOCK: [2022-11-12 Sat 18:45:14]
																		  CLOCK: [2022-11-12 Sat 18:45:24]--[2022-11-12 Sat 19:50:12] =>  01:04:48
																		  :END:
																	- 19:50 - 21:54
																	  collapsed:: true
																		- #continue
																			- ((62fa66a5-2fca-4bba-98dc-dc88140218fb)) ：[[小黑公园]] 走起 [*](((63f4f1b2-416f-40a1-a075-e2b8ad3677b8)))
																			  id:: 636fa904-a79b-4fd1-9c0c-2d38a0998af6
																			  collapsed:: true
																			  :LOGBOOK:
																			  CLOCK: [2022-11-12 Sat 19:51:30]--[2022-11-12 Sat 21:54:07] =>  02:02:37
																			  :END:
																				- #回忆
																					- [[今日份快乐]]：搞了半天，孩子最近粘妈的答案在我！[[爸爸时间]]太少了，算一下上个月24号到今天12号，已经19天没有好好带 [[娃]] [[探索]] 了。今天这一出去，2个小时，全程没有 [[找妈妈]]，还是 [[小黑公路]] - [[小黑公园]]的 [[探索路线]]。一路上 [[听虫子叫声]]，特别 [[带劲]] ！公园里还碰到一个 [[捧哏]] 小姐姐，7岁的样子，听到 [[耨耨]] [[荡秋千]] [[英语]] [[古诗词]] 惊呆了！哈哈哈 全回来了， [[元神]] 归位的感觉！语言功能恢复！记忆力恢复！成就模式恢复！耐心功能恢复！      #毛懋 #成长
																					  id:: 636fbbf8-6f16-461d-89b6-f23ae53ed36a
																					  collapsed:: true
																						- #照片
																						  collapsed:: true
																							- ![WechatIMG266.jpeg](../assets/WechatIMG266_1668268246867_0.jpeg){:width 245}
																							- ![WechatIMG267.jpeg](../assets/WechatIMG267_1668268261758_0.jpeg){:width 245}
																	- 21:55 - 22:10
																	  collapsed:: true
																		- DONE 回 [[自习室]]
																		  :LOGBOOK:
																		  CLOCK: [2022-11-12 Sat 21:55:13]
																		  CLOCK: [2022-11-12 Sat 21:55:17]--[2022-11-12 Sat 22:09:59] =>  00:14:42
																		  :END:
																	- 22:10 - 22:14
																	  collapsed:: true
																		- #continue
																		  collapsed:: true
																		  :LOGBOOK:
																		  CLOCK: [2022-11-12 Sat 22:10:25]--[2022-11-12 Sat 22:14:46] =>  00:04:21
																		  :END:
																			- {{embed ((636fa904-748f-4ccd-b4b3-8e738831e783))}}
																	- 22:15 - 22:19
																	  collapsed:: true
																		- CANCELED [[namespace]] -> #recycle
																		  id:: 63730f2a-2048-4601-a238-2cacd0736a70
																		  :LOGBOOK:
																		  CLOCK: [2022-11-12 Sat 22:15:47]--[2022-11-12 Sat 22:19:13] =>  00:03:26
																		  :END:
																			- [[小黑公园]]
																			- [[小黑公路]]
																			- [[骑自行车]]
																	- 22:42 - 22:59
																	  collapsed:: true
																		- :LOGBOOK:
																		  CLOCK: [2022-11-12 Sat 22:42:15]--[2022-11-12 Sat 22:59:51] =>  00:17:36
																		  :END:
																	- 23:00 - 23:42
																	  collapsed:: true
																		- {{embed ((636fa904-a79b-4fd1-9c0c-2d38a0998af6))}}
																	- 23:48 - 00:00
																	  collapsed:: true
																		- DONE [[上传]] [[照片]]
																		  :LOGBOOK:
																		  CLOCK: [2022-11-12 Sat 23:48:47]
																		  CLOCK: [2022-11-12 Sat 23:48:50]--[2022-11-13 Sun 00:00:53] =>  00:12:03
																		  :END:
																			- {{embed ((636fa904-a79b-4fd1-9c0c-2d38a0998af6))}}
																			- {{embed ((636fbbf8-b256-40ec-a3a3-36b3f5aa078f))}}
																			- {{embed ((636fbc01-597b-47c1-ba3d-ff95e88f4778))}}
																	- 00:01 - 00:18
																	  collapsed:: true
																		- ((636fbc07-5e6a-4e03-bb7b-144cff0fb88e))
																- ((636fcd10-3373-4924-8f8f-c3b9a5262211))