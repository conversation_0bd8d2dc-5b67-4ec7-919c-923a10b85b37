# 23:56 ((63770389-74d0-4fb9-ae3a-a3490dbc448b)) ((637721a5-65fa-4b42-93e4-f1c59d066406)) ((6377c591-7db7-4f09-9fe6-ff66c67d4680))
id:: 637659bc-0940-4cbf-9340-793994e5940d
collapsed:: true
	- #break
		-
		- ((62efedee-e55f-496f-8865-21cfa6fbc8c3))
		- :LOGBOOK:
		  CLOCK: [2022-11-18 Fri 00:21:36]--[2022-11-18 Fri 00:39:31] =>  00:17:55
		  :END:
	- 01:34
		- ((651aa257-6b75-4549-82e2-69f005661986))
		- 09:30
			- :LOGBOOK:
			  CLOCK: [2022-11-18 Fri 09:42:06]--[2022-11-18 Fri 10:17:04] =>  00:34:58
			  :END:
				- #回忆
					- 100 30 2
			- 10:16
				- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
				  :LOGBOOK:
				  CLOCK: [2022-11-18 Fri 10:19:20]--[2022-11-18 Fri 10:26:04] =>  00:06:44
				  :END:
				- 10:26
				  id:: 63770389-74d0-4fb9-ae3a-a3490dbc448b
					-
						- #[[回忆]]
							- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 昨晚做了 [[冥想]] ，为啥 [[结果]] 出来 [[反而变差]] 了呢？总睡眠 6.5小时， [[深度睡眠时长]] 只有1小时40分， [[快速眼动睡眠]] 基本没有，难道是 [[睡前冥想]] [[太精神]] 了？ -> ((63765535-3803-4fa8-bbb6-7fb2c29c2283)) -> ((6357e8d9-3d60-42dc-89fd-741069ae2bd3))
								- #截图
								  collapsed:: true
									- ![2022-11-18-10-28-44.jpeg](../assets/2022-11-18-10-28-44.jpeg){:width 245}
					- 10:33
						- #回忆
						  collapsed:: true
							- [老婆]([[王露]]) 的 [[logseq]] 被 [[iCloud]] 吞了9天的内容…… [[fuck!]] #丢失
								- 帮她切换到本地存储，删除app，连数据一并删除了！吓得我！幸好通过 icloud 云端恢复了。
									- DONE 问 [[Charliie]] [[iPad]] [[git]] [[SYNC]] 能不能搞
									  id:: 6377301b-98fd-4101-8236-b5782f03ac50
										- #if
											- DONE 能搞
											  :LOGBOOK:
											  CLOCK: [2022-11-18 Fri 11:45:17]
											  :END:
												- #then
													- CANCELED [[闪念]]：[[iPad]] [[logseq]] [[git]] [[SYNC]] 
													  SCHEDULED: <2023-02-28 Tue ++1w>
													  :LOGBOOK:
													  * State "DONE" from "TODO" [2022-11-26 Sat 00:19]
													  * State "DONE" from "TODO" [2022-12-04 Sun 15:32]
													  * State "DONE" from "TODO" [2022-12-10 Sat 19:34]
													  * State "DONE" from "TODO" [2022-12-18 Sun 17:11]
													  * State "DONE" from "TODO" [2022-12-18 Sun 17:12]
													  * State "DONE" from "TODO" [2022-12-27 Tue 11:40]
													  * State "DONE" from "TODO" [2022-12-31 Sat 11:30]
													  :END:
								- [老婆]([[王露]]) 说 [[有道笔记]] 的内容删了就删了，没啥感觉。 [[logseq]] 的内容被删了，还是挺遗憾了，觉得一下子这段使用时间变成空白了。说明我还是有写一些东西的，这是我刻意记录的感觉。 #冥想盆
								  id:: 63ea0dc8-bdf9-49a2-a17f-053ae062ae23
									- #配图
										- ![6b4765a4102047e994a4c2dee1805702.jpeg](../assets/6b4765a4102047e994a4c2dee1805702_1668742924776_0.jpeg){:width 245}
										- ![adadb5b7d90544d29063a92d824dbae8.jpeg](../assets/adadb5b7d90544d29063a92d824dbae8_1668742930789_0.jpeg){:width 245}
									- #link
										- ((6376f7bb-d408-49ea-9c3c-d0804c40fce8))
						- #log
						  collapsed:: true
							- 11:18
							  collapsed:: true
								- #闪念
									- DONE [[超级元素]] [[条形码]] [[续费]]
									  id:: 637c304d-aa35-48ef-a3de-d8ea45187ec6
									  SCHEDULED: <2022-11-23 Wed .+1d>
									  DEADLINE: <2022-11-25 Fri>
										- #必要条件
											- DONE [[高祥]] 了解👇下面资料怎么准备
												- 【广州 [[编码中心]] 】贵公司 [[条码证]] 即将到期，过期将被注销系统成员资格，使用注销商品条码将面临产品下架、执法部门罚款等风险，请带齐营业执照复印件（A4）、续展表、汇款凭证及公章，于11月25日前到广州市越秀区八旗二马路38号3号楼1楼办理，详情请咨询 020-83228627、83228624、83845191、83527005、83228625 #电话
												  id:: 639a80f6-9007-440d-9286-96c32a6995e6
													- #pdf
														- [APP续展流程.pdf](../assets/APP续展流程_1669107337943_0.pdf)
														- [商品条码证书续展通知新（阶段性降费至2022.12.31）.pdf](../assets/商品条码证书续展通知新（阶段性降费至2022.12.31）_1669107379567_0.pdf)
														- [关于配合开展商品条码信息核实工作的通知.pdf](../assets/关于配合开展商品条码信息核实工作的通知_1669107408092_0.pdf)
													- #word
														- [续展登记表（表格需要正反两面双面打印在一张A4纸上）.doc](../assets/续展登记表（表格需要正反两面双面打印在一张A4纸上）_1669107386415_0.doc)
														- [续展登记表（填写模板）.doc](../assets/续展登记表（填写模板）_1669107396166_0.doc)
													- #excel
														- [商品源数据样品受理委托单2021.5.31.xls](../assets/商品源数据样品受理委托单2021.5.31_1669107402197_0.xls)
												- DONE 拿到官方 [[邮件]]
													- 您好，请您汇款到附件六的帐号，并提交以下资料一式一份（续两期资料是一式两份）并盖好公章来前台办理：汇款凭证复印件（汇款账号请查看附件六）、营业执照副本复印件、填续展表格（附件二下载表格）。
														- 其中：**附件一**是手机app线上办理流程。若您忘记登陆卡号密码，需写好遗失证明并盖公章发邮件至本邮箱审核，核实后邮件会回复您登陆信息。
											- DONE 电话询问
											  collapsed:: true
												- #问题
													- 是否可以 app 全部办理 ((637c3fcf-9d14-4502-a0d4-c8b9373a0590))
														- #答案
															- 是，进入审核后即视为在截止日前完成续展。 ((637c444b-e53e-465c-beeb-bd635584772a))
													- 汇款汇哪个*（有3个选项）*
														- #答案
															- 1160
										- #step
											- DONE 登录 [[编码中心]] 网站 或 [[中国编码]] app ，提交资料办理 #条形码
											  id:: 637c3fcf-9d14-4502-a0d4-c8b9373a0590
											  collapsed:: true
												- DONE 问 [[高祥]] 要密码
													- DONE 密码不对，找回密码
														- #回忆
															- 妈的 [[高祥]] 给了我 [[花小补]] 的
												- #网址
													- [[条形码]] www.gds.org.cn
														- 账号：1892222
														- 密码：509945
														- app： [[中国编码]]
														- #示意图
															- ![WechatIMG706.jpeg](../assets/WechatIMG706_1669089874014_0.jpeg){:width 245}
												- DONE [[汇款]] - [[编码中心]] - 1160
													- 通过银行汇款至以下 [[银行账号]] :
														- 开户名称:中国物品[[编码中心]]
														- 开户银行:交通银行北京分行和平里支行
														- 帐 号:110060224010149084052
													- #汇款凭证
														- ![WechatIMG317.jpeg](../assets/WechatIMG317_1669089720591_0.jpeg){:width 245}
													- DONE [[企业银行]] [[余额]] 不够，打500进去 #pass
											- #做选择
												- CANCELED 15个工作日，等短信通知，携带资料去 [[编码中心]] 领取
												  id:: 637c444b-e53e-465c-beeb-bd635584772a
												  collapsed:: true
												  SCHEDULED: <2022-12-12 Mon ++1w>
													- CANCELED 准备资料*（[[盖公章]] ）*
														- DONE 续展登记表
															- CANCELED 正反面打印，盖章
														- CANCELED 汇款凭证复印件
														- CANCELED 营业执照副本复印件
										- #回忆
											- ((639ac42a-a1ed-443e-aab0-16a08a3cd56d))
							- 11:46 - 12:08
							  collapsed:: true
								- DONE [[namespace]] -> #recycle
								  :LOGBOOK:
								  CLOCK: [2022-11-18 Fri 11:46:31]
								  CLOCK: [2022-11-18 Fri 11:46:34]--[2022-11-18 Fri 11:55:25] =>  00:08:51
								  :END:
									- [[gtd]]
									- `[[Favorites/workflow/gtd]]`
									- [[证明]]
						- 12:11
						  collapsed:: true
							- #break
							- 12:15
								- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
								  :LOGBOOK:
								  CLOCK: [2022-11-18 Fri 12:15:24]--[2022-11-21 Mon 16:48:51] =>  76:33:27
								  :END:
								  ```calc
								  
								  ```
								- 12:20
									- :LOGBOOK:
									  CLOCK: [2022-11-18 Fri 12:21:13]
									  :END:
									- 12:27
										- #回忆
											- 午饭 [[王露]] [[散步]]
											  :LOGBOOK:
											  CLOCK: [2022-11-18 Fri 12:27:46]--[2022-11-18 Fri 14:09:35] =>  01:41:49
											  :END:
												- #and
													- [[自习室]] [[小黑屋]] 不开灯，听 [[雨声]] [[白噪音]] 还真以为外面下雨了。 btw，电脑的屏幕足够好，亮度也足够舒服， [[开灯]] 反而太 [[刺眼]] 了。
													- 现在的老习感觉就是当年的马云，他做大国崛起的梦，下面的人负责实现，他带头战狼外交，西方一群小弟不服了，妥妥的霸道总裁上升记。世界工厂，产业投资，举全国之力硬扛周期，加码科学和军事，内部稳定，这样的国家，放眼全世界找不到第二个。
													  id:: 637f6bba-f61f-43ed-916f-e8b58ba6e9b1
													  #习近平 #大国崛起
														- #then
															- DONE 抽空 [[整理]] 下对 [[中国]] 当前发展 [[乐观]] 的 [[理由]] 
															  SCHEDULED: <2022-11-25 Fri ++1w>
															  id:: 637f6bba-8726-493e-a8e7-00f74aee2185
															  :LOGBOOK:
															  * State "DONE" from "TODO" [2022-11-18 Fri 14:03]
															  :END:
														- #闪念
															- 20大以后，都是好事。随你国外怎么说 [[人权]]，但没有人会说 [[中国]] [[不稳定]] ，对合作来说， [[稳定]] 难道不是最重要的吗？ 这是中国第一无二的一张 [[王牌]]
																- 不要 [[辜负]][老习]([[习近平]] 啊 啊哈哈
												- #闪念
													- [[社会人生]] 下半场 —— [[70岁]] 为 [[零界点]] ， [[35岁]] 就是 [[人生过半]] 。 #人生的半山坡 #中年 #选题
										- 14:09
										  id:: 637721a5-65fa-4b42-93e4-f1c59d066406
											- #break
												- DONE [[工商银行]] [[取现]] 12万
												  SCHEDULED: <2022-11-22 Tue .+1d>
												  id:: 637a5bb2-e5c7-4701-9f9b-f84636902307
												  :LOGBOOK:
												  CLOCK: [2022-11-21 Mon 16:49:39]--[2022-11-21 Mon 17:08:44] =>  00:19:05
												  * State "DONE" from "DOING" [2022-11-21 Mon 17:08]
												  :END:
													- DONE 交给 [[王露]]
												- ((651bc212-523a-48b7-bce5-e444544ae14b))
												  :LOGBOOK:
												  CLOCK: [2022-11-18 Fri 14:38:10]
												  CLOCK: [2022-11-18 Fri 14:38:14]--[2022-11-18 Fri 19:08:05] =>  04:29:51
												  :END:
												- #摘录
													- CANCELED [一个博主写为什么用logseq](cubox://card?id=********************************) 
													  #Logseq #workflow #心得
													- CANCELED [马斯克发推50张图文讲认知偏差，职场中的你中了几条？](cubox://card?id=********************************)
													- CANCELED [刘润：如何把一天用到极致？ - 知乎](cubox://card?id=********************************)
													- CANCELED [最真实的长津湖：志愿军断粮7日，有部队靠吃美军尸体活命](cubox://card?id=********************************)
													- CANCELED [有个姑娘叫彭萦](cubox://card?id=********************************)
												- ((651aa256-9459-42af-8400-536aadc21970))
												  :LOGBOOK:
												  CLOCK: [2022-11-18 Fri 14:46:53]--[2022-11-18 Fri 14:51:59] =>  00:05:06
												  :END:
											- 14:52
											  id:: 63772b8f-1835-4f5c-8a41-01855b17d1f4
												- DONE [[闪念]]： ((63752ca4-ffdd-4056-a4b0-0bf6d11336b8)) -> 基本全部搞完
												  id:: 63774b31-1b23-4201-ae45-1fd6d3ab0a3e
												  :LOGBOOK:
												  CLOCK: [2022-11-18 Fri 15:16:36]
												  CLOCK: [2022-11-18 Fri 15:16:40]--[2022-11-18 Fri 17:11:53] =>  01:55:13
												  :END:
													- #回忆
														- 其实感觉没啥必要，有点浪费时间
														  id:: 63982e34-cdd8-463e-a96e-8d4a81a7528f
															- *17:48*
																- 干！接着干！
																  id:: 63984a3d-4484-4592-8e39-103f53d51570
												- #log
													- 17:13 - 17:42
													  collapsed:: true
														- #continue
														  collapsed:: true
															- {{embed((6375f283-6d9b-4374-83ff-917c5e49cc42))}}
													- 17:42 - 17:47
													  collapsed:: true
														- #smoking #drink-water-800ml #pee-time
													- 19:08 - 20:28
													  collapsed:: true
														- :LOGBOOK:
														  CLOCK: [2022-11-18 Fri 19:08:24]
														  CLOCK: [2022-11-18 Fri 19:08:29]--[2022-11-18 Fri 20:27:56] =>  01:19:27
														  :END:
															- #回忆
																- 今天 [[爸爸时间]] [[请假]] 了
													- 22:21 - 22:27
													  collapsed:: true
														- #smoking
												- 23:50
												  id:: 6377c591-7db7-4f09-9fe6-ff66c67d4680
													- #闪念
														- CANCELED [分享]([[分享]]) [[logseq]] [[workflow]] 及 [[使用技巧]]
														  id:: 63790474-b550-42a9-8a6d-b3d72d543913
														  collapsed:: true
														  :LOGBOOK:
														  * State "DONE" from "TODO" [2022-11-22 Tue 16:46]
														  CLOCK: [2022-11-23 Wed 17:04:18]--[2022-11-23 Wed 20:27:20] =>  03:23:02
														  * State "DONE" from "TODO" [2022-11-24 Thu 10:31]
														  * State "DONE" from "TODO" [2022-12-23 Fri 11:20]
														  * State "DONE" from "TODO" [2023-02-28 Tue 00:26]
														  * State "DONE" from "TODO" [2023-03-07 Tue 00:20]
														  :END:
															- #recycle
															  collapsed:: true
																- ((6377038e-985e-450c-83ae-57fbfc5838df))
																- ((6377ae68-879c-4b84-bd64-b1f9eea3320c))
																- ((6377ae68-146d-406c-a82c-0935ca3d7212))
															- #提纲
																- *使用场景*
																	- #闪念
																	  collapsed:: true
																		- [[Logseq都可以有哪些骚操作]]
																	- *核心场景*
																		- [卡片盒]([[zettel]])
																		  collapsed:: true
																			- 习惯
																			  collapsed:: true
																				- ((637e44ea-8248-49bd-8115-f2c0b263bb5e))
																			- 绳结
																			  collapsed:: true
																				- 卡片属性：卡片盒的头部，就像钥匙串的圈，绳结的结 *(确保输入时第一个就是它)* #[[property]]  #pass
																				  collapsed:: true
																					- #闪念 #深思 #回忆 #质疑 #肯定 #摘录
																					  collapsed:: true
																						- 正文 ` [[标签]] `
																						  collapsed:: true
																							- 正文  ` [[标签]] `
																							  collapsed:: true
																								- #link
																								  collapsed:: true
																									- ` /bl r `
																			- 嵌套
																			  collapsed:: true
																				- 无限嵌套就是无限卡片：每一个块，每一个 [[嵌套]] 关系，只要带上了 [[标签]] ，都是一张卡片，呈现在 `linked-reference` 中 ->
																				  collapsed:: true
																					- 无论是写作，还是记录自己的感想，都很有用
																					  collapsed:: true
																						- #案例
																						  collapsed:: true
																							- {{embed ((637dd0d9-4b0c-42b8-8389-132301820b2c))}}
																							- {{embed ((637dd0da-dbef-4067-ae59-80edb6e008f9))}}
																							- {{embed ((637b4a72-97b8-408d-a401-059992e9fdb4))}}
																				- 卡片也可以嵌套
																			- [[todo-list]]
																			  collapsed:: true
																				- 也可以是卡片
																			- [特殊标签]([[特殊标签]])
																		- [[时间记录]]
																			- [[时间切线]] + [[聚焦输入]] + [[log]] + [[时间单元]] = [[追踪注意力]]
																			- #案例
																			- *格式*
																			  collapsed:: true
																				- `time`
																				  collapsed:: true
																					- `[卡片盒]([[logseq/zettel]])`
																					  collapsed:: true
																						- `[卡片盒]([[logseq/zettel]])`
																					- `[卡片盒]([[logseq/zettel]])`
																					- `[卡片盒]([[logseq/zettel]])`
																					- `[卡片盒]([[logseq/zettel]])`
																					- `[卡片盒]([[logseq/zettel]])`
																					- `#log`
																					  collapsed:: true
																						- `time` - `time` `(h min)`
																						- `time`
																	- *附加*
																		- [[任务管理]]
																		  collapsed:: true
																			- 用好 `/sch /dea` ，基本不用 `[[data]]`
																			- 习惯
																				- 想到要做
																		- [[习惯追踪]]
																		  collapsed:: true
																			- ((634c211a-2413-4451-ad14-bfd9833abe00))
																		- [[数据库]]
																		- [[项目管理]]
																		- [[背诵]]
																		- [[写作]]
																		- [[资源池]]
																		- 最优雅的输入方式
																		  collapsed:: true
																			- [[时间记录]] + [[聚焦输入]] + [[无限嵌套]] +[特殊标签]([[特殊标签]]) = [[上下文关系]]
																			- 你不需要记住存放位置
																			- 你不需要从头开始写
																			- 你也不需要记住标签（如果你打的足够多）
																- *使用目的与目标*
																  collapsed:: true
																	- *目的*
																		- [[管理大脑]]
																			- [[管理想法]]
																				- 轻松的找到想法 - [[搜索]] [筛选]([[filter]])
																				- 轻松的联想 - [块引用]([[block-ref]])
																				- 管理任务 轻松的规划 项目的归项目，任务的归任务
																	- *目标*
																		- 为了更好的找到想法
																		- 为了让想法管理更优雅
																		- 最大化的利用 [[logseq]] 的特性
																- *理解 [[logseq]] *
																	- *区别*
																		- ~~没有文件夹结构*（[[namespace]]）*~~ #recycle
																		- [[细粒度]]
																			- 最小单元*（最有价值）*是 [块]([[block]]) ，而不是 [页面]([[page]])
																		- [[双链]]
																			- [页面]([[page]]) 双链
																			- [块]([[block]]) 双链
																			  id:: 637dd0db-4ddd-4a9c-b873-e3518e442445
																		- [DailyNote]([[journal-page]]) 也是 [页面]([[page]])
																	- *特色功能*
																		- [[搜索]] - [页面]([[page]]) & [块]([[block]])
																		- [筛选]([[filter]]) - [[标签]] [块嵌套]([[block-embed]]) （父子）
																		- [[面包屑]] - [[上下文关系]]
																		- [[右侧栏]]
																			- 不跳出主界面完成所有任务
																		- [块引用]([[block-ref]])
																		- [块嵌套]([[block-embed]])
																			- 10:00
																				- 任务 a
																				  id:: 637df517-83f4-4208-a0e9-8ac54969f907
																				- #log
																					- 12:00 - 13:00
																						- 午饭
																		- [[聚焦]]
																			- 忘掉首页
																			- 永远当下
																			- 全神贯注
																		- alias [[属性]]([[property]]) #property #pass
																		- [[闪卡]]
																		- [[提醒]]
																		- [[字体]]
																			- **[[The quick brown fox jumps over a lazy dog]]**
																			- *[[The quick brown fox jumps over a lazy dog]]*
																			- ==[[The quick brown fox jumps over a lazy dog]]==
																			- `[[The quick brown fox jumps over a lazy dog]]`
																	- *个人理解*
																		- [[Favorites]]  #recycle
																		- [[Contents]] #pass
																		- []()
																- *快捷操作*
																  collapsed:: true
																	- [[keyboard-shortcuts]]： `cmd + e/d` ；`cmd + w/r` #[[keyboard-shortcuts]] #pass
																	  id:: ************************************
															- #step
																- ((637de218-2ed0-46ba-927c-63ca857e2b33))
																- DONE 同步 [[logseq-css]] & ((6329e2b4-935e-4b64-bef8-89a91bc08b0e))
																- DONE 优化 [[iPad]] [[logseq-css]] 与 [[Config]]
															- #link
													- ((6377c75e-f755-469c-9430-0c211e976152))
											-