- 00:13 ((63818ec1-2dd7-478a-8539-d4b75e91f6b6))
  id:: 6380e993-ed27-47f2-bc53-5bbd31bffd4f
  collapsed:: true
	- #offline
	- 11:57
	  id:: 63818ec1-2dd7-478a-8539-d4b75e91f6b6
		- #online #weekend
			- #continue
				- ((651aa257-6b75-4549-82e2-69f005661986))
				- ((651aa256-3898-464e-bd91-6752c7aed81e))
			- #[[回忆]]
			  collapsed:: true
				- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 昨天 [[睡得早]] ，1点 [[上床]] ，1:20睡着，10点醒，睡了8.5小时。 [[深度睡眠时长]] 2.5小时，非常 [[舒服]] #2小时深度睡眠
					- 对比之前有一天，睡了8小时， [[深度睡眠时长]] 达到了3小时，那个质量是贼高。
						- #截图
							- ![WechatIMG336.jpeg](../assets/WechatIMG336_1669437902738_0.jpeg){:width 245}
						- #link
							- ((6380e8c6-bcdc-4e7a-af52-d2a53d8c2d08))
			- #continue
				- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
				  :LOGBOOK:
				  CLOCK: [2022-11-26 Sat 12:36:08]
				  :END:
				  ```calc
				  251.8
				  ```
					- #and
						- DONE 11.25 ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
						  ```calc
						  29.7+1+27
						  ```
		- #log
			- 12:51 - 13:10
			  collapsed:: true
				- DONE 帮 [[王露]] 拿旧的 [[儿童餐桌]]
				  :LOGBOOK:
				  CLOCK: [2022-11-26 Sat 12:51:10]
				  CLOCK: [2022-11-26 Sat 12:51:25]--[2022-11-26 Sat 13:10:37] =>  00:19:12
				  :END:
			- 13:30 - 16:25
			  collapsed:: true
				- 午饭
				  :LOGBOOK:
				  CLOCK: [2022-11-26 Sat 13:30:53]--[2022-11-26 Sat 16:24:46] =>  02:53:53
				  :END:
				- DONE [[午休]]
				- ((651bc212-523a-48b7-bce5-e444544ae14b))
			- 16:36 - 16:43
			  collapsed:: true
				- #摘录
					- [[间隙日记]] ：结合 [[笔记]] 、 [[待办事项]] 和 [[时间跟踪]]
					  link:: [Interstitial journaling: combining notes, to-do & time tracking - Ness Labs](cubox://card?id=********************************)
					  #RoamResearch #Logseq #时间记录
						- [[Interstitial journaling]] 是由 [[Tony Stubblebine]] 创造的一种 [[生产力技术]] 。据我所知，这是将记笔记、任务和时间跟踪结合在一个独特的工作流程中的最简单方法。您不需要任何特殊软件，但由于日常笔记的灵活性，Roam Research 使它变得更加容易。插页式日记对我的 [[工作效率]] 和 [[创造力]] 产生了惊人的影响，我想很多人都会喜欢它。
							- 插页式日记的基本思想是每次休息时写几行，并跟踪您做这些笔记的确切时间。例如：
								- 10:04 - 将完成正念生产力文章的初稿。
								- 10:46 - 我又掉进了推特黑洞！回去工作。
								- 11:45 - 进展顺利。需要准备好与查理会面。
								- 11:49 - 审查议程和文件。感觉有点着急，但我想一切都会好起来的。需要在会议结束后打电话给 Anna 汇报情况。
							- 注意目标（“完成初稿”）、自我意识（“陷入 Twitter 黑洞”、“感到焦虑”）、自我审查（“进展顺利”）和可操作项目（“给安娜打电话”）的组合)?
							- 我喜欢插页式日记，因为它是让你的休息时间更加用心的好方法。
								- 主动休息：反思你之前的任务，为下一个任务做计划，把握自己的心理脉搏，记下任何想到的其他事情，以减轻你的认知负担。
								- 拖延休息：了解这些休息时间以及实际需要多长时间。当你养成记下所有休息时间的习惯时，不打开新标签“快速”查看 Twitter 就会变得更容易。您不想自己承认失败。
							- 您的插页式日记不仅是一本日记，还是一份待办事项清单、一个笔记系统，以及一种有意义地跟踪您的时间的方式。正如我提到的，您可以在任何地方保存插页式日记。即使是文本文件也能正常工作。
							- 如果您是 Roam Research 用户，让我们看看如何轻松地在那里进行设置。我说的是“设置”，但实际上……工作已经为您完成。
						- 在漫游研究中保留插页式日志
							- 在我的 Roam 初学者指南中，为了简单起见，我完全省略了 Daily Notes 部分。现在一起来看看吧。这就是带有插页式日记的每日笔记的样子。
								- #示意图
									- ![](https://cubox.pro/c/filters:no_upscale()?imageUrl=https%3A%2F%2Fnesslabs.com%2Fwp-content%2Fuploads%2F2020%2F04%2Finterstitial-journaling-example.png){:width 245}
								- [[跟踪时间]] 。键入 /time 以插入当前时间，然后键入您想要的任何内容。
								- [[跟踪任务]] 。输入 /todo 来创建待办事项。完成后勾选这些待办事项。
								- [[跟踪内容]] 。当你偶然发现一些有趣的东西会扰乱你的工作流程时，将它添加到主列表中，例如 [[To read]]。你可以看到我在这张截图中用一篇看起来很有趣的文章完成了它，但它与我试图写的文章无关。
								- [[跟踪想法]] 。同样，如果您想到今天想做的其他事情，只需在您想到的时间和地点将其添加为待办事项即可。对于使用 [[今天]]、[[明天]]、[[某天]] 系统的人，您还可以将其添加到待办事项中，或添加特定日期，就像我在“呼叫 Morgane”中所做的那样。 ”
								- [[跟踪幸福感]] 。我喜欢在一天的工作开始时快速记录一下我的感受，有时让我 [[彻夜难眠]] 的任何事情，以及我预计当天会遇到的任何主要障碍。它很少超过一个要点，但它是照顾我整体健康的好方法。我还以类似的快速结束语结束了一天的工作。
							- 我真的觉得写这些说明很愚蠢。这是一个非常简单的系统，可以让您的日记更具可操作性——或者真正开始日记练习。与往常一样，它可能并不适合所有人，但它对我来说创造了奇迹。我仍然使用 Plus Minus Next 日记来进行我的每周回顾，而插页式日记让您更容易回过头来看看一周内哪些进展顺利，哪些进展不顺利。
			- 16:58
				- #continue
					- ((636a7bea-180a-42ff-b63a-a7fc4446f498))
					  :LOGBOOK:
					  CLOCK: [2022-11-26 Sat 16:58:57]--[2022-11-27 Sun 00:07:50] =>  07:08:53
					  :END:
				- #log
					- 17:17 - 17:39
					  collapsed:: true
						- CANCELED [儿子]([[毛懋]]) [[睡醒]] 了， [[翻转犁]] [[快递]] 到了 
						  :LOGBOOK:
						  CLOCK: [2022-11-26 Sat 17:17:54]
						  CLOCK: [2022-11-26 Sat 17:18:02]
						  :END:
							- #回忆
								- [老婆]([[王露]]) 搞错了， [儿子]([[毛懋]]) [[空欢喜]] 一场
					- 17:45 - 20:12
					  collapsed:: true
						- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
						  :LOGBOOK:
						  CLOCK: [2022-11-26 Sat 17:46:05]--[2022-11-26 Sat 20:12:52] =>  02:26:47
						  :END:
						- 晚饭
					- 20:12 - 23:10
					  collapsed:: true
						- ((651bc21c-3131-406f-9608-99fda127b5e3)) & ((651bc212-523a-48b7-bce5-e444544ae14b))
						  :LOGBOOK:
						  CLOCK: [2022-11-26 Sat 20:13:04]--[2022-11-26 Sat 22:47:02] =>  02:33:58
						  :END:
						- DONE [[高祥]] [[电话]]
						  collapsed:: true
							- #回忆 #感恩，珍惜当下
								- 高祥还没 [[放弃]] 我，真 [[不容易]]。
						- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
					- 00:41 - 00:53
					  collapsed:: true
						- DONE [[设置]] ： ((63752ca4-ffdd-4056-a4b0-0bf6d11336b8)) -> 试试把 `#` 颜色也改成和 `i` 一样呢
						  id:: 63982e31-7c38-4d13-bc04-2415ddc66d91
						  :LOGBOOK:
						  CLOCK: [2022-11-27 Sun 00:41:09]
						  CLOCK: [2022-11-27 Sun 00:41:16]--[2022-11-27 Sun 00:53:03] =>  00:11:47
						  :END:
				- ((63824a01-542f-4eb6-a106-32797233018e))