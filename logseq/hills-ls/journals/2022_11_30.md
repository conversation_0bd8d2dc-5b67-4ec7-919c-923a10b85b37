- 23:47
  id:: 6386298b-d63e-4e0d-b0cc-304111da1f85
  collapsed:: true
	- #break
	  id:: 638629b4-9d33-4605-a95a-b062bfa3c026
	- 01:16
		- #offline
		- 10:01
		  id:: 6386b959-e528-48f6-a145-8815d12427e5
			- #online
			  id:: 6389fb14-3973-4d0d-a38a-69c872f59901
				- [[封控]] [[Day]] 1
					- #回忆
					  collapsed:: true
						- 100 30 2
						- [[卧槽]]， [[家]] 楼下有人 [[阳]] 了。整栋楼 [[封控]] 了。 #黄埔花园
						  id:: 6389fb14-3023-4cbb-b9b8-950b47824569
					- #[[回忆]]
					  collapsed:: true
						- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 1点44入睡，醒来8点30*（其实我感觉有睡到9点）*，睡饿了6小时46分，[[深度睡眠时长]]2小时，非常标准。我的 [[高质量]][[睡眠]] 应该是睡6.5小时，有2小时 [[深度睡眠]] #2小时深度睡眠
						  id:: 6387993f-7798-4a31-8788-97f4e52db7cf
							- #截图
								- ![WechatIMG348.jpeg](../assets/WechatIMG348_1669774160850_0.jpeg){:width 245}
					- DONE [[核酸]]
					- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
			- 11:20
			  id:: 6386dfab-166a-4d88-8868-f318b465da3f
				- #break
					- ((651bc212-523a-48b7-bce5-e444544ae14b))
					  :LOGBOOK:
					  CLOCK: [2022-11-30 Wed 11:20:48]--[2022-11-30 Wed 14:52:56] =>  03:32:08
					  :END:
					- 午饭
					- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
				- 14:53
				  id:: 6386fdd0-fb04-4a65-b6c7-6378a1a9964c
					- #摘录
						- CANCELED [如何成为黑客 · Dark Side](cubox://card?id=********************************)
						  #黑客 #[[Eric S. Raymond]]
						- CANCELED [ryanhanwu/How-To-Ask-Questions-The-Smart-Way： 本文原文由知名黑客Eric S.Raymond写，教你如何正确地提出技术问题并获得你滿意的答案。](cubox://card?id=********************************) #黑客 #[[Eric S. Raymond]]
						- CANCELED [增强长期记忆力](cubox://card?id=********************************)
						- CANCELED [这书本的封面撕不烂？这本书的页码是00：00：00？](cubox://card?id=********************************)
					- #闪念
						- TODO [想看]([[想看]])[[处子之山]]
					- 15:18
						- #break
							- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
							  ```calc
							  0
							  ```
						- 15:46
						  id:: 638704ec-d722-414a-bd07-8167e9371aee
							- #continue
								- ((638c9074-68d9-4f30-a847-679b8d4706cf)) p11
								  id:: 638819d2-ee6e-441e-a524-80194d196aab
								  :LOGBOOK:
								  CLOCK: [2022-11-30 Wed 15:46:47]
								  CLOCK: [2022-11-30 Wed 15:46:51]--[2022-12-01 Thu 01:45:42] =>  09:58:51
								  :END:
							- #log
							  collapsed:: true
								- 17:03 - 17:07
								  collapsed:: true
									- #pee-time
								- 17:08 - 17:28
								  collapsed:: true
									- DONE 更新： ((6386dfcd-3a47-4814-9252-ba23afba7ede))
									  id:: 63871d7b-9314-4f8e-85e3-bd7ec01e7974
									  :LOGBOOK:
									  CLOCK: [2022-11-30 Wed 17:08:39]--[2022-11-30 Wed 17:09:31] =>  00:00:52
									  :END:
								- 18:08
								  collapsed:: true
									- #break
										- #回忆
											- [儿子]([[毛懋]]) [老婆]([[王露]]) [[午睡]] 醒了
										- 晚饭
										  :LOGBOOK:
										  CLOCK: [2022-11-30 Wed 18:20:06]--[2022-11-30 Wed 20:06:55] =>  01:46:49
										  :END:
										- ((651bc21c-3131-406f-9608-99fda127b5e3))
										  :LOGBOOK:
										  CLOCK: [2022-11-30 Wed 20:04:57]--[2022-11-30 Wed 20:41:30] =>  00:36:33
										  :END:
										- ((651bc212-523a-48b7-bce5-e444544ae14b))
										  :LOGBOOK:
										  CLOCK: [2022-11-30 Wed 20:41:33]--[2022-12-01 Thu 14:11:56] =>  17:30:23
										  :END:
							- 01:45
								- #continue
									- DONE 搞定了 [[logseq]] 原生 [[pdf]] [[摘录]] [[workflow]]
									  id:: 6471e8bd-c122-47e9-81f2-22b46e3e21e3
									  collapsed:: true
								-
- [[Comments]]
  collapsed:: true
	- #回忆
		- 太 [[丝滑]]，太 [[酸爽]] —— [[真香定律]]，之前还反复尝试反复拒绝来着，在 [[Marginnote]] - [[logseq]] 导出划线实在是不好用之后，再一次尝试：发现还是非常舒服的： -> ((6471e8bd-c122-47e9-81f2-22b46e3e21e3))
			- 边阅读，边摘录 -> 摘录直接进入 [[logseq]] 数据库，可被搜索，可编辑，可打标签。
			- 梳理文章结构、知识点可以直接在摘录页面操作，省去复制黏贴的那部分时间
			- 边阅读，边记感想的速度加快 -> 直接复制摘录的块引用到 [[journal-page]] 即可
				- 双开、三开都可以
					- 单开，一边记感想
					- 三开，一边记感想，一边整理内容知识结构
			- 摘录页面可以与书名别名上，和书名有关的内容同步[linked-reference]([[linked-reference]])过去