- 00:07 ((63922307-ed1c-41a7-8ba5-c0a7dd704adc)) ((63929fb9-8a5b-48d3-9481-b85646206101)) ((6392a3ca-548b-4c30-b573-d916ed6ec50a)) ((6392b1c0-947e-4252-8eb7-bac158fa7f8e)) ((63930b1a-c6a0-46ea-abe5-8fa608453411))
  id:: 63920bb9-8881-4afd-b826-f7d62c4bb19c
  collapsed:: true
	- #回忆
		- 今天 [[正事]] 儿又没干，明天早上起床第一件事就 [[干正事]] 。
		  id:: 6392aa1d-4388-4ff4-8f17-4d9576b35e45
	- 00:09
		- :LOGBOOK:
		  CLOCK: [2022-12-09 Fri 00:10:03]--[2022-12-09 Fri 00:37:02] =>  00:26:59
		  :END:
			- DONE [[上传]] [[pdf]] -> ((63f2e47b-c89f-4a7b-a634-121d68c65e30))
			  :LOGBOOK:
			  CLOCK: [2022-12-09 Fri 00:21:45]
			  CLOCK: [2022-12-09 Fri 00:22:08]--[2022-12-09 Fri 00:36:40] =>  00:14:32
			  :END:
		- 00:37
			- #break
				- #回忆
					- DONE 和 [老婆]([[王露]]) 聊明天的 [[面试]]，给了她几点 [[建议]]
					  id:: 6392aa1d-e228-4491-9b64-1c5988c4854d
					  collapsed:: true
					  #成功的面试
						- 如果根据自己 [[兴趣]] 选择的面试机会，不要辜负这一份兴趣。
						- 每个人都有不足，不要拿着岗位要求再次肯定自己的不足。没有任何必要。我难道不知道吗？
						- 应当把不足看作挑战，用兴趣迎接挑战，你有几分胜算？ #问题
							- #案例 以跨境电商为例，你觉得自己的不足*（挑战）*有这3点：
								- 1、跨境选品工具不熟悉
								- 2、境外用户需求不了解
								- 3、跨境研发生产供应链不熟悉
							- #答案 1和3只要公司给你提供支持和资源，2-3个星期足矣；至于第二个，就看公司给不给你试错空间了，是要一下做爆品呢，还是有做多个品的是错机会？
								- 所以这些挑战，分析之后，就可以把大部分不确定的变成确定性的目标。
						- 不要以入职作为目标，那只是众多 [[靶子]] 的最后一个，你应该追求的是一次[[成功的面试]]。
							- 你更了解了对方公司的这个行业的职位要求
								- 你对自己的不足面对这个行业岗位有一个清晰的判断
						- 至于别人给不给你机会，就看你的优势在人家眼里够不够需要了
						- 面试尽全力成功，入职看缘分。无非是多打几个靶子而已，总会打到 [[靶心]] 的，只要每一次都以拿到[[成功的面试]]为目标。
						- 至于态度，都要考虑面试用什么态度了，那一定是你面试的岗位你是得心应手，毫无压力，准备好火力全开，收下面试官的膝盖了吧！
							- 就像你去面试教育培训类的运营岗，那可以考虑用什么态度。
							- 但如果你面试的岗位，你是有看得见的短板的，有不确定性的，而且面试你的人看起来是实操的、经验丰富的…… 那还是 [[诚恳]] 的和对方开诚布公的沟通吧，不用端着，也不用跪着，平等的交流，没有人能拒绝 [[真诚]] 和 [[坦诚]]
								- #link
									- ((63930b1a-b44c-4d0b-ac44-836e26dbb474))
						- #link
							- {{embed ((63921119-b7b1-4730-bca4-b24beb9e1d0f))}}
			- 01:35
				- #break
					- ((651aa257-6b75-4549-82e2-69f005661986))
				- 01:46
				  id:: 63922307-ed1c-41a7-8ba5-c0a7dd704adc
					- #offline
					  id:: 63922309-ad12-4ed4-bccf-c8b57ec2d303
					- 10:38
					  id:: 63929fb9-8a5b-48d3-9481-b85646206101
						- #online
							- #continue
								-
								- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
							- collapsed:: true
							  :LOGBOOK:
							  CLOCK: [2022-12-09 Fri 10:42:44]--[2022-12-09 Fri 10:46:19] =>  00:03:35
							  :END:
							- #[[回忆]]
								- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 1点46收工，2点就睡着了。睡了7个小时14分， [[高质量]][[睡眠]] 是在前5个小时。[[深度睡眠时长]]有2.5小时，接近50%了。贼 [[牛逼]] #2小时深度睡眠
									- #截图
										- ![WechatIMG373.jpeg](../assets/WechatIMG373_1670553845198_0.jpeg){:width 245}
							- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
							  :LOGBOOK:
							  CLOCK: [2022-12-09 Fri 10:46:27]--[2022-12-12 Mon 10:57:52] =>  72:11:25
							  :END:
							  ```calc
							  1
							  ```
						- 10:56
						  id:: 6392a3ca-548b-4c30-b573-d916ed6ec50a
							- #回忆
								- [[今日份快乐]]：在昨天我表达了懒得提交 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) 给官方之后。[[logseq]] [[电报]] [[群主]] 在帮我积极的和 [[官方]] 沟通，已经问我要了 github 账号了，哈哈哈哈。
								  id:: 6392aa1d-8012-475b-af24-3bf8d489715c
									- [[分享]]：[[logseq]] [[面包屑]] [[logseq-css]] 我做了什么
									  id:: 63934678-8ca4-494e-875a-56c849e15f37
									  collapsed:: true
										- 目前的思路是把面包屑中不适合显示的内容隐藏掉：
											- 早前的版本我已经隐藏了 scheduled 和 [[block-ref]]相关信息，例如sch & deadline 时间，时间统计等等，这些碎片信息直接隐藏就行。
											- 最新的版本，是处理了涉及整个block的不适合在面包屑显示的：embed、图片、property、箭头hover & click、iframe（youtube、tiwitter）、quote、code
											  id:: 6392aa1d-cc69-44b3-a8b2-90f59f176cb8
												- 思路是用缩略图或者〇替换到整个div
												- 遇到的问题是，embed 可以替换，但无法点击；iframe 无法替换，也无法点击。
											- 再补充一点：[[block-ref]]在面包屑中显示时，是无法起到导航作用的：点击后会脱离当前逻辑线，跳到[[block-ref]]的原始block。
												- 因此我尝试把面包屑的箭头改为可点击，这样在遇到[[block-ref]]出现在面包屑上时，只要点击前面的箭头，就可以正常的按逻辑线进行父子级浏览了。
									- #截图
									  collapsed:: true
										- ![WechatIMG374.jpeg](../assets/WechatIMG374_1670555533331_0.jpeg){:width 245}
										- ![WechatIMG375.jpeg](../assets/WechatIMG375_1670555548770_0.jpeg){:width 245}
									- #link
										- ((6392222d-a18b-44a4-a6bc-74f76218ad06))
							- 11:20
								- #break
									- DONE [[pdf]] [[上传]] ((63f2e4bd-e58b-4e30-a424-21475822210a))
									  :LOGBOOK:
									  CLOCK: [2022-12-09 Fri 11:26:58]
									  CLOCK: [2022-12-09 Fri 11:27:02]--[2022-12-09 Fri 11:32:36] =>  00:05:34
									  :END:
								- 11:39
									- #回忆
										- 光速 [[打脸]] ((6392aa1d-4388-4ff4-8f17-4d9576b35e45))
									- #smoking
										- #回忆
											- 看到 [老婆]([[王露]]) 的新 [[发型]]，很不错！ [[Charliie]] 又在 [[Discord]] 上 [[肯定]] ，建议我去开 [[GitHub]] 提交代码，哈哈哈。 [[正反馈]] [[增强回路]]。感觉好爽！
											  id:: 6392b0f3-5e1e-44b7-8943-0fa7714cf1cf
												- #截图
												  collapsed:: true
													- ![WechatIMG376.jpeg](../assets/WechatIMG376_1670558099065_0.jpeg){:width 245}
												- #link
													- ((6392aa1d-8012-475b-af24-3bf8d489715c))
									- 11:55
									  id:: 6392b1c0-947e-4252-8eb7-bac158fa7f8e
										-
											-
										- #log
										  id:: 6392b3d8-464f-46fb-845c-f19aad3efc86
										  collapsed:: true
											- 11:55 - 12:30
												- #break
											- 12:30 - 15:01
												- #回忆
													- 午饭 with [[王露]]
													  :LOGBOOK:
													  CLOCK: [2022-12-09 Fri 12:30:47]--[2022-12-09 Fri 15:01:09] =>  02:30:22
													  :END:
														- #and
															- ((6392b0f3-5e1e-44b7-8943-0fa7714cf1cf))
															  collapsed:: true
																- CANCELED [[Charliie]] 说教我使用 [[GitHub]] [[pr]] #Logseq #logseq-css #recycle
																  id:: 63934678-79bb-458f-b47c-bb6bc7858e0d
																	- #link
																		- ((63982e3c-3efb-4612-b661-dfa44c81b992))
																- #回忆
																	- 体会到了 [[开源]] [[世界]] 的好。[[coding]] 是一门 [[世界语言]] 啊。
																	  id:: 63934678-ae96-4a1f-adb1-476fcd22c250
															- [老婆]([[王露]]) 又去 [[面试]]了，说还是有点小 [[紧张]]。没事的！ [[lucky]]！
															  id:: 6392d6ce-f391-460f-bd25-1c24bbece8be
															  collapsed:: true
																- *14:46*
																	- 微信说，居然还要 [[做题]] 哈哈哈 笑死了
																		- 买书。小红和小丽一块到新华书店去买书,两个人都想买《综合习题》这本书,但钱都不够,小红缺少4.9元,小丽缺少0.1元,用两个人合起来的钱买一本,但是钱仍然不够,那么,这本书的价格是多少呢?
																		- 切西瓜 ，切10刀 最多切多少块 最少多少块
															- [[再见]] 了， [[核酸检测]]。不， [[再也不见]] 。
															  collapsed:: true
															  #后会无期 #疫情 #新冠疫情 #奥密克戎
																- #照片
																	- ![WechatIMG381.jpeg](../assets/WechatIMG381_1670563949284_0.jpeg){:width 245}
																- #link
																	- ((638eea9b-7395-4400-bcab-d376aa6131fe))
																- ps： [[新十条]] 发布后， [[健康码]] 走进历史，进出各种场所不需要再看。昨天看 [[王志安]] ，说到 [[中国]] [[防疫政策]] 如此之大的 [[急转弯]] ，感动的 [[热泪盈眶]] 。应该要感谢那些 [[勇于发声]] 的 [[年轻人]] 。从 [[应检尽检]] 到 [[应阳尽阳]] ，生活在这片土地，如此 [[荒诞]] ，但我们还是如此爱他。
																	- #摘录
																		- TODO [王局拍案｜正常的日子终于要回来了！](https://youtu.be/Bmynu45SQRM) #文案 #pass
																- ps2：从昨天 [[黄埔花园]] [[北门]] 开启，进出无需掏手机扫码。到今天 [[阿叔猪扒包]] 很自然的坐下 [[点餐]]，仿佛 [[隔离]] [[核酸检测]] [[扫码]] [[健康码]] 都不曾发生过，非常 [[魔幻]]。想起 [[Twitter]] 上的 [[反贼]] 们的 [[垂死针扎]]： [[中国人]] 是健忘的，永远不要忘记…… 没错，我觉得我也很快要 [[忘记]] 这3年发生的一切了。在记忆中被自动 [[抹除]] 了。
														- #闪念
															- 如果你 [[不想上班]] ， 想[[摆烂]]， 就去得 [[新冠]] 。 #文案
														- #摘录
															- DONE 果然 [[真诚]] 才是 [[必杀技]]
															  link:: [微博](https://m.weibo.cn/detail/4844488549478050)
															  id:: 63930b1a-b44c-4d0b-ac44-836e26dbb474
															  collapsed:: true
															  #笑话
																- #截图
																	- ![a2861cf0gy1h8wmxpwi2kj20y00u0ad2.jpg](../assets/a2861cf0gy1h8wmxpwi2kj20y00u0ad2_1670568592184_0.jpg){:width 245}
																	- ![a2861cf0gy1h8wmxqfx3gj20u0140whl.jpg](../assets/a2861cf0gy1h8wmxqfx3gj20u0140whl_1670568605404_0.jpg){:width 245}
																	- ![截屏2022-12-09 14.49.34.png](../assets/截屏2022-12-09_14.49.34_1670568622619_0.png){:width 245}
															- DONE [一个真实的强奸案，案子之外的事叫人流泪](https://m.weibo.cn/detail/****************)
															  collapsed:: true
															  #故事 #法律 #中国 #家庭 #穷 #法制 #诬告 #坐牢
																- #截图
																	- ![WechatIMG384.jpeg](../assets/WechatIMG384_1670568779950_0.jpeg){:width 245}
															- DONE [深圳个人破产信息网上，看完一百种普通人的失败](cubox://card?id=********************************)
															  SCHEDULED: <2022-12-20 Tue .+1d>
															  id:: 63b39ff8-bed9-4969-a90b-b0b9f39fbd6c
															  link:: [深圳个人破产实施首月：申报的债务多为复合型 金融债权占比较高 | 每经网](cubox://card?id=********************************)
															  collapsed:: true
															  :LOGBOOK:
															  * State "DONE" from "TODO" [2022-12-10 Sat 19:34]
															  * State "DONE" from "TODO" [2022-12-13 Tue 10:25]
															  * State "DONE" from "TODO" [2022-12-14 Wed 09:30]
															  * State "DONE" from "TODO" [2022-12-15 Thu 14:36]
															  * State "DONE" from "TODO" [2022-12-15 Thu 15:00]
															  * State "DONE" from "TODO" [2022-12-19 Mon 15:50]
															  :END:
															  #破产 #深圳 #中年 #普通人 #人生 #失败
																- _**破产，在这两年不是一件[[新鲜事]]。**_
																  collapsed:: true
																	- 热搜里总是不缺某家商业巨头宣告破产的消息，或者是某位风云人物行将破产的传闻。
																	- ![图片](https://image.cubox.pro/article/2022120819145929198/91371.jpg){:width 245}
																- _**破产，往往意味着一种[[盖棺定论]]的[[失败]]。**_
																  collapsed:: true
																	- 看着那些熟悉的名字与破产出现在一起，人们或多或少都会生出些宏大的唏嘘。
																	- 然而，世界擅长为大人物的失败写传奇，却鲜有人会为小人物的失败作序。
																- _**就像个人破产这件事，怎么听着都让人感觉有些陌生。**_
																- 直到最近，我们发现了这样一个网站 —— **深圳个人破产案件信息网。**
																  collapsed:: true
																	- ![图片](https://image.cubox.pro/article/2022120819150182497/67586.jpg){:width 245}
																	- 此官方网站记录了近年来深圳地区大量的个人破产案例，基于当地的个人破产信息公开和公示机制，多数案例都是不予保密的。
																	- 通过这些案例中事无巨细的个人破产申请公告，我们可以窥见当下许多**属于[[普通人]]的[[失败]][[故事]]。**
																		- 其间各个阶层、各色人等，各种令人缄默不语抑或怆然涕下的破产原因跃然屏上。
																		  collapsed:: true
																			- 犹如一场时代的失败学教育。
																				- 不同的是，成功学的故事都是为你编的。
																					- 但个人破产案件信息网上的每一桩失败案例，都是真实发生的，
																						- _**或者正在发生的。**_
																		- **01**
																			- **我们先说说，什么叫个人破产？**
																			  collapsed:: true
																				- 学术上的说法是这样的↓
																					- ![图片](https://image.cubox.pro/article/2022120819150157869/11451.jpg)
																				- **个人破产，并不是不用还钱了。**
																					- 有能力偿还的要**全额还款**，暂时没能力的可以申请**分期还款**，永远没有能力偿还的，那你**还剩多少就要还多少**。
																						- 总之，申请个人破产首先意味着**个人资产和个人信用的全面崩溃。**
																							- **你几乎是一无所有了。**
																			- **在深圳个人破产信息网的公开案件中，造成债务人一无所有的原因，可以说五花八门。**
																			  collapsed:: true
																				- 有一对从湖北来深打拼的小夫妻，两人刷了几十万信用卡，于2019年中开始创业，加盟了一家主营机动车驾驶员培训报名的公司。 #故事
																				  collapsed:: true
																					- 令人欣慰的是，到2019年底，两人的生意就步入了正轨，好的时候，月收入能达到4万元。
																						- 按这个节奏，他们很快便可以回本，然后美美地赚上一笔。
																							- _**但比这更快的是，疫情来了。**_
																								- 2020年过年，受疫情影响，他们的生意持续下滑，两人又被困在了湖北老家很久。
																									- 其间，他们还在按时缴纳场地租金，直到一些合同开始违约，直到部分学员未按时交钱，直到连环效应下他们垫资崩坏开始亏损，_**最终宣布破产。**_
																										- _**至申请破产时，两人债务共计约83万元。**_
																				- 还有一位外地来深连续创业的中年老哥，自2018年开始在深圳创办了3家公司。 #故事
																				  collapsed:: true
																					- 第一家已被注销，第二家现已转让，他扛着前两家公司存续期间以个人名义承担的80万元债务，继续经营着第三家公司。
																						- **而[[中年人]]的[[磨难]]好像总是[[集中到来]]的。** #金句
																						  collapsed:: true
																							- 2020年，他先是遭遇母亲重病住院；2021年，他的女儿出生，却是先天唇腭裂。
																							- 他不得以透支多张信用卡，以及从多家金融机构贷款，来偿还对外债务并维持基本生活。
																							- 他的最后一家公司，又是一家电子商务公司，在疫情持续之下艰难运转。
																							- **2021年最后一个季度，他来申请破产清算。**
																			- **“创业+借**__**贷”≈ 破产，似乎是个人破产案件信息网上的一组流行公式。**
																			  collapsed:: true
																				- 我发现许多人的破产，都与这两个因素有关。
																				- _**而疫情是**__**其中的“黑天鹅事件”，犹如催化剂迫使这个公式加速倒向结果。**_
																				- 有人被疫情引爆了本来就隐患深重的财务状况。
																					- ![图片](https://image.cubox.pro/article/2022120819150113301/97568.jpg)
																				- 有人是因为疫情失了业，就马上走向了破产。
																					- ![图片](https://image.cubox.pro/article/2022120819150174707/20381.jpg)
																				- _**还有很多人走到破产这一步，则颇有些宿命论的味道。**_
																					- 一位2014年研究生毕业的80后，第一份工作铁饭碗，孝顺的他借债10万给母亲翻修了老房子。
																						- 随后，他却倒霉地撞坏了教练车，负债达到了十几万。
																							- _**但这件小事只是他不幸的开始。**_
																								- 后来，他来到深圳工作，谈婚论嫁时女友父母要**求必须有房子**，于是他_**拿**__**小额贷买期房上了车。**
																									- 结果由于_**每月的房贷利息极高**_，女友也因为这件事跟他分手了。
																										- 直到跟女友分开几年后，他仍没能住进这间新房。
																											- 5年后，他终于等到交楼把房子卖掉，此时却已不够清偿利息和债务。
																												- _**于是，这位80**__**后，在他毕业后的第八年，个人负债达到了150余万元。**_
																													- 与他命运相似的，还有一位2016年在珠海买了房的航空机师，如今住在深圳的公租房里。
																														- 本来没有交集的他们，现在名字一起出现在个人破产信息网的公示栏里。
																			- **再有一些人的破产，乍看上去像是运气不好，细想之下却有着时代伤痕。**
																			  collapsed:: true
																				- 有人在群里关注了一个公众号做任务，最后被诈骗了50多万元，于是破产。
																					- **有人被陌生人骗，就有人被熟人骗。**
																					- 有许多案例，都是员工不明不白替公司老板背了贷款，随后老板跑路了，至今自己还在还债。
																						- _**我们现在看来可能会觉得他们警惕性太低，可是社畜更多的也许是身不由己。**_
																			- 当然，终归也有人的破产几乎完全是由自己造成的。
																			  collapsed:: true
																				- 他们横冲直撞，屡次扎进经济规律的坟场，其破产案例就是社会断层里的电子化石。
																				- 我印象最深的是一个从2015年在股市里挣扎的中年散户，从股票炒到期货，七年来大盘风云几次，他最后亏损300多万。
																					- **他的经历，应该也是一代典型股民的缩影。**
																			- 在深圳个人破产案件信息网浏览久了，你会愈发觉得这里像是一所普通人的失败博物馆。
																			  collapsed:: true
																				- 每一桩破产，你也许都能在自己的生活里找到一个模糊的名字，或者一张具体的脸。
																					- **就像故事主人公们脚下这座共同的城市——深圳，仍是一个生动的时代标本。**
																		- **02**
																			- 思绪回到现在，我们再看向深圳个人破产信息网上的故事，就难免生出一丝丝惆怅与唏嘘。
																			- _**在那些破产案例的自述中，多数年轻人你不能说他们是不勤奋的。**_
																			- 据深圳破产法庭庭长曹启选等发表的文章《个人破产制度先行先试中的实践示范与体系构建》，在1031份破产申请样本中，七成以上申请人处于30～50岁的青壮年时期。
																			- _**“有创业失败或者经营不善经历的破产申请593件，占比57.5%。”**_
																			- 他们依旧辛苦，依旧辛勤，依旧为了过上更好的生活而不停地折腾。
																			- 看起来跟曾经扛着大包来到深圳火车站的那些人，好像没什么差别。
																			- _**可事实终究证明，在时代的浪潮面前，一些人的能动性也许是十分有限的。**_
																			- ![图片](https://image.cubox.pro/article/2022120819150181391/99371.jpg)
																			- 关于破产这件事，之前人们总是乐于看到谁在天台上纵身而下。
																			- ![图片](https://image.cubox.pro/article/2022120819150151770/69761.jpg)
																			- 又或者是爱看某个大人物很快就东山再起，王者归来。
																			- ![图片](https://image.cubox.pro/article/2022120819150130951/63271.jpg)
																			- 可如今，你有空的时候看看个人破产信息网也挺好的。
																			- _**可以一窥时代的浮沉。**_
																			- 2004年，慕容雪村写过一本书叫《天堂向左　深圳往右》。
																			- 时过境迁，我们面对的很多情况都发生了变化。
																			- 但这本书的豆瓣简介上有一段话，我觉得仍适合作为这篇文章的结尾：
																			- **_“这是最好的时代，这是最坏的时代，诸神俱死，英雄凋谢。_**
																			- **_然而凡俗的生活依然有着最刻骨铭心的力量。”_**
												- #log
													- 13:43 - 14:26
														- ((6391bc96-2377-45a7-9e18-393012e7bcca))
															- DONE [[Twitter]] ，还有tiwitter
															  id:: 63930b1a-436b-44be-b4e2-29c253d0e147
															  :LOGBOOK:
															  CLOCK: [2022-12-09 Fri 13:43:53]
															  CLOCK: [2022-12-09 Fri 13:44:02]
															  CLOCK: [2022-12-09 Fri 13:44:15]
															  CLOCK: [2022-12-09 Fri 13:45:09]--[2022-12-09 Fri 14:26:10] =>  00:41:01
															  :END:
																- #案例
																	- {{tweet https://twitter.com/ruanyf/status/1600822319911694336?s=52&t=kXDJpKJnVblu0BoKbq0CdA}}
																	  :LOGBOOK:
																	  CLOCK: [2022-12-09 Fri 14:14:04]
																	  :END:
																		- #摘录
																			- [[20年]] 后，你 [[后悔]] 的是那些没做过的事情。所以扔掉 [[保龄球]] 吧，从 [[避风港]] 启程，让 [[信风]] 推着你的 [[帆]] 。 [[探索]] 。 [[梦想]] 。 [[发现]] 。
																			  #中年 #进步主义 #励志 #永远年轻 #金句 #比喻 #奋斗 #人生向前
																- #link
																	- ((6392aa1d-cc69-44b3-a8b2-90f59f176cb8))
													- 14:28 - 14:34
														- #pee-time #smoking
											- 15:04 - 17:16
												- #break
													- ((651bc212-523a-48b7-bce5-e444544ae14b))
													  :LOGBOOK:
													  CLOCK: [2022-12-09 Fri 15:11:19]--[2022-12-09 Fri 17:14:47] =>  02:03:28
													  :END:
													- DONE [[采耳]]
													  :LOGBOOK:
													  CLOCK: [2022-12-09 Fri 15:16:15]--[2022-12-09 Fri 17:14:49] =>  01:58:34
													  :END:
												- #log
													- 17:14 - 17:16
														- ((6392d6ce-f391-460f-bd25-1c24bbece8be))
															- #回忆
																- [老婆]([[王露]]) 说 拿到了一次 [[成功的面试]] ！ [[nice]]！
																	- #link
																		- ((6392aa1d-e228-4491-9b64-1c5988c4854d))
											- 17:16 - 17:18
												- DONE [[洗把脸]]
											- 17:18 - 17:44
												- DONE 看看 [[进展]] ((6392aa1d-8012-475b-af24-3bf8d489715c))
												  id:: 63934677-4e8d-4d59-b174-743f0a3e7f23
												  :LOGBOOK:
												  CLOCK: [2022-12-09 Fri 17:20:22]
												  CLOCK: [2022-12-09 Fri 17:20:25]--[2022-12-09 Fri 17:36:12] =>  00:15:47
												  :END:
													- #回忆
														- 我的 [[面包屑]] [[logseq-css]] 已经合并 [[GitHub]]  [[logseq]] [[官方]] 代码库了！
														  id:: 63930b1a-47c5-4c76-b290-5f092e249302
															- #截图
																- ![WechatIMG388.jpeg](../assets/WechatIMG388_1670578475430_0.jpeg){:width 245}
																- ![WechatIMG389.jpeg](../assets/WechatIMG389_1670578491898_0.jpeg){:width 245}
																- ![WechatIMG390.jpeg](../assets/WechatIMG390_1670578508921_0.jpeg){:width 245}
															- #link
																- ((6391bc96-2377-45a7-9e18-393012e7bcca))
											- 17:45 - 17:54
												- #摘录
													- DONE [[大V]] 们分享自己的 [[新冠]] [[转阴]] 经验
													  :LOGBOOK:
													  CLOCK: [2022-12-09 Fri 17:48:41]--[2022-12-09 Fri 17:50:25] =>  00:01:44
													  :END:
													  #阳 #奥密克戎 #防疫 #新冠病毒
														- #截图 #微博
															- ![WechatIMG4960.jpeg](../assets/WechatIMG4960_1670579341373_0.jpeg){:width 245}
														- #钟文泽 #Youtube
															- {{video https://www.youtube.com/watch?v=IfUOwQvke9g}}
											- 17:54 - 18:13
												- #break
										- 18:13
										  id:: 63930b1a-c6a0-46ea-abe5-8fa608453411
											- #闪念 #recycle
												- CANCELED 有关 [[超级元素]] 的一切  #ending #[[大项目]]
												  SCHEDULED: <2023-02-28 Tue ++1w>
												  id:: 63930b1a-fec9-4af0-9ee5-b2d658d04439
												  :LOGBOOK:
												  CLOCK: [2022-12-09 Fri 18:18:34]--[2022-12-10 Sat 00:32:31] =>  06:13:57
												  * State "DONE" from "TODO" [2022-12-14 Wed 15:38]
												  CLOCK: [2022-12-14 Wed 15:38:51]--[2022-12-14 Wed 15:38:52] =>  00:00:01
												  * State "DONE" from "TODO" [2022-12-15 Thu 14:36]
												  * State "DONE" from "TODO" [2022-12-28 Wed 18:22]
												  * State "DONE" from "TODO" [2023-01-11 Wed 09:19]
												  * State "DONE" from "TODO" [2023-01-12 Thu 20:24]
												  * State "DONE" from "TODO" [2023-01-30 Mon 12:01]
												  * State "DONE" from "TODO" [2023-02-02 Thu 18:11]
												  * State "DONE" from "TODO" [2023-02-14 Tue 01:10]
												  * State "DONE" from "TODO" [2023-02-25 Sat 09:43]
												  * State "DONE" from "TODO" [2023-02-27 Mon 22:57]
												  :END:
													- #背景
													  collapsed:: true
														- 开启这个 [[项目]] 的原因，是因为我写 [[销售文案]] 写不进去，写进去了又要看关于 [[品牌]] [[产品]] 的 [[策划]]，时间拖久了，都快忘记了。索性，重新 [[规划]] 一遍，使关于 [[超级元素]] 的信息变的更有条理一点。希望能促进我更快的写出 [[文案]]。
														- ((63993947-ac6a-42f8-83cb-99b6a62e2bfc)) ((63993947-a1f1-4d5b-85e3-d296abf5f9b2))
													- #标签
													  collapsed:: true
														- *[[用户]] [[人群]]*
															- [[中年]]
																- #中年人生 #中年女人 #中年妇女 #中年少女 #中年阿姨 #新中年 #中青年 #人到中年 #人至中年 #步入中年 #中年危机 #中年困境 #中年失业 #中年的幸福 #疲惫中年人的日常 #如何翻越中年的那座山 #有了小孩以后就是中年 #活着就已经用尽全部力气的中年人 #醒醒吧！中年人再不卷，真没有机会了 #老阿姨 #人生中场 #人生过半 #半山坡 #人生的半山坡 #陀螺人生
																  #中年
															- [[女性]]
															- [[老人]]
															- [[中产]]
																- #中产梦破灭  #中产焦虑  #理想的中产生活 #穷中产 #中产人士 #中产家庭
															- [[普通人]]
															- [[学生]]
														- *[[场景]]*
															- [[低谷]]
																- #人生低谷 #人生困境 #人生无望 #人生泥潭 #失去希望 #心理困境 #困境 #情绪低谷
															- [[失眠]]
																- #睡不够 #睡不好 #睡不着 #睡太少 #睡得短 #睡不好觉 #睡不着觉 #睡得不好 #睡眠不足 #睡眠变浅 #睡眠剥夺 #睡眠拖延 #睡眠障碍 #碎片睡眠 #失眠症 #失眠者 #失眠患者 #慢性失眠 #短期失眠
															- [[人生]]
															- [[破产]]
														- *[[需求]]*
															- [[活力]]
															- [[如何提高精力]]
															- [[励志]]
															- [[希望]]
																- #人生向前 #再造人生 #摆脱困境 #走出困境 #在绝望中寻找希望
															- [[睡好觉]]
																- #睡得好 #把觉睡好 #睡少又睡好 #睡的少睡的好 #把觉睡好，人生才有更多可能。 #睡眠是最好的医疗手段 #睡好 #当晚就不失眠了 #你睡不睡的好 #我睡不睡的好
															- [[睡眠管理]]
															- [[保持好状态]]
														- *[[产品]]*
															- ((62d2f4a3-33c8-41e7-9f4a-42db1b024eb9))
															- ((6306282f-6ee9-447f-abda-1b78da19648b))
															- `[[如何向新中年女性安利超级元素？]]`
															- [[深度睡眠]] [[延长深度睡眠]] [[2小时深度睡眠]] [[3小时深度睡眠]]
															- [[超级元素晚安瓶]]
													- #问题
													  collapsed:: true
														- #品牌 #产品
														  collapsed:: true
															- CANCELED 超级元素的品牌故事是什么？
															- CANCELED 超级元素到底是个什么产品？它的魅力又在哪里？
															- ((63938be3-18ee-4218-9261-9c5b60e1db61))
															- ((6396ba19-7863-49f3-b4bd-bc3ac9bef05d))
															- ((63996e19-96b0-4a6e-ac1f-8284516e8478))
															- ((63996e19-3e95-4785-aec1-bbb8b231a75e))
															- ((63996e19-5b53-4582-88a7-2865354f4da4))
															- ((63996e19-a5d2-4d98-8e9b-abb44c248288))
															- ((63996e19-7d3d-48a2-8298-622622a4cf4a))
															- ((63996e19-fe79-48ff-b5a4-5f18788345de))
															- ((63996e19-4573-4962-8b8a-9d3b74730233))
															- ((63996e19-58fa-4bbb-a2f5-b243312898a4))
															- ((63996e19-c797-44cd-8829-d2b546a86512))
														- #产品
														  collapsed:: true
															- ((62f128c7-314f-4b6c-b121-0cbf83b96ca4))
														- #需求
														  collapsed:: true
															- ((63996e19-4e6c-490e-b1bf-541db3a2ded2))
															- ((63996e19-3fbc-40ba-87dd-3f0583aa339f))
															- ((63996e19-6d2d-41c6-80d6-f727f580ac41))
															- ((63996e19-d93f-4245-8102-6a6f5249c28b))
															- ((63996e19-1d45-4e98-aae3-8a1bab5a3f01))
															- ((63996e19-44f5-413b-85ac-5300c2978cf4))
															- ((63996e19-afb7-4d80-8d08-940953628162))
															- ((63996e19-b919-40a5-befc-8c5a501ebac7))
															- ((63996e19-2133-4196-bbb6-93cdfc37e571))
															- ((63996e19-c3f2-4b97-bef6-1550abce4e41))
															- ((63996e19-48ea-4aca-ad27-80d0e1544d8b))
															- ((63996e19-aea7-48b4-b7df-11a38ec988cf))
															- ((63996e19-02b5-4551-9607-09c26d6def02))
															- ((63996e19-55a0-4822-8d12-f638d54c2b51))
															- ((63996e19-2078-4883-b3a9-930fb5684a36))
															- ((63996e19-a32e-4111-af90-447eb4c9d86f))
															- ((63996e19-15d1-42f2-88c6-3e39d82d9349))
															- ((63996e19-fced-46be-8883-d5d21962ff89))
														- *其他*
															- ((63900716-6737-4064-88b7-efb0cb41daf3))
														- #recycle
														  collapsed:: true
															- ((62d2d070-9444-482e-ad62-cb2f7e06c33c))
													- #答案
													  collapsed:: true
														- ((6396ba24-32c4-458e-806d-b46874989908))
														- ((6396ba26-1239-49fc-9056-d091cc1033f5))
														- [[这注定不是一篇讨巧的销售文案。]]
														- [[为什么改善睡眠，要先提高精力]]
															- [[如何提高精力]]
														- ((63996e1d-c94e-4391-ab3e-2624044160f9))
														- ((63996e34-4e44-4262-8915-54f2f16e4e26))
													- #小项目
													  collapsed:: true
														- #品牌
														- #产品
														- #内容营销
															- ((6306282f-6ee9-447f-abda-1b78da19648b))
															  id:: 63960fd8-2231-450f-b439-9495f0758f55
															- ((62d2f4a3-33c8-41e7-9f4a-42db1b024eb9))
															- ((631cb3d0-11ad-43f3-8884-9d13f48d1370))
														- #运营
															- ((6396ba2e-55a7-4760-b096-2c5768d00e7c))
											- #log
												- 18:45 - 18:59
													- DONE 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) [[block-ref]] 显示有点问题
													  id:: 63934677-a7db-4795-b769-e8232be8fe4a
													  :LOGBOOK:
													  CLOCK: [2022-12-09 Fri 18:45:52]--[2022-12-09 Fri 18:58:57] =>  00:13:05
													  :END:
												- 18:59 - 21:03
												  collapsed:: true
													- #continue
														- DONE 根据 [logseq官方收录我的css内容](((63930b1a-47c5-4c76-b290-5f092e249302))) 调整下我自己的 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) -> 精简代码，把收录部分单独注释出来，待下次版本更新，可以直接删除 #Logseq
														  id:: 63934677-deb8-4eff-a7a7-1b8246e3bf46
														  collapsed:: true
															- ``` css
															  /**
															    * Fix broken styles of breadcrumb caused by mixed contents
															    * TODO: implement the logic in code instead of CSS selector
															   */
															  
															   /*Fix images*/
															   .breadcrumb .image-resize {
															     width: auto;
															     height: 17px;
															     display: inline-block;
															     vertical-align: middle;
															     top: -1px;
															   }
															  
															   .breadcrumb .image-resize img {
															     height: 17px;
															     width: auto;
															   }
															  
															   .breadcrumb .image-resize :is(.asset-action-bar, .erd_scroll_detection_container) {
															     display: none;
															   }
															  
															   /*Fix iframe(e.g YouTube)*/
															   .breadcrumb :is(.embed-block>div, iframe) {
															     display: none;
															   }
															  
															   /*Fix embed-block, properties*/
															   .breadcrumb>a>div:not([style="display: inline;"]),
															   .breadcrumb .embed-block {
															     display: inline-block;
															   }
															  
															   /*Use ... to replace the invalid items*/
															   .breadcrumb .embed-block {
															     background: initial;
															   }
															  
															   :is(.breadcrumb .embed-block, .breadcrumb>a>div:not([style="display: inline;"]))::after {
															     content: "...";
															   }
															  
															   /*Fix blockquote*/
															   .breadcrumb :is(cp__fenced-code-block, blockquote) {
															     display: none;
															   }
															  ```
													- #闪念
														- CANCELED [[logseq]] 目前的 [块引用]([[block-ref]]) 显示体验，可能以后要多用 link 别名了。 #闪念
														  id:: 63934677-b17d-498d-be93-da31b0ddcf4f
															- CANCELED 尝试别名一下
															  id:: 6393163d-b975-487d-96b3-467687be1de1
																- #深思
																	- 想到再说吧。 link别名里放block+ref的场景是不需要立即看别名的内容，但很多时候是要看的。所以 ref 换行再 [[block-ref]] 是有价值的。只能说又多了一种  [[block]] [[link]] 的 [[用法]] ：
																	  id:: 63934677-3682-460a-8dfd-50fa894eef6b
																		- #list
																			- 在正文前面引用，既能显示下文，也能在面包屑看到上文
																			  id:: 639323e2-9676-44c4-babd-7cf586cc96db
																			- 在正文内引用，和上面那条差不多，难点在于正文和引用内容能结合起来
																			  id:: 639323f0-8d72-4588-a8f5-8ae1e86b4728
																			- 在正文底部引用，只能在面包屑看到上文
																			  id:: 639323f5-dfac-4d94-852b-31f8cdd8c14d
																			- 在正文内link，不需要看[[block-ref]]的情况下，又想加个链接
																			  id:: 639ad63e-63fe-4ad7-bf68-b49748642c84
																		- #btw
																			- [[RoamResearch]] 在 [[block-ref]]这块的细节处理的特别好：
																				- 1、它好像是把面包屑也当作一个块来显示的，并且把块引用放进面包屑里
																					- 如果块引用下面没内容，则只显示面包屑，并折叠块
																						- 展开面包屑，块引用又会跳出面包屑，成为正文
																							- 如果块引用下面有内容，则展开块
																					- [2、块功能：按住cmd点击右侧按钮，直接生成块引用，并在右侧栏打开，在块引用下新建一个空白块](((6392aa1e-57f4-4863-9b05-eb154cc86c1c)))
																						- DONE 以上整理提交 [[GitHub]] [[logseq]] 吧
																						  link:: https://github.com/logseq/logseq/issues/7657
																						  id:: 63934677-7695-4dff-8374-553560afd63b
																						  :LOGBOOK:
																						  CLOCK: [2022-12-09 Fri 20:20:24]--[2022-12-09 Fri 20:57:10] =>  00:36:46
																						  :END:
																							- Two functional suggestions about [[block-ref]]:
																							  1. When viewing the reference of a block, put the block into the breadcrumbs: When there is no content below the [[block-ref]], only breadcrumbs are displayed;
																							  2. Press cmd, hover the mouse over a block, and the "+" button is displayed at the end of the block. Click the "+" button to create a new block at the bottom of the current page and put it into block+ref. At the same time, open the right column, create a new blank block indented under the block of block+ref
																							  Background: My [[block-ref]] usage scenario
																							  1. Citing in front of the text, it can display the following text and see the text above in the breadcrumbs
																							  2. Citing within the text, similar to the one above, the difficulty lies in the combination of the text and the cited content
																							  3. Cited at the bottom of the text, you can only see the above in the breadcrumbs
																							  4. In the link in the text, I want to add a link without looking at the [[block-ref]]
																							  关于[[block-ref]] 的两点功能建议：
																							  1、查看block的reference时，把block放进面包屑里：当[[block-ref]]下面没有内容时，只显示面包屑；
																							  2、按下cmd，鼠标悬停在某个block，block 的尾部显示 "+"按钮，点击"+"按钮，在当前页面底部创建新的block，并放入block+ref，同时，打开右侧栏，在block of block+ref下缩进新建一个空白块
																							  背景：我的[[block-ref]]使用场景
																							  1、 在正文前面引用，既能显示下文，也能在面包屑看到上文  
																							  2、在正文内引用，和上面那条差不多，难点在于正文和引用内容能结合起来  
																							  3、在正文底部引用，只能在面包屑看到上文  
																							  4、在正文内link，不需要看[[block-ref]]的情况下，又想加个链接
																				- 还有它的 [[面包屑]] 显示，默认是全显示，但在 [[block-ref]] 中，会进行折叠
																					- #示意图
																						- ![image.png](../assets/image_1670588356546_0.png){:width 245}
																					- #质疑
																						- 这样就看不到上文了，废了我两个块引用的使用场景 ((639323f5-dfac-4d94-852b-31f8cdd8c14d)) ((639323f0-8d72-4588-a8f5-8ae1e86b4728))
													- #log
														- 17:04 - 19:10
															- #pee-time #smoking
														- 20:21 - 20:25
															- #smoking
												- 21:03 - 23:05
													- #continue
														- {{embed ((6392aa1d-e228-4491-9b64-1c5988c4854d))}}
													- #log
														- 21:08 - 21:12
															- DONE 提交 [[GitHub]] - [[logseq]] 新需求：增加面包屑的显示个数
															  id:: 63934677-4ef1-40a7-bdd6-e75547a366ae
															  :LOGBOOK:
															  CLOCK: [2022-12-09 Fri 21:09:04]--[2022-12-09 Fri 21:12:08] =>  00:03:04
															  :END:
														- 21:12 - 23:03
															- #深思
																- 昨晚和 [老婆]([[王露]]) 说到，我到目前，也只是 [看清了自己]([[看清自己]])，自己的 [[内心世界]] 比较 [[完整]] 了而已，这只是 [[创业]] 的基础中的基础，我现在才修行完成。我们俩其实都缺少 [[社会证明]] 。而我的 [[资源]] *（ [[骚操作]] ）*太多，但都没有和 [[社会价值]] 绑定。
																  #内心秩序
																	- 然后今天老婆 [[面试]] 回来，走在路上聊起，事实上我们的资源只要和 [[社会价值]] 绑定，就能多多少少 [[滋养]] 到我们，给我们 [[正反馈]]，建立 [[积极]] [[状态]] 的 [[增强回路]]。
																	  id:: 63934677-1c83-4269-9197-a28366e8c796
																		- 就比如我的 [[logseq]] [[logseq-css]] 在我没分享出去以前，就只是 [[资源]]，但在我分享之后，获得了一群人的点赞，还有志愿者帮我与官方联系，采纳我的代码，官方又确认了可以采纳，还有大神愿意教我怎么在 [[GitHub]] 上协作 …… 小小的 [[资源]] ，大大的 [[增强回路]]。
																		  id:: 63934677-1019-41ee-ba56-12ee783427b3
																			- 再比如 [老婆]([[王露]]) 给 [儿子]([[毛懋]]) 买的 [[蘑菇]]，如果不是给 [[晓琳]] 也送了一份，那就只能自己 [[独乐乐]]，现实是， [[喜乐]] 喜欢的不得了，每天早上跑去看长大了没，晓琳因此还在她的 [[育儿]] 群里推荐大家购买 …… 小小的 [[资源]] ，大大的增强回路。
																				- 此时保持这种状态，快速切到 [[主线任务]] 上，思路也会更清晰，状态也会不减，就像此时我写下的 [[memo]]，是因为这种 [[正反馈]] 带给我的影响从早上一直保持到了现在。 #状态切换
																				  id:: 63934677-7969-4979-a513-8c0b8275ac4d
																					- 所以还是要 [[主动]] 的、有 [[分享]] 你的 [[心得]] 的 [[意愿]] ，并且不怕辛苦，乐此不疲的去分享。这也许就是把 [[资源]] 与 [[社会价值]] 绑定的方式。 —— [[正反馈]] 的 [[增强回路]]，也是 [[积极状态]] 的 [[正向循环]]。
																					  id:: 63934677-be96-4c69-9022-ac32dd102701
																						- #link
																							- ((639337dd-b039-4fba-95c7-8a47166225af))
																							- ((63936352-26e8-4ac3-99ec-042ae70d1e95))
																							- ((63934678-c97f-4d0c-bac7-ed70679a4b27))
																							- ((6368aff4-b518-4490-af00-e297338f461e))
															- #log
																- 21:14 - 21:27
																	- #smoking
																- 21:27 - 21:33
																	- #闪念
																		- 或许可以考虑把我所有的 [[资源]] [[社会化]] - 与 [[社会价值]] 绑定 #资源社会化
																		  id:: 639337dd-b039-4fba-95c7-8a47166225af
																			- #例如
																				- CANCELED 义务帮 [[logseq]] 推广中国市场 #市场推广
																				  id:: 63934677-d11d-4260-8128-df266a4e9a22
																					- 包含了 [[自媒体]] [[效率生活]] [[市场营销]] ……
																			- #link
																				- ((63900716-6737-4064-88b7-efb0cb41daf3))
																- 21:48 - 23:01
																	- CANCELED 更新 ((6393163d-b975-487d-96b3-467687be1de1)) -> 就拿这次 [[logseq-css]] 提交 [[官方]] 的 [卡片盒]([[zettel]]) 来试验 [[Thread]] 的 [[回溯]] 式排版视觉 #Logseq #闪念
																	  id:: 63934677-f31f-4cfe-b8a9-0ec31b1cc39d
																		- #回忆
																			- 再次失败，没有意义，既不符合心流， [[logseq]] 的 [[block-ref]] 显示也是狗日的蛋疼。 -> [1、block-ref应该放进面包屑](((63934677-7695-4dff-8374-553560afd63b)))；2、[[block-ref]] 他妈的不能展开
																			  id:: 63982e2d-621c-4b54-945a-8fe612dae228
																				- DONE [[fuck!]]本来 [[block-ref]] 放 property 是个不错的解决方案，但是他妈的不显示！连个数字标记都没有！！这又是个bug！ -> [[GitHub]]
																				  id:: 63934677-9647-4400-9365-915e41cf25b5
																					- #深思
																						- 其实要整理 [[Thread]] ，那就整理好了，不需要提前有什么规范，基本上写下当前这个 [[memo]]，能想起来的以前的 [卡片盒]([[zettel]]) 也是 [[碎片化]] 的。不存在边写边 [[整理]] 这一说。
																						  id:: 63934c36-a2e1-4a42-920d-48242cfb4bf7
																	- #log
																		- 21:54 - 22:52
																			- DONE 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) -> 又发现了一个 [[logseq-css]] [[bug]]： [[block-ref]] 放在 [[property]] 里面，首行显示没有修复  #property #pass
																			  id:: 63934677-c1ff-4ff9-a96e-32135a81f5bc
																			  :LOGBOOK:
																			  CLOCK: [2022-12-09 Fri 21:55:21]--[2022-12-09 Fri 22:51:45] =>  00:56:24
																			  :END:
																				- DONE 整理下 [[property]] 的 [[logseq-css]]  #property #pass
																				  id:: 63934b84-8344-4b33-9bbf-f839b5b1f7ff
												- 23:09 - 00:32
													- #break
														- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
															- #回忆
																- [儿子]([[毛懋]]) 见到我， [[秒嗨]] …… [[午觉]] 没睡，晚上8点半睡到9点半强行起来， [老婆]([[王露]]) 说是[[爸爸时间]]程序没走完……
														- #shit-time
											- ((63936343-5d25-4cd2-b7d5-3b3ad5ba1a32))
										-