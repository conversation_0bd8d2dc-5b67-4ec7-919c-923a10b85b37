- 00:33
  id:: 63936343-5d25-4cd2-b7d5-3b3ad5ba1a32
	- #review
		- ((63904b32-63a5-4c99-a67d-7b9b81355f27)) [[logseq-css]] 到 [[logseq]] [[电报]] 群之后…… [[获得]] 的 所有 [[正反馈]] #ending #增强回路 #正向循环 #汇总 #review
		  id:: 63936352-26e8-4ac3-99ec-042ae70d1e95
		  :LOGBOOK:
		  CLOCK: [2022-12-10 Sat 00:34:50]--[2022-12-10 Sat 03:09:25] =>  02:34:35
		  :END:
			- #recycle
			  collapsed:: true
				- ((63934677-b17d-498d-be93-da31b0ddcf4f)) ((6393163d-b975-487d-96b3-467687be1de1)) ((63934677-f31f-4cfe-b8a9-0ec31b1cc39d))
			- #list
			  collapsed:: true
				- ((6391bc96-2377-45a7-9e18-393012e7bcca))
				- ((63934678-3ef8-4c77-928d-b262401d3f3d))
				- ((63934678-16c9-4d66-957c-c270c9fdc326))
				  id:: 63982e2c-9e17-4f89-b95e-428794fdf513
				- ((63934678-24b6-4619-b710-03f93f6c2a2e))
				- ((63934678-2adc-4b2e-9490-9c6ce54823f1))
				- ((63934678-64a3-431c-bea8-3405574ad7ac))
				- ((63934678-0dc8-4a64-be5b-226c5ea59178))
				- ((63934678-79bb-458f-b47c-bb6bc7858e0d))
				- ((63930b1a-436b-44be-b4e2-29c253d0e147))
				- ((63934677-4e8d-4d59-b174-743f0a3e7f23))
				- ((63934677-a7db-4795-b769-e8232be8fe4a))
				- ((63934677-deb8-4eff-a7a7-1b8246e3bf46))
				- ((63934677-4ef1-40a7-bdd6-e75547a366ae))
				- ((63934677-d11d-4260-8128-df266a4e9a22))
				- ((63934677-7695-4dff-8374-553560afd63b))
				- ((63934677-9647-4400-9365-915e41cf25b5))
				- ((63934677-c1ff-4ff9-a96e-32135a81f5bc))
				- ((63934b84-8344-4b33-9bbf-f839b5b1f7ff))
				- CANCELED [[gtd]] 下的 [[block-ref]] 去掉前缀
				  id:: 63982e2c-3b6d-4723-9c9f-223efcd1b7bb
				- DONE [[block-ref]] 在 [linked-reference]([[linked-reference]]) 下的首行编辑状态字体修复
				  id:: 63982e2c-5068-4ad0-9977-839103b05ea0
				- DONE cancel 重复 [[block-ref]] 导致透明度叠加
				  id:: 63982e2c-ac0d-4daf-aa97-98864a6efc80
				- CANCELED 把箭头放到尾部
				  id:: 63982e2c-f14f-47cf-a007-5718b3b403d4
				- DONE [[GitHub]] 提交 h1-h6 编辑状态下左侧圆点箭头抖动
				  id:: 63982e2c-6126-4a0b-9976-44828e4815fd
				- DONE [[continue]] [[logseq-css]]
				  :LOGBOOK:
				  CLOCK: [2022-12-10 Sat 02:30:14]
				  CLOCK: [2022-12-10 Sat 02:30:20]--[2022-12-10 Sat 03:09:32] =>  00:39:12
				  :END:
				- ((63943ccb-ade7-4e18-8331-c4c6a04dbd02))
				- ((63943ccb-1f14-4de8-8353-f85d8b27630c))
				- ((63943ccb-cc1d-466b-a119-e5d2aa25846d))
				- ((63943ccb-d877-47be-83b1-a02f49e6ebf0))
			- #回忆
			  id:: 63982e2c-55c9-4ca9-8a4f-13323dd0a0cb
			  collapsed:: true
				- ((6392222d-a18b-44a4-a6bc-74f76218ad06))
				- ((6392aa1d-8012-475b-af24-3bf8d489715c))
				- ((6392b0f3-5e1e-44b7-8943-0fa7714cf1cf))
				- ((63934678-ae96-4a1f-adb1-476fcd22c250))
				- ((63930b1a-47c5-4c76-b290-5f092e249302))
			- #深思
			  id:: 63982e2c-0d80-4072-a029-4c0b4595be34
			  collapsed:: true
				- *1 [[正反馈]]*
					- ((63934677-1c83-4269-9197-a28366e8c796))
					- ((63934677-1019-41ee-ba56-12ee783427b3))
					- ((63934677-7969-4979-a513-8c0b8275ac4d))
					- ((63934677-be96-4c69-9022-ac32dd102701))
				- *2 [[workflow]]*
					- ((63934677-3682-460a-8dfd-50fa894eef6b))
					- ((63934c36-a2e1-4a42-920d-48242cfb4bf7))
			- #output
			  collapsed:: true
				- {{embed ((63934678-8ca4-494e-875a-56c849e15f37))}}
	- 03:10
	  collapsed:: true
		- #break
			- #log
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-12-10 Sat 03:15:43]
			  :END:
				- 03:15 - 03:27
					- DONE 下楼买包 [[烟]]
					  :LOGBOOK:
					  CLOCK: [2022-12-10 Sat 03:15:56]
					  CLOCK: [2022-12-10 Sat 03:16:00]--[2022-12-10 Sat 03:25:48] =>  00:09:48
					  :END:
					- #continue
						- CANCELED [[超级元素]]是什么？以及我们为什么创造它？
						  id:: 63938be3-18ee-4218-9261-9c5b60e1db61
				- 03:27 - 04:01
					- DONE 小 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) -> 隐藏 gtd for blr 后，间距略微调整。
					  id:: 63943ccb-ade7-4e18-8331-c4c6a04dbd02
					  :LOGBOOK:
					  CLOCK: [2022-12-10 Sat 03:28:18]--[2022-12-10 Sat 03:51:10] =>  00:22:52
					  :END:
				- 04:01 - 04:36
				  collapsed:: true
					- DONE [[continue]]： [[logseq]] [[GitHub]] [[issues]]
					  id:: 63943ccb-1f14-4de8-8353-f85d8b27630c
					  :LOGBOOK:
					  CLOCK: [2022-12-10 Sat 04:02:15]--[2022-12-10 Sat 04:35:50] =>  00:33:35
					  :END:
		- 04:36
		  id:: 63943ccb-0657-4ff9-980f-582e6ddbac37
			- #offline
			- 10:50
			  id:: 63943ccb-244e-440d-a0a4-bfc448fdd19a
				- #online #weekend
					- #continue
						-
					- DONE 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) ：尝试自己改 [[block-ref]] 列表下的首行显示。
					  link:: [I really can't stand the display of block-ref, the whole screen is full of the same line](https://github.com/logseq/logseq/issues/7662#issuecomment-1345157750)
					  id:: 63943ccb-cc1d-466b-a119-e5d2aa25846d
					  :LOGBOOK:
					  CLOCK: [2022-12-10 Sat 10:51:34]
					  CLOCK: [2022-12-10 Sat 10:51:38]--[2022-12-10 Sat 17:07:33] =>  06:15:55
					  :END:
						- DONE 做示意图提交 [[GitHub]] -> [[block-ref]] 的3个问题：linked-ref显示，点击跳转错误，面包屑点击错误 ((63934b84-8344-4b33-9bbf-f839b5b1f7ff))
						  id:: 63943ccb-d877-47be-83b1-a02f49e6ebf0
					- #log
						- 15:22 - 15:25
						  collapsed:: true
							- #continue
								- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
									- #回忆
										- 上午 [[王露]] 和 [儿子]([[毛懋]]) 一起去 [[骑车]] 去 [[小黑公园]]，今天一遍 [[荡秋千]] ，一边 [[古诗词]] ， [[英语]]，惊呆了一众哥哥姐姐。
							- #日常英语
								- 留在当前的嵌套树上 #card
									- stay on the current nesting tree
					- 17:41
					  id:: 63945442-9ea8-41c2-90e7-843e6be4dbc2
						- #break
							- ((651bc212-523a-48b7-bce5-e444544ae14b))
							  :LOGBOOK:
							  CLOCK: [2022-12-10 Sat 18:06:52]--[2022-12-10 Sat 19:33:47] =>  01:26:55
							  :END:
						- #log
							- 18:05 - 18:10
								- #回忆
									- 分享一个 [[耨儿]] 优秀的故事：现在已经不给看手机了，但最近都呆在家，周末、放假不能出去，我又重新他看电影。本来挺担心他上瘾的，但是，耨儿居然每次都自己主动站起来说不看了！！而且平时也不会吵着要看电影。这个让我感觉太意外、太神奇了。是每一次哦！就像今天，我跟他说还能看10分钟，不到2分钟，耨儿就站起来要自己关电视了！我们耨儿是真的宠的起的啊哈哈哈 还有，耨儿最近在[[爸爸时间]] 里喜欢上 [[整理]] [[玩具]] 了！
									  #育儿 #充分满足 #充分放权
							- 18:11 - 19:34
								- :LOGBOOK:
								  CLOCK: [2022-12-10 Sat 18:11:55]
								  CLOCK: [2022-12-10 Sat 18:12:05]--[2022-12-10 Sat 19:34:52] =>  01:22:47
								  :END:
								- 晚饭
						- 19:35
						  id:: 63946ee7-ec1e-42e4-a317-c0270549c663
							- #break
								- ((651bc21c-3131-406f-9608-99fda127b5e3))
								  :LOGBOOK:
								  CLOCK: [2022-12-10 Sat 19:35:13]--[2022-12-10 Sat 21:16:07] =>  01:40:54
								  :END:
								- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
							- 00:13
								- #continue
									- ((63943ccb-cc1d-466b-a119-e5d2aa25846d))
									  collapsed:: true
										- #回忆
											- 没想到要改的[[logseq-css]]太他妈的多了。终于全部改完，顺便细节再做很多优化：例如，顶部面包屑与左侧第二个对齐，顶部标题与聚焦对齐，还把 [[block-ref]] -list 与下面小圆点对齐 …… 还有很多，一边看一边改，包括代码都整理了一遍，这一个版本我是不会再动了。 #logseq-css #pass
											  id:: 63982e2c-d7dd-43d7-a50c-d4325ed7c1e6
											  collapsed:: true
												- 顺便吐槽一句： [[logseq]] 的 [块]([[block]]) [[workflow]] 体验是真 [[垃圾]] ， [[面包屑]] 和 [块引用]([[block-ref]]) 简直是惨不忍睹。想起 [[电报]] [[群主]] 的笑话：在面包屑里面看 [[视频]] 。
												  id:: 63982e2c-b5cd-4939-a95c-e90250c92d0f
													- 而我则是在块引用列表下看重复标题。日了狗了。
													  id:: 63982e2c-21ff-4404-b860-67fe248e56a3
														- [[Twitter]] 看到这段的时候真是 [[内牛满面]]
														  link:: [天生与康纳的对话](https://twitter.com/conaw/status/1374354779195854852?s=61&t=KcxmjBO4Pc1dCgeOVqFIVw)
														  collapsed:: true
														  #天生 #[[Conor White-Sullivan]]
														  collapsed:: true
															- #示意图
																- ![image.png](../assets/image_1673419052750_0.png){:width 245}
																	- 不过我现在可以完全不怕在非文本的块下main嵌套了，啊哈哈哈哈哈，真· [[无压输入]] ！
																	  id:: 63982e2c-242b-49ec-b6e6-ab954324b4ee
																		- #link
																			- ((63904b32-63a5-4c99-a67d-7b9b81355f27))
								- DONE 去买包 [[烟]]
								  :LOGBOOK:
								  CLOCK: [2022-12-11 Sun 00:13:24]--[2022-12-11 Sun 00:25:47] =>  00:12:23
								  :END: