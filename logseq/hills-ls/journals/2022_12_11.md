- 00:25 ((6394c1a9-4d8f-456e-8020-bfc97099e31c)) ((63954196-f322-4c6a-b929-170d3cedacd9))
  id:: 6394b36f-417b-4f3a-a104-4bb897c8b6f7
  collapsed:: true
	- #continue
		- {{embed ((63930b1a-fec9-4af0-9ee5-b2d658d04439))}}
			- #回忆
				- 他妈的， [[正事]] 一点也没搞，全他妈的搞 [[logseq-css]] 了 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) #logseq-css #pass
				  id:: 63982e2c-c3af-4d00-a200-42b0ec215325
	- #log
		- 00:59 - 01:28
		  collapsed:: true
			- #continue
				- ((631c3ef0-b23e-4f7b-87f9-074809747e3e))
				  :LOGBOOK:
				  CLOCK: [2022-12-11 Sun 00:59:51]
				  CLOCK: [2022-12-11 Sun 01:00:00]--[2022-12-11 Sun 01:03:29] =>  00:03:29
				  :END:
					- #and
						- DONE 把 [[摘录]] 内容放进 [[page]]
						  :LOGBOOK:
						  CLOCK: [2022-12-11 Sun 01:03:32]--[2022-12-11 Sun 01:24:38] =>  00:21:06
						  :END:
		- 01:28 - 10:34
		  id:: 6394c1a9-4d8f-456e-8020-bfc97099e31c
		  collapsed:: true
			- #offline
		- 10:34 - 11:02
		  id:: 63954196-f322-4c6a-b929-170d3cedacd9
		  collapsed:: true
			- #online #weekend
			  collapsed:: true
				- #continue
				  collapsed:: true
					-
				- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
				  :LOGBOOK:
				  CLOCK: [2022-12-11 Sun 10:37:24]
				  CLOCK: [2022-12-11 Sun 10:37:32]--[2022-12-11 Sun 12:05:30] =>  01:27:58
				  :END:
		- 11:02 - 16:51
		  collapsed:: true
			- ((651aa258-198e-4b8e-af29-1b768aeb7a1a)) -> 把 [[电子书]] [[摘录]] 迁移到 hls-pages
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-12-11 Sun 16:50:55]--[2022-12-11 Sun 16:51:09] =>  00:00:14
			  :END:
				- DONE [[pdf]] [[上传]] 
				  :LOGBOOK:
				  CLOCK: [2022-12-11 Sun 11:02:12]
				  CLOCK: [2022-12-11 Sun 11:02:22]--[2023-01-11 Wed 00:47:42] =>  733:45:20
				  :END:
					- ((631c3efc-3162-4136-89aa-b5c1999e0d38))
					  :LOGBOOK:
					  CLOCK: [2022-12-11 Sun 11:23:14]--[2022-12-11 Sun 12:02:24] =>  00:39:10
					  :END:
					- ((63f2e4c3-ae3f-46e0-b1b0-695f76b88451))
					- ((631c3efc-7a4f-4f4c-a74f-41c3c54da55b))
					  :LOGBOOK:
					  CLOCK: [2022-12-11 Sun 12:02:55]--[2022-12-11 Sun 14:27:16] =>  02:24:21
					  :END:
					- ((631c3ef8-f932-4b1f-b06d-24d76f68b14e))
					  :LOGBOOK:
					  CLOCK: [2022-12-11 Sun 14:34:03]--[2022-12-11 Sun 16:26:46] =>  01:52:43
					  :END:
					- ((636a7bea-180a-42ff-b63a-a7fc4446f498))
					- ((6361dae4-5718-4443-aa38-1e826fb9bcb6))
					  :LOGBOOK:
					  CLOCK: [2022-12-11 Sun 16:27:09]--[2022-12-11 Sun 16:39:55] =>  00:12:46
					  :END:
				- ((631cc627-fa0c-4fc7-8379-c784e38f58b6))
			- #log
			  collapsed:: true
				- 12:05 - 13:23 (1h18min)
				  collapsed:: true
					- DONE [[做饭]] [[回锅肉]]
					  :LOGBOOK:
					  CLOCK: [2022-12-11 Sun 12:05:42]
					  CLOCK: [2022-12-11 Sun 12:05:50]--[2022-12-11 Sun 13:23:39] =>  01:17:49
					  :END:
					- 午饭
					- #break
				- 13:57 - 13:57
				  collapsed:: true
					- #日常英语
					  collapsed:: true
						- 打捆机 #card
						  collapsed:: true
							- baler
		- 16:51 - 19:01
		  collapsed:: true
			- #break
				- DONE ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))：去 [[游乐场]]
				- 晚饭
		- 19:01 - 21:44
		  collapsed:: true
			- #闪念
				- CANCELED [看]([[想看]])[[三体]] [[动画片]] [[Bilibili]]
				  id:: 63c418ab-546e-4d84-a794-7139505d44b5
				  SCHEDULED: <2023-01-14 Sat 11:00 ++1w>
			- ((63c418ab-546e-4d84-a794-7139505d44b5))
			  :LOGBOOK:
			  CLOCK: [2022-12-11 Sun 19:01:37]--[2022-12-11 Sun 21:44:25] =>  02:42:48
			  :END:
				- #摘录
				  collapsed:: true
					- #三体 #动画片 #剧照
						- ![截屏2022-12-11 19.21.39.png](../assets/截屏2022-12-11_19.21.39_1670757738780_0.png){:width 245}
							- 一个人 [[自我]] 的 [[边界]] ， [[神圣不可侵犯]] 。至于 [[圆圈之外]] 的事，哪怕是 [[世界末日]]，也 [[轮不着]] 我们 [[烦心]] 。 #金句
			- #log
				- 19:04 - 21:44
				- DONE [[pdf]] [[上传]] ->
				  :LOGBOOK:
				  CLOCK: [2022-12-11 Sun 19:05:25]
				  CLOCK: [2022-12-11 Sun 19:05:32]--[2022-12-11 Sun 21:44:32] =>  02:39:00
				  :END:
					- ((63afb286-2809-4e95-bd5c-a330d6fb7f03))
					- DONE ((63f2e47c-5149-4d37-9b31-1e7aa1b8aea9))
					- DONE ((63f2e48c-ff9b-41fc-94ac-e5d804d7a4ae))
					  :LOGBOOK:
					  CLOCK: [2022-12-11 Sun 19:47:49]--[2022-12-11 Sun 20:02:38] =>  00:14:49
					  :END:
					- DONE [[营销管理（第16版）]]
					- DONE ((63f2e4ac-81ea-4995-ad28-e758bc50b7e2))
		- 21:44 - 02:04
		  collapsed:: true
			- #break
			  id:: 6395e0fc-392c-47f5-99a0-c4cdca163005
				- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
				  id:: 6395f5b6-2e7a-4e13-a688-10cf235a43ae
				- DONE 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) [[GitHub]] 上一位 [[大神]] 用 [[logseq-css]] 解决了 [[block-ref]] [[面包屑]] 跳转逻辑错误
				  id:: 63982e2c-1e57-458e-80e9-17bc17b1c9ca
				  :LOGBOOK:
				  CLOCK: [2022-12-11 Sun 23:22:50]
				  CLOCK: [2022-12-11 Sun 23:23:01]--[2022-12-12 Mon 00:07:38] =>  00:44:37
				  CLOCK: [2022-12-12 Mon 00:09:44]--[2022-12-12 Mon 00:18:15] =>  00:08:31
				  :END:
					- DONE 同理, [[block-ref]] 只包含 [[page-ref]] 的问题是否也可以解决？ <- 解决
					  id:: 63982e2c-2388-4aa2-8466-dc74a55e6be9
					  collapsed:: true
						- #then
							- DONE [[block-ref]] 的箭头可以 [[注释]] 了 <- 仅去掉 [[面包屑]]，其他保留 —— 对阅读还是有帮助的。
							  id:: 63982e2c-99ca-4592-a42e-9fbe0ccf31f8
								- #before
									- DONE 看下 [[RoamResearch]] 的 `#` 和 `link` 样式有什么不同
										- #then
											- DONE 修改 `link` 样式
					- CANCELED 同理, `[[pdf-highlight]]` [[block-ref]] 跳转逻辑问题是否也可以解决？ <- 搞不定 #recycle
					  id:: 63982e2c-c2de-4777-b258-650058e46b51
					- DONE 发现 [[缩略图]] 好像显示有问题 <- 原来是注释没加 `*/`
					  id:: 63982e2c-3a14-4976-83f6-8eeee4f949f9
					- DONE [[block-ref]] 右边的编辑按钮间距调整下
					  id:: 63982e2c-84d7-49f4-b9ea-8a0108aa43c7
					- DONE [[page-embed]] 像素级左移 & 多级展开颜色调整， [[恶心]] 死我了。*（ps： [[logseq]] [[官方]] 就没想过用透明的字体颜色吗？）* & 其他代码优化：面包屑根据我的使用保留第三个`a` 下面的 [[block-ref]] 和箭头 ……
					  id:: 63982e2c-191b-4165-9ee1-c519b660cb2f
					- DONE [[关闭]] [[GitHub]] 我自己已经搞定的功能
					  :LOGBOOK:
					  CLOCK: [2022-12-12 Mon 02:00:31]
					  CLOCK: [2022-12-12 Mon 02:00:36]--[2022-12-12 Mon 02:04:53] =>  00:04:17
					  :END:
	- ((63961c49-06bf-430f-9541-afb6659cbfb9))
	  id:: 63961dcb-5402-4259-95ec-f3e7c9a2404c