- 02:13 ((6396932c-0583-458c-9e6b-6b0b12a5fdd7)) ((63969334-baf5-4fae-afdd-97412c8d1541))
  id:: 63961c49-06bf-430f-9541-afb6659cbfb9
  collapsed:: true
	- #offline
		- ((651aa257-6b75-4549-82e2-69f005661986))
	- 10:34
	  id:: 6396932c-0583-458c-9e6b-6b0b12a5fdd7
		- #online
			- #continue
				-
				- DONE [[自习室]] [[订位]]
				- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
			- #闪念
				- [[多数人]] 的 [[声音]] 很多时候是不可靠的。 你问问 [[高三]] 学生，取消高考，直接上大学愿意不愿意，多数人会选择愿意。然而这样真的好吗？
			- collapsed:: true
			- #[[回忆]]
				- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 入睡2点21，醒来9点，睡了6.5小时，[[深度睡眠时长]] 2.5小时，非常 [[标准]] 。就在剩下的那么点点的 [[快速眼动睡眠]] 里，[[梦到]] [[张致玮]] 给我 [[评估]] 文案…… #2小时深度睡眠
					- #截图
						- ![WechatIMG397.jpeg](../assets/WechatIMG397_1670813380757_0.jpeg){:width 245}
			- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
			  :LOGBOOK:
			  CLOCK: [2022-12-12 Mon 10:56:20]--[2022-12-13 Tue 10:22:19] =>  23:25:59
			  :END:
			  ```calc
			  11+43+12
			  ```
				- #continue
					- DONE 12.11 ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
					  ```calc
					  12+11+3+15+5
					  ```
					- DONE 12.10 ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
					  ```calc
					  12+20
					  ```
			- ((651aa256-9459-42af-8400-536aadc21970))
			  :LOGBOOK:
			  CLOCK: [2022-12-12 Mon 11:02:30]--[2022-12-12 Mon 11:04:03] =>  00:01:33
			  :END:
		- 11:04
			- #smoking
				- DONE 拿 [[瑞幸咖啡]]
				  id:: 6396ba08-d1e1-4faf-bda0-5de4d4c9253e
				- #continue
					- DONE [[GitHub]]
					  :LOGBOOK:
					  CLOCK: [2022-12-12 Mon 11:16:55]--[2022-12-12 Mon 11:16:56] =>  00:00:01
					  :END:
			- 11:17
			  id:: 63969334-baf5-4fae-afdd-97412c8d1541
			  collapsed:: true
				- :LOGBOOK:
				  CLOCK: [2022-12-12 Mon 11:24:11]--[2022-12-13 Tue 00:06:04] =>  12:41:53
				  :END:
					- {{embed ((63930b1a-fec9-4af0-9ee5-b2d658d04439))}}
				- #log
					- 11:38 - 14:51
					  collapsed:: true
						- #闪念 #pass
							- DONE [[清除]] `[[.embed]]`
							  :LOGBOOK:
							  CLOCK: [2022-12-12 Mon 11:39:02]
							  CLOCK: [2022-12-12 Mon 11:39:15]--[2022-12-12 Mon 11:54:39] =>  00:15:24
							  :END:
								- DONE 查下 [[失效]] [[block-ref]] #recycle
								- DONE [[namespace]] -> #recycle
								  :LOGBOOK:
								  CLOCK: [2022-12-12 Mon 11:55:07]
								  CLOCK: [2022-12-12 Mon 11:55:11]--[2022-12-12 Mon 11:56:34] =>  00:01:23
								  :END:
									- DONE [[清除]]
									- DONE [[失效]] #recycle
								- DONE 调整 [[block]] 下 [query]([[queries]]) 下的显示 #logseq-css
								  id:: 63982e2c-45c1-4b3d-b8c0-eb814cd21b8d
									- DONE [[面包屑]] 下 [query]([[queries]]) 的显示
									  id:: 63982e2c-1b5f-412d-87d6-9dd6140677ac
									  :LOGBOOK:
									  CLOCK: [2022-12-12 Mon 13:10:01]
									  CLOCK: [2022-12-12 Mon 13:10:07]--[2022-12-12 Mon 14:44:32] =>  01:34:25
									  :END:
								- DONE 把 [[官方]] 颜色都换成自己的
						- #log
						  collapsed:: true
							- 13:19 - 14:43
								- 午饭 [[王露]]
					- 14:51 - 14:52
					  collapsed:: true
						- DONE [[闪念]] ((635e9c1a-0021-4eff-8c3f-75f1e324deb9)) 
						  id:: 63975dd5-9e74-4ac6-b8ab-4acf8d2946d4
						  SCHEDULED: <2022-12-13 Tue .+1d>
					- 14:52 - 17:02
					  collapsed:: true
						- #break
							- DONE [[优化]] [[tabs]] [[logseq-css]] ((63904b32-63a5-4c99-a67d-7b9b81355f27))
							  id:: 63974911-ec4b-4c7a-a23d-0101a5c28d0b
							  :LOGBOOK:
							  CLOCK: [2022-12-12 Mon 15:54:51]
							  CLOCK: [2022-12-12 Mon 15:54:53]--[2022-12-12 Mon 16:38:11] =>  00:43:18
							  :END:
						- #log
							- 15:54 - 15:58
								- #pee-time
					- 17:02 - 17:28
					  collapsed:: true
						- #shit-time #惠润广场
						  :LOGBOOK:
						  CLOCK: [2022-12-12 Mon 17:02:33]--[2022-12-12 Mon 17:28:38] =>  00:26:05
						  :END:
					- 17:29 - 17:33
					  collapsed:: true
						- DONE 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) 右侧栏底部圆角安排上 #logseq-css
						  id:: 63982e2c-3ce9-4ee7-880b-a0255bc9673f
						  :LOGBOOK:
						  CLOCK: [2022-12-12 Mon 17:29:22]--[2022-12-12 Mon 17:33:40] =>  00:04:18
						  :END:
					- 17:41 - 17:48
					  collapsed:: true
						- #回忆
						  collapsed:: true
							- [[行程码]] 今日退出 [[历史舞台]]
							  #纪念日 #疫情 #新冠 #新冠疫情 #共产党
								- #截图
									- ![WechatIMG401.jpeg](../assets/WechatIMG401_1670838190456_0.jpeg){:width 245}
						- #摘录
							- CANCELED [成立125年，年产2500吨健康原料的龙沙是谁？](cubox://card?id=********************************)
							- TODO [TheFutor这期播客嘉宾是个狠人：因为觉得大学太水而辍学，自学社交媒体运营，现在做“影子写手”，帮人运营推特，承诺60天涨粉两万，收两万美元一月，做了七八个客户。他分享了几条内容经验，相当干货。其中关于故事弧光的理论我之前在麦基的《故事》里也读到过，厉害了。](https://twitter.com/bearbig/status/1601157158477107200?s=61&t=BjTmei4HatHOTVdnxaLtoQ)
							- CANCELED [赶在黎明前最后一波](cubox://card?id=********************************)
					- 18:04 - 18:27
					  collapsed:: true
						- DONE 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) [[block-ref]] [[面包屑]] [[箭头]] 保留第3位-其他全隐藏这个设计只发生在顶部导航 <- 解决，真麻烦，越写越复杂，进入到不断堆积代码修复bug的阶段，找时间再优化代码 —— 6.0版呼之欲出了啊[[卧槽]]
						  id:: 63976008-c218-4b64-b9a9-3f7872581602
						  :LOGBOOK:
						  CLOCK: [2022-12-12 Mon 18:05:41]--[2022-12-12 Mon 18:27:31] =>  00:21:50
						  :END:
					- 18:31 - 18:35
					  collapsed:: true
						- #smoking
					- 18:44 - 18:49
					  collapsed:: true
						- ((6396ba62-28b9-4ffa-bba4-770d15c1613b))
							- #闪念
								- ==像年轻时一样生活== #slogan #超级元素
									- 生活在年轻
					- 18:50 - 18:59
					  collapsed:: true
						- DONE 更新 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) 修复hightlight link
						  id:: 63982e2c-90d3-4f49-9ab5-06c8d5d84ca0
							- ==test test [[test]]==
					- 18:59 - 21:53
					  collapsed:: true
						- :LOGBOOK:
						  CLOCK: [2022-12-12 Mon 18:59:37]--[2022-12-12 Mon 21:52:42] =>  02:53:05
						  :END:
						- 晚饭
						- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
					- 21:53 - 23:37
					  collapsed:: true
						- ((651bc21c-3131-406f-9608-99fda127b5e3))
						  :LOGBOOK:
						  CLOCK: [2022-12-12 Mon 21:53:22]--[2022-12-12 Mon 23:37:09] =>  01:43:47
						  :END:
						- ((63974911-ec4b-4c7a-a23d-0101a5c28d0b))
							- DONE 再优化下
					- 23:37 - 00:05
						- DONE [[优化]] [[logseq-bullet-threading]] [[page-embed]] 左侧不显示以及颜色 ((63904b32-63a5-4c99-a67d-7b9b81355f27))
						  id:: 63982e2c-63e7-4f96-b408-4df7dee65590
						  :LOGBOOK:
						  CLOCK: [2022-12-12 Mon 23:37:42]
						  CLOCK: [2022-12-12 Mon 23:37:50]--[2022-12-13 Tue 00:05:43] =>  00:27:53
						  :END:
							- #摘录
							  collapsed:: true
								- ``` css
								  :root {
								    --ls-block-bullet-active-color: var(--ls-block-bullet-threading-active-color-overwrite,
								      var(--ls-link-text-color)
								    );
								    --ls-block-bullet-threading-width: var(--ls-block-bullet-threading-width-overwrite,1px);
								  }
								  
								  .block-control {
								    z-index: 1;
								  }
								  
								  .block-children {
								    border-left-color: var(--ls-guideline-color);
								    border-left-width: var(--ls-block-bullet-threading-width) !important;
								  }
								  
								  .block-children-container {
								    position: relative;
								  }
								  
								  .block-children-left-border {
								    z-index: 1;
								    position: absolute;
								    height: 100%;
								    width: 4px;
								    padding-right: 0px;
								    border-radius: 2px;
								    background-color: transparent;
								    left: -1px;
								    transition: background-color 0.2s;
								  }
								  
								  .block-children-left-border:hover {
								    background-color: var(--ls-block-bullet-active-color);
								  }
								  
								  .block-content-wrapper {
								    position: relative;
								    overflow-x: visible !important;
								    /* Fixme: overflow? */
								  }
								  
								  .bullet-container {
								    height: 14px !important;
								    width: 14px !important;
								    position: relative;
								    transform: translate(1px, -1px);
								  }
								  
								  /* Block bullet path should only show in a nested block */
								  .ls-block {
								    /* Fix for headings like h1, h2 etc */
								    /* Basic "curve" */
								    /* Fix multi-line blocks with children */
								  }
								  .ls-block .bullet {
								    background-color: var(--ls-block-bullet-active-color);
								    box-shadow: 0 0 0 1px var(--ls-block-bullet-active-color);
								  }
								  .ls-block div.items-center {
								    position: relative;
								    height: 26px !important;
								  }
								  .ls-block > .items-baseline {
								    align-items: initial;
								  }
								  .ls-block:not(:focus-within) .bullet {
								    background-color: var(--ls-block-bullet-color);
								    transform: scale(1);
								    box-shadow: none;
								  }
								  .ls-block:not(:focus-within) > .items-baseline {
								    align-items: baseline;
								  }
								  .ls-block .ls-block > div > div.items-center::before {
								    pointer-events: none;
								    content: "";
								    left: calc(var(--ls-block-bullet-threading-width) * -1);
								    right: 6px;
								    top: calc(-50% + var(--ls-block-bullet-threading-width) * 0.5 - 1px);
								    bottom: 50%;
								    /* shift left 1px for border */
								    position: absolute;
								    border-left: var(--ls-block-bullet-threading-width) solid transparent;
								    border-bottom: var(--ls-block-bullet-threading-width) solid transparent;
								    border-bottom-left-radius: 10px;
								  }
								  .ls-block .ls-block:focus-within > div > div.items-center::before {
								    border-color: var(--ls-block-bullet-active-color);
								  }
								  .ls-block .block-children > .ls-block::before {
								    pointer-events: none;
								    content: "";
								    top: -1rem;
								    bottom: 0;
								    border-left: var(--ls-block-bullet-threading-width) solid transparent;
								    left: calc(var(--ls-block-bullet-threading-width) * -1);
								    position: absolute;
								  }
								  .ls-block .block-children:focus-within > .ls-block:not(:focus-within)::before {
								    border-color: var(--ls-block-bullet-active-color);
								  }
								  .ls-block .block-children:focus-within > .ls-block:focus-within ~ .ls-block::before {
								    border-color: transparent;
								  }
								  .ls-block[haschild] > div > .block-content-wrapper::before {
								    pointer-events: none;
								    content: "";
								    top: 12px;
								    bottom: 0;
								    left: -15px;
								    position: absolute;
								    border-left: var(--ls-block-bullet-threading-width) solid transparent;
								  }
								  .ls-block[haschild]:focus-within > div > .block-content-wrapper::before {
								    border-color: var(--ls-block-bullet-active-color);
								  }
								  
								  :is(.embed-block > div, .embed-page) > div > div > div > div.ls-block > div > div.items-center::before {
								    border-color: transparent !important;
								  }
								  
								  @media (max-width: 640px) {
								    .ls-block[haschild] > div > .block-content-wrapper::before {
								      left: -11px;
								    }
								  }
								  /* Disable for doc mode */
								  .doc-mode div.items-center::before,
								  .doc-mode div.items-center::after,
								  .doc-mode .ls-block::before,
								  .doc-mode .ls-block::after {
								    display: none;
								  }
								  
								  .doc-mode .block-children {
								    border-left-width: 0px !important;
								  }
								  ```
					- 00:05 - 00:55
					  collapsed:: true
						- DONE [[优化]] [[page-embed]] 在 [linked-reference]([[linked-reference]]) 下的显示 ((63904b32-63a5-4c99-a67d-7b9b81355f27))
						  id:: 63975884-151b-4941-902d-7dfae9e65552
						  :LOGBOOK:
						  CLOCK: [2022-12-13 Tue 00:36:56]--[2022-12-13 Tue 00:53:52] =>  00:16:56
						  :END:
							- DONE [[细节]]-> [[面包屑]] 中 [[block-ref]] 的 ref 的箭头要恢复*（为了可阅读性）*
							  id:: 63982e2c-99cd-4b67-a89b-77b5d279c4a1
							  :LOGBOOK:
							  CLOCK: [2022-12-13 Tue 00:52:42]
							  CLOCK: [2022-12-13 Tue 00:52:49]
							  CLOCK: [2022-12-13 Tue 00:52:55]--[2022-12-13 Tue 00:53:50] =>  00:00:55
							  :END:
				- ((63975cee-2031-416a-81b7-297046e7750d))