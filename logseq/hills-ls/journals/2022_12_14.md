- 00:37 ((63993947-225a-4b89-b16a-ce69206357ed)) ((63993ad3-35d2-4d00-939d-db902b39d13d)) ((63998f3a-1300-453e-8e8b-08b40c20834d)) ((639994a2-d921-415d-aef7-33524a78f95b)) ((6399c58a-7858-4875-9c3b-6e3b7630d646))
  collapsed:: true
	- #break
	- 00:47
		- #offline
		- 09:24
		  id:: 63993947-225a-4b89-b16a-ce69206357ed
			- #online #自习室
			  collapsed:: true
				- #continue
				  collapsed:: true
					-
					-
					- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
				-
				- collapsed:: true
				- #[[回忆]]
					- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 这两天都没带手表，今天8点多醒的，睡了应该有超过6.5小时。 [[状态]] 还可以。
				- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
				  :LOGBOOK:
				  CLOCK: [2022-12-14 Wed 09:27:41]--[2022-12-15 Thu 10:00:04] =>  24:32:23
				  :END:
				  ```calc
				  21.56
				  ```
				- ((651aa256-9459-42af-8400-536aadc21970))
				  :LOGBOOK:
				  CLOCK: [2022-12-14 Wed 09:28:16]--[2022-12-14 Wed 09:29:10] =>  00:00:54
				  :END:
				- :LOGBOOK:
				  CLOCK: [2022-12-14 Wed 09:29:23]--[2022-12-14 Wed 09:34:48] =>  00:05:25
				  :END:
			- 09:34
				- #break
				  collapsed:: true
					- ((651bc212-523a-48b7-bce5-e444544ae14b))
					  :LOGBOOK:
					  CLOCK: [2022-12-14 Wed 09:37:06]--[2022-12-14 Wed 10:52:24] =>  01:15:18
					  :END:
				- #log
				  collapsed:: true
					- 09:34 - 10:18
					  collapsed:: true
						- DONE [[continue]] ((635e9c1a-0021-4eff-8c3f-75f1e324deb9))
						  id:: 639928ca-33f4-4bbc-b374-d2e7cf4f9012
						  :LOGBOOK:
						  CLOCK: [2022-12-14 Wed 09:52:04]--[2022-12-14 Wed 10:17:41] =>  00:25:37
						  :END:
							- 09:50 - 09:59
								- CANCELED [[尝试]] 更新 ((63982e3d-77cd-49c5-8644-5d867d01976d))
								  :LOGBOOK:
								  CLOCK: [2022-12-14 Wed 09:54:29]
								  :END:
									- 算了 ((63904b32-63a5-4c99-a67d-7b9b81355f27)) 先用一段时间。
										- 10:23 - 10:52
											- DONE [[block-ref]] 的 [[block-ref]] 的 padding 重置
											  id:: 63993398-3299-483f-b848-359b427ee32d
											- DONE [[block]] 第二行优化：编辑状态 & 默认状态 都为 13px 及蓝/白颜色
											  id:: 639938ba-f651-4ec6-8954-1dae16352950
				- 10:54
				  id:: 63993ad3-35d2-4d00-939d-db902b39d13d
					- #continue
					  collapsed:: true
						- {{embed ((63930b1a-fec9-4af0-9ee5-b2d658d04439))}}
					- #log
					  collapsed:: true
						- 11:10 - 11:15
						  collapsed:: true
							- #smoking
						- 11:40 - 12:17
						  collapsed:: true
							- ((651aa258-198e-4b8e-af29-1b768aeb7a1a)) [[大项目]] #recycle
							  :LOGBOOK:
							  CLOCK: [2022-12-14 Wed 11:49:11]
							  CLOCK: [2022-12-14 Wed 11:49:17]--[2022-12-14 Wed 12:17:47] =>  00:28:30
							  :END:
								- 12:03
									- DONE [[namespace]] [[春节假期]] -> `[[我/春节假期]]` #recycle
									  :LOGBOOK:
									  CLOCK: [2022-12-14 Wed 12:01:48]
									  CLOCK: [2022-12-14 Wed 12:01:56]--[2022-12-14 Wed 12:06:40] =>  00:04:44
									  :END:
							- #log
								- 12:04 - 12:05
									- DONE [[春节]] [[2023]] #ending
									  id:: 63a51eec-46f0-4ed3-b6f3-e96e4ccc3301
									  :LOGBOOK:
									  * State "DONE" from "TODO" [2022-12-16 Fri 10:38]
									  * State "DONE" from "TODO" [2022-12-19 Mon 15:50]
									  * State "DONE" from "TODO" [2022-12-27 Tue 17:40]
									  * State "DONE" from "TODO" [2023-01-06 Fri 20:34]
									  * State "DONE" from "TODO" [2023-01-12 Thu 20:23]
									  * State "DONE" from "TODO" [2023-01-16 Mon 12:00]
									  * State "DONE" from "TODO" [2023-01-18 Wed 11:18]
									  :END:
										- *准备* #必要条件
										  collapsed:: true
											- DONE 了解 [[2岁育儿]] [儿子]([[毛懋]]) 坐 [[飞机]] [[注意事项]]
											  id:: 63b65e41-aae6-44f0-87e2-657f42529663
											- DONE [[全家]] [[行李]] [[整理]]
											  id:: 63b65e41-06f6-40c8-aff0-369f28c31d79
												- DONE 了解 [[台州]] [[杭州]] [[天气]]
												  id:: 63b65e41-e328-4547-b449-83992684be19
												- DONE 确定回家 [儿子]([[毛懋]]) 带什么？玩具要带吗？
												  id:: 63b65e41-171a-49f7-bec9-51adc06d81ef
												- DONE 带两个围脖枕头
											- DONE [[黄埔花园]] [[大扫除]]
											  id:: 63b65e41-ab65-480c-9801-93429568b1d6
												- DONE [[整理]] [[规整]] [[物品]]
												  id:: 63b65e41-3a2a-4857-be2c-2871b034d1ac
												- DONE [[晾晒]] 床上 [[四件套]]
												  id:: 63b65e41-ae70-40ec-a004-5af59400dc72
												- DONE 整理 [[衣橱收纳箱]] [[玩具]]
												  id:: 63b65e41-da66-4192-95c6-d770cc225dc4
											- DONE 确定到 [[路桥]] [[机场]] 后，[[回老家]] 的 [[交通方式]]
											  id:: 63b65e41-f836-471b-8f16-5a23279cde54
											- DONE 确定几号 回 [[杭州]] ，以及 [[交通方式]]，以及 ((63aab924-a8c7-44d2-8e27-84a40df7c2f4))
											  id:: 63b65e41-4d62-4813-9c3e-04cde5a56f1e
												- ((63aab833-dfa8-47f0-b34a-fd676d74bed4))
											- DONE 我和[[王露]]的利息提前准备好
											  id:: 63b65e41-e73a-49b3-8e1a-21a4b853375b
											- ((63aaa21d-525a-4b31-b9b2-365df955b250))
											- DONE 买 [[回程]] [[机票]]
										- *[[week]] 1*
										  collapsed:: true
											- DONE [[Day]] 1
											  id:: 63b65e41-c7e4-456a-aa2c-018a4d4e2be2
											  SCHEDULED: <2023-01-06 Fri>
												- DONE [[岳父]] [[岳母]] [[全家]] 一起回到[[仙居]][[横溪镇]]
												  id:: 63bec632-0afa-4eee-acac-86d1399e7653
													- ((63bf72f5-320c-47fe-9a84-0615f463d9eb))
														- {{embed ((63bf735e-cef9-44db-921a-d818fb539a2b))}}
														- {{embed ((63b91f47-a281-4653-8c9b-30971a0b5add))}}
											- DONE [[Day]] 2]]
											  id:: 63b91891-515e-46fe-a80c-777f1c27e11c
											  collapsed:: true
											  SCHEDULED: <2023-01-07 Fri>
												- ((63bec632-3444-4040-b4c3-7528c1e7f312))
													- {{embed ((63bec632-ab13-4899-969d-1f50b7d16fbe))}}
												- ((63b95b58-7f9a-46e1-984a-490e8c0a4a37))
													- ((63b95b5b-607f-4952-9713-08cbd456e2c0))
														- {{embed ((63bec632-db31-42b3-87b0-334633b9c09f))}}
											- DONE [[Day]] 3]]
											  id:: 63b91f47-ad39-44fa-a8ff-2a0bb172e70d
											  SCHEDULED: <2023-01-08 Fri>
												- ((63ba624f-525b-4e2d-9c05-b7905f627d03))
													- {{embed ((63bec632-619a-4b2c-8fa1-d01b5da43c36))}}
													- {{embed ((63bec632-7820-4210-97b2-8a06acc4a64e))}}
												- ((63ba96a1-ddc6-48a6-8adc-c73ec82e8504))
													- {{embed ((63ba96a1-8bf0-452d-b389-6d4d263c7b2d))}}
												- ((63bec62d-2341-485c-9d03-675b8d71f4d4))
													- {{embed ((63ba96a1-c9dd-48ea-a911-7b7bfca376ad))}}
												- ((63baf735-a04f-4a3d-995f-f1b0f031e7e5))
												- ((63bb6e51-c634-4aaf-b34b-2e34c64f7089))
											- DONE [[Day]] 4]]
											  id:: 63baf735-0a7b-4097-b142-f914593d3f33
											  SCHEDULED: <2023-01-09 Fri>
												- ((63bb6e68-9d10-4bc0-babb-df1dfce2e80b))
													- ((63bb6f02-e885-4a15-9678-4eab65867fc4))
												- ((63bec62c-088f-49a7-b61c-b6d619d40d99))
													- {{embed ((63bb73a7-57f1-4f2e-b670-373dbc3bc3ba))}}
												- ((63bbc775-2f1f-494e-9276-c365f7e08f19))
													- {{embed ((63bbc775-3176-4427-8dbb-6d56aa59341e))}}
												- ((63bec62c-f6d2-4ae7-952f-eb1ed8dfffe3))
													- ((63bec62c-e626-4965-a600-d7f110afd239))
												- ((63bec62c-c428-482d-a2fe-9787413f024a))
													- ((63bec62c-476c-4b68-b0b5-c763b82334c3))
												- ((63bec62c-ad7f-4d56-81dd-c917b311eb7b))
													- {{embed ((63bc1c96-a60c-4894-b59b-41af2a397ddc))}}
											- DONE [[Day]] 5]]
											  SCHEDULED: <2023-01-10 Fri>
											  id:: 63baf735-2360-4314-a066-a8a3ffb96e04
											  collapsed:: true
											  :LOGBOOK:
											  CLOCK: [2023-01-10 Tue 12:38:24]--[2023-01-11 Wed 00:09:44] =>  11:31:20
											  :END:
												- ((63bceb82-f642-4700-981c-8e0f43732501))
													- {{embed ((63bcec19-f59f-403a-a844-929cf262e184))}}
													- ((63bec632-7881-4a35-952d-2c0b34f6755b))
												- ((63bec62c-7f76-4ee5-9279-70a35c5b9e7c))
													- {{embed ((63bec632-ddfd-41f3-93bd-fda59f6f998c))}}
											- DONE [[Day]] 6]]
											  SCHEDULED: <2023-01-11 Fri>
											  id:: 63bd414b-e095-4f22-8139-6d8ad5a23530
											  collapsed:: true
											  :LOGBOOK:
											  CLOCK: [2023-01-11 Wed 12:25:17]--[2023-01-11 Wed 20:50:28] =>  08:25:11
											  :END:
												- ((63be1117-b8d0-4090-b59e-a6648e878fef))
													- ((63be111b-cab5-4496-b250-f1307b321fa6))
													- ((63be1f7d-a861-49a7-8ad2-54055b3b72e6))
												- [12:20](((63be3919-1349-463c-b63f-f88273dc6430)))
													- ((63be3928-7429-49b9-827b-087cb300588e))
												- ((63be4fbe-995c-411a-813a-371a753fe730))
													- {{embed ((63be4fbe-0adc-40b1-b7fd-c22c381c1ad2))}}
												- ((63be7ba0-c12f-4163-9260-9ed7ed5b3451))
													- {{embed ((63be7c90-c734-4adb-a6f6-e2ad0c97317f))}}
												- ((63bec62b-9bf6-4924-9ccb-e7b5376e5741))
												- ((63bec62b-b1fa-4634-a363-0fe694655735))
												- ((63bec62b-157e-4f8d-ab4e-77ab916cdac7))
											- DONE [[Day]] 7]]
											  SCHEDULED: <2023-01-12 Fri>
											  id:: 63bec632-5ed8-4e62-aacc-d03b850ba681
											  collapsed:: true
											  :LOGBOOK:
											  CLOCK: [2023-01-12 Thu 11:13:14]--[2023-01-12 Thu 18:42:01] =>  07:28:47
											  :END:
												- ((63bf52bc-6bce-4a88-9f1c-44f0a6d97873))
													- ((63bf52c9-85eb-4b68-a44f-a437a710d892))
												- [08:59](((63bf5b64-6200-44b3-8b0f-2b3d51662979)))
													- {{embed ((63bf5b66-e20e-444d-acc9-88f2752ce2bd))}}
												- ((63bfe34e-42d8-472c-af05-95a89f0a2331))
													- {{embed ((63bfe34e-a5c2-4267-91dd-c7d7b21f81d0))}}
												- [20:05](((63bff984-eb21-46a1-9566-f5314bbedae6)))
													- ((63bff988-de36-4a11-8632-87b0c62d74d3))
										- *[[week]] 2*
										  collapsed:: true
											- DONE [[Day]] 8]] [老婆]([[王露]])的[[生气日]]
											  SCHEDULED: <2023-01-13 Fri>
											  id:: 63c02792-fd41-46f8-98c8-dfd4ad0794da
											  collapsed:: true
											  :LOGBOOK:
											  CLOCK: [2023-01-13 Fri 13:44:24]--[2023-01-13 Fri 20:41:44] =>  06:57:20
											  :END:
												- [08:36](((63c0c357-2d49-4d40-b6cc-a0ca81178ced)))
													- {{embed ((63c0c357-acdc-4a03-b834-ae272537aad0))}}
												- [13:00](((63c0e715-72ea-4262-a20c-d570fac86cf6)))
													- ((63c0e727-9f50-425a-a771-8f74450dc46b))
													- ((63c0e74e-c93d-4748-9edd-7932987fa0db)) ((63c0ed5e-fbe0-4b84-814b-6b9fe1050eb5))
												- [14:05](((63c0f4a3-86ef-4604-8edd-a3dec85f02af)))
													- {{embed ((63c0fe39-1d56-4637-a096-bf854fb38091))}}
													- ((63c10026-3abf-4938-93f6-48186ff81db3))
												- [19:31](((63c14e01-053f-4384-866d-91f94375671c)))
													- ((63c14e01-3a58-4c1e-886b-e541f002b66d))
												- [19:45](((63c14e01-dc69-4d5c-90c0-9875fbf740ff)))
													- {{embed ((63c14e01-dbce-4050-80bb-38d55fd7bcfa))}}
												- [20:13](((63c14e23-284d-44f7-9650-1e71a3fe8cae)))
													- {{embed ((63c14e2a-f324-4ea8-afe3-a64d32a58c20))}}
												- [22:30](((63c16c49-4e1d-4098-bae1-15c8939ab6af)))
													- ((63c16c82-39d9-408b-9772-2e9a9a842303))
											- DONE [[Day]] 9]]
											  id:: 63c19c4a-5111-430f-a077-4300b0771fa1
											  SCHEDULED: <2023-01-14 Fri>
												- ((63c230d0-c960-47ff-ab60-b5c91a78f3a8))
												  collapsed:: true
													- {{embed ((63c230d0-9ba9-4062-b1bc-6ee8878f8508))}}
												- ((63c230d0-2559-4f73-ab59-d6debf9d4d21))
												  collapsed:: true
													- {{embed ((63c230d0-499e-488b-a59d-4b7325446670))}}
												- [19:05](((63c29cb0-13dd-4f6a-ac63-7cd8677cf82a)))
												  collapsed:: true
													- {{embed ((63c29cb0-32ec-4251-9885-de905d6201f8))}}
													- ((63c29cb0-f732-4329-a6a2-57f0221c1b25))
												- [20:00](((63c29ce6-ba3f-4467-a5ed-85cdef33b5e1)))
												  collapsed:: true
													- {{embed ((63c29cb0-dc2b-4f43-8ea5-19dadc29f87a))}}
											- DONE [[Day]] 10
											  id:: 63c247d6-89c6-4bef-a1e5-5ef60c2f81fb
											  SCHEDULED: <2023-01-15 Fri>
												- ((63c35f42-9636-4b55-b768-cd89ca889074))
													- {{embed ((63c35f42-96ae-46e5-b401-fddb9ac8f177))}}
												- [18:18](((63c3d2e9-5a8b-4bbf-8807-9970ac542968)))
													- ((63c3fd05-de92-4a2f-93ac-52e20f7af9f3))
													- ((63c3ec52-f56e-446c-be25-02fcf4c7a627))
													- {{embed ((63c3ec0d-c114-4a96-97cb-37a5fe277a66))}}
											- DONE [[Day]] 11
											  id:: 63c418aa-f990-47fe-9aa2-b0de275c6e63
											  SCHEDULED: <2023-01-16 Fri>
												- ((63c4c7e5-eb1d-492c-8cfc-23e12ed2a012))
													- ((63c4c7eb-5496-412a-9103-5405c32e902f))
												- ((63c5112a-df1d-4d0c-9181-8690fd3bdab5))
													- ((63c51b7f-0865-458f-bffe-e713f505a20e))
												- ((63c52dbe-8b27-45a7-9f5a-cdf02f498c95))
													- ((63c530e6-49ae-40ce-b8f2-80b22d415ca6))
											- DONE [[Day]] 12]]
											  id:: 63c418aa-d684-4149-977a-32353ef997eb
											  SCHEDULED: <2023-01-17 Fri>
												- ((63c668ac-2cee-40ef-a341-e3a8666263ae))
													- {{embed ((63c669a1-e130-40bd-853b-86133343a35f))}}
												- [19:19](((63c68424-dba1-4ed0-a003-a8a68240be7a)))
													- {{embed ((63c6842b-f4f5-475a-905d-cba41a1cceae))}}
											- DONE [[Day]] 13
											  id:: 63c418aa-3bb3-4883-9335-b97c2a1ffb76
											  SCHEDULED: <2023-01-18 Fri>
												- ((63c7619c-c112-42cf-8c36-3adcf99f0ad4))
												  collapsed:: true
													- ((63c7848c-bf86-49c8-8393-529af8e3fa15))
													- ((63c785af-d684-45e4-85c2-60ecc3b193dc))
												- ((63c7dde1-ccf8-4341-b526-84c289128ffa))
												  collapsed:: true
													- {{embed((63c7de1e-1fc7-4828-99be-7861292c1d82))}}
											- DONE [[Day]] 14]]
											  id:: 63c8d646-8e37-4823-84ae-a7e053bdba4f
											  SCHEDULED: <2023-01-19 Fri>
												- ((63d55bbd-d33d-4e47-9282-e0438df1f367))
										- *[[week]] 3*
										  collapsed:: true
											- DONE [[Day]] 15
											  id:: 63c8d646-1db2-41a9-a73b-f64f528e4e6b
											  SCHEDULED: <2023-01-20 Fri>
												- CANCELED 带 [儿子]([[毛懋]]) 去爬个山
												  id:: 63c8d646-1bcf-4502-9abb-46227ac2266d
												- ((63ca28d5-4a6e-4e4d-9e21-b0a7f15b653e))
													- {{embed ((63ca2aa5-ccdb-46fc-8d2c-c262af5d19da))}}
												- [16:53](((63ca5681-9a85-473c-a792-59dd50de3656)))
													- {{embed ((63ca5686-ca3b-43b1-b0eb-d7772bfcb5b5))}}
													- ((63d4a1f6-12da-43df-92f3-0d45a9539853))
												- [20:46](((63ca8d09-5131-46bb-b11a-408142960a3d)))
													- ((63ca8d0c-51ac-4dc0-86ee-be385c952883))
											- DONE [[Day]] 16 年三十
											  SCHEDULED: <2023-01-21 Fri>
											  id:: 63c8d646-45da-4ced-832d-6b9888203c9a
											  :LOGBOOK:
											  CLOCK: [2023-01-21 Sat 14:07:49]--[2023-01-28 Sat 11:07:14] =>  164:59:25
											  :END:
												- ((63cb59e2-cc2e-4f81-9b4e-9dec97737b5c))
													- {{embed ((63cb7b7a-ba98-4362-ab2a-9c0a40507159))}}
													- ((63d55b68-e914-4326-ad60-8846380a87e3))
												- [14:08](((63cb8145-f7eb-418b-b67b-e95ff1982997)))
													- ((63cb821e-ea27-4dcb-a576-769ba76feb91))
													- {{embed ((63d569f6-6562-40ab-b79f-e5f3ebe63ae3))}}
											- DONE [[Day]] 17 大年初一
											  id:: 63d14d43-fcf8-4fa1-a78f-f57c4c09fd17
											  SCHEDULED: <2023-01-22 Fri>
												- ((63d53c3a-b8da-4a07-9bbf-8b0f0732ef7a))
													- {{embed ((63d5650a-7ff4-4c37-93fa-6d397ad37058))}}
												- {{embed((63d53c4b-c015-4423-8d96-493606d838a3)) }}
												- DONE 了解 [[杭州]] 半日游路线 @ [[毛义明]]
												  id:: 63aab924-a8c7-44d2-8e27-84a40df7c2f4
													- DONE 哪里[[坐船]]
													  id:: 63d14d43-394c-452b-a52e-76bcd2474a73
													- CANCELED 哪里[[喝茶]]
													  id:: 63d14d43-a778-4041-bac3-57792880a2a1
													- DONE 初2是否营业？ [*包括老头儿油爆虾*](((63bd414b-ba0c-41f5-8cb1-a9d62f131e16)))
													  id:: 63d14d43-7d56-4fcd-a70f-d90a897baea2
													- ((63d4975d-3539-4baa-9f07-b337b813681e))
												- DONE 收拾 [[行李]]
												  id:: 63d14d43-35ca-4190-bfd9-6a8614b8383e
													- {{embed ((63d52182-1671-4d27-9861-7405fff2e8ee))}}
											- DONE [[Day]] 18 大年初二
											  id:: 63aaacad-0e38-47d5-8392-1dbe6f3fb105
											  SCHEDULED: <2023-01-23 Fri>
												- ((63aab833-dfa8-47f0-b34a-fd676d74bed4))
													- {{embed ((63d8e976-4721-435b-8a7a-e208cd0549d1))}}
													- DONE 回 [[杭州]] ，接上 [老妈]([[丁满冬]])，去 [[周记]]吃午饭
													  id:: 63bd414b-ba0c-41f5-8cb1-a9d62f131e16
													- DONE 回到 [老妈]([[丁满冬]]) 家，看[儿子]([[毛懋]])要不要[[午睡]]
													  id:: 63d14d43-1424-4fb6-8945-6a6fd263160e
														- #if
															- CANCELED 不[[午睡]]
																- #then
																	- CANCELED 去 [[西湖]] [[断桥]] 走走（看夜景？） #recycle
																	  id:: 63d14d43-a834-4868-9d54-bf2c3ca37a70
																	- CANCELED 晚上在[[西湖]]边吃饭 #recycle
																	  id:: 63d14d43-19bf-4978-861f-0fffcb2d0610
											- DONE [[Day]] 19]] 大年初三
											  id:: 63c391e5-0b1a-4cd1-a9f5-8b0a879d4fed
											  SCHEDULED: <2023-01-24 Fri>
												- {{embed((63d49714-f465-43b1-994f-c4312e621b6b))}}
												- DONE [[杭州]] 回 [[广州]]
												  id:: 63d4a2f8-7283-4456-9f1e-6967021d569d
													- {{embed ((63d4bf3c-db61-4093-90fc-524d1989d158))}}
											- DONE [[Day]] 20]] 大年初四
											  id:: 63d4a2f8-eaae-48a5-968a-26f8f6b6abad
											  SCHEDULED: <2023-01-25 Fri>
												- DONE 休整一天
												  id:: 63d4a2f8-01e9-4504-9e5e-74c6a03739e7
											- DONE [[Day]] 21]] 大年初五
											  id:: 63d4a51e-899a-46c3-b012-3c526151f45d
												- ((63d4be5f-7e22-4f60-b92b-514e278141bc))
										- *[[week]] 4*
										  collapsed:: true
											- DONE [[Day]] 22]] 大年初六
											  id:: 63d4a528-5180-480d-a506-d0d231646ce3
												-
												- {{embed ((63d4a547-3d04-43dd-8761-be1d82bc0561))}}
												- {{embed((63d4bc2c-6453-4035-9008-d68968329892))}}
										- #then
										  collapsed:: true
											- *[[春节]] [[2024]]*
											  id:: 63d4b6e2-4638-4296-88a7-a57c0173967e
												- ((63d4b75a-320c-4f6e-b8a0-b5c39409d696))
						- 12:17 - 12:32
						  collapsed:: true
							- #smoking
								- 12:25 - 12:31
									- #深思
										- [[回顾]] 就是 [[复盘]]。常常回顾，理不清的思路也会逐渐清晰， [[规划]] 不好的，也会变好， [[碎片化]] 的想法，也会逐渐形成逻辑。好处多多啊。看到我自己的[回顾列表]([[回顾]]) 越来越壮大，也是一种 [[正反馈]]
											- #then #namespace #recycle
												- DONE [[复盘]]
												- DONE [[小项目]]
						- 12:33 - 13:31
						  collapsed:: true
							- 午饭 with [[王露]]
							  :LOGBOOK:
							  CLOCK: [2022-12-14 Wed 12:33:39]--[2022-12-14 Wed 13:31:43] =>  00:58:04
							  :END:
								- #摘录
									- [[跨境电商]] [[面试]] [[问题]] #速卖锋
									  collapsed:: true
										- 开品流程
										- 一年开了几个品？
										- 品的销售渠道和销量。
										- 团队配置，核心做产品的有几个人？ 团队成员总共有多少？ 怎么分工
										- 说出3个产品的卖点：
										- 如果供应链寄10个品过来，你怎么挑出合适的1个？ 判断依据是什么？ （如何测品）
										- 你想做的产品，没有合适的生产线，怎么选择？怎么处理？
										- 如何找到靠谱的 供应商？
										- 对于供应链和产品开发的 管理，有没有标准化的文档，和模块化的流程？
										- 原有的和电商的工作经验
										- 对自己职场的规划
										- 整体对话总结：我感觉比较在意：品的稳定性高于对品的创新性。 作品的过程是增加确定性，减少成本风险。
						- 13:31 - 15:33
						  collapsed:: true
							- #break
								- ((651bc212-523a-48b7-bce5-e444544ae14b)) & [[Twitter]]
								  :LOGBOOK:
								  CLOCK: [2022-12-14 Wed 13:32:55]--[2022-12-14 Wed 15:33:22] =>  02:00:27
								  :END:
							- #log
								- 13:33 - 15:33 (2h)
								  collapsed:: true
									- ((63930b1a-fec9-4af0-9ee5-b2d658d04439))
										- DONE [[namespace]] -> #recycle
										  :LOGBOOK:
										  CLOCK: [2022-12-14 Wed 13:38:13]
										  CLOCK: [2022-12-14 Wed 13:38:22]--[2022-12-14 Wed 15:32:54] =>  01:54:32
										  :END:
											- [[中年]]
											- [[中产]]
											- [[女性]]
											- [[失眠]]
											- [[睡眠]]
											- [[低谷]]
											- [[希望]]
					- 15:40
					  id:: 63997cb8-3124-4766-9a59-8ead19f64b99
						- #smoking
						- #log
						  collapsed:: true
							- 16:18 - 16:58
							  collapsed:: true
								- DONE [[logseq]] [workflow]([[workflow]])：把非 [[namespace]] 并带有 [[alias]] 的 [[page]] 合并进 [[alias]]，这样更 [[直观]]。 #recycle
								  id:: 639a80e1-c7d5-486d-a01a-e5eb9ce0a604
								  collapsed:: true
								  :LOGBOOK:
								  CLOCK: [2022-12-14 Wed 16:19:07]--[2022-12-14 Wed 16:58:00] =>  00:38:53
								  :END:
									- DONE [[比较视野]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[低谷]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[积极倾听]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[超级元素]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[高三]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[陈波]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[公司信息]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[公众号]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[老家]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- DONE [[打水漂]]
									  :LOGBOOK:
									  CLOCK: [2022-12-16 Fri 12:44:04]--[2022-12-16 Fri 12:44:04] =>  00:00:00
									  :END:
									- #then
										- DONE [[删除]] [[logseq]] [[page]] [[文件夹]] 下的 [[空页面]]
						- 16:58
						  id:: 63998f3a-1300-453e-8e8b-08b40c20834d
							- #摘录
							  collapsed:: true
								- DONE [[挑战]]：[[兴致勃勃]]地去失败
								  #邓亚萍
									- {{video https://www.youtube.com/watch?v=qjkrngJPAVQ}}
										- #案例
											- {{youtube-timestamp 219}} [[梅西]]有轻度 [[自闭症]]， [[C罗]]是 [[自恋型人格]]， [[问题]]： [[顶尖]]的 [[运动员]]是不是都由 [[特殊材料]]制成的？ [[特殊性格]] 、 [[特殊人格]] ……
												- #答案
													- {{youtube-timestamp 244}} [[邓亚萍]]： [[运动员]]的特点就是 [[不服]]， [[较真儿]]，不管对事还是对自己，它都比较较劲。就是这个劲儿， [[轴]]。第二个就是要有天生的自信。——[[二愣子]]精神。
														- #案例
															- 从小就是谁赢了就不让他走。
																- [[迈克·乔丹]]也是，一个病。打扑克也不行。
													- #金句
														- {{youtube-timestamp 374}} [[顶尖]][[运动员]]非常 [敢于去面对]([[敢于面对]]) [[真实的自己]]
															- #案例
																- 那些没打好的时候，甚至有的时候是落后的，可能是翻盘的，那你为什么是落后的，那又为什么又翻盘了，我们是完全要扒开，一层一层扒下去的，完全是扒到体无完肤的时候，你真正看到你最真实的自己。甭管是在技战术层面上，还是在心理层面上。
										- #金句
											- {{youtube-timestamp 491}} 任何事情，从现在做都不晚。任何事情，从零开始学也都不晚。
											  id:: 6399933c-2268-4a5c-9237-9dadb561b3f0
												- #案例
													- 因为我从运动员来讲，我是完整的走了一个完整的路。就是从小孩儿，从不会打球到会打球，最后到成为一个顶尖的运动员。从不会到成为第一个世界冠军，11年的时间，我是这条路是完整的走了一圈儿了。我知道一个事情，你是需要多少的努力，需要多少的时间，才能沉淀出来的。我即使去学习，无非也是这一趟吧。所以这一趟，没有可能给你任何的捷径，一开始上来就是量，先起量，量到一定程度是质的变化，那你只有压到那个程度以后，才有质。你前面没有量，就没有质。读书也一样，学英语也一样。
													  id:: 63b259a3-00fb-4752-8194-8df2b0b81ecd
														- 我不是特别害怕做任何事情吧，我觉得这个方法论是非常非常重要的。
														  id:: 639994dd-8fe6-4400-97fa-fb225424cba0
												- {{youtube-timestamp 581}} 那么怎么能够抓住自己最擅长的东西，就是 [[扬长避短]]。
													- 跟我们打球是一摸一样的。你老去跟人打到人家的套路里头去，你还想赢吗，是吧。因为每个人的特点都是不一样的。你一定要尽可能拉到你的套路里来，甭管比赛打到什么程度，你都得打回到自己最舒适的地方去。一些东西呢，是需要通过技战术来变化的。比如说有些时候，你需要打球打的更快一点，打乱他的节奏，如果这个时候你的势不在你这边，那时候你需要去变节奏。因为你只有变，才有可能再打回来。才又打回到你的套路里来。
														- 所有我们其实在做事情，我们特别想追求不变。
							- 17:27
								- #break #pee-time
								- 17:32
								  id:: 639994a2-d921-415d-aef7-33524a78f95b
									- DONE 更新 ((6306282f-6ee9-447f-abda-1b78da19648b))
									  :LOGBOOK:
									  CLOCK: [2022-12-14 Wed 17:35:37]--[2022-12-14 Wed 22:50:29] =>  05:14:52
									  :END:
									- #log
										- 18:55 - 20:46
										  collapsed:: true
											- :LOGBOOK:
											  CLOCK: [2022-12-14 Wed 18:55:27]--[2022-12-14 Wed 20:45:31] =>  01:50:04
											  :END:
											- 晚饭
											- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
									- 20:46
									  id:: 6399c58a-7858-4875-9c3b-6e3b7630d646
										- #break
											- ((651bc21c-3131-406f-9608-99fda127b5e3))
											  :LOGBOOK:
											  CLOCK: [2022-12-14 Wed 20:59:38]--[2022-12-14 Wed 21:36:01] =>  00:36:23
											  :END:
											- DONE [[老高与小茉]]
											  id:: 6399ce54-dff7-4c0f-aff8-77b056960672
											  :LOGBOOK:
											  CLOCK: [2022-12-14 Wed 21:23:35]
											  CLOCK: [2022-12-14 Wed 21:23:39]--[2022-12-14 Wed 21:36:02] =>  00:12:23
											  :END:
											- #摘录
												- DONE 没人可以看完这个影片，因为不超过2分钟你就会 [[睡着]] 了 #老高与小茉 #视频 #Youtube #睡眠 #超级元素 #失眠 #睡好觉 #身体放松
												  SCHEDULED: <2022-12-20 Tue .+1d>
												  id:: 63b05a53-a9cd-4679-a517-9628511141f6
												  collapsed:: true
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2022-12-15 Thu 15:00]
												  * State "DONE" from "TODO" [2022-12-19 Mon 15:50]
												  :END:
													- {{video https://www.youtube.com/watch?v=EyPi1OlkCJE}}
														- {{youtube-timestamp 12}} [[美军睡眠法]] - 2分钟入睡 - [[巴德]] [[田径教练]]
															- {{youtube-timestamp 132}} 96%的[[美国]][[空军]]都能用这个方法入睡
																- 晚上好用，白天也可以用，在战场上也可以用，外面炮火连天、机枪扫射都完全没有问题。
															- 类似一种[[自我催眠]]
																- {{youtube-timestamp 149}} 两种[[睡眠法]]的结合：
																	- [[肌肉渐进松弛法]]
																	- [[全身扫描冥想法]]
																- {{youtube-timestamp 158}} 原理：让身体不断的[[紧张]][[放松]][[紧张]][[放松]]的同时， {{youtube-timestamp 575}} 让你的大脑去创造一个舒适的环境，然后你的注意力都在你身体上面，就把你这个身体单独扫描下来之后，放到了这个你创建的虚拟环境里，就好像一个元宇宙一样，然后在这个元宇宙里得到彻底的放松。而这个元宇宙实质是什么，就是梦。就是你自己先创造一个舒适的环境，然后把自己的身体送到这个梦境，就实现了睡眠。
																	- 重复2分钟以内，人就会渐渐感到睡意。
																	  collapsed:: true
																		- {{youtube-timestamp 171}} [[睡不着觉]]的主要原因：全身的肌肉不够放松，一直处于一个紧绷状态，但是有些人感觉不到它的身体处于紧绷的状态，因为用力是人有意识控制的，但是放松这个事情不是人有意可以控制的。怎么才能让无意识中紧绷的身体放松呢？就必须有意识的让身体紧张一下，然后再去放松的话，身体就能进入彻底放松的状态。 #知识点
																			- #案例
																				- 伸出你的手，你现在处于一个放松状态，我说：你进一步放松。你就做不到了，因为你已经很放松了，你想进一步放松也放松不了
																					- 怎么让它进一步放松呢？就是握紧拳头，10秒钟，用力握紧拳头10秒钟，然后再松开，你的手就进入一个非常放松的状态。
																						- 就是这种紧绷缓解的话，它就是彻底的缓解。但平时的状态，你也不知道现在是紧绷的还是要缓解，为了让它能进入缓解，彻底的缓解，就先紧绷一下，然后再缓解。
																	- 用力闭眼，再松开
																	  collapsed:: true
																		- 用力闭嘴，再松开
																			- 吸气的时候用力，呼气的时候放松
																				- 做完眼睛做嘴巴，做完面部，做肩部
																					- 然后是手臂，先主手，再副手
																						- 结合[[全身扫描冥想法]]，让注意力集中到具体的部位。
																							- 最后做[[大脑放松]]，想象10秒钟，身处安逸舒适的环境。
																	- {{youtube-timestamp 615}} 重点
																		- 不要用力太大
																		- 注意节奏，和呼吸的节奏配合上。人在一个很有序的节奏下是很容易睡着的。
																- {{youtube-timestamp 232}} 其实[[睡不着]]不是[[不困]]，是[[很困]]，但[[睡不着]]。
																  collapsed:: true
																	- 对，你困是[[大脑困]]，但[[身体不困]]，它现在就让你身体进入一个[[放松状态]]，才能[[配合]]你的[[大脑]][[去睡觉]]。
																- {{youtube-timestamp 244}} 他们说[[不困]]的时候就[[不要睡]]，[[睡不着]]的时候就[[不睡觉]]。
																  collapsed:: true
																	- 其实这是一个[[错误]]的[[观念]]。有可能这样想你会觉得[[放松]]，但是你[[心理]]上这个放松，不一定能造成你[[身体]]的[[放松]]，它现在是告诉你如何让[[身体放松]]。
																		- {{youtube-timestamp 257}} 你的身体不完全由你的意识控制。你想紧张能紧张，但想放松非常的难。怎么才能真正的放松呢？就是先让它紧张一下，然后它自己才会放松。
														- #问题
															- {{youtube-timestamp 646}} 人为什么要 [[睡觉]] ？
																- #知识点
																  collapsed:: true
																	- 人类有三大[[底层欲望]]，[[食欲]]、[[性欲]]、[[睡眠欲]]。
																	  id:: 639a00a5-82f9-4e40-8690-52d5be97b8d7
																		- 所谓欲望就是人类必然的需求，就有这三样。其中最为不重要的，是[[性欲]]。因为人没有[[性欲]]也可以活着。
																		- 食欲和睡眠与当中，[[睡眠欲]]更强一些，因为食欲是可以用人的意识控制的，你只要不想吃饭，它就可以不吃饭。而睡眠是不能控制的，就是我想让自己不睡觉，可能做不到。就是你可以绝食把自己饿死，但是你不可以绝睡把自己困死。
																			- 原因就是[[睡眠]]这个[[欲望]]不完全由你的[[主观意识]]控制，它由一部分由[[潜意识]][[控制]]。
																				- #案例
																					- {{youtube-timestamp 703}} 人类不睡眠最长时间是11天。
																					  id:: 63b2e49e-4f03-4c3d-b6cf-c3104fd307f9
																						- 所以[[睡眠]]可以说是[[人类]][[最根本]]最根本的一个[[需求]]。
																							- #冲突
																								- 但是大家仔细想想这事有点[[诡异]]，因为人类作为自然界的一种动物吧，每天有8个小时处于一个睡眠的状态，就是一个极度危险的状态，你不觉得很奇怪吗？就是你每天有8个小时，是处于一个完全没有防备的状态。比如说你的仇家要干掉你，每天都有8个小时的机会。自然届的动物也要睡觉，这不就很奇怪吗？按照达尔文的进化论的话，这种东西就应该被淘汰掉。——要睡觉的动物最终就应该被淘汰。不要睡觉的动物菜应该存活下来。为什么现在普遍的动物都需要睡觉？这不符合进化的理论。而且如果没有睡眠的话，我们就有更多的时间玩啊，享受生活啊。而且现在社会里有很多事故，都是因为人犯困而引起。所以怎么看，都觉得睡眠这个东西存在很不合理。但是偏偏地球上大部分物种都需要睡眠。有的长有的短而已，尤其是人类，一天需要睡8个小时。1/3的时间都在睡觉。就显得更不合理了。
																								  id:: 63b2e4f1-892b-4bea-8718-b68e8862ce38
																									- {{youtube-timestamp 800}} 事实上这个问题一直困扰着所有的生物学家
																										- 一种说法：人在[[睡觉]]的时候，有些记忆会被固定。就是对生存很有必要的一些信息，它就会在睡眠的时候固定到你的大脑里，成为一个清晰的记忆。这样的话就更加有利于生存。比如说肌肉记忆，情感记忆。
																										  id:: 63b2e93a-ef3c-4b4c-b9e7-b99986fdeab3
																										  collapsed:: true
																											- 为什么看恐怖片会睡不着，就是大脑不想把这个恐怖的经历记下来。 #反常识
																											  id:: 63b2ea0c-2c2c-4261-a8f4-97afe6ec8675
																												- 所以你受伤了也千万不要睡觉，因为你一旦睡觉的话，你这个运动损伤就会被记录下来，你就再也不敢去比如说抓单杠啦，不敢去跑步了。
																												  id:: 63b2ea42-1ee1-455b-b9d7-4f7224fd2af3
																													- 所以大家如果感觉到睡不着，往往可能是白天经历过什么特别糟心的事。很悲伤的时候也不要睡觉，这个悲伤的情感会被记录下来。
																													  id:: 63b2eafc-21c4-4000-9903-7cd5b6d62d37
																										- {{youtube-timestamp 963}} 另一种说法：为什么要睡8个小时？而不是1、2个小时？——[[睡眠]]有可能是大脑清除[[脑内垃圾]]的一种[[机制]]，也就是一种[[洗脑]]机制
																										  id:: 63b2ec0c-a906-4312-9c8f-a77d4a8175a0
																										  collapsed:: true
																											- {{youtube-timestamp 967}} 他们发现[[人类]]在清醒的时候，就是大量使用[[大脑]]的时候，大脑由于[[新陈代谢]]，就会产生一些[[垃圾]]，主要成分是一些有害的蛋白质。
																											  collapsed:: true
																												- #案例
																													- [[β-淀粉样蛋白]] ：在[[阿兹海默症]]患者中大量发现。也就是说大脑产生了一种垃圾，让我们自己产生老年痴呆这些症状。
																													  id:: 63b2f05d-b89a-49e1-b315-358e3ac421b3
																													  collapsed:: true
																														- 正常情况下，[[β-淀粉样蛋白]]会被[[脑脊液]]清洗掉。
																															- 人在醒着的时候，大脑细胞就会膨胀，细胞与细胞之间的距离就没有了，[[脑脊液]]就留不进来了，大脑产生的垃圾就一直留在这些缝隙里，冲洗不掉。
																																- 人在睡觉的时候，脑细胞就会缩小，缝隙就产生了，[[脑脊液]]就流进来，把它们冲洗掉了。
																												- #问题
																													- [[睡眠充足]]就不会[[老年痴呆]]了吗？
																														- #答案
																															- 对，为什么叫[[老年痴呆]]，因为老年人都[[睡眠不足]]。其实每个人都需要睡到8个小时，但是老年人睡不到，他们以为自己睡4个小时就够了，其实不够。
																																- #案例
																																	- {{youtube-timestamp 1065}} 为什么很多成功人是会鄙视睡觉，因为他们会认为睡觉是在浪费时间。像[[爱迪生]]，现在的人都睡太多，我是不会睡那么多的，我死后会一直睡。
																																	  id:: 63b2f287-9f8f-47ee-9a3a-da60002002ae
																																		- [[爱迪生]]年轻的时候的确睡的很少，但是到老的时候，随便找个地方就睡了，拍到他很多在草地上睡觉的照片，就是其实他是想睡就睡，他说他享受睡觉那个过程，他在睡觉的时候也在思考，他自己说的。说明他不是不在睡，而是他一直都在睡。只是告诉你们，你们的睡觉是没有意义的，我的睡觉是有意义的，这样一种感觉。
																																			- 所有大家千万不要轻信说自己能够[睡的很短]([[睡得少]])就能[[保持精力旺盛]]。——可能短时间内可以，代价就是[[大脑]]内一直有[[垃圾]]清理不掉。
																											- {{youtube-timestamp 1140}} 所以，就是说我们一直醒着的话，就不会被老虎吃掉，但是一直醒着就会傻掉。
																												- ((63b2e49e-4f03-4c3d-b6cf-c3104fd307f9)) 从第二天开始，身体就出现各种症状：
																													- 眼睛无法聚焦
																													- 恶心
																													- 分不清红绿灯
																													- 注意力无法集中
																													- 产生幻觉
																													- 幻觉也变得片段化
																													- 看世界变成二维的
																													- 无法控制自己的身体
																													- 无法进行100以内的加减运算
																													- 口齿不清晰
																													- 发音困难
																													- 无法进行正常的思考
																													- 记忆里和语言能力明显下降
																													- 说明不睡觉大脑一直存有垃圾，这个垃圾一直在阻碍他的思想。——[[老年痴呆]]的加速版。
																		-
																- *总结*
																	- {{youtube-timestamp 1381}} 睡眠对所有人来说，都是必须的。我们在醒着的时候大脑就会产生垃圾，这个[[垃圾]]就一直存在脑子里。[[睡眠]]会帮我们清楚这些垃圾，如果长时间[[不睡觉]]，或者长时间睡眠不足的话，这些垃圾就会一直存在脑子里面，我们就会傻掉，就会出现[[老年痴呆]]的症状，解决这个问题的方法只有一个，就是睡眠，没有其他任何东西可以替代睡眠，这也就是为什么睡眠在生存淘汰当中没有被淘汰掉的原因，因为它对我们所有生物来说，对我们所有生物的大脑来说都是必要的。
										- 22:47
											- #continue
											  collapsed:: true
												- {{embed ((6399f0ad-33f2-4776-bb1f-c9ca9ea88f53))}}
											- #log
											  collapsed:: true
												- 23:08 - 23:16
												  collapsed:: true
													- #超级元素
													  collapsed:: true
														- #闪念
															- 2023新年了，在困难也要迈过去 #标题
															- 2023， [[中年人]] 要勇起来。 #标题
																- [83年]([[1983]]) 生的人迈进了 [[40岁]]，有多少人拿到了成果？
																  id:: 639ad63c-2d4e-4b48-bc96-713886426e07
																	- [[中年]] ，可能是 [[人生]] 的最好的 [[礼物]] ，也可能是 [[前程]] 的 [[坟场]]
																	  id:: 639ad63c-7d23-484d-b0f7-06c80e76ca51
													- #闪念
													  collapsed:: true
														- [[王露]] 是通过 [[面试]] 来 [[复盘]] 她在 [[超级元素]] 的经历 [[宝石]] 啊
														  #宝藏 #经历
													- #摘录
													  collapsed:: true
														- CANCELED [我，给佛祖打工](cubox://card?id=********************************) #中年
															- 从知乎热门提问「35岁以上的人都去哪儿了」到脉脉上的热议「35岁非升即走」再到大厂招聘JD上的明确要求「年龄在35岁以下」。在社交网络上，往往是年龄越大、越难找工作。
												- 23:34 - 23:54
												  collapsed:: true
													- DONE [[Conor White-Sullivan]] in [[Youtube]] 一篇[[文章]]的多个[[草稿]] #写作
													  :LOGBOOK:
													  CLOCK: [2022-12-14 Wed 23:38:47]
													  CLOCK: [2022-12-14 Wed 23:38:54]--[2022-12-14 Wed 23:42:38] =>  00:03:44
													  :END:
														- {{video https://youtu.be/Ag_22rnauts}}
											- ((6399f535-0f27-4990-93df-336dfb219c06))
									-
				-