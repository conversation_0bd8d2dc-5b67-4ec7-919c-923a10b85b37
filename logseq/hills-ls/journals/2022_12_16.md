- 00:27 ((639c3bd2-454a-4d45-ad5f-027995328e49)) ((639c3bd2-c8f5-4a93-b280-4e4c3c0ab9db)) ((639c4bbc-8d24-4ff1-a7f2-7625678fa4f9))
  id:: 639b4b07-15af-40fb-bc33-0e08c0f414a1
  collapsed:: true
	- #offline
	- 10:19
	  id:: 639c3bd2-454a-4d45-ad5f-027995328e49
		- #online
		  collapsed:: true
			- #continue
			  collapsed:: true
				-
				-
				-
				- ((651bc214-fdd3-47ef-b38d-a5a27ae3eff3))
			- collapsed:: true
			- #[[回忆]]
				- ((6353a251-268b-4d21-a10d-e5e664f14e47)) => 1点05睡着，9点30正式醒来，睡了8小时25分。那是睡的相当的沉啊。看来跟 [[焦虑]] 也有关系，[[深度睡眠时长]]勉强达到2小时。这么看的话，越焦虑，越要睡足8小时？ #2小时深度睡眠
				  collapsed:: true
					- #截图
						- ![WechatIMG420.jpeg](../assets/WechatIMG420_1671157500863_0.jpeg){:width 245}
			- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
			  :LOGBOOK:
			  CLOCK: [2022-12-16 Fri 10:30:50]--[2022-12-17 Sat 11:35:53] =>  25:05:03
			  :END:
			  ```calc
			  
			  ```
			- ((651aa256-9459-42af-8400-536aadc21970))
			  :LOGBOOK:
			  CLOCK: [2022-12-16 Fri 10:31:07]--[2022-12-16 Fri 10:37:39] =>  00:06:32
			  :END:
			- :LOGBOOK:
			  CLOCK: [2022-12-16 Fri 10:38:05]--[2022-12-16 Fri 10:54:31] =>  00:16:26
			  :END:
			- #书单
				- TODO [[hls__幸福的陷阱_1670516555928_0]] [*](((639b3ef2-f10e-4cb6-b6f9-24220b5e5325)))
				  id:: 63f2e47b-c89f-4a7b-a634-121d68c65e30
				  :LOGBOOK:
				  * State "DONE" from "TODO" [2022-12-16 Fri 10:45]
				  * State "DONE" from "TODO" [2022-12-27 Tue 11:41]
				  * State "DONE" from "TODO" [2022-12-31 Sat 11:53]
				  * State "DONE" from "TODO" [2023-01-07 Sat 13:44]
				  * State "DONE" from "TODO" [2023-01-16 Mon 12:00]
				  * State "DONE" from "TODO" [2023-01-29 Sun 01:55]
				  * State "DONE" from "TODO" [2023-01-29 Sun 01:56]
				  * State "DONE" from "TODO" [2023-02-03 Fri 06:33]
				  * State "DONE" from "TODO" [2023-02-10 Fri 13:50]
				  * State "DONE" from "TODO" [2023-02-17 Fri 15:06]
				  * State "DONE" from "TODO" [2023-02-24 Fri 00:19]
				  * State "DONE" from "TODO" [2023-03-03 Fri 10:25]
				  :END:
			- DONE [workflow]([[workflow]]) ((639a80e1-c7d5-486d-a01a-e5eb9ce0a604))
			  collapsed:: true
			  :LOGBOOK:
			  CLOCK: [2022-12-16 Fri 10:54:48]--[2022-12-16 Fri 12:32:24] =>  01:37:36
			  :END:
				- DONE [[快速眼动期]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[老人]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[认可自己]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[失眠]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[习近平]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[新冠]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[营销]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[做梦]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[Conor White-Sullivan]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:19] =>  00:00:01
				  :END:
				- DONE [[中年]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:20] =>  00:00:02
				  :END:
				- DONE [[中产]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:20] =>  00:00:02
				  :END:
				- DONE [[育儿]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:18]--[2022-12-16 Fri 12:32:20] =>  00:00:02
				  :END:
				- DONE [[育儿 · 家庭]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
				- DONE [[希望]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
				- DONE [[孩子是债务]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
				- DONE [[睡好觉]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
				- DONE [[社科 · 纪实]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
				- DONE [[比较政治学]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
				- DONE ((62d2f4a3-33c8-41e7-9f4a-42db1b024eb9))
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
				- DONE [[老年痴呆]]
				  :LOGBOOK:
				  CLOCK: [2022-12-16 Fri 12:32:19]--[2022-12-16 Fri 12:32:20] =>  00:00:01
				  :END:
			- ((651bc21c-3131-406f-9608-99fda127b5e3))
			  :LOGBOOK:
			  CLOCK: [2022-12-16 Fri 10:54:55]--[2022-12-16 Fri 12:32:27] =>  01:37:32
			  :END:
			- ((651bc212-523a-48b7-bce5-e444544ae14b))
		- 12:32
		  id:: 639c3bd2-c8f5-4a93-b280-4e4c3c0ab9db
			- #continue
			  collapsed:: true
				- {{embed ((6399f0ad-33f2-4776-bb1f-c9ca9ea88f53))}}
			- #log
				- 12:43 - 13:32
				  collapsed:: true
					- 午饭 with [[王露]]
					  :LOGBOOK:
					  CLOCK: [2022-12-16 Fri 12:43:08]--[2022-12-16 Fri 13:30:10] =>  00:47:02
					  :END:
				- 13:32 - 15:59
				  collapsed:: true
					- #break
						- #闪念
							- DONE [看]([[想看]])[[银河护卫队]] 圣诞特别篇
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 13:34:30]--[2022-12-16 Fri 14:37:46] =>  01:03:16
							  :END:
							- DONE [看]([[想看]])[[解放黑奴]]
							  id:: 639cb3a6-8c97-4ff1-9805-de642229d79e
							  :LOGBOOK:
							  CLOCK: [2022-12-17 Sat 12:44:11]--[2022-12-17 Sat 15:14:32] =>  02:30:21
							  :END:
						- #continue
							- DONE [[抖音]]
					- #log
						- 13:48 - 14:12
						  collapsed:: true
							- #回忆
							  collapsed:: true
								- DONE [[小叶阿姨]] 问我们帮买 [[试剂盒]]。 *（5支一盒，她想要10盒）*[[王露]] 说不是很好买。家里我之前让王露囤的还有10几根，先寄10根过去！哈哈哈！ 
								  id:: 639c2421-6750-41dc-8cab-39c2577fc945
								  SCHEDULED: <2022-12-16 Fri>
									- [老婆]([[王露]]) 又问 [[朱婷婷]] 要了20根！
							- DONE 更新 ((639a80e1-c7d5-486d-a01a-e5eb9ce0a604))
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 13:48:43]
							  CLOCK: [2022-12-16 Fri 13:48:52]--[2022-12-16 Fri 14:10:16] =>  00:21:24
							  :END:
							- ((639c2421-6750-41dc-8cab-39c2577fc945))
							  collapsed:: true
								- #地址
									- [叶爱春]([[小叶阿姨]]) 13588722225浙江省 杭州市下城区石桥街道 北景园新鼎家园3-1-1502室
				- 18:04 - 18:42
				  collapsed:: true
					- :LOGBOOK:
					  CLOCK: [2022-12-16 Fri 18:04:33]--[2022-12-16 Fri 18:42:44] =>  00:38:11
					  :END:
			- 18:43
			  id:: 639c4bbc-8d24-4ff1-a7f2-7625678fa4f9
				- #break
					- DONE [[测试]] `[[logseq-powertag]]`
					  :LOGBOOK:
					  CLOCK: [2022-12-16 Fri 18:44:06]
					  CLOCK: [2022-12-16 Fri 18:44:10]--[2022-12-16 Fri 18:55:54] =>  00:11:44
					  :END:
					- 18:56 - 19:11
						- DONE [[namespace]] [[书单]] -> 把 [[Marginnote]] 的 [[电子书]] [[分类]] 导入进来 #recycle
						  collapsed:: true
						  :LOGBOOK:
						  CLOCK: [2022-12-16 Fri 18:56:12]
						  CLOCK: [2022-12-16 Fri 18:56:19]--[2022-12-16 Fri 19:11:45] =>  00:15:26
						  :END:
							- DONE [[人物 · 传记]] [[人物 · 传记]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:39] =>  00:00:01
							  :END:
							- DONE [[观点 · 工具]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:39] =>  00:00:01
							  :END:
							- DONE [[绘本 · 漫画]] [[绘本 · 漫画]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:39] =>  00:00:01
							  :END:
							- DONE [[科幻 · 玄幻]] [[科幻 · 玄幻]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:39] =>  00:00:01
							  :END:
							- DONE [[科学 · 新知]] [[科学 · 新知]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:40] =>  00:00:02
							  :END:
							- DONE [[历史 · 文化]] [[历史 · 文化]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:40] =>  00:00:02
							  :END:
							- DONE [[商业 · 经管]] [[商业 · 经管]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:40] =>  00:00:02
							  :END:
							- DONE [[社科 · 纪实]] [[社科 · 纪实]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:40] =>  00:00:02
							  :END:
							- DONE [[诗歌 · 散文]] [[诗歌 · 散文]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:40] =>  00:00:02
							  :END:
							- DONE [[推理 · 悬疑]] [[推理 · 悬疑]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:40] =>  00:00:02
							  :END:
							- DONE [[外国 · 文学]] [[外国 · 文学]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:38]--[2022-12-16 Fri 19:11:40] =>  00:00:02
							  :END:
							- DONE [[中国 · 文学]] [[中国 · 文学]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:39]--[2022-12-16 Fri 19:11:40] =>  00:00:01
							  :END:
							- DONE [[心理 · 哲学]] [[心理 · 哲学]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:39]--[2022-12-16 Fri 19:11:40] =>  00:00:01
							  :END:
							- DONE [[艺术 · 设计]] [[艺术 · 设计]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:39]--[2022-12-16 Fri 19:11:40] =>  00:00:01
							  :END:
							- DONE [[营销 · 运营]] [[营销 · 运营]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:39]--[2022-12-16 Fri 19:11:40] =>  00:00:01
							  :END:
							- DONE [[影视 · 戏剧]] [[影视 · 戏剧]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:39]--[2022-12-16 Fri 19:11:41] =>  00:00:02
							  :END:
							- DONE [[育儿 · 家庭]] [[育儿 · 家庭]]
							  :LOGBOOK:
							  CLOCK: [2022-12-16 Fri 19:11:39]--[2022-12-16 Fri 19:11:41] =>  00:00:02
							  :END:
				- 19:12
					- 晚饭
					  :LOGBOOK:
					  CLOCK: [2022-12-16 Fri 19:12:07]--[2022-12-16 Fri 19:40:23] =>  00:28:16
					  :END:
					- DONE [[整理]] [[玩具]]
					- 19:40
						- #break
							- 20:24 - 20:50
								- ((651bc21c-3131-406f-9608-99fda127b5e3))
								  :LOGBOOK:
								  CLOCK: [2022-12-16 Fri 20:25:00]--[2022-12-16 Fri 20:50:27] =>  00:25:27
								  :END:
							- 20:50 - 23:47
							  collapsed:: true
								- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
							- 23:47
								- DONE 解决 ((631cc627-fa0c-4fc7-8379-c784e38f58b6)) [[乱码]] 问题
								  id:: 639cacec-c843-43e1-a1d8-6ff157c7dd6d
								  :LOGBOOK:
								  CLOCK: [2022-12-16 Fri 23:48:10]
								  CLOCK: [2022-12-16 Fri 23:48:19]
								  :END:
									- #if
										- DONE 不是 [[logseq]] 的问题，在 [[Telegram]] 上寻求帮助
											- #网址
												- [mac软件破解](https://appstorrent.ru) #[[Apple Mac]] #破解版
											- #then
												- DONE 下载 [[ABBYY-FineReader]] [[破解版]]
												  id:: 639c9349-c774-405f-a76b-b9324ff31c0c
												- DONE 移除原始书摘页面，包括清除 asset 文件
												  :LOGBOOK:
												  CLOCK: [2022-12-16 Fri 23:54:26]
												  CLOCK: [2022-12-16 Fri 23:54:30]--[2022-12-17 Sat 00:09:37] =>  00:15:07
												  :END:
													- #回忆
														- [[re-index]] 是真的烦！
												- DONE [[pdf]] 重新 [[上传]] ((631cc627-fa0c-4fc7-8379-c784e38f58b6))
												  id:: 639cacec-4584-40fd-a1d4-e8119cbdcc9e
												  :LOGBOOK:
												  CLOCK: [2022-12-16 Fri 23:47:52]--[2022-12-17 Sat 01:32:42] =>  01:44:50
												  :END:
								- #log
									- 00:12 - 00:15
									  collapsed:: true
										- #continue
											- {{embed ((639c970e-c295-486d-a5d4-69e832567f3b))}}
									- 00:15 - 01:08
									  collapsed:: true
										- DONE [[电报]] 群分享 [[logseq]] [workflow]([[workflow]]) 使用经验
										  :LOGBOOK:
										  CLOCK: [2022-12-17 Sat 01:01:05]
										  CLOCK: [2022-12-17 Sat 01:01:12]--[2022-12-17 Sat 01:08:46] =>  00:07:34
										  :END:
											- #回忆
												- 获得了一致肯定，哈哈哈。
													- #截图
														- ![WechatIMG427.jpeg](../assets/WechatIMG427_1671210512153_0.jpeg){:width 245}
								- ((639cabf0-53d4-4be0-baba-fbff364b19f9))