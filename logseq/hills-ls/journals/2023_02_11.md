- 10:39
	- #回忆
	  :LOGBOOK:
	  CLOCK: [2023-02-11 Sat 10:17:53]
	  CLOCK: [2023-02-11 Sat 10:18:18]--[2023-02-11 Sat 10:38:41] =>  00:20:23
	  :END:
		- [[捕蝇草]]几天没看长的好大！[[小萝卜]][[发芽]]🌱啦！[[蝈蝈]]在叫！美好的一天开始啦 @[儿子]([[毛懋]])[老婆]([[王露]])[老妈]([[丁满冬]])
		  collapsed:: true
			- #照片
				- ![2023-02-11-14-20-45.jpeg](../assets/2023-02-11-14-20-45.jpeg)
				- ![2023-02-11-14-21-04.jpeg](../assets/2023-02-11-14-21-04.jpeg)
				- ![2023-02-11-14-22-13.jpeg](../assets/2023-02-11-14-22-13.jpeg)
- 10:54
	- #闪念
		- DONE [[解决]] [[logseq]] [[SYNC]] 卡住的问题 -> 原来是 0.8.17 使用 http2 ，而我的 [[VPN]]不支持，回撤到 0.8.16 就好了
- 15:01
  id:: 63e7a608-fd36-4c0a-adf6-96a3678df382
	- #闪念
		- CANCELED [[复盘]]：从第一条 [[journal-page]] 开始，思考 [[logseq]]的[[使用逻辑]] to [[May 18th, 2022]] [*](((63e720af-ed97-40b1-9a79-26bb1a4518ff))) 
		  SCHEDULED: <2023-02-12 Sun .+1d>
		  id:: 63e7431d-81de-40af-bf0e-cb73e225b181
		  :LOGBOOK:
		  CLOCK: [2023-02-11 Sat 15:27:26]--[2023-02-11 Sat 18:19:51] =>  02:52:25
		  * State "DONE" from "DOING" [2023-02-11 Sat 18:19]
		  :END:
	- #log
		- 15:32 - 16:07
			- #break
			  :LOGBOOK:
			  CLOCK: [2023-02-11 Sat 15:32:23]--[2023-02-11 Sat 16:07:07] =>  00:34:44
			  :END:
		- 17:11
			- TODO [[冯唐]]：[[睡觉]]是一项[[运动]] #摘录 [*](((63e7216f-60e7-4258-ba7c-ec3d07eec142)))
			  link:: [抖音](https://www.douyin.com/video/7198094622848027907)
				- #视频
					- ![不要贬低睡觉这种运动，一觉解千愁。@冯唐讲书.mp4](../assets/不要贬低睡觉这种运动，一觉解千愁。@冯唐讲书_1696243511229_0.mp4)
		- 18:20 - 20:02
			- #break
		- 20:05
			- #continue
				- ((63982e3d-77cd-49c5-8644-5d867d01976d)) -> 试下 block 下的 tag 隐藏 -> 不好看
				  :LOGBOOK:
				  CLOCK: [2023-02-11 Sat 20:05:22]
				  CLOCK: [2023-02-11 Sat 20:05:27]
				  :END:
		- 22:33 - 22:57
		  id:: 63e7a734-c639-402b-9548-acdb3a770807
			- #continue
				- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8)) - [[关电灯]][[喝牛奶]][[红红]]和[[黄黄]]
- 22:57
  id:: 63e7b698-ef4e-4622-8921-05f855356ff5
	- #continue
		- ((63e35de8-f4a5-4f4b-8370-f272b6ec9e3e))
- 23:36
  id:: 63e7b698-cef5-445d-814c-a29979000c81
	- #break
		- #闪念
			- DONE [[logseq]] [[bug]] [[提交]] [[Github]]：[[block-ref]] [[排序]] 让人困惑
- [[Comments]]
  collapsed:: true
	- #深思
		- [[logseq]][[使用逻辑]]思考： -> ((63e7431d-81de-40af-bf0e-cb73e225b181))
		  id:: 63e74365-bc71-48cb-877e-af8e62425a29
			- 遵循产生想法的过程，一个[卡片]([[zettel]])能联系到什么？
			  id:: 63f244cd-ab24-4870-ae37-0b67f98c53d0
				- 之前的一个事件
				- 之前的一个想法
				- 之前的一个固定选题
				  id:: 63e743a1-de22-4e74-908b-e82ddda7a610
					- #质疑
						- 选题一多，很难坚持。人是关键词动物啊……
			- [[gtd]] 是顺时进行的
			- [[想法]]是逆势进行的
		-