- **[[Morning-Page]]**
- [[Society]]
  collapsed:: true
	- 10:06 - 11:41
	  id:: 63f42727-fae7-4df6-9bbc-121ef43e576c
		- #闪念 #recycle
			- CANCELED [[武汉]][[云仓]][[苏总]]介绍了[[武汉]]的新[云仓]([[云仓]])：[[武汉]][[中诺]][[云仓]][[贺总]]，今天确认下具体[[细节]] #云仓 #转仓 [*](((63f30a99-d07c-46ed-a579-48414a7eeb2c))) [*](((63f2e46a-4fdb-400e-8856-210517f8a0b6)))
			  id:: 63f427aa-1a26-4fba-9b75-3bad50acd10c
			  SCHEDULED: <2023-02-28 Tue>
				- #recycle
				  collapsed:: true
					- ((63ed145e-6a3f-464f-bd69-76f2a62250c1)) ((63f30a99-aeee-4b95-8cdd-5b50018b4008)) ((63f30a99-19b5-482c-8591-e0c7ba83d03d)) ((63f30a99-4479-465a-a8ea-7daf1e68c271)) ((63f30a99-a1a7-4aa6-b77e-f4b7d889781a)) ((63f30a99-343c-4929-993a-3996c2e91950)) ((63f30a99-01f4-4fad-99ec-a5252a6dd2fb)) ((63f30a99-69b8-4e3f-8f0c-fc5892139bbb)) ((63f30a99-0cc3-4d16-90ce-f563fbee049f))
				- #背景
				  collapsed:: true
					- [[武汉]][[云仓]][[苏总]] 和 [[武汉]][[中诺]][[云仓]][[贺总]] 都是 [[顺丰]] 出来的
					- [[武汉]][[中诺]][[云仓]][[贺总]] 比 [[武汉]][[云仓]][[苏总]] 大很多倍
				- #before
				  collapsed:: true
					- ((63edca4a-b1e7-4945-82e7-9fdd0c880c10))
					- #step
						- [[武汉]][[云仓]][[苏总]] #recycle
							- CANCELED 安排[[货车]][[转仓]]
								- CANCELED [[费用]]是多少？
								- CANCELED 有专门人负责押货吗？
								- CANCELED 需要保证损耗率？
						- [[武汉]][[中诺]][[云仓]][[贺总]]
							- DONE [[费用]]怎么算？
								- DONE [[仓储]]费用：1200；每天达到400单时，可以免[[租金]]。
								- CANCELED [[快递]]费用，需到货后，拆箱确认
									- #做选择
										- CANCELED [[京东]]？
										- CANCELED [[其他]]？
								- CANCELED [[合同]][[快递]][[盖章]]
									- #word
										- [运输服务合同.docx](../assets/运输服务合同_1676950858110_0.docx)
							- DONE 后面发快递单怎么个流程？ -> 群里发地址就行
							- DONE 有没有专门的系统对接？
							- DONE 距离[[武汉]][[中诺]][[云仓]][[贺总]]多远？ -> 10 公里左右
							- DONE [[转仓]]后需检查损耗
		- #回忆
			- 忍不住催了一下[[朱峻修]]，他说明天下午约，再约时间？还是见面？我日 [*](((63edca4a-b1e7-4945-82e7-9fdd0c880c10)))  #pass
			  id:: 63f43a6f-39c9-40cf-8b02-60e0b9f1150c
	- 18:15
	  id:: 63f499a6-04e9-4e74-bdce-a2eb91fc83ef
		- #continue #recycle
			- CANCELED [[continue]]相关[[标签]]，顺便调整结构 [*](((63f59419-e3bf-4158-aa9b-c333b2c2cb41))) [*](((63930b1a-fec9-4af0-9ee5-b2d658d04439)))
				- #recycle
					- ((63e766a1-1913-4f24-90a4-353370311a5b))
				- DONE ~~[[continue]][[超级元素]] [[namespace]] -> 只保留2级~~
				  id:: 63f73261-bc9b-494e-bff2-083f34699df1
- [[Family]]
  collapsed:: true
	- *19:24*
		- #回忆
			- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8)) => [[电动车]] -> [[小黑公园]] 之旅，再加一餐[[薯条]]+[[拉明顿]]。 #毛懋
- [[Myself]]
  collapsed:: true
	- *15:41*
		- #回忆
			- [[fuck!]] 感觉 [[Twitter]] [[API]] 永远也等不到了！ [*](((63fb7522-0b38-4886-8186-25e813c9e92f)))
		- #回忆
			- [[手表]][[充电器]]找到了，包括找到了之前的，所以现在家里有两个。 [*](((63f450d6-02fd-416a-8115-c1289b47d82d)))
		- #深思 #recycle
		  collapsed:: true
			- ~~不需要大改，其实主要[[矛盾]]在 [[namespace]] 的[[用法]]上，目前用下来，[[namespace]] = [[常青笔记]] = [[项目]][[归档]]，也就是 [[para]] 中的 [[project]]部分，可以用这种目录结构。不与标签库共享。~~ [*](((63e720af-ed97-40b1-9a79-26bb1a4518ff))) [*](((63f47d08-96f9-4f7e-88e3-65d107c0d490)))
			  id:: 63f73261-8ebb-4393-91aa-35e4e0fc8af5
				- ~~因此[[整理]]，是一个单独的[[workflow]]：~~ #recycle
				  id:: 63f73261-d8f8-4d0e-ac6c-19608ec95822
					- ~~[[创建]] [[namespace]]~~ #recycle
					  id:: 63f73261-cfa3-4489-907a-714fb0aa82e1
					- ~~搜索相应的[[关键词]]，打上[[namespace]]标签~~ #recycle
					  id:: 63f73261-4533-45bb-b7da-7adea07e5e8a
					- ~~进入 [[namespace]]页面开始[[continue]]~~ #recycle
					  id:: 63f73261-ef16-4a90-93df-3838dbadc4a8
					- ~~用`linked` `pass` `回收` 把整理过的内容[[过滤]]~~ [*](((63f450d6-c840-4a49-bd08-e888e22db91d))) #recycle
					  id:: 63f73261-6bfe-45b6-96cd-9ebf5a220c06