- **[[Morning-Page]]**
- [[Society]]
	- 00:16 - 00:28
	  collapsed:: true
		- #review
		  collapsed:: true
			- ((643fcf42-14d8-4a67-9722-a0ee97ef0dff)) -> ((64415a3f-cfa6-4f28-a447-c4e670ccd399))  [*](((643fca30-0a2e-442e-ab7e-3549956527ce)))
				- ### [[采购]][[流程]]
					- 企业微信告知[[于淼]]，然后走报销流程  [*](((64415a3f-1fd0-469a-a614-e03fe1286f55)))
				- ### [[差旅]][[流程]]
					- 把我的行程和[淼姐]([[于淼]])说，然后企业微信里申请[[外勤]]
				- ### 谁来立项-[我]([[毛义明]])  -> ((64415a3f-10df-4470-8ab7-d56d040ec8c1))
					- 既然划到事业部了，减少其它邮件环节，保留立项邮件即可。[[产品规划]][[订量]]后即可[[邮件立项]]，发送给[[于淼]]、[[凯敏]]、[[朱文豪]]。紧急的拉群加急审核合同、打款。
					  id:: 64465c4b-d628-443f-a5b5-dfbb0cc10ea9
						- #示意图
						  collapsed:: true
							- ![企业微信截图_25e10c34-7879-4848-a8da-513364c63fe2.png](../assets/企业微信截图_25e10c34-7879-4848-a8da-513364c63fe2_1682094031663_0.png)
						- #then
							- DONE 构思[邮件立项]([[邮件立项]])必要内容 with [[王露]] 
							  id:: 6442b879-692e-4636-9c81-5450cde436b5
							  SCHEDULED: <2023-04-24 Mon>
	- 00:28
	  collapsed:: true
		- #肯定
			- [[大快人心]]！ -> ((64426e04-62d2-4ca9-a5d3-a7776d1f72aa))
	- 11:04
	  collapsed:: true
		- #闪念
			- DONE [明天](Apr 23rd, 2023)[[朱文豪]]去[[强哥]][[公司]]，那 [[nyo-3]] 的[[分销]]资源和其他 
			  id:: 6443e24f-d033-418a-bd4d-89bd4f26fa59
			  SCHEDULED: <2023-04-23 Sun 14:00>
			- CANCELED 周四[淼姐]([[于淼]])一起去[[大旺]]-[[橡果]]见[[橡果/刘清]] 
			  id:: 64465c4b-b271-49c1-814e-b1863d872db2
			  SCHEDULED: <2023-04-28 Fri>
- [[Family]]
	- 21:33
	  collapsed:: true
		- #回忆
			- ((64415a3f-402d-4707-bc56-a5100c0abd0f)) -> 累趴了还坚持去，感觉[我]([[毛义明]])要[[生病]]了，胸疼，出冷汗，应该有点[[低烧]]。   [*](((64409cb7-714d-4fbc-9631-63cee1d4c5cd))) [*](((64415a3f-aa30-4791-ad60-a26147a14fe2))) [*](((64415a3f-9bb4-4e50-8a7c-751b2178f966)))
- [[Myself]]
	- 22:00
	  collapsed:: true
		- #摘录
		  collapsed:: true
			- [[失效]][块引用]([[block-ref]])
				- ``` clojure
				  #+BEGIN_QUERY
				  {:title "Broken references" 
				  :query [:find (pull ?b [*]) 
				  :in $ ?matcher 
				  :where [(re-pattern ?matcher) ?regex] 
				  [?b :block/content ?c] 
				  [(re-find ?regex ?c)] 
				  [?b :block/refs ?br] 
				  [(missing? $ ?br :block/content)] 
				  [(missing? $ ?br :block/name)] ] 
				  :inputs ["\\([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\)"] 
				  :table-view? false}
				  #+END_QUERY
				  ```
	- 22:48
	  collapsed:: true
		- #闪念
			- DONE [看]([[想看]])[[蚁人与黄蜂女：量子狂潮]]
			  id:: 646ded3e-5b2c-4b47-9043-eb548af1f272