- **[[Morning-Page]]**
- [[Society]]
	- 11:00
	  collapsed:: true
		- #continue
			- {{embed ((64e24d86-ab1f-419c-863d-851a879f462d))}}
	- 22:59
	  collapsed:: true
		- #摘录
			- by[普惠]([[朱峻修]])：[[管理者]]的[[核心输出]]：
				- 1）基于战略，做好关键性的工作事项选择和创新提升空间；
				- 2）招募并保持团队的协作和战斗力状态；并关注团队成员的成长，尤其专业能力和能量心态，致力于团队成长；
				- 3）关键性业务协作流程，流程型组织一条龙的搭建；
				- 4）组织愿景，使命和文化的传递。
				- 七夕节无意打扰大家[呲牙]，想到这个点觉得要加粗标红，得和大家同步一下，补充一下：管理者最核心的首先还是脑子要特别好使，能在众多信息中围绕目标找出重点而不靠使蛮劲、砸资源、堆事情，也就是第一点----基于战略，做好关键性的工作事项选择和创新提升空间
- [[Family]]
- [[Myself]]
  id:: 64e432d0-6c8d-417c-98a2-5616efefa89d
	- 10:04
	  collapsed:: true
		- #质疑
			- 当你对一个人（[[王露]]）的成长不在负有责任时，你对她的[[包容]]也消失了。这很神奇。这也是[[常识]]吧。人如果不能在环境中适应并吸取营养（学习规则到驾驭规则、学习人性到驾驭人性），那她唯一能做的就是抱怨这个世界对她的不公。 -> ((64e2602e-90ca-481c-9683-b5fc623e47bb))
				- #link
					- ((64e2602e-2b9d-4399-9abf-167bbb58cfdb))
	- 11:51
	  collapsed:: true
		- #摘录
			- [[灵感易逝]]
				- 我们每个人都有想法。想法是不朽的。它们会永远存在
				- 不会永远存在的是灵感。灵感就像新鲜的水果或者牛奶:它有保质期。
				- 如果你想做什么，现在就去做。你不能把它搁在架子上等两个月再抽时间去做。你不能说稍后再做。稍后你就没那么激动了。
				- 如果你在星期五时灵感来了，就下决心在周末就投入这个项目。如果你灵感涌动，两个星期的工作你只要 24 小时就能完成。灵感此时成了时间机器。
				- 灵感是神奇的东西，是生产力的倍增器，激励因素。但是它不会等你。灵感是现在的事。如果灵感找上你了，你也要抓住它，然后投入工作。
				- #截图
					- ![WechatIMG922.jpg](../assets/WechatIMG922_1692676386517_0.jpg)
	- 12:36
	  collapsed:: true
		- #摘录
		  collapsed:: true
			- [[smart-search]] 使用说明
				- 编辑时，用默认快捷键 `ctrl+space` 呼出，可在设置中更改。
				- `esc` 退出输入框。
				- 可按照标签搜索内容。格式为 `#tag`，`##tag` 如要包含它的子孙块以及 `#>tag` 如要包含它的子块。
				- 可搜索不包含指定标签的内容，用于组合搜索，见下方。格式为 `#!tag`。
				- 键入部分 tag 名称可弹出对 tag 名的补完。
				- 可通过 `>tag` 语法筛选出标签候选，以便在后续查询中使用。
				- 可搜索拥有某属性的内容。格式为 `@property`。
				- 可搜索不拥有某属性的内容。格式为 `@!property`。
				- 可按照属性值搜索内容，也可按部分内容搜索。格式为 `@property: value`。
				- 可搜索不包含指定值的属性。格式为 `@!property: value`。
				- 可按照数字属性值搜索内容。格式为 `@property >value`，操作符有 `>` `<` `=` `<=` `>=`。
				- 可按照日期属性值搜索内容。格式为 `@property~ -1w~d`，支持的单位有 `d` `w` `m` `y`，分别是天、周、月和年；`~` 所代表的时间段为可选。也可使用 `yyyyMMdd` 这样的绝对日期，例如 `20230131~d`。
				- 可搜索各种状态的任务。格式为 `[]nltidwc`。`n`=`NOW`, `l`=`LATER`, `t`=`TODO`, `i`=`DOING`, `d`=`DONE`, `w`=`WAITING`, `c`=`CANCELED`.
				- 可搜索日记块。格式为 `%j -1w~d`，支持的单位有 `d` `w` `m` `y`，分别是天、周、月和年；`~` 所代表的时间段为可选。也可使用 `yyyyMMdd` 这样的绝对日期，例如 `20230131~d`。
				- 可全文检索。例如：`learning note`。
				- 可任意组合文字、标签、属性、任务以及日记搜索内容，以 `,` 分割。格式为 `#Book, @published: 2022`。
				- 可在最后加 `;` 进一步过滤查询结果。例如：`#book; holmes`。
				- 能识别中文标点，不用刻意切换到英文。
				- 支持键盘上下键选择或鼠标点击。
				- 正常选择（回车或鼠标点击）插入引用，按住 `cmd` 或 `ctrl` 插入嵌入，按住 `cmd+shift` 或 `ctrl+shift` 插入子级嵌入（需要 Another Embed 插件提供嵌入子级的支持），按住 `opt` 或 `alt` 插入文字内容。
				- 按住 `shift` 选择跳转到块或页面，按住 `shift+alt` 选择在右侧边栏打开块或页面。
				- 支持拼音搜索。
				- ## 时间段示例
					- # 本周需完成的任务
					- %j w, []nl
					- # 上周的任务
					- %j -1w, []nld
					- # 下周的待办任务
					- %j +1w, []l
					- # 一年前的Logseq日记
					- %j -1y, logseq
					- # 两年前至今的Logseq日记
					- %j -2y~d, logseq
					- # 某一天的Logseq日记
					- %j 20230210, logseq
					- # 某一段时间内的Logseq日记
					- %j 20221231~20230210, logseq
					  id:: 52fa3461-492f-444d-928f-0ac136401f7f