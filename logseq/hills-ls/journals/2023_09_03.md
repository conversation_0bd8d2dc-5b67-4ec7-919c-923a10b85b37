- **[[Morning-Page]]**
- [[Society]]
- [[Family]]
- [[Myself]]
	- 10:54
	  collapsed:: true
		- #摘录
		  collapsed:: true
			- 有了[[牵挂]]，[[猛虎自囚于牢笼]] -> ((64f35a3d-0739-4b88-9ce4-5382d2233a8b)) #超能异族
	- 17:48
	  collapsed:: true
		- #闪念
		  collapsed:: true
			- DONE 妈的，买个手机，居然被安利了[[联通]]换[[电信]]，还要去中国联通取消[[副号]] #小米13 
			  id:: 64f4a50b-2d6c-4171-b261-e8dbf543f6d7
			  SCHEDULED: <2023-09-10 Sun .+1d>
	- 18:49
	  collapsed:: true
		- #摘录
		  collapsed:: true
			- TODO 其实我的PIM和PKM的[[工作流程]]基本在这两年没怎么动过，也就是三四年前小折腾了下，随大流的玩点插件啊之类的。基本玩半年后也就算了，因为这些跟「产出/输出」没有本质的关系。2023年还有3个多月就过完了，提前在梳理2023年度工具论（此文会贴在《PPS（Personal- Productivity Systems）个人生产力系统》 内），给各位同志做个参考。
			  collapsed:: true
				- 👨‍🌾再次说明：
				- 1. 工具≠ 效率 ，有些工具看似帮扶，实际上在减少自身「对脑力和认知的提升」
				- 2. 所谓效率 = 重复流程的优化，以便快捷提效，单一提效=单功能熟悉，组合提效=PMO流程化管理
				- 3. PIM = 提升信息的质量，等于第一层 阶段/颗粒度 的筛选，属于「提升认知」第一层
				- 4. PKM = 在信息有质量的前提，进行整理，等于第二层 阶段/颗粒度 的筛选，属于「提升认知」第二层
				- 5. 合理且正确的PIM+PKM = 输出，输出即「认知精华」，属于「提升认知」第三层
				- 6. 「认知精华」是需要不断的提升和提炼的，所以需要利用工具来进行整合优化，此提升/提炼的过程中，同样可分不同阶段/颗粒度，这取决于自身的「流程化管理」是否顺畅，是否合理。
				- 7. 大多数/部分 内容/信息/输出 均都不需要刻意整理，尤其是在第一层的时候。只需要在想找到的时候，能快速找到就行。在第一层整理，属于浪费时间且无效果。精华都在第三层，第二层都算不上！
				- 👨‍🌾正文开始（我会从我自身「需求角度」和「[[工作流程]]」的组合形态）
				- I. 第一层 阶段/颗粒度
				- 1. 提过N次，【Telegrambot + Logseq 】碎片化无敌流。需要把Telegram作为主力软件使用，频次甚至高于wechat 。不论通过什么媒介看到的信息/内容 ，经过短暂思考后觉得有可「利用价值」或想说点什么，均用此组合，碎片化输出占比80%。
				- >优点：快速便捷 ；缺点：必须在1-3天内在PC上打开Logseq同步一次。
				- 2. PC（macOS）碎片化输出或临时记录，要么直接在Telegram对话框内直接操作，或利用【Raycast】中Logseq插件【quick note】组件直接记录（此状态均在未打开Logseq软件）
				- > 三方场景，在Logseq中，均设置的可搜索词组为：[[quick capture
				- ]] ,好处：三种行为下标记均统一，遇到特别情况可再次打需求tags或双链
				- 3. 无PC无网络状态下，Apple Watch 中安装【Minimal】或【bear/熊掌记】，在手表上「快捷语音输出」。
				- II . 第二层 阶段/颗粒度
				- 在第一层中提到过，三方场景/行为 均为[[quick capture]] ，每3天或7天，自我整理进行提炼。提炼后的内容，均 导出/复制 在Obsidian 中。
				- Obsidian设置：
				- 1. quick capture文件夹+quick capture双链+quick capture模版（EvergreenNotes A）
				- 2. EvergreenNotes B 为第二层阶段/颗粒度 输出，均通过EvergreenNotes A 得来。
				- III . 第三层 阶段/颗粒度
				- Obsidian中EvergreenNotes B 整合后输出的结果均 导出/复制 到【DEVONthink】做最终的文件（内容/信息）归档，属于自身「真正的知识或认知」，属于常看常新，或许某天突发奇想又会进阶（提升/提炼），如有这种情况可单独再次建立EvergreenNotes C，最后在归档。
				- 👨‍🌾要点是，每次修改文档一定要打出「最后修改文档的时间戳」，看看自身认知变化的过程，看看自己是如何提升的。
				- IV . 档案归档管理
				- 从文档最终归档和认知精华提炼来讲，DEVONthink这个软件够了。如果不想花钱用【Zotero】代替也可以。当然你用破解的DEVONthink 同样可以，可能就时不时会有Bug。虽然我说DEVONthink很好，但是我不建议你先去找这个软件看这个软件，因为你还没有把第一层和第二层搞明白，你直接到第三层，可能么？有必须么？
				- 不要总做这些没意义，浪费时间的事情。很多事情都是需要进阶的，如果自身能力不足，要“认”。“认”的同时就要先把基础搞明白后在去「进阶」。
				- V . 图书阅读及管理流程
				- 只有【Calibre】又免费又好用，我们会经常下载一些电子书，不论是PDF还是ePub，用什么看？怎么看？如何整理？在此，我在简单介绍下我的流程。
				- 我很早就说过，非专业学科的书籍均为闲书，且不论书籍属于个人成长、金融管理、经济理财、思维管理、科学科技、心理、文学、艺术、哲学宗教、政治军事等等，均为「闲书」。
				- 为什么？因为这些说，讲好听点并不能让你增长「硬技能」，这些都是「软技能」，所谓属于「闲书」。
				- 什么是「硬技能」？比如，如何做好川菜？如何做西式糕点？诸如此类，能让你真正的得到一项可以「生存的技能」，才叫「硬技能」。
				- 看闲书（软技能书籍）的方式方法，我讲过很多次，具体在【置顶信息 - 读】中查看。下面就介绍看一本闲书的流程：
				- 1. 首先用简单工具大致翻翻，看看有没有自己的「看点」，可用Readwise、Omnivore或其他均可（要点是，要与第一阶段使用的工具可以同步简单划线和阅读理解的内容），无非是「简单划线和阅读理解」步骤，这里属于PIM 中 第一层 阶段/颗粒度 ，也就是刚才提到的【I. 第一层 阶段/颗粒度 】
				- 2. 「简单划线和阅读理解」后继续 II-> III 流程，在IV中，由DEVONthink或Zotero 换成 Calibre
				- 👨‍🌾这就是看明白一本书的完整 流程/过程 。
				- VI . 一些提示及相关 软件/插件 介绍
				- 以上流程我相信都能看明白？如果你能真的看明白，你的脑子中就应该马上浮现出来一副图形，把其中的关联性都连接起来。
				- 如果你浮现不出来，说明你的【框架性】、【结构性】、【逻辑性】和【系统性思考】均很差劲，不客气的讲是非常的差劲！说明你没有【思维管理】，甚至是没有「认知思维」。没有前面这4点，你怎么会有【创造性思维】呢？都没有你怎么才能达到认知突破、异于常人呢？你怎么才能到那20%的少数派呢？而不是那些买盐的80%呢？
				- 看似简单的东西，往往差距就在这。差距不是一次性形成的，而是慢慢的积累的。
				- 1. Obsidian
				- 其实现在用的插件不多，Dataview也用的很少。因为流程内的归属性不同，所以官方的基本够用，大多数还用不到。更多的用的有loat search 和better search view，这两个插件几乎不需要做什么操作，只是视觉上的优化。
				- 而且Obsidian嵌入了 Query 语法，已经上线2年多了，一些简单的处理是没有问题的。
				- 且不论你是不是跟我的流程类似，还是主力用Obsidian，内容过多后更多的需求其实是【搜索/查找】，要不然你堆积过多怎么找呢？只需要在想找到的时候，能快速找到就行。所以我现在很多笔记的标题就是一些关键词组合或者问句。
				- 因为在第一层中属于简单理解和表达，而在第二层Obsidian中，更多的是「梳理+整理」，后面才是二次提炼。如果你真的理解了这个概念，根本不用记下来，没有理解的话，哪怕用自己的话记录也是欺骗自己。这种行为不能出现在【第二层】，如果有也要出现在【第一层】。
				- 2. Zotero
				- Zotero 6 目前比较稳定，Zotero 7 beta也出来很久了。看自己需求，不论是用Win还是macOS，均可以2个版本独立存在。
				- 这里重点说的是7，对底层代码进行了特别大的升级、支持 64 位Win、升级版阅读器，目前7除了支持打开阅读 PDF 文件之外，现在还额外支持了 EPUB 和 HTML 阅读和划线标注（对于后续还会不会支持更多格式，目前还不清楚，不过有这三种应该能满足大部分需求了）。
				- 不过，由于 7 现在多支持了两种格式的文件和多种标注，所以如果用 7 划线标注了，那么就没法切换回 6 了，包括一些插件也并不支持7 。所以在选用版本上，尽量做一下取舍和功能流程化界定。
				- 在个人PPS（Personal- Productivity Systems）系统中，我选用配套软件最多的就是Zotero和Omnivore，与Logseq和Obsidian均可集成适配，非常的方便。
				- 3. 无能的笔记圈
				- PKM从海外流行至国内也有几年了，逼逼赖赖的破产品也出来很多。不管新老人均爱讨论的一个问题就是：为什么要做笔记？
				- 其实最应该 回顾/问 下自己：
				- A以前做不做笔记？
				- B现在做笔记为了什么？
				- C以前做过的笔记自己还记得吗？有提炼吗？有提升吗？
				- 实际情况是：很多笔记是在骗自己，如果当时没有理解记下来的东西，事后也不会想起来自己记过这个东西。而真正理解了，好像也没有记下来的必要了，这算不算是一个笔记的悖论呢？
				- 包括讨论「双链」一样，有意思吗？这么简单的东西搞的那么玄幻，是不是脑子出什么问题了？
				- 现代笔记系统无非就是形式和以前不一样了，遇到的时候需要用什么关键词快速找到答案，建立起一个个锚点。所以「搜索技能和[[知识管理]]软件的搜索体验」很重要，当然这个是建立在你有自己完整的[[工作流]]，如我一样。
				- 如果你没有？
				- 继续回归到大段大段的复制粘贴，「利用工具」更快更好的大段大段的复制粘贴。
				- 👨‍🌾 所以折腾无意义、纠结过多方法论更没意义、讨论为什么要做笔记没意义、讨论双链更没意义，有些同志们是要应该好好想想了，缺哪里补哪里才是正途。
				- 你看我废话这么久，无非就在Telegram输入框内完成的。哎，输出就是这么简单，虽然只属于「第一层 阶段/颗粒度 」。
			- TODO 新时代的营销其实很简单，真的，都是90年代我们玩剩下的，换了个皮肤。由于新时代新青年们，虽然接触比当时的我们更多，但是也更杂乱。那么这个事情简单来说，甚至用互联网行话来说，这叫：「连接对话/社交」
			  collapsed:: true
				- 怎么个正向的解释呢？比如：新智能是场景对话，新下沉是即时对话，新审美就是观念对话，新社群是微观对话、新效率是创造力对话。
				- 再具体点呢？
				- 1. 从「发现你自己」已经进入「发明你自己」的阶段。我们应该像GPT一样更加精确提示词，学习建设「对话界面」：理解面对什么样的品牌、什么样的创新者、什么样的用户。
				- 2. 新商业、新零售、会员制仓储超市、社交拼团、DTC 品牌、自动驾驶订阅、内容平台订阅等等……这些新模式都在指向一个结论，新社群商业的本质是「真实关系」的集合。
				- 3. 商业一般通过「发现问题」和「解决问题」创造财富。过去，社会中的问题很多，这就意味着决定商业规模的瓶颈在于「解决问题」这一环节。但是，这种瓶颈关系在AI时代正在颠倒，即变成了「问题稀少」而「解决能力过剩」。
				- 再放大点？
				- 不提苏格拉底以启发式谈话而得名的「知识的助产术」；而熊彼特「开门理论」：如果你向我提一个问题，我给了你答案，划了句号，相当于我给你关了门。
				- 比如狼斯克早在12岁时开始追问人生的意义，直到从《银河系漫游指南》中感悟出：任何的Answer（答案）都是荒诞的，只有Answer（答案）背后所蕴含的那个问题，才是有意义的。
				- 既然是「对话」那就倒推一下，分别是：提问->情绪 ，所以废话这么多是为了铺垫「情绪价值」。
				- 以前的营销呢，搞一个定位，提一个概念，吹一种生活方式，造一个网红，消费者蜂拥而至。今天呢？没有「情绪」很难吸引人群。具体表现为：
				- 1. 淄博烧烤、特种兵旅游、村BA村超的逻辑是人间烟火中的「情绪的远征」
				- 2. 搭子、City walk、多巴胺是基于陪伴与对话能力的人「情绪的共鸣」
				- 3. APP攻克附近、街边咖啡/小酒馆、围炉烤茶则表现为「情绪的陪伴」
				- 👨‍🌾所以你们看，所有这些现象的起点，都可以归结为「情绪」，跳出了的阶层/产品/圈层/兴趣组，而是某种「情绪」。这也就是为什么新时代的消费者/用户 只要你把握住「情绪」，他们就会买单。顺便提一下，某政党在内宣一直管用的手段/策略 同样如此，比比皆是，百年内从来没有变过，具体自己好好对照下。
				- 当然，在对立志于在当下这个不安定不确定复杂世界中有所作为的品牌而言，谁能把握「情绪」，谁就掌握了新赛道的主动权；谁懂得通过「对话」释放「情绪价值」，谁就能与更多用户建立起紧密相依互为信任的长效联结；谁能在「对话」中寻找到自己和用户的「意义」，谁就可能获得品牌持续增长的新动能，迈向未来的新起点。
				- 从某种意义来说，与用户沟通（品牌营销）从来没有如此艰难又简单过。
				- 其实从营销（市场营销）本质来讲，总共就分三部分，分别是：
				- 1. 互联网思维：增长营销、全域营销、直播营销、无界营销
				- 2. 商业思维：叙事营销、体验营销、情绪营销、交付营销
				- 3. 客户思维：科技营销、大健康营销
				- 这里面没一部分都能讲出很多东西，本文也是情绪营销的一部分。这三部分总结下来同样还有三个本质，分别是：
				- 1. 互联网思维=品牌网感化和渠道网络化
				- 2. 商业思维=客户注意力和体感、口碑传播的强化
				- 3. 客户思维=客户认知成本相对比较低的教育路径
				- PS. 今日废量不大， #MKT  下内容较少，所以补一点，今天结束
			- TODO 以前做品牌，喜欢打造个人设。做市场做运营同样如此，哪怕你电商平台卖个真货假货都要人设，更不要提那些主播KOB们了。
			  collapsed:: true
				- 「人设」犹如王者荣耀皮肤，再怎么换，加点就这么多。做生意要人设，去酒吧的男女老少们同样需要，举例来说：
				- 【玩擦边的黑s学霸】
				- 喜欢在短视频发擦边视频，借机吸粉，并设置加微信的门槛，比如：一个飞机加V等等。
				- 你以为她们只是普普通通的混圈女，但打开短视频上的简介，你会惊奇的发现她们对外的人设是：本科清北[[or]]从小在国外上学，连续获得国家级奖学金，研究生拿到了世界顶级院校的offer，漂亮且独立的象牙塔尖上的学霸。
				- 显然，这些有99.99999%都是编的，但总会有人相信。
				- 她们这么做一是：为了接触到优秀的异性；二是：杜绝一些下头男的骚扰；三是：通过自己的假人设更换自己的圈子。
				- 之所以要冒用学历，也是没办法的事情，「毕竟大家出来玩，家庭背景、学历、工作都得凭本事自己给」
				- 【没打过碟的学术型DJ】
				- 之所以称她们是搞学术的DJ，是因为她们真的从来没打过碟。因为厌倦国内电音圈环境，加上怀才不遇、同行排斥，最后选择退隐江湖，跳出了这个圈子。
				- 之所以选择这个IP，是因为这样既能表现自己跟随头部电音潮流，还能吸引有钱大哥、富二代的注意力。
				- 【能屈能伸的天菜妹妹】
				- 天菜就厉害啦，顺风天菜，逆风接单，绝境bbs。
				- 如果你现在看半年前，看这些夜店女孩的朋友圈，你一定会感到纳闷，因为在你记忆中，她只是个攒局的不入流营销。
				- 现在看的话，她们摇身一变，成为了国外海龟的高质量天菜，或者是下沉市场成为了一名正式的绿色接单员。
				- 她们身份切换这么频繁，也是为了适应经济环境变化，在这样的背景之下，她们也宛如变色龙一般，通过不停转换自己的ip，赚到足够的钱。
				- 【反差藏宝式钓鱼】
				- 大多数男人，没法拒绝反差。
				- 不论是妆容还是穿搭风格，都表现出一种生人勿近的高冷气质。这种高冷范儿会劝退一大帮条件一般且没呢么自信的选手，当然，也会吸引一些有竞争欲望和征服欲望的实力派别选手。
				- 而接触后发现她们却又表现的很放得开，这种隐藏自己的能力成了她们的核心竞争力。
				- 之所以利用这种藏宝式ip，是因为它能够给对方一种在探索之后的获得感和征服感，故意让对方心理博弈的胜利，从而让傻逼们爱不释手。
				- 所以，「反差」成了她们钓鱼的进阶技能。
				- 🧑‍🌾你说这四种算诈骗吗？的确算不上，因为真正的诈骗用的都是高科技，传统三件套（短信电话等）这种还能受骗的，的确不配称呼为“傻逼”，因为连“傻逼”都不如。而那现在的高科技都是用AI＋大数据匹配到你这种傻逼的。
				- 分不清？
				- 传统三件套属于「主动式」
				- 现在的高科技属于「被动式」
				- 可怜的就是那些无知的男人或女人，被简简单单套牢住。你说，他们是傻呢？还是蠢？还是脑子里缺点什么？或是身残智也残？
				- 哎本来想写点正经东西，居然写成了「人设营销」的反诈教程。
				- 哎下次有空提下「爱国营销」诈骗案例吧
	- 20:20
	  collapsed:: true
		- #闪念
		  collapsed:: true
			- 换了新手机后，所有旧消息都没了，也挺好， [level3]([[level-3]]) 轻松开始 [*](((64f4a50b-2d6c-4171-b261-e8dbf543f6d7)))
			- DONE 复盘可以和ending组成namespace，output也是
			  id:: 64f4a60d-37c2-4cab-90de-59555ce2ff64
	- 23:04
	  collapsed:: true
		- #摘录
		  collapsed:: true
			- TODO 「积累」分几个层次：
			  collapsed:: true
				- 1. 可积累
				- 2. 可持续积累
				- 3. 更快的持续积累
				- 4. 很难被破坏的，更快的持续积累
				- 这里面最重要的是很难被破坏，其次是可以持续，第三才是更快。一旦引入“赛道”这个概念，而这个“赛道”是赢者通吃的话，就大概率无法积累，甚至把之前积累的资源全部赔进去。最好的赛道是只有你一个人能定义和参与的游戏。
				- “可持续积累”是指：你积累的资源具有极好的扩展性，以至于几十年前你掌握的某种能力，资源，等等，现在仍然可以被利用，产生效益。
				- “更快的积累”是指：需要一种越来越广阔的全球化视野，看得越多越远，自然可做取舍，不断引入增速更快的业务淘汰掉增速缓慢的业务。
				- “很难被破坏”是指：具有抽象上的“易守难攻”的特质，不至于被外人轻松的把你多年的积累摧毁。 © 王川
				- 👨‍🌾 「积累」这个话题，的确能展开很多，“读写说”属于“可持续积累”；“教”属于“更快的积累”；而“思维和认知”则是“很难被破坏”。说白了，「积累」只能存在于“可持续积累”+“更快的积累”，而「复利」属于“很难被破坏”。老话讲：“有大略者，不可责以捷巧，有小智者，不可任以大功。” ，同样的意思。
			- TODO 如何「有效写作」的十条建议
			  collapsed:: true
				- 一、最优秀的写作不是目标，而是结果。
				- 1. 每日坚持写作
				- 2. 长时间散步,寻找灵感
				- 3. 屏蔽互联网,避免干扰
				- 4. 随时随地记录灵感
				- 5. 阅读高质量的内容来源
				- 6. 不看手机，专注写作
				- 👨‍🌾建立一个让写作灵感的创作系统。
				- 二、永远不会有一个完美的写作起点/主题
				- “一旦......然后我就开始写作”这是拖延的终极形式。
				- 1.  一旦工作完成
				- 2. 一旦我觉得自己准备好了
				- 3. 一旦这个项目结束
				- 👨‍🌾“一旦”总会存在，所以从今天、从现在、马上开始。
				- 三、写作时，不要试图给写很大的主题/话题或目标为了给所有人写
				- 相反，需要明确并瞄准你的一个目标、一个话题或一名受众。比如，我是：给/为 我自己写
				- 1. 使用具体的例子
				- 2. 解决一个具体的问题
				- 3. 揭示/提示 一个具体的好处
				- 4. 激发一个具体的情感
				- 5. 鼓励一个具体的行动
				- 👨‍🌾具体胜于抽象，宏伟需要抽象，而你现在需要具体/具象。
				- 四、不要再把它看作“写作”
				- 相反，可以把你的写作看成一次创业项目或一个项目管理。
				- 1. 发布一个最小可行产品（MVP）
				- 2. 与自己/受众（不论线上线下） 交流
				- 3. 优先考虑分发/传播 媒介
				- 4. 解决具体问题
				- 5. 关注读者体验
				- 6. 说一些不同的主题/话题，而不是所谓“更好的东西”
				- 👨‍🌾好了，你现在是一个CEO了。
				- 五、所谓文案很简单
				- 1. 指出一个具体的问题
				- 2. 指出解决这个问题的具体好处
				- 3. 表明你的产品是显而易见的解决方案
				- 👨‍🌾如果你的广告/传播 内容，不属于这3大类的任何1类，那就删掉它。
				- 六、初学应该学会写这两类事物
				- 1. 现在对你来说显而易见，但两年前啥也不是的东西
				- 2. 现在让你困惑，但两年内你想要更好地理解的东西
				- 👨‍🌾这是一个最简单的框架，可以在建立可信度的同时，持续学习“写”。如果你写一个主流/潮流，甚至自己都一无所知、半知半解的话，你会写不下去，甚至更容易放弃。
				- 七、写作和编辑是不同的过程，更是不同的职业
				- 1. “写作”全部是添加：将你脑海中的原材料/素材/想法 抽取出来，放到页面上。在这个阶段，忘记退格键。
				- 2. “编辑”全是删减：在这个阶段，学会用“退格键”，去删掉其他多余的东西。
				- 八、你不需要等是专家的时候，才能写某个主题/话题
				- 现实是，我们很难从专家那里学习(因为专家领先我们太远)。 从，比你领先一步的人那里学习更容易。所以开始学习新东西，并为那些与你同在学习道路上的人一起进行浓缩、提炼和总结。
				- 九、如果你想保持写作的连贯性，你需要随处捕捉灵感
				- 1. 学会在手机上快速记录、或语音记录
				- 2. 在淋浴间张贴白板，洗澡的时候有时候想法会突然出现
				- 3. 在各处放笔记本或者你能“写”的工具
				- 👨‍🌾一旦你的大脑相信你可以在任何地方获取灵感，它就可以自由地产生创造力。工具永远都是辅助的载体，便于收集、统计或增加效率收集和统计。
				- 十、如果你的目标是加速个人成长，写作是最有效的工具/方法
				- 1. 可以更清晰的思考，理清逻辑
				- 2. 因为线上线下的交流，更容易结交新朋友，或重新认知“朋友”
				- 3. 学会每日坚持，雷打不动
				- 4. 学习新话题、巩固新灵感
				- 5. 提高框架性、结构性的思维意识
				- 6. 可以广泛应用的到各种职场/生活 场景，因为“写”只是一个基础技能
				- 📌最后，写作需要坚持、清晰，不需要自以为是和自作聪明。你想要“大聪明”的尝试只会导致自我/受众 困惑。你或受众一旦感到困惑，就会回到各种短视频或网络剧集中。
			- TODO 今天在404群组插嘴多说几句，其中提到一个关键词「自我管理」。提到这个词儿，就不得不提彼得·德鲁克的《管理自我（Managing Oneself）》。
			  collapsed:: true
				- 本文首次发表于《哈佛商业评论》1999年3-4月刊，节选自彼得·德鲁克的著作《21世纪的管理挑战》（Management Challenges for the 21st Century，Harper Collins出版社，1999）。本文被称为《哈佛商业评论》创刊以来重印次数最多的文章之一。也是《哈佛商业评论》出版的《自我管理的十大必读》当中的第一篇，同时也是最受欢迎的文章之一。文中彼得·德鲁克提出了七大问题，并自问自答。
				- 作为自我探索的重要一步，希望各位同志真的花1小时的时间好好作答一下。七个问题也是分不同年纪、甚至是阶段的。建议如下：
				- 1.  18-25岁回答前5条
				- 2.  25-40全部回答
				- 每个人状态、阶段均不同，所以以我为例，作答前五个作为各位的参考。当然全部回答也可以，但是没必要，这玩意我早就做过，我就是纯为了打点字而已，不写点难受。
				- 一、What Are My Strengths？（我的长处是什么？）
				- 真正的长处是「吃喝玩乐」，并及其专业。因为吃喝玩乐的专业，导致了第二长处就是「扯淡」，正经一点说就是：「布道」。我把布道理解为「因为喜欢和认可而持续推广某种事物的行为」。比如我喜欢“写”，并从中受益，我会持续地推广“写”。我喜欢价值投资，并且从中获得回报，我会坚持推荐价值投资。这也是本频道一直在“写”的真正的长期主义 = 「人生决策」系统 + 「价值投资」系统。
				- 事实上，在我过去二十多年的职业生涯中，我换过七八家公司，做过十余种不同的工作（职业）和角色，包含零售业销售、金融市场渠道、互联网市场策划、文案策划、互联网运营、品牌策划、公共关系、讲师、产品、组织发展、战略策略、互联网创业等等。看似没有任何关联，但背后始终不变的是我持续地在「布道」。布（规划）自己的道（职业道理），甚至不断在邮件组、公司群体、客户群体持续推荐我所喜欢和认可的事物。因此获得新的工作机会，也因为这种特点而深深爱上了一些工作并赚到了钱。
				- 所以，扯淡（布道者）是我的身份，扯淡（布道）是我的特长。我所宣扬的，正是我所相信的，也是我所践行的，而且是行之有效、有结果（思维认知/金钱），更是我相信他人同样可以从中受益的东西。
				- 二、How Do I Perform？（我的工作方式是怎样的？）
				- 我的工作方式很简单，我需要自己探索、实践一些东西，找到我认可的事物，然后向更多人推广，使之被接纳，从自己相信的东西变成大家相信的东西，从最初的想法变成现实中的事物。为了让更多的人相信，我自己首先要相信。为了让更多的人改变，我需要自己先改变。一言以蔽之，我需要知行合一，以行践言。
				- 我们需要持续的变革，而我认为，推动公司改变、推动部门改变和推动自己改变，究其本质而言是同一件事。而且我们一定要从自己改变开始，推动部门改变，最后推动公司改变。如果我们自己不愿意改变，拿什么去推动他人改变？如果你不改变迟早是最早淘汰的那一个，跟年纪有关系吗？呵呵。
				- 三、What Are My Values？（我的价值观是什么？）
				- 频道做了要1年，其实早就反复的说过，那就是：知行合一，即心里想的，嘴上说的，手上做的保持一致。这件事听起来容易，但做起来很难。因为我们一般习惯于「严于律人，宽以待己」，而且我们认为是他人的问题，而不是自己的问题，期待他人改变，而不是自己改变。
				- 我不是圣人，我也有类似的问题。所以我会用「知行合一」不断去校正自己的行为，我争取做到只做我自己相信的事情，而坚决不做我自己不信的东西。
				- 「知行合一」不仅是价值观，也是方法论，是我认知迭代的方法论。每当我看到一个认可的方法、理念，我就会去生活工作中践行。而随着践行的深入，我对于这一方法或者理念的认知会更加深刻。“纸上得来终觉浅，绝知此事要躬行”，这句小学就学到的诗句直到现在依然对我们有用。
				- 「知行合一」也对我选择朋友，选择工作起到了关键的作用，我只会选择那些同样知行合一的人成为我的朋友，所以我真正意义上的朋友很少。我也只会选择那些同样知行合一的公司为我自己的工作环境，我希望能在这个工作环境中坦坦荡荡地做我自己，而不用为了迎合他人而做自己不相信、不愿意做的事情。
				- 因为持久的「知行合一」，我有能力、有决策的判断任何事物，所以在职场上，多数都在我在操这些职场老白兔们。大家根本就不在一个段位，跟我玩，就两个选择：一死、二如何死。
				- 这就是我「知行合一」的结果。
				- 四、Where Do I Belong？（我属于何处？）
				- 我对于公司的「成长性」很在意，因为我不喜欢简单的重复，也不喜欢一眼看到头的工作生活。我希望不断接触更新鲜的东西，不断尝试之前从未尝试过的工作内容，我希望不断刷新自己，不断成长。因此公司的成长性是我非常在意。
				- 我们大部分人的生命会归于琐碎，不会在这世间留下任何痕迹。如何在注定平凡的宿命中寻找一线生机，其实最好的办法是：find something bigger than yourself，即找到比我们个人更伟大的事业，成为它的一部分。一个人可以走得很快， 但一群人可以走得更远，前提是这群人都有共同的、远大的目标 —— 这是好公司的终极秘密。
				- 当然，要 达到/拥有 这些“底气”，你需要「知行合一和持续成长」，有了阶段性的结果后，才会 达到/拥有 这些“底气”。
				- 五、What Should I Contribute？（我该做出什么贡献？）
				- 我会扯淡，我能扯淡，而我所能做出的最大贡献，应该是尽可能大范围地深入推荐那些真正值得推广的理念，比如纯扯淡、有思维的扯淡、有道理的扯淡、知行合一的扯淡。直到这些理念也进入更多人的视野，被他们所接纳，逐步开始尝试，最终变成他们的一部分，对他们的人生产生实质意义的改变，正如这些理念在我的人生产生了价值一样。
				- 所以，决定我扯淡 多少/大小 的主要是以下的要素：
				- 1. 这些理念是否真正有价值，真正的行之有效？
				- 2. 这些理念对他人的影响能持续多久？
				- 3. 这些理念能影响多大范围的人？
				- 4. 这些理念能够在多深的程度上影响这些人？
				- 事实上，后面三条要素最早出现在2006-2008年左右，那时我还年少且傻逼的工作，尝试着去通过改变自己而获得新生。我在至少一个工作笔记本的第一页写下了如下公式：
				- 1. 个人影响力 = 影响多少人 x 影响他们多久 x 影响他们多深
				- 2. 公司影响力 = 个人影响力x N
				- 3. 成就 = 个人影响力+公司影响力
				- 👨‍🌾 以上，就是我的回答。同时，也期待看到你们的 「自我/真我 回答」。
				- PS.  我一直说，学会“问”很重要，但优先级最高的则是，先学会“问”自己。才能 学会/有效 的“问”他人。《德鲁克七问》如下：
				- 1. What Are My Strengths？
				- 2.How Do I Perform？
				- 3. What Are My Values？
				- 4. Where Do I Belong？
				- 5. What Should I Contribute？
				- 6. Responsibility For Relationships
				- 7. The Second Half Of Your Life
	- 23:28
	  collapsed:: true
		- #review
		  collapsed:: true
			- ((64f4a60d-37c2-4cab-90de-59555ce2ff64))
	- 23:52
	  collapsed:: true
		- #摘录
		  collapsed:: true
			- TODO 有同志反馈这几天的信息有意思，比如何为WBS，比如《德鲁克七问》。其实前两天的内容均有连贯性，就看各位老少爷们能不能贯穿连接了。今天就具体拆解在一一展开，小废一点。
			  collapsed:: true
				- 何为WBS？其实就是：工作分解结构（Work Breakdown Structure，简称WBS）跟因数分解是一个原理，就是把一个项目，按一定的原则分解，项目分解成任务，任务再分解成一项项工作，再把一项项工作分配到每个人的日常活动中，直到分解不下去为止。
				- > 即：项目 -> 任务 -> 工作 -> 日常活动
				- 但由于每个项目的独特性、在实际应用场景中仍然会遇到一些问题，所以梳理以下4个重点便于理解。
				- 一、明确颗粒度，责任细化到 人/事
				- WBS 的实质思想之一，是要体现在项目过程中「项目职责的落实和明确划分」。由于项目管理的自身特点，在项目规划阶段，我们很难盘点项目涉及的全部事项，对一些远期才能完成的成果，项目初期可能无法分解。即使是在一个理想环境下，工作分解过细，也会带来管理成本的无效耗费，资源使用效率低下，同时 WBS 各层级数据汇总困难，所以我们需要明确WBS的作用到底是什么？
				- 1. 分解自上而下，逐级进行分解。
				- 2. 对于小项目分解层级一般为4-6级就足够了，层级越多越不易于管理。
				- 3. 节点最终分解到一个人工作量以日为单位。
				- 4. 相同任务只能在WBS的一个节点上出现，不能出现工作重复的节点内容。
				- 5. 一个任务节点也只能一个人负责，其他人配合。
				- 6. 分解的任务节点，应该与实际工作情况一致，这样才能对项目进行指导。
				- 明确颗粒度，“责任到人”是项目管理的核心，在每一层次 WBS 分解过程中都考虑到项目责任划分和归属，尽可能每一个最底层的节点根据WBS分解结构的特点，来确定分解的任务节点以及唯一责任人（或部门）。
				- 👨‍🌾 我TM第一问：是不是感觉跟 PIM/ PKM 相似了？拆解、颗粒细分、构架层级并一一归属，笔记不是？所谓写作不是？做PPT不是？个人复盘/项目复盘不是？哪怕年中或年终总结汇报不是？呵呵PARA。
				- 二、项目差异化的合理应用
				- 在【实践】中，一个项目往往有多种分解方法，可以按照工作的流程、可交付成果分解，也可以在不同层级使用不同的方法，在一些大型复杂项目中，常常还涉及诸多的对外采购活动，合同中清单分解项目依赖具体的场景需求，有时候还会受到独立的行业标准和惯例约束。
				- 不同的分解方式侧重点不同、相互之间难以统一，造成了 WBS 方法在理论上容易理解但在具体场景下操作实施的难度。
				- > 👨‍🌾我TM第二问：这点在PIM/PKM 上不会体现吗？你学了再多的方法论，依旧没多少笔记，依旧不会做笔记的病根就在这。
				- 解决这一矛盾，首先要理解 WBS 方法的另一作用，是：「实现项目进度/成本控制的基础」。如果没有这个功能，WBS 在具体活动中没有任何特殊意义，只是一个工作备忘录。结合这一作用，可以考虑在应用 WBS 方法的时候，将其分为两个部分：
				- 1. 【上层部分】为大项工作分解结构，可以参考项目的高层级目标，将整个项目按级别划分若干大项和单项。大项分解可以参考项目的生命周期、各个阶段、各个里程碑控制点等原则划分。
				- 2. 【下层部分】为小项工作分解结构，部分划分也不一定严格遵循80小时等传统原则，尽可能有一个相对完整等交付成果即可，如果涉及对外合同，尽量让底层部分的分解层次位于合同清单项之上，避免混乱、也利于工作量和成本衡量。总之，要活学活用。
				- 那么结合【上下两层部分】，又要考虑到WBS的分解原则和因素，举例如下：
				- 【原则】
				- 1. 100%原则 - 100%覆盖项目的可交付物，每一层分解的子任务也要100%覆盖它的父级任务范畴；
				- 2. 元素互斥 - WBS结构中各个元素是相互独立不交叉的；
				- 3. 围绕产出 - 在列举VBS工作包时，要按照期望的产出物计划，而不能只是规划行动事件；
				- 4. 颗粒度 - 可执行、可交付、可分配、责任到人。
				- 5. 横向到边原则 - 任务分解不能出现漏项，不能包合不在项目范围内的任何产品或活动；
				- 6. 纵向到底原则 - 任务分解要足够细，最底层的任务活动可直接分配到个人去完成。
				- 【因素】
				- 1. 确定适当的WBS层次，最底层WBS的元素需要对应有形的交付物。
				- 2. 对WBS生命周期的考虑，需要考虑不同项目阶段的活动发展。
				- 3. 项目计划、绩效报告、整体变更控制、包括范围管理等。
				- 4. 资源计划和风险管理的需要。
				- 👨‍🌾我TM第三问：难道不应该做一份思维导图吗？然后换个皮肤套在PIM/PKM 上在来一份思维导图？
				- 三、具体 流程/步骤
				- WBS具体 流程/步骤 需要做到：
				- 1. 提出需求并进行需求确认： 所有项目参与人员，会议讨论需求，确认所有主要项目工作，确定项目工作分解的方式，同时识别和分析可交付成本及相关工作。
				- 2. 确定WBS的结构和编排方法，画出WBS的层次结构图并制作详细WBS：将项目细分，详细到可以对该工作包进行估算（成本和历时）、安排进度、做出预算、分配负责人员或组织单位； （采用自上而下逐层细化分解）
				- 3. 为WBS的组织部分指定和分配标示编码；在WBS中，要将各项工作由上层向下层用多为码编排，编码规则可以根据项目工作的数量、个人思维习惯等进行设定，但是要保证每项工作有唯一的编码，并可以体现出上下层工作之间的从属关系。
				- 4. WBS审查及更新：验证上述分解的准确性，并随着其他计划活动的进行，不断地对WBS更新或修正，直到覆盖所有工作。
				- 👨‍🌾我TM第四问：这跟PKM有什么不同？3和4点均为工作分解结构（WBS）的层次
				- 四、相关名词、优点及任务分解维度
				- 1. WBS相关名词
				- 【控制账户】是一个管理控制点，在该控制点上，把控范围、预算、实际成本和进度加以整合，测量绩效。
				- 【工作包】最底层次的组件，通常表达为可交付成果。工作包可以对相关活动进行归类，以便对工作进度进行估算，开展监督和控制。
				- 【规划包】规划包同样是WBS最低层次组件，位于控制账户之下，工作范围是已知的，但其包含的活动或者对应的工期和预算是当前未知的，需要随着项目的深入进一步了解。
				- 【WBS词典】用来描述各个工作部分，WBS词典通常包括工作包描述，进度日期、成本预算和人员分配等信息。
				- 【活动】活动是工作包（规划包）的组成部分，不属于WBS组件，活动经常被细分为任务。
				- 👨‍🌾我TM第五问：【控制账户】难道不能是MOC？【工作包】】难道不能是TOC？【规划包】难道不能是各个主题双链？【WBS词典】】难道不能是注脚或标注Comment？
				- 2. 分解结构优点、标准及方式
				- 这里我就简单描述了，自己继续拆。
				- 【优点】
				- a.能够为工作定义提供更有效的控制
				- b.把工作氣围的相应的工作包中，可以获得相应的授权
				- c.有利于限定风险，协同明确信息沟通的基础
				- 【标准】
				- a.所有项目版块全部定义清晰
				- b.包含临时里程碑和监控点
				- c.集成了所有的关键因素
				- d.逻辑上形成了一个大的项目活动
				- e.分解后的项目活动结构青晰
				- 【方式】
				- a.按产品的物理结构分解
				- b.按产品或项目的功能分解
				- c.按实施 过程/流程 分解
				- d.按项目的地域分布分解
				- e.按项目的各个目标分解
				- f.按部门分解
				- g.按职能分解
				- 3. 任务分解维度
				- a.按照产品物理结构
				- b.按照项目功能用途
				- c.按照项目实施过程
				- d.按照项目的组织地域分布
				- > 这里会跟【方式】雷同，其实并不重合，而是具体的更细一级别的拆解维度
				- 总之WBS对项目管理非常重要，是撬动时间、成本、质量和风险管理的基础，学会运用WBS可以帮助我们保证项目结构的系统性和完整性，使得项目的形象透明化，也为后续的工作奠定了基础，更是各部门沟通协同协调的手段。
				- WBS如何在进度管理、风险管理、团队管理等也需要每一个项目负责人自我挖掘提取适合属于自己的方式。
				- 👨‍🌾我TM第六问：这么看是不是PARA弱爆了？呵呵，我在提示下，任何【数字工具产品】的教程，一定不能看还是学生身份的，哪怕是个博士或者博士后，如果你坚持走学术派，甚至深挖深耕是可以。否则没有一点价值，虽然我这话不好听，现实就这样，因为一点用都没有。甚至说一点「利用价值」都无。因为什么？谁靠TM天天纯理论，没点实战？呵呵，你以为记笔记，甚至写作就不是实战么？那看了这么多方法论，看了这么多教程后，有TM多少笔记呢？假如量有了，有质变吗？理论质变吗？俗称脑补幻想？
				- 📌下面我讲如何复盘，你们自己思考下，是不是能套进去。