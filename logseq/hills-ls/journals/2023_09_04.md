- **[[Morning-Page]]**
- [[Society]]
  collapsed:: true
	- 10:39
	  collapsed:: true
		- #质疑
			- [雪儿]([[刘雪儿]])又不想要这样 -> ((64f4c12c-9bd8-4b7e-aecb-28135af6c048))
	- 11:09
	  collapsed:: true
		- #闪念
			- [供应链中心]([[教具商品研发组]])[[新人]]来了以后，可以组织小小的[[破冰]]：
				- 老司机各自介绍下
					- 职业经历
					- 来[[兴趣岛]]做了哪些事情
					- 目前正在做什么事
					- 兴趣和爱好
				- 新人介绍
					- 职业经理
					- 兴趣和爱好
					- 对公司、部门有什么疑问
				- #then
					- ((64eac700-85b1-4b5c-9045-4be9ab3e841c))
						- #step
							- DONE [[兴趣岛]][[业务流程]]、[[组织结构]] [白板]([[whiteboard]])
							  id:: 64f58384-52e8-4d21-8c7d-df10b62dab3c
							- DONE [白板]([[whiteboard]])：[供应链中心]([[教具商品研发组]])[[业务流程]]和[[生存模式]]
							  id:: 64f58384-9c39-48b7-9f26-d5cd547a44c3
	- 12:14
	  collapsed:: true
		- #continue
		  collapsed:: true
			- ((64f4c12c-261f-40e3-9799-abeff898f9be)) => 都[[大事化小]]了，随便聊聊吧，也看看[普惠]([[朱峻修]])想跟我聊点啥。
				- #recycle
					- ((64f4c12c-62aa-4fb7-9a83-e5e7c39b927d))
	- 12:30
	  id:: 64f57913-2c66-4e29-b9b8-4be823829144
	  collapsed:: true
		- #闪念
			- [教具电商]([[教具电商]])也可以加入公益，老人们都喜欢[[慈善]]
	- 13:05 - 13:08
	  collapsed:: true
		- #摘录
			- 源泉混混，不舍昼夜。盈科而后进，放乎四海。 #金句  [*](((63de2835-0a64-4208-8293-b263a5cf0872))) [*](((640ac9e0-662e-4bec-8105-24f3aeafedb2)))
				- [[欧阳开桂]]
					- 水永远要填满所有坑
	- 13:43
	  id:: 64f57913-6c48-498c-8e81-8e620e1fd429
	  collapsed:: true
		- #摘录
			- [杨舒]([[杨舒]])的[[新需求]]：[药皂](兴趣岛/第二事业部/供应链中心/产品规划/产品池/药皂) #需求管理
			- [[于淼]]
	- 15:22 - 15:29 *7min*
	  collapsed:: true
		- #摘录
			- by[贺帅]([[贺帅]])：[居家养生]([[居家]])[[用户旅程]]
				- #示意图
					- ![whiteboard_exported_image.png](../assets/whiteboard_exported_image_1693812523090_0.png)
	- 15:46
		- #continue
			- ((64eac700-85b1-4b5c-9045-4be9ab3e841c)) -> ((64f58384-52e8-4d21-8c7d-df10b62dab3c)) -> ((64f58384-9c39-48b7-9f26-d5cd547a44c3))
			  id:: 64f6ad5c-ebd4-48f3-b6bd-fe81bc191c9d
				- #and
					- ((64f58384-7e3c-4a1b-9b2c-eb172c7c793c))
					- ((64f58384-5300-4139-9bbe-e5fb6e53c80d))
		- #log
			- 16:46 - 16:47 *1min*
			  collapsed:: true
				- #摘录
					- [[兴趣岛知识地图]]
						- #海报
							- ![海报.jpeg](../assets/海报_1693817194846_0.jpeg)
							  id:: 64f59969-dd53-4c68-b549-4d187d75e137
			- 17:00 - 17:11 *11min*
			  collapsed:: true
				- #闪念
					- DONE [[王露]]叫我代替她去[[面试]]
			- 17:56
				- #continue
					- ((64f4c12c-261f-40e3-9799-abeff898f9be))
- [[Family]]
- [[Myself]]
  collapsed:: true
	- 00:12
	  collapsed:: true
		- #continue
			- ((64f4af32-db1a-4a2d-ae84-40ecfb3add86))
	- 01:08
	  collapsed:: true
		- #review
			- ((64f1c3f4-f1c5-41be-945e-8309be768757))
	- 01:16
	  collapsed:: true
		- #break
	- 09:29
	  collapsed:: true
		- #摘录
			- TODO 如何高效地进行项目复盘！
			  collapsed:: true
				- 任何工作、任何职务，都需要复盘。哪怕是最low（中式英语）的日报、周报、月报、季报、年报及各种月季年总结均是。
				- 你很讨厌写是不是？我相信讨厌写的人起码占80%。我则喜欢写，为什么？
				- 1. 持续训练“写”的能力
				- 2. 复盘，反向拆分、逆推后在成型
				- 3. 总结、总结、再总结，总结为了质变
				- 严格上来说，这3点同样可以再次优化，比如：
				- 1. 提高思考与反思能力
				- 2. 提高团队协作和沟通
				- 3. 改善工作质量
				- 4. 促进个人成长和发展
				- 一、复盘的步骤
				- 1. 收集信息与材料
				- 收集关于项目的所有信息和材料，包括参与者的角色和职责，过程中的关键节点和决策，以及最终的结果和反馈等。
				- 【项目文档】
				- > 收集项目文档，包括项目计划、进展报告、变更请求、问题清单、会议记录、工作日志等。这些信息可以帮助我们了解项目的全貌，回顾项目历程，同时也是判断项目成功与否的重要依据。
				- 【反馈调查】
				- > 组织项目成员和相关方参与反馈调查，了解他们对项目的评价，包括项目过程、成果质量、沟通协作、风险管理等方面。这有助于我们从不同角度了解项目的情况，同时也能发现潜在的问题和挑战。
				- 【专家意见】
				- > 邀请专家（朋友、大哥、贵人）参与项目复盘，结合他们的经验和知识，给出宝贵的建议和反馈。这有助于我们更客观地评估项目的表现，同时也能提供有效的改进方案。
				- 【收集成员意见】
				- > 收集项目组成员的经验总结，了解他们在项目中遇到的挑战、采用的解决方案以及成功的经验。这有助于我们发掘项目中的最佳实践，为今后的项目提供有价值的经验借鉴。
				- 👨‍🌾这些东西均是平时收集并积累，属于PIM。结合WBS的方法逆推属于《二、项目差异化的合理应用 - 2. 【下层部分】》
				- 2. 梳理流程和关键问题
				- 在收集完信息和材料后，对整个事件、项目或任务进行梳理和总结，明确其中的流程和关键问题，以及所涉及的各项资源和影响因素。在项目复盘中，采用以下几种方式来梳理流程和关键问题：
				- 【信息排序】
				- > 收集所有的材料和信息，包括项目文档、反馈调查、专家意见、会议记录和经验总结等。然后我会对这些信息进行分类整理，将相关的内容归类成一组，以便更好地理解项目复盘的主题。
				- 【发现关键问题】
				- > 在整理出项目复盘的目的后，会重点关注关键问题，并进一步梳理项目的流程。通过收集反馈、讨论和经验总结等信息，从中找出关键问题，包括项目过程、成果质量、沟通协作、风险管理等方面的问题，从而更好地发掘项目中存在的挑战和风险。
				- 【形成自我思维导图】
				- > 为了更好地梳理流程和关键问题，大多数人会使用思维导图工具。我一直在说少用这类工具，因为你用的越多，你的思维就会逐渐的缺乏逻辑性和结构性。虽然具象图可以更清晰地展现项目的整体框架和思路，同时也可以方便地加入各种信息和分析结果。但是这种要看项目的体量大小，可以慢慢的锻炼，小体量自己脑补，脑里形成结构框架。多锻炼后，大体量就自如了。
				- 【制定行动策略】
				- > 在梳理出项目的流程和关键问题之后，根据思维中的分析结果，制定相应的行动计划。行动计划中通常包括改进措施、责任人、时间表和监控措施等，这有助于更好地推进实施过程，确保改进方案能够得到有效地执行。
				- 3. 分析原因和结果
				- 在明确流程和关键问题后，对项目进行深入分析，找出其中的原因和结果，并分类汇总，以评估工作质量和效果。在项目复盘中，分析原因和结果是非常重要的一步。复盘中分析原因和结果的一般步骤为：
				- 【数据支撑】
				- > 根据之前收集的可用数据，包括项目进程记录、反馈调查、专家意见、会议记录和经验总结等。更好地了解项目的历史和发展趋势，从而更准确地分析原因和结果。数据是唯一的论据、依据。犹如高楼的地基！
				- 【归纳总结】
				- > 在收集到数据后，对数据进行梳理和分类，归纳总结出项目的关键问题和成功或失败的结果。考虑成本、时间、资源使用情况以及项目达成的目标等方面的因素。
				- 【分析原因和结果】
				- > 根据所制定的原因和结果图表，应该深入分析项目中存在的问题、成果质量、沟通协作、风险管理等方面的原因，找到这些问题发生的具体原因。同时，分析项目的结果，包括成功和失败的原因，以及这些结果对项目的长期影响。
				- 【制定改进方案】
				- > 在深入分析原因和结果之后，根据分析结果制定相应的改进方案。这些方案通常包括具体的措施、目标、时间表和监控措施等，以便为项目的下一步发展提供有效的支持和建议。
				- 4. 总结经验教训
				- 在分析完原因和结果后，总结发现的经验教训，指出成功的因素和失败的原因，找出不足之处和改进方向，以便下一次工作中避免重复错误，提高工作水平。在复盘中，总结经验教训是非常重要的一环。总结经验教训的一般步骤为：
				- 【根据数据提取经验】
				- > 收集所有可用的数据，包括项目进程记录、反馈调查、专家意见、会议记录和经验总结等。这些数据可以帮助我更好地了解项目的历史和发展趋势，从而更准确地总结出经验教训。
				- 【总结归纳】
				- > 在收集到数据后，对数据进行梳理和分类，归纳总结出项目中的优点和不足之处，提取出「有价值且有可执行性的经验教训」。
				- 【分析经验教训】
				- > 持续拆解，根据所制定的总结经验教训图表，深入分析项目中的优点和不足之处，找出这些问题的根本原因，寻找可以改进的方案。
				- 【总结经验教训】
				- 在深入分析经验教训之后，对项目的成功经验和不足之处进行总结。对于项目中的成功经验，总结其特点和原因，并探索如何将这些成功经验应用于未来的项目中；对于项目中的不足之处，总结出问题的根本原因，并提出相应的解决方案，以避免这些问题在未来的项目中再次出现。
				- 所以在进行复盘时，一定需要准备充分并认真对待，并邀请相关人员参与，包括直接参与该事件、项目或任务的人员、负责该领域的专家和领导等，以收集更多的信息和意见，并达成共识。同时，复盘的结果应及时通报给团队成员和管理层，以便他们对复盘结果提出意见和建议，推动改进措施的落实。
				- 👨‍🌾我TM再问：工作（项目/汇报/总结）复盘是如此，PKM不能这样吗？锻炼思维不是这样吗？写作不是这样吗？不都是这样？底层最基础的都做不好，你凭什么其他高级更高阶的就能做的好做对？
				- 📌那我TM的通过「WBS + 逆向复盘」，在给你来个《创业公司如何考虑商业模式》如何？
			- TODO 创业公司如何考虑商业模式？
			  collapsed:: true
				- 任何「[[商业模式]]」都是动态的，但是也有底层的框架逻辑。那就是亚历山大·奥斯特瓦德 (Alexander Osterwalder) 设计的商业模型画布。「[[商业模式]]画布」可以帮助任何初创定义自身业务组成部分和框架。
				- [[商业模式]]画布一共由九个部分组成，每块都代表业务的一个重要部分。它们分别是（不分前后顺序）：
				- 1. 重要合作/伙伴(Key Partnerships)
				- 2. 关键业务（Key Activities）
				- 3. 价值主张（Value Propositions）
				- 4. 客户关系（Customer Relationships）
				- 5. 客户细分（Customer Segments）
				- 6. 核心资源（Key Resources）
				- 7. 渠道通路（Channels）
				- 8. 成本结构（Cost Structure）
				- 9. 收入来源（Revenue Streams）
				- 👨‍🌾其实这9条仅仅是分析的方法论，实际上可以用四个维度来解释，那就是：价值主张、运营模式、界面模式、盈利模式 。因这是总结的结果，所以暂时不再赘述，我还是根据底层9条分别展开。
				- 一、价值主张（Value Propositions）
				- 价值主张是企业的核心：它是客户选择一家公司而不是另一家公司的原因。
				- 企业为客户创造的价值，可以是新颖、性能、定制、设计、品牌、价格、降低成本和[[降低风险]]。价值主张应该清晰、简洁，并专注于产品中最引人注目的方面。根据客户反馈和市场洞察不断测试和完善价值主张。
				- 比如：“对于难以抽时间去健身房的忙碌上班族，我们的健身应用程序可直接向您的手机提供个性化、有效的锻炼。与其他应用程序不同，我们使用人工智能来实时调整日常活动，确保最佳进度。我们拥有8,000个成功案例，前6周内健身活动平均提高20%，我们根据您的情况和健身目标提供有效的方案。”
				- 二、客户细分（Customer Segments）
				- 客户是任何[[商业模式]]的核心。没有客户成交，公司就无法长期生存。同时还可以将他们分为具有某些共同需求、行为或其他属性的细分客群，以提高客户满意度。思考公司产品要进入什么市场。主要市场类型包括：
				- 1. 大众市场：目标受众不细分，所有客户都有相似的需求。
				- 2. 利基市场：具有独特需求、价值观等的特定客户类别。
				- 3. 细分市场：两个或多个具有不同需求但痛点相似的客户群。
				- 4. 多元市场：两个或多个具有完全不同需求和痛点的客户群。
				- 5. 多边市场：相互关联的客户群。
				- 比如：豪华手表的企业可能会瞄准重视独特性和工艺的高净值人士，也就是「利基市场」。或者，智能手机企业可能会瞄准「大众市场」来吸引广泛的韭菜用户。
				- 三、收入来源（Revenue Streams）
				- 如何从确定的目标客户群中产生现金。如果客户是[[商业模式]]的核心，收入就是动脉。可以从每个客户群中产生不同的收入来源。公司可以根据客户类型、愿意支付的费用以及他们喜欢的支付方式，通过多种方式产生收入。收入来源类型大致包括：
				- 1. 直接销售：产品销售/服务销售。
				- 2. 租赁：比如房产租赁或者设备租赁。
				- 3. 广告和赞助。
				- 4. 许可和知识产权：许可费/特许权使用费。
				- 5. 经常性收入：订阅费/会员费，比如视频网站会员费。
				- 6. 联盟营销：通过推广其他公司的产品或服务获得销售佣金。
				- 7. 使用收入：按使用付费-根据客户使用产品或服务（例如云服务）的数量向他们收费/按交易费用-为促进各方之间的交易赚取一定百分比或固定费用，比如第三方支付软件交易费。
				- 8. 免费增值：通过免费提供产品的基本版本并对高级功能或增强版本收费而获得的收入。
				- 9. 咨询和服务：咨询费/专业服务费（例如法律、财务或设计服务）。
				- 10. 数据收入：数据销售或许可，通过向第三方出售或许可从客户或用户收集的数据而获得的收入。
				- 四、渠道通路（Channels）
				- 确定渠道，通过各种渠道，公司才能与客户沟通，并向目标客户群提供价值。沟通、分销和销售渠道都是渠道的一部分，也是公司如何沟通和交付价值的一部分。公司的价值主张经由特定渠道传递给客户，渠道也是客户在市场上找到不同公司产品和服务的方式。「渠道」可以是：
				- 1. 直接渠道：公司网站或内部销售渠道。
				- 2. 间接渠道：零售合作伙伴/分销商/代理商或经纪人。
				- 3. 数字渠道：电商平台/社交媒体/移动应用程序。
				- 4. 服务渠道：客户支持中心/自助服务工具：提供在线资源和工具，供客户自行查找信息和解决方案。
				- 5. 实体渠道：快闪店/贸易展览和活动-参加行业活动和展览，展示公司产品并与潜在客户互动。
				- 6. 合作伙伴渠道：联盟营销/推荐计划。
				- 7. 订阅和会员渠道：订阅-定期为客户提供精心挑选的产品/会员计划-为注册会员的客户提供独家访问权、折扣或福利。
				- 五、关键业务（Key Activities）
				- 这是公司需要做的最重要的事情，以确保[[商业模式]]发挥作用。在定义关键业务时，重点关注那些直接有助于为客户提供价值以及确保[[商业模式]]成功，有效地执行对于实现公司的价值主张和建立可持续发展的业务至关重要。
				- 那我们需要去思考至少以下几点：
				- 1. 传递价值、服务时候需要什么？
				- 2. 需要怎样的产品、界面覆盖用户的展示侧？
				- 3. 需要线下怎样的形式来进行线上的承接?
				- 4. 用户的连接需要什么样的产品功能、SOP？
				- 5. 收入的来源层面需要怎样的收益方式？
				- 那么这些思考诱发的关键业务，有可能是：
				- 1. 生产：产品设计、开发、交付、质量控制。
				- 2. 服务活动：客户支持/维护和修理/咨询。
				- 3. 营销和销售活动：营销活动/销售流程。
				- 4. 配送和交付活动：物流/交付。
				- 5. 技术与创新活动：研究与开发/技术管理。
				- 6. 运营与管理：供应链管理/财务管理/人力资源。
				- 7. 网络和合作伙伴关系：合作伙伴关系-建立和维护与供应商、分销商和其他合作伙伴/行业关系-与同行、利益相关者和行业活动互动，以保持信息灵通。
				- 8. 客户参与和反馈：反馈收集-收集客户的反馈以改进产品、服务和流程/社群建设-创建客户社群进行参与、支持和宣传。
				- 六、核心资源（Key Resources）
				- 确定了执行[[商业模式]]所需的关键业务，就能确定公司所需的资源类型。资源对于初创公司的成功至关重要，关键资源是确保[[商业模式]]可以正常运行的最重要的资源。要经营任何业务，都需要特定的资源。如果没有必要的资源来创建和交付价值主张、与客户沟通、赚取收入和发展公司，[[商业模式]]就无法实现。了解不同资源之间的关系，可能某些资源需要其他资源来支持。
				- 常见的通类资源类型有：
				- 1. 物理资源：基础设施-经营业务所需的物理设施、设备和资产（例如工厂、仓库、机器）/库存-生产和销售所需的产品或材料的库存。
				- 2. 人力资源：员工/管理层。
				- 3. 知识产权：专利和商标/版权。
				- 4. 财务资源：资本-启动成本、运营、增长和投资所需的资金/收入-销售和其他收入流产生的收入。
				- 5. 技术资源：软件和工具/数据和分析。
				- 6. 合作伙伴关系和关系：供应商合作伙伴关系/分销合作伙伴关系。
				- 7. 品牌和声誉：品牌认知度/客户忠诚度。
				- 8. 物理位置：零售空间/办公空间。
				- 9. 网络和社区：行业关系/客户社群。
				- 七、客户关系（Customer Relationships）
				- 维护客户关系的目标包括：如何获得新客户？如何提高现有客户的留存率？如何通过某些类型的追加销售和交叉销售来增加收入？根据客户群的期望、目标和偏好，确定如何与客户建立关系。此外，还要考虑产品的性质和业务目标。客户关系大致分为几类：
				- 1. 个人协助：个人建议-根据客户的个人需求和偏好提供个性化的建议、建议和解决方案/咨询-提供深入的讨论和咨询，指导客户做出明智的决策。
				- 2. 专门支持：专门支持团队-指派特定团队或代表来满足个别客户或细分市场的需求/客户经理-指定客户经理与关键客户建立牢固关系并确保满足其需求。
				- 3. 自助服务：自助服务工具-提供资源和工具，使客户能够自行查找信息和解决问题/[[知识库]]-提供常见问题解答、指南和教程的全面在线数据库。
				- 4. 自动化服务：自动聊天机器人-使用人工智能驱动的聊天机器人与客户实时互动，回答常见问题并指导他们完成流程/自动通知-发送自动消息和更新，让客户了解他们的订单情况、服务、和促销活动。
				- 5. 社群和参与：在线社群-创建客户可以联系、分享经验和互相帮助的平台/用户论坛，举办客户可以讨论产品、交流技巧和提供反馈的论坛。
				- 6. 共同创造：反馈与协作-邀请客户提供反馈、建议和产品改进的想法/Beta测试-允许选定的客户在新功能或产品正式发布之前测试新功能或产品。
				- 7. 活动和体验：研讨会和网络研讨会-举办活动，使客户能够更多地了解公司的产品和行业/独家活动-为忠实客户组织特别活动或聚会，以建立社群意识。
				- 8. 忠诚度计划：奖励和折扣-为重复购买和客户忠诚度提供激励/分层计划-实施根据客户忠诚度级别提供不断增加福利的计划。
				- 9. 长期关系：通过保持联系、了解客户不断变化的需求并适应变化来培养与客户的持续关系。
				- 10. 交易关系：确保客户的购买体验，重点关注支付和订单处理的便捷性。
				- 11. 社交媒体互动：查看社交媒体渠道以了解客户情绪、回复查询并管理声誉。
				- 八、重要合作/伙伴(Key Partnerships）
				- 重要合作伙伴网络是[[商业模式]]正常运营的关键！合作伙伴可以提供资源、专业知识、分销渠道等，帮助公司业务发展并取得成功。大多数公司依靠强大的合作伙伴网络来实现业务成功，比单打独斗效率更高、发展更快。考虑到优化运营、降低成本和风险、创造规模经济，确定与哪些关键合作伙伴建立关系。主要合作伙伴类型有：
	- 12:13
	  collapsed:: true
		- #review
			- ((64f4c12c-baf2-444c-b62b-c92cdcb08d62)) => 涂回那两瓶药就行了。我估计是涂了[[过期]]的[[达克宁]]起[[反作用]]了。