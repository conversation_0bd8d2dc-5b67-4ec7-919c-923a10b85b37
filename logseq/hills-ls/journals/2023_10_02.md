- **[[Morning-Page]]**
- [[Society]]
	- 02:44
	  collapsed:: true
		- #摘录
			- [供应商]([[供应商]])：[[想真企业]] - [[有机产品]] - [[阿里巴巴]] - [[远方好物]]
				- [[圣牡有机]] - 非常值得[[溯源]] - [[梁丰巧克力]]的[[备选]]
	- 13:44
	  collapsed:: true
		- #摘录
			- by[普惠]([[朱峻修]])： 昨晚睡在[[松赞林寺]]周边，半梦半醒之间听到[[觉者传法]]（[[潜意识]]突然告诉自己有重要的事情），醒来后马上把它记了下来 :
				- [[正视苦难]]。人生不应该总是求着一切皆是如自己所愿，人生中其实必有苦难，苦难让我们会更为清醒，苦难也让我们看到[[朴实人生]]的可贵之处，[[幸福]]和[[苦难]]总会相伴，这才是[[真实的人生]]。
				  logseq.order-list-type:: number
				- [[自我观察]]。能偶尔甚至经常地从自我角色中跳离出来以[[第三者]]的[[视角]]看待自己，我们就会慢慢把自己那个[[小我]]看得透透的，于是便可以获得一种小我“[[解脱]]”之自由，带着这样的自由而活，我们[[人生的质量]]也许会高一个层次。
				  logseq.order-list-type:: number
	- 14:49 - 14:51 *2min*
	  collapsed:: true
		- #闪念
			- CANCELED 想读[[hls__去杠杆下的投资机会_1696229363311_0]] by [[冯卫东]]
			  id:: 651aa22b-9315-4734-9949-6fed57d6d238
	- 21:15
	  collapsed:: true
		- #摘录
			- [当下电商，有三种主流的生态，传统的搜索电商，前两年...](https://v.douyin.com/idJVucxw/)
				- #视频
					- ![当下电商，有三种主流的生态，传统的搜索电商，前两年兴起的直播电商，以及远方好物这样为代表的是社交电商。.mp4](../assets/当下电商，有三种主流的生态，传统的搜索电商，前两年兴起的直播电商，以及远方好物这样为代表的是社交电商。_1696267963879_0.mp4)
						- #质疑
							- 完全没有[[层级制]]我也是不认可的！你不可能永远[[用爱发电]]吧？消费满1500即可申请店主，那这个店主的上级能否继续享受他带来的收益的分成？还是只有他自己的消费能分成？这明显不合理！
	- 21:28 - 21:29 *1min*
	  collapsed:: true
		- #continue
			- ((64ff4179-290e-4369-9e2f-3c5c3198f500)) => 先去[[鼎艾科技]]，再去[[众康科技]]
- [[Family]]
	- 00:55 - 01:43 *48min*
	  collapsed:: true
		- #continue
			- {{embed ((65128fea-5d18-4b8b-ab01-aecb1d1c0b2c))}}
			  id:: 651a6582-7683-46bd-af77-77dbda00940f
				- #then
					- DONE [[OGSM2023Q4]] - [我]([[毛义明]]) - [[Myself]] 
					  SCHEDULED: <2023-12-31 Sun ++3w>
					  DEADLINE: <2023-12-31 Sun>
					  id:: 651a6582-e61e-463d-acac-5ddb53d7ac0e
					  collapsed:: true
					  :LOGBOOK:
					  * State "DONE" from "TODO" [2023-10-02 Mon 20:35]
					  * State "DONE" from "TODO" [2023-10-10 Tue 19:41]
					  * State "DONE" from "TODO" [2023-10-22 Sun 17:28]
					  * State "DONE" from "TODO" [2023-11-13 Mon 11:35]
					  * State "DONE" from "TODO" [2023-12-03 Sun 00:42]
					  * State "DONE" from "TODO" [2023-12-25 Mon 15:37]
					  :END:
						- [[目的]]
						  id:: 6582a35b-d11d-47a7-80f7-5091a58cf404
							- TODO 通过持续的[[自我管理]]，[[学习]]，使自己保持心智和身体上的[[自律]] [*](((6519b19b-39da-4572-a45e-43c03473e95c))) [*](((651a6582-a718-4772-96b5-b2c5d11c72c3)))
							  id:: 65cb8baf-4c75-49b6-860b-4754ffa3db9f
							  collapsed:: true
								- 控制妄念，控制野心：在新的[[妄念]]面前，能够更好的[[自控]]，让自己时刻在当下思考，理性的规划未来。
								- 保持理性，拥有更好的精力，留出时间学习：随着挑战不断的升级，自己的原有的能力和经验快跟不上了。
								- 不要[[偷懒]]，你所在的职位，需要你勤快的去跑，用两只脚踩出资源和经验。
							- [[目标]]
							  id:: 65cb8baf-aaf1-4bfc-a3a0-51141281c0ac
								- TODO 持续锻炼：减重20斤
								  id:: 6655a11a-35c2-419f-bc50-94f3bf84f056
								- TODO 持续学习英语：能说短句
								  id:: 6655a11a-e6bd-4ec4-97a4-068f06c18eb9
								- TODO 持续看书：感觉到认知产生质变
								  id:: 6655a11a-9ba1-4103-9f68-e1124349030e
								- TODO 持续向外部资源学习，对接更多资源，获取行业更多最新知识：感觉到自己掌握了[下一阶段（level4）]([[level-4]])所需的思维框架，包括组织建设、平台运营、营销运营、节奏打法等
								  id:: 6655a11a-7576-4368-b82e-328e1b66e1b3
								- DONE 投入时间陪[儿子]([[毛懋]])：能看到儿子不再对[[父母]]有脾气，安全感十足，充满自信。
								- ((65cb1988-6a3c-46e2-b672-99f10adec4f4)) => 还有[[多邻国]]的老板也是如此清澈
								  id:: 6655a11a-6a3b-439a-85a9-6bad4c5e22c8
							- [[策略]]
							  id:: 65cb8baf-fddc-472d-bafe-61ef30812527
							  collapsed:: true
								- TODO 通过重复曾经定下的[[进步100天]]，坚持积极的[[生活习惯]]，每天[[打卡]]，达到自己的[[身体状况]]保持在一个[[时刻准备着]]的状态。 [*](((63523a62-9f24-4f62-95ba-17f930798fc4))) -> ((6353a26a-9189-487e-bb03-7068a0973ccc))
								  id:: 651aa22b-c91c-424f-93e7-cb755c8bbe6e
								  :LOGBOOK:
								  * State "DONE" from "TODO" [2023-10-07 Sat 23:48]
								  * State "DONE" from "TODO" [2023-10-14 Sat 12:42]
								  * State "DONE" from "TODO" [2023-10-29 Sun 19:54]
								  * State "DONE" from "TODO" [2023-11-06 Mon 13:49]
								  * State "DONE" from "TODO" [2023-11-11 Sat 12:08]
								  * State "DONE" from "TODO" [2023-11-18 Sat 17:48]
								  * State "DONE" from "TODO" [2023-11-26 Sun 22:38]
								  * State "DONE" from "TODO" [2023-12-02 Sat 11:48]
								  * State "DONE" from "TODO" [2023-12-10 Sun 23:17]
								  * State "DONE" from "TODO" [2023-12-17 Sun 14:10]
								  * State "DONE" from "TODO" [2023-12-24 Sun 19:11]
								  * State "DONE" from "TODO" [2023-12-31 Sun 16:41]
								  * State "DONE" from "TODO" [2024-01-08 Mon 11:27]
								  * State "DONE" from "TODO" [2024-01-16 Tue 13:11]
								  * State "DONE" from "TODO" [2024-01-22 Mon 14:17]
								  * State "DONE" from "TODO" [2024-01-29 Mon 18:42]
								  * State "DONE" from "TODO" [2024-02-13 Tue 15:28]
								  * State "DONE" from "TODO" [2024-02-18 Sun 10:10]
								  * State "DONE" from "TODO" [2024-02-26 Mon 10:24]
								  * State "DONE" from "TODO" [2024-03-05 Tue 15:54]
								  * State "DONE" from "TODO" [2024-04-01 Mon 11:56]
								  * State "DONE" from "TODO" [2024-04-14 Sun 17:44]
								  * State "DONE" from "TODO" [2024-04-18 Thu 12:03]
								  * State "DONE" from "TODO" [2024-04-22 Mon 13:05]
								  * State "DONE" from "TODO" [2024-05-06 Mon 11:04]
								  * State "DONE" from "TODO" [2024-05-13 Mon 12:09]
								  * State "DONE" from "TODO" [2024-05-20 Mon 11:25]
								  * State "DONE" from "TODO" [2024-05-27 Mon 10:01]
								  :END:
									- [[衡量]]：80%的step都做到
									  id:: 6655a11a-a301-41a7-a843-05f099523f44
										- #step
											- [[作息]]与[[运动]]
											  logseq.order-list-type:: number
											  id:: 6655a11a-51e9-4c88-b042-5b31aacad463
												- CANCELED 每天5点30起床，9点30上床（[[出差]]也不耽误，[儿子]([[毛懋]])7点30起床） 
												  collapsed:: true
												  SCHEDULED: <2024-05-29 Wed .+1d>
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2023-10-03 Tue 15:01]
												  * State "DONE" from "TODO" [2023-10-05 Thu 21:22]
												  * State "DONE" from "TODO" [2023-10-06 Fri 00:05]
												  * State "DONE" from "TODO" [2023-10-07 Sat 13:00]
												  * State "DONE" from "TODO" [2023-10-08 Sun 07:47]
												  * State "DONE" from "TODO" [2023-10-09 Mon 10:47]
												  * State "DONE" from "TODO" [2023-10-10 Tue 19:41]
												  * State "DONE" from "TODO" [2023-10-11 Wed 10:22]
												  * State "DONE" from "TODO" [2023-10-14 Sat 12:41]
												  * State "DONE" from "TODO" [2023-10-22 Sun 13:16]
												  * State "DONE" from "TODO" [2023-10-29 Sun 19:54]
												  * State "DONE" from "TODO" [2023-11-06 Mon 13:49]
												  * State "DONE" from "TODO" [2023-11-11 Sat 12:08]
												  * State "DONE" from "TODO" [2023-11-18 Sat 17:48]
												  * State "DONE" from "TODO" [2023-11-26 Sun 22:38]
												  * State "DONE" from "TODO" [2023-12-02 Sat 11:48]
												  * State "DONE" from "TODO" [2023-12-10 Sun 23:17]
												  * State "DONE" from "TODO" [2023-12-17 Sun 14:10]
												  * State "DONE" from "TODO" [2023-12-24 Sun 19:11]
												  * State "DONE" from "TODO" [2023-12-31 Sun 16:41]
												  * State "DONE" from "TODO" [2024-01-08 Mon 11:37]
												  * State "DONE" from "TODO" [2024-01-16 Tue 13:11]
												  * State "DONE" from "TODO" [2024-01-22 Mon 14:16]
												  * State "DONE" from "TODO" [2024-01-29 Mon 18:42]
												  * State "DONE" from "TODO" [2024-02-13 Tue 15:28]
												  * State "DONE" from "TODO" [2024-02-18 Sun 10:10]
												  * State "DONE" from "TODO" [2024-02-26 Mon 10:24]
												  * State "DONE" from "TODO" [2024-03-05 Tue 15:54]
												  * State "DONE" from "TODO" [2024-04-01 Mon 11:56]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:43]
												  * State "DONE" from "TODO" [2024-04-18 Thu 12:03]
												  * State "DONE" from "TODO" [2024-04-22 Mon 13:05]
												  * State "DONE" from "TODO" [2024-04-30 Tue 17:40]
												  * State "DONE" from "TODO" [2024-05-06 Mon 11:04]
												  * State "DONE" from "TODO" [2024-05-13 Mon 12:09]
												  * State "DONE" from "TODO" [2024-05-20 Mon 11:25]
												  * State "DONE" from "TODO" [2024-05-21 Tue 15:59]
												  * State "DONE" from "TODO" [2024-05-22 Wed 11:30]
												  * State "DONE" from "TODO" [2024-05-23 Thu 12:07]
												  * State "DONE" from "TODO" [2024-05-24 Fri 10:36]
												  * State "DONE" from "TODO" [2024-05-27 Mon 10:01]
												  * State "DONE" from "TODO" [2024-05-28 Tue 15:11]
												  :END:
													- #before
														- DONE [买]([[想买]])双[[跑鞋]] 
														  id:: 652ca33d-66f5-4ce4-bacc-b723fc9404fc
														  SCHEDULED: <2023-10-17 Tue .+1d>
												- CANCELED 05:00-06:30 早上跑步40分钟，听播客 
												  SCHEDULED: <2024-05-29 Wed .+1d>
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2023-10-03 Tue 15:01]
												  * State "DONE" from "TODO" [2023-10-08 Sun 07:47]
												  * State "DONE" from "TODO" [2023-10-09 Mon 10:47]
												  * State "DONE" from "TODO" [2023-10-10 Tue 19:41]
												  * State "DONE" from "TODO" [2023-10-11 Wed 10:22]
												  * State "DONE" from "TODO" [2023-10-14 Sat 12:41]
												  * State "DONE" from "TODO" [2023-10-22 Sun 13:16]
												  * State "DONE" from "TODO" [2023-10-29 Sun 19:54]
												  * State "DONE" from "TODO" [2023-11-06 Mon 13:49]
												  * State "DONE" from "TODO" [2023-11-11 Sat 12:08]
												  * State "DONE" from "TODO" [2023-11-18 Sat 17:14]
												  * State "DONE" from "TODO" [2023-11-26 Sun 22:38]
												  * State "DONE" from "TODO" [2023-12-02 Sat 11:48]
												  * State "DONE" from "TODO" [2023-12-10 Sun 23:17]
												  * State "DONE" from "TODO" [2023-12-17 Sun 14:10]
												  * State "DONE" from "TODO" [2023-12-24 Sun 19:11]
												  * State "DONE" from "TODO" [2023-12-31 Sun 16:41]
												  * State "DONE" from "TODO" [2024-01-08 Mon 11:37]
												  * State "DONE" from "TODO" [2024-01-16 Tue 13:11]
												  * State "DONE" from "TODO" [2024-01-22 Mon 14:16]
												  * State "DONE" from "TODO" [2024-01-29 Mon 18:42]
												  * State "DONE" from "TODO" [2024-02-13 Tue 15:28]
												  * State "DONE" from "TODO" [2024-02-18 Sun 10:10]
												  * State "DONE" from "TODO" [2024-02-26 Mon 10:24]
												  * State "DONE" from "TODO" [2024-03-07 Thu 12:10]
												  * State "DONE" from "TODO" [2024-04-01 Mon 11:56]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:43]
												  * State "DONE" from "TODO" [2024-04-18 Thu 12:03]
												  * State "DONE" from "TODO" [2024-04-22 Mon 13:05]
												  * State "DONE" from "TODO" [2024-04-30 Tue 17:40]
												  * State "DONE" from "TODO" [2024-05-06 Mon 11:04]
												  * State "DONE" from "TODO" [2024-05-13 Mon 12:09]
												  * State "DONE" from "TODO" [2024-05-20 Mon 11:25]
												  * State "DONE" from "TODO" [2024-05-21 Tue 15:59]
												  * State "DONE" from "TODO" [2024-05-22 Wed 11:30]
												  * State "DONE" from "TODO" [2024-05-23 Thu 12:07]
												  * State "DONE" from "TODO" [2024-05-24 Fri 10:36]
												  * State "DONE" from "TODO" [2024-05-27 Mon 10:01]
												  * State "DONE" from "TODO" [2024-05-28 Tue 15:11]
												  :END:
												- CANCELED 06:30-08:30 看书，做计划 
												  SCHEDULED: <2024-05-29 Wed .+1d>
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2024-05-17 Fri 11:09]
												  * State "DONE" from "TODO" [2024-05-19 Sun 00:09]
												  * State "DONE" from "TODO" [2024-05-20 Mon 11:25]
												  * State "DONE" from "TODO" [2024-05-21 Tue 15:59]
												  * State "DONE" from "TODO" [2024-05-22 Wed 11:30]
												  * State "DONE" from "TODO" [2024-05-23 Thu 12:07]
												  * State "DONE" from "TODO" [2024-05-24 Fri 10:36]
												  * State "DONE" from "TODO" [2024-05-27 Mon 10:01]
												  * State "DONE" from "TODO" [2024-05-28 Tue 15:11]
												  :END:
												- TODO 每天9点30上床，再调整早起时间
												  id:: 6655a6e1-b26c-42d6-b46a-3bb7da96c0a5
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2024-05-29 Wed 16:32]
												  :END:
												- DONE 工作日周一到周五中午健身
												  id:: 6655a8a8-4ea4-4db9-94b8-29843c178982
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2024-05-28 Tue 17:57]
												  * State "DONE" from "TODO" [2024-05-29 Wed 16:32]
												  * State "DONE" from "TODO" [2024-05-30 Thu 10:15]
												  :END:
											- 日常[[学习]]与[[思考]]
											  logseq.order-list-type:: number
											  id:: 6655a11a-4622-469a-bee1-93958cc64757
												- ((63c8d635-cda8-4ba0-9c1e-7e76fa442502))-> ((63612f09-cdf1-4cf0-bb30-99ada7c1b06e))
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2023-10-04 Wed 00:06]
												  * State "DONE" from "TODO" [2023-10-06 Fri 14:07]
												  * State "DONE" from "TODO" [2023-10-07 Sat 23:18]
												  * State "DONE" from "TODO" [2023-10-09 Mon 10:47]
												  * State "DONE" from "TODO" [2023-10-10 Tue 19:41]
												  * State "DONE" from "TODO" [2023-10-11 Wed 10:22]
												  * State "DONE" from "TODO" [2023-10-14 Sat 12:42]
												  * State "DONE" from "TODO" [2023-10-22 Sun 17:28]
												  * State "DONE" from "TODO" [2023-10-29 Sun 19:54]
												  * State "DONE" from "TODO" [2023-11-06 Mon 13:49]
												  * State "DONE" from "TODO" [2023-11-13 Mon 11:35]
												  * State "DONE" from "TODO" [2023-11-19 Sun 02:06]
												  * State "DONE" from "TODO" [2023-11-26 Sun 22:38]
												  * State "DONE" from "TODO" [2023-12-03 Sun 00:30]
												  * State "DONE" from "TODO" [2023-12-10 Sun 23:17]
												  * State "DONE" from "TODO" [2023-12-17 Sun 14:10]
												  * State "DONE" from "TODO" [2023-12-25 Mon 15:37]
												  * State "DONE" from "TODO" [2023-12-31 Sun 16:41]
												  * State "DONE" from "TODO" [2024-01-08 Mon 11:37]
												  * State "DONE" from "TODO" [2024-01-16 Tue 13:11]
												  * State "DONE" from "TODO" [2024-01-22 Mon 14:16]
												  * State "DONE" from "TODO" [2024-01-29 Mon 18:42]
												  * State "DONE" from "TODO" [2024-02-03 Sat 22:23]
												  * State "DONE" from "TODO" [2024-02-13 Tue 15:28]
												  * State "DONE" from "TODO" [2024-02-18 Sun 10:10]
												  * State "DONE" from "TODO" [2024-02-26 Mon 10:24]
												  * State "DONE" from "TODO" [2024-03-05 Tue 15:54]
												  * State "DONE" from "TODO" [2024-03-10 Sun 00:32]
												  * State "DONE" from "TODO" [2024-03-17 Sun 17:27]
												  * State "DONE" from "TODO" [2024-03-26 Tue 11:51]
												  * State "DONE" from "TODO" [2024-04-01 Mon 11:55]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:43]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:44]
												  * State "DONE" from "TODO" [2024-04-22 Mon 13:05]
												  * State "DONE" from "TODO" [2024-04-28 Sun 11:48]
												  * State "DONE" from "TODO" [2024-05-06 Mon 11:04]
												  * State "DONE" from "TODO" [2024-05-13 Mon 10:02]
												  * State "DONE" from "TODO" [2024-05-19 Sun 00:09]
												  * State "DONE" from "TODO" [2024-05-20 Mon 11:25]
												  * State "DONE" from "TODO" [2024-05-21 Tue 15:59]
												  * State "DONE" from "TODO" [2024-05-22 Wed 11:30]
												  * State "DONE" from "TODO" [2024-05-23 Thu 12:07]
												  * State "DONE" from "TODO" [2024-05-24 Fri 10:36]
												  * State "DONE" from "TODO" [2024-05-27 Mon 10:01]
												  * State "DONE" from "TODO" [2024-05-28 Tue 15:11]
												  :END:
												- CANCELED 22:00-22:30 每天[[冥想]]20分钟 
												  SCHEDULED: <2024-05-29 Wed .+1d>
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2023-10-06 Fri 21:01]
												  * State "DONE" from "TODO" [2023-10-07 Sat 23:18]
												  * State "DONE" from "TODO" [2023-10-09 Mon 10:31]
												  * State "DONE" from "TODO" [2023-10-10 Tue 19:41]
												  * State "DONE" from "TODO" [2023-10-11 Wed 10:22]
												  * State "DONE" from "TODO" [2023-10-14 Sat 12:41]
												  * State "DONE" from "TODO" [2023-10-22 Sun 13:15]
												  * State "DONE" from "TODO" [2023-10-28 Sat 10:59]
												  * State "DONE" from "TODO" [2023-11-06 Mon 13:49]
												  * State "DONE" from "TODO" [2023-11-11 Sat 12:08]
												  * State "DONE" from "TODO" [2023-11-18 Sat 17:48]
												  * State "DONE" from "TODO" [2023-11-26 Sun 22:38]
												  * State "DONE" from "TODO" [2023-12-02 Sat 11:48]
												  * State "DONE" from "TODO" [2023-12-10 Sun 23:17]
												  * State "DONE" from "TODO" [2023-12-17 Sun 14:10]
												  * State "DONE" from "TODO" [2023-12-24 Sun 19:11]
												  * State "DONE" from "TODO" [2023-12-31 Sun 16:41]
												  * State "DONE" from "TODO" [2024-01-08 Mon 11:37]
												  * State "DONE" from "TODO" [2024-01-16 Tue 13:11]
												  * State "DONE" from "TODO" [2024-01-22 Mon 14:16]
												  * State "DONE" from "TODO" [2024-01-29 Mon 18:42]
												  * State "DONE" from "TODO" [2024-02-04 Sun 12:02]
												  * State "DONE" from "TODO" [2024-02-13 Tue 15:28]
												  * State "DONE" from "TODO" [2024-02-18 Sun 10:10]
												  * State "DONE" from "TODO" [2024-02-26 Mon 10:24]
												  * State "DONE" from "TODO" [2024-03-05 Tue 15:54]
												  * State "DONE" from "TODO" [2024-03-10 Sun 00:32]
												  * State "DONE" from "TODO" [2024-03-17 Sun 17:27]
												  * State "DONE" from "TODO" [2024-03-26 Tue 11:51]
												  * State "DONE" from "TODO" [2024-04-01 Mon 11:55]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:43]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:44]
												  * State "DONE" from "TODO" [2024-04-22 Mon 13:05]
												  * State "DONE" from "TODO" [2024-04-28 Sun 11:48]
												  * State "DONE" from "TODO" [2024-05-06 Mon 11:04]
												  * State "DONE" from "TODO" [2024-05-13 Mon 10:02]
												  * State "DONE" from "TODO" [2024-05-19 Sun 00:09]
												  * State "DONE" from "TODO" [2024-05-20 Mon 11:25]
												  * State "DONE" from "TODO" [2024-05-21 Tue 15:59]
												  * State "DONE" from "TODO" [2024-05-22 Wed 11:30]
												  * State "DONE" from "TODO" [2024-05-23 Thu 12:07]
												  * State "DONE" from "TODO" [2024-05-24 Fri 10:36]
												  * State "DONE" from "TODO" [2024-05-27 Mon 10:01]
												  * State "DONE" from "TODO" [2024-05-28 Tue 15:11]
												  :END:
												- TODO 利用[[通勤时间]]、[[休息时间]]在[[多邻国]]上 [学]([[想学]]) [[英语]] & 有时间再刷[日常英语]([[日常英语]])
												  SCHEDULED: <2024-08-08 Thu ++1w>
												  id:: 651aa912-cc35-4651-a1e1-67d11964e693
												  collapsed:: true
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2023-10-02 Mon 20:35]
												  * State "DONE" from "TODO" [2023-10-06 Fri 14:07]
												  * State "DONE" from "TODO" [2023-10-07 Sat 23:48]
												  * State "DONE" from "TODO" [2023-10-09 Mon 10:47]
												  * State "DONE" from "TODO" [2023-10-10 Tue 19:41]
												  * State "DONE" from "TODO" [2023-10-11 Wed 10:22]
												  * State "DONE" from "TODO" [2023-10-14 Sat 12:41]
												  * State "DONE" from "TODO" [2023-10-22 Sun 17:28]
												  * State "DONE" from "TODO" [2023-10-29 Sun 19:54]
												  * State "DONE" from "TODO" [2023-11-06 Mon 13:49]
												  * State "DONE" from "TODO" [2023-11-13 Mon 11:35]
												  * State "DONE" from "TODO" [2023-11-19 Sun 02:06]
												  * State "DONE" from "TODO" [2023-11-26 Sun 22:38]
												  * State "DONE" from "TODO" [2023-12-03 Sun 00:30]
												  * State "DONE" from "TODO" [2023-12-10 Sun 23:17]
												  * State "DONE" from "TODO" [2023-12-17 Sun 14:10]
												  * State "DONE" from "TODO" [2023-12-25 Mon 15:37]
												  * State "DONE" from "TODO" [2023-12-31 Sun 16:41]
												  * State "DONE" from "TODO" [2024-01-08 Mon 11:37]
												  * State "DONE" from "TODO" [2024-01-16 Tue 13:11]
												  * State "DONE" from "TODO" [2024-01-22 Mon 14:16]
												  * State "DONE" from "TODO" [2024-01-29 Mon 18:42]
												  * State "DONE" from "TODO" [2024-02-04 Sun 12:02]
												  * State "DONE" from "TODO" [2024-02-13 Tue 15:28]
												  * State "DONE" from "TODO" [2024-02-18 Sun 10:09]
												  * State "DONE" from "TODO" [2024-02-26 Mon 10:24]
												  * State "DONE" from "TODO" [2024-03-05 Tue 15:54]
												  * State "DONE" from "TODO" [2024-03-10 Sun 00:32]
												  * State "DONE" from "TODO" [2024-03-17 Sun 17:27]
												  * State "DONE" from "TODO" [2024-03-26 Tue 11:51]
												  * State "DONE" from "TODO" [2024-04-01 Mon 11:56]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:43]
												  * State "DONE" from "TODO" [2024-04-14 Sun 17:45]
												  * State "DONE" from "TODO" [2024-04-22 Mon 13:05]
												  * State "DONE" from "TODO" [2024-04-28 Sun 11:48]
												  * State "DONE" from "TODO" [2024-05-06 Mon 11:04]
												  * State "DONE" from "TODO" [2024-05-13 Mon 10:02]
												  * State "DONE" from "TODO" [2024-05-19 Sun 00:09]
												  * State "DONE" from "TODO" [2024-05-27 Mon 10:01]
												  * State "DONE" from "TODO" [2024-06-03 Mon 11:44]
												  * State "DONE" from "TODO" [2024-06-11 Tue 11:05]
												  * State "DONE" from "TODO" [2024-06-18 Tue 11:55]
												  * State "DONE" from "TODO" [2024-06-24 Mon 13:06]
												  * State "DONE" from "TODO" [2024-07-01 Mon 10:22]
												  * State "DONE" from "TODO" [2024-07-08 Mon 14:20]
												  * State "DONE" from "TODO" [2024-07-17 Wed 10:59]
												  * State "DONE" from "TODO" [2024-08-01 Thu 15:22]
												  :END:
													- #recycle
														- ((636c7d01-61ee-40b9-8b7b-b11fde68b7ec)) ((6353a265-0963-42c5-8471-9824f53b61e7))
											- [[育儿]]
											  logseq.order-list-type:: number
											  id:: 6655a11a-5dd3-422e-ab28-fb075bb04f66
												- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8)) => 8:00-9:30 每天晚上1.5小时[[爸爸时间]] [*](((651a61e0-b9dc-4a6f-94b7-c864b89a33d7)))
												  id:: 664dd6f7-1fc5-4012-9c7d-8c8c0b8f6537
													- #注意
														- 避免无脑瞎玩，定期想想[[爸爸时间]]的[[主题]]是什么，一定要把[儿子]([[毛懋]])[[当下]]的[[成长目标]]融入进去。
														  collapsed:: true
															- #案例
																- ((63f1d1c2-8a98-411a-84c6-c6454bce1de0))
																- ((63451d95-8e08-4f6f-8032-b095961a90c9))
																- {{embed ((636fd7e3-4990-4bdd-a3a9-59edd45b2305))}}
													- #recycle
														- ((6353a265-88e0-4bc8-89e6-7fc89dba4150))
														- ((651aa268-1d83-40c5-936a-32eee3d79a72))
												- DONE 新[[爸爸时间]]：每个周末带娃穿梭在各个培训教室 [*](((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))) [*](((651a61e0-b9dc-4a6f-94b7-c864b89a33d7))) [*](((664dd6f7-1fc5-4012-9c7d-8c8c0b8f6537))) [*](((6656e0f6-9a07-431d-8d93-3623ed563801))) [*](((6656e14f-881d-40fa-8fa9-5f543a05a6d1))) [*](((6656e14f-f2fa-42fa-88b8-676596f4b87c))) 
												  SCHEDULED: <2024-06-15 Sat ++1w>
												  id:: 6655a11a-b2c2-495f-8a48-a93db07e1359
												  :LOGBOOK:
												  * State "DONE" from "TODO" [2024-06-11 Tue 11:05]
												  :END:
											- [[财务]]与[[小习惯]]
											  logseq.order-list-type:: number
												- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
												- ((6353a251-268b-4d21-a10d-e5e664f14e47))
												- ((658b9f3d-c364-4069-a408-8ef20b056011))
							- #recycle
							  collapsed:: true
								- 项目相关领域的资源对接，趋势、技术学习
								  logseq.order-list-type:: number
									- CANCELED 每个月约5个电商、私域、垂直品类相关的负责人、创始人喝茶、吃饭聊天
									  id:: 65cb8baf-8c22-43e4-bed7-32c50a4bb2a2
									- CANCELED 密集的安排自己去参加各种展会，对接资源
									  id:: 65cb8baf-ba6a-4bc0-a395-177e7ab094fc
									- CANCELED 报名参加相关的课程、社交圈子
									  id:: 65cb8baf-0ef6-4b7c-bf19-ff789b65847b
								- ((651e9119-8236-4221-ac4b-7edf8caff485))
								- ((6353a265-ea7d-4f89-8b57-b79973550032))
								- ((63ef1f48-ff8f-4154-90da-1c5c8cdf331d)) ((651aa23f-7279-4201-8add-f4e1ab04090e)) ((651aa23f-3c63-40ea-be5f-1bf0ab323c60)) ((644cc917-1fb7-41d9-a069-d33c90652b0a)) ((644cc917-a10d-450a-bf5c-89011201db16)) => [[拉倒吧]]，以后可以考虑和 [[sethyuan]]大佬合作一把，现在先取消了。
								- ((64898244-c52d-42bf-94bc-ba32e332bdf8)) ((63f603e1-e057-4468-be91-6c780fe389a0)) => [[拉倒吧]]，先顾好自己
								- ((64552bca-0c6a-4db1-b023-fd2596ee8caa)) => 前提是先存下钱
	- 21:23 - 21:26 *3min*
	  collapsed:: true
		- #review
			- ((637454b5-cdea-40a3-a26a-b4783c3b64fa)) => [pet父母效能训练]([[hls__父母效能训练：让亲子沟通如此高效而简单_1676861618848_0]])里面已经讲的很清楚了。
- [[Myself]]
	- 00:30 - 00:47 *17min*
	  collapsed:: true
		- #continue
			- ((65197351-0c3d-486c-bb7e-b3111db84598))
	- 01:43 - 01:54 *11min*
	  collapsed:: true
		- #质疑
			- 我能感觉到我身上的[[懒虫]]在跟我[[较劲]]！！！只要你[[躺在床上]]，打开电脑，你就不想要起来，即使你心里想了很多次，找个地方，正而八经的敲电脑！结果，肯定是赖上个几个小时。甚至几次下来，即使我到了[[星巴克]]，效率也低的一塌糊涂…… 躺床上真能把你给懒死！ [国庆]([[国庆]])[老婆]([[王露]])[儿子]([[毛懋]])一回老家，我就做了几天的[[懒鬼]]！而且是一瞬间！颠倒日夜，一天只吃一顿饭，浑浑噩噩的睡去，浑浑噩噩的醒来！！！这样不行，绝不能再碰床了！ -> ((63047ca5-55f2-4171-a424-bed5548e0437)) #中年 #懒惰 #习惯 #打破
			  id:: 6519b19b-39da-4572-a45e-43c03473e95c
	- 01:54 - 02:17 *23min*
	  collapsed:: true
		- #质疑
			- 我要立刻、马上、现在就开始（重启）[我]([[毛义明]])的[[自我管理]]的各种[[小习惯]][[训练]]了。 [*](((6519b19b-39da-4572-a45e-43c03473e95c)))
			  id:: 651a6582-a718-4772-96b5-b2c5d11c72c3
			  collapsed:: true
				- 背景是，我越来越清晰的感觉到，随着[供应链中心]([[教具商品研发组]])的[[挑战]]和[[期待]]（[[野心]]）越来越大，我的[[能力]]和[[经验]]越来越[[力不从心]]，我常常害怕我是否还能保持[[选择]][[做对的事]]和[[把事做对]]。或者直白点说，我能不能一直赢？
					- 这种[[害怕]]（或者说[[恐惧]]）特别的不当下。
						- 我甚至都开始怀疑我还有没有[[专注当下]]的[[能力]]了，[[碎片]]的事情，生产的，规划的，拉通的；[[挑战]]的事情，模式的，直播的，选品的；[[野心]]的事情，未来的，选择的，构想的；[[八卦]]的事情，[第二事业部]([[兴趣岛第二事业部]])的，[淼姐]([[于淼]])的，课程的，公司的；[[情绪]]的事情，质疑的，流程的，向上公关的 …… 太多东西需要思考，然而思考太多东西却没能让我更好的工作。
							- [中药饮片]([[中药饮片]])[[成功]]以后，[吃瘦]([[吃瘦]])的[[直播]]我全程参与，却没有那么兴奋了，即使我看到[万慧]([[万慧]])从[[首播]]一直兴奋到[[返场]]，我还是没有当初的那么兴奋，答案只有一个：**我的心飘远了，不当下了。膨胀了。精力却不够了，被分散了，快乐的状态下，我仍然感觉到累。其实是经验和能力得不到真正的正反馈（构成经验树，形成逻辑秩序），那么就全是捧杀。**
								- 想跑的更快没有错，想飞也没有错，这是战胜竞争的不二法门。前提是，你要留出时间深度思考，大胆做梦，小心求证，仔细规划。
									- 如果我没有能力驾驭那么多信息，那么我就要选择屏蔽，屏蔽对我没用的信息。因此和团队保持距离，也是应该要做的。带他们打胜仗即可，不要投入过多的感情。
										- 6个月，把我在[[超级元素]] & [[自习室]][[闭关]]的[[收获]]，消耗的一干二净。我需要每天留出时间给自己[[充电]]了。
	- 16:25 - 20:18 *233min*
	  collapsed:: true
		- #continue
			- {{embed ((651a6582-e61e-463d-acac-5ddb53d7ac0e))}}
		- #log
			- 16:38 - 17:07 *29min*
			  collapsed:: true
				- #闪念
					- DONE 搞一下 [[书单]] #workflow
					  id:: 651a8174-7cd4-4b3c-a487-a8f89c88af44
			- 18:29 - 18:59 *30min*
			  collapsed:: true
				- #review
					- ((651a61df-0c2d-4bee-9147-579f171f1343)) #好评 #中药饮片
					  id:: 651a9b9f-3793-4e43-8810-f5dfd0021130
					  collapsed:: true
						- #截图
							- ![mmexport1696089224812.jpg](../assets/mmexport1696089224812_1696242618829_0.jpg)
							- ![mmexport1696089231130.jpg](../assets/mmexport1696089231130_1696242627378_0.jpg)
							- ![mmexport1696089227986.jpg](../assets/mmexport1696089227986_1696242637797_0.jpg)
					- ((651a61f4-4b72-4202-84f7-a01c3e79d239))
				- #recycle
					- ((651a61f3-f755-436b-9c34-cb8e2dd313b7))
					- ((651a61f5-1022-4545-b79a-1ce1f4914bdf))
					- ((64552bcb-bcac-4ce4-b9fb-80a692028d71))
					- ((651a61f6-d46a-4766-9070-613637c55dc6))
					- ((63790474-b550-42a9-8a6d-b3d72d543913))
					- ((63e762ad-1053-4119-8ac8-2812177e7677))
	- 20:24 - 20:30 *6min*
	  collapsed:: true
		- #continue
			- ((62ffabc0-b8ff-4f4e-abcf-c877a52aa54a))
				- ```calc
				  130.92+19.25+3+13+54+13+13
				  ```
					- #回忆
						- 10月1日，[我]([[毛义明]])的[[微信支付]]终于解除[[限制]]，可以[[收钱]]了。半年了。 -> ((6429a6bc-9598-4701-8a8b-5e5000509929)) #超级元素 #纪念日
	- 21:27 - 21:48 *21min*
	  collapsed:: true
		- #闪念
			- DONE [我]([[毛义明]])自己的[中秋]([[中秋]])、[国庆]([[国庆]])安排 -> ((64ff4180-3745-4b11-8472-e83d5c166d4c)) 
			  id:: 651af9ff-0c60-4289-ac11-c33cf877a3d9
			  collapsed:: true
			  SCHEDULED: <2023-10-06 Fri .+1d>
			  DEADLINE: <2023-10-07 Sat>
				- #step
				  id:: 6521783f-2e8b-4645-b4ed-208374e5a7af
				  collapsed:: true
					- [[规划]]
						- DONE ((65128fea-5d18-4b8b-ab01-aecb1d1c0b2c))
						- DONE ((651a6582-e61e-463d-acac-5ddb53d7ac0e))
						- DONE ((65190660-bbb8-42c4-9ad6-a2c61048cb25))
					- [[复盘]]
						- DONE ((65190660-f797-42e0-ade5-25d47f570540))
					- [[行动]]
						- CANCELED ((64d377b0-fd2f-49da-81e4-c624c3103bf8))
						- DONE ((64ff4179-290e-4369-9e2f-3c5c3198f500))
						- DONE ((651aa22b-fe61-4d67-a16c-2a590aa2c68a))
						- DONE ((651aa22b-c91c-424f-93e7-cb755c8bbe6e))
						- CANCELED ((63de2835-0a64-4208-8293-b263a5cf0872))
						- CANCELED ((64d377b0-54b2-4574-a792-0f60e0d2d53f))
						- CANCELED ((63612f17-bf56-4051-a15d-c9e5928c6552))
						- CANCELED ((651aa22b-9315-4734-9949-6fed57d6d238))
						- DONE ((651aa22b-45ca-46ae-82da-d5b3fbc37218))
						- CANCELED ((651aa22b-9d37-4541-a5d7-705fa98f45c2))
						  id:: 651ac8cb-b8e2-42fc-9ff0-b7ac4c128e0d
						- DONE ((651aa22b-bf68-430c-b590-5cc77b5b09fa))
						- DONE 花1天时间整理家，把[儿子]([[毛懋]])的玩具都收拾了，把衣服都洗了，找阿姨深度清洁4小时
						  id:: 651ee1a4-35c9-4cce-90cd-79924c377e29
						  SCHEDULED: <2023-10-06 Fri>
				- #回忆
					- ((6519b19b-39da-4572-a45e-43c03473e95c))
					- ((651a6582-a718-4772-96b5-b2c5d11c72c3))
	- 21:48 - 01:31
	  collapsed:: true
		- #continue
			- ((644e42dc-1690-4e52-86a9-c72086d6a254))
		- #log
			- 21:51 - 01:31
				- #闪念
					- TODO 想[看]([[想看]])[[漫长的季节]]
				- #log
					- 22:16 - 01:16
					  collapsed:: true
						- #闪念
							- DONE [[workflow]]：还得把那些[[习惯]]的`todo`改回 [[block-ref]]
								- ((633cf03e-4596-4457-ac84-54d101da35f6))
								- ((6306282f-6ee9-447f-abda-1b78da19648b))
								- ((651aa254-42e5-4aee-90ed-ea1b827cc06d))
								- ((651aa254-c842-4405-921a-872aff707c14))
								- ((651aa258-198e-4b8e-af29-1b768aeb7a1a))
								- ((651aa254-d18d-49a4-965d-5751fc02ddf9))
								- ((651aa254-a8c1-4683-a8b5-0f49781bff33))
								- ((63f4f1b2-416f-40a1-a075-e2b8ad3677b8))
								- ((651aa254-419b-4f4e-8a81-aca8d98fc744))
								- ((651aa257-40c3-4f31-8155-81a2923bfc42))
								- #and
									- DONE 用 [[smart-search]]把 block下的link 的 ref 放到第一行 #pass 
									  id:: 651af9ff-f8cc-46f7-8665-8e6e59206718
									  SCHEDULED: <2023-10-03 Tue>
						- #log
							- 23:35 - 00:15
							  collapsed:: true
								- #break
- [[Comments]]
  collapsed:: true
	- #肯定
		- 把 [[page-embed]]强行对齐了，真舒服 -> ((651a8174-7cd4-4b3c-a487-a8f89c88af44))