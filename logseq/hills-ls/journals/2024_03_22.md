- **[[Morning-Page]]**
- [[Society]]
	- 16:36
	  collapsed:: true
		- #continue
			- ((65ef080d-5a41-4b7a-ace6-5930308f3e95))
			  id:: 6600f6b9-c4d8-4950-8a84-0787dc71220f
			  collapsed:: true
				- #大纲
					- [[教具商品大协作一张表]]  [*](((65f72a2b-9378-48f6-a2e0-d9837fb999d8)))
						- [供应商管理]([[供应商管理]])
						- [品质标准]([[品质验收标准]])
						- [研发统筹]([[研发]])
						- [生产统筹]([[生产]])
						  collapsed:: true
							- 货品表
								- 注意：
									- 货品不是货盘，是指入库的最小单位的货。
									- 由产品经理填写，生产经理维护—— **新品立项前添加完成**
									- 唯一性：编码——条码——货品——供应商
										- 意味着：
											- 包装不变，供应商变了，新增编码，新增条码，新增供应商
											- 供应商不变，包装变了，新增编码，新增条码
								- 自动生成SKU码（货品编码）：SPU000000
									- 空编码：新品尽量往这里填
								- 润泽仓：定制、批采
								- 代发分销仓
								- 16个必填字段
									- 类目
									  logseq.order-list-type:: number
									- 品类
									  logseq.order-list-type:: number
									- 发货类型
									  logseq.order-list-type:: number
										- 任意发货：单发、组合发
										  logseq.order-list-type:: number
										- 单独发货：由于超重，必须单独发货 **（需制定发货策略）**
										  logseq.order-list-type:: number
										- 多件发货：不可单件发货
										  logseq.order-list-type:: number
										- 组合配件：必须和其他货品组合发货
										  logseq.order-list-type:: number
										- 通用耗材：
										  logseq.order-list-type:: number
										- 组合发货：
										  logseq.order-list-type:: number
									- 货品特征
									  logseq.order-list-type:: number
									- 品牌（或厂牌）
									  logseq.order-list-type:: number
									- 货品简称
									  logseq.order-list-type:: number
									- 规格
									  logseq.order-list-type:: number
									- 重量
									  logseq.order-list-type:: number
									- 保质期（天数）
									  logseq.order-list-type:: number
										- 没有就填3600
									- 最新成本
									  logseq.order-list-type:: number
									- 生产周期（天数）
									  logseq.order-list-type:: number
										- 从立项到到仓
											- 内部流程时间
											- 外部生产时间
									- 69码或商家条码
									  logseq.order-list-type:: number
										- 必填，仓库通过这个扫码
										- 仓库只看69码，69码没有再看编码，编码可以复制到69码的框里。
									- 仓库
									  logseq.order-list-type:: number
										- 云仓
										  logseq.order-list-type:: number
										- 代发、分销
										  logseq.order-list-type:: number
									- 关联供应商
									  logseq.order-list-type:: number
									- 货品状态
									  logseq.order-list-type:: number
										- **正常**
										  logseq.order-list-type:: number
										- **异常**∂
										  logseq.order-list-type:: number
										- 滞销
										  logseq.order-list-type:: number
										- 报废
										  logseq.order-list-type:: number
										- 淘汰
										  logseq.order-list-type:: number
										- 断货
										  logseq.order-list-type:: number
										- 返厂
										  logseq.order-list-type:: number
										- 临期
										  logseq.order-list-type:: number
									- 负责人
									  logseq.order-list-type:: number
								- 手动发库存预警 & 下单预测
								  id:: 660eb362-5757-4732-a557-332e3e4a5163
									- 现有库存：生产运营每日更新，总仓、教具仓、次品仓
									- 临界值：
										- 建议下单（90天）/ 90 = **每日平均用量**
										- 乘以：生产周期 or 到仓时间 - 今天 = **最小在仓天数**
										- 乘以：系数—— **教具 1.5倍；电商 1.3 倍**
										- = **在仓库存临界值**
									- 预警
										- 在仓库存低于临界值就会出现❗️
									- 建议下单
										- 教具
											- 过去90天教具仓发出的总数
											- 未来90天运营提出的订单需求对应的货品件数
											- 预警：
												- 未来大于过去的2倍
												- 未来小于过去
												- 都会出现❗️，需要先处理感叹号，数据才有意义
											- 算法
												- 未来90天大于或小于
												- ❓
										- 电商
						- [运营统筹]([[运营]])
				- #闪念
					- TODO [[研发组各种流程]]
					- TODO 拆单策略
					- TODO 批次管理
					- TODO 赠品和销售分开
					- TODO 教具电商库存借调
					- TODO 销转时间表
					- DONE 货品 商品 售卖规格 教具用量 电商用量
					- TODO 快递费用，拆单情况
					- TODO 条形码
					- TODO 调货记录
					- DONE [研发组]([[教具商品研发组]])内开一个sop共创会
					  id:: 662b2381-7bd6-4d2e-b2f5-8d94780798b2
					- DONE 一个spu只能关联一个工厂
					- TODO 加个工厂维度的产品质量问题汇总
					- DONE 入库单
					- TODO 加工单-组合单
						- #问题
							- DONE 组合后是spu还是sku，是整箱还是可以分散？ 例如：燕窝和燕窝的袋子 
							  id:: 660eae58-68c3-4f09-978c-d33a7ed61dd1
							  SCHEDULED: <2024-04-04 Thu .+1d>
					- DONE 先是基础数据搭建，再是智能化，进一步提升效率
					- ((6620961e-eff0-49d7-a071-edd0bfefd836))
					- DONE 库存预警进一步优化
- [[Family]]
- [[Myself]]