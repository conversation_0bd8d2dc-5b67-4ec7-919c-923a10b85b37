- [[Society]]
	- 09:18
		- #continue
			- {{embed ((6641f678-df6d-46e6-9302-39d0222e6237))}}
		- #log
			- [[Myself]]
			  collapsed:: true
				- 11:00
				  collapsed:: true
					- #摘录
						- 《论 总结和复盘》此话题源于第四期 [[Ship 30 for 90]] day 11 。也是突然发现，很多人不太懂总结和复盘到底是什么，以及如何运用，甚至会把总结和复盘混为一谈。但其实，总结与复盘是有区别的，所以此问题还是有必要“教”下。🤷
						  collapsed:: true
							- 什么是总结？
							- 总结 —— 找到问题却没有深入，也没有改善措施。主要分析成功或失败的原因。
							- 什么是复盘？
							- 复盘 —— 找到问题原因，给出改善措施并全力以赴，持续行动。不仅可以分析成功失败的原因，还能够通过假设的推演，找到不同的方法和可能性。
							- 所以，复盘是对自己有新认识和新行动，而总结是没有的。复盘和总结是隶属关系，总结是复盘的一部分。总结是平面的，复盘是立体的。
							- 总结与复盘之间，最显著的区别在于“推演”。
							- > 复盘一词源于围棋术语。对弈之后，棋手会把对局重演一遍，以便发现自己的错误，理解对手或自己的博弈思路。
							- 总结，是对事件过程进行梳理，是对已经发生的行为和结果进行描述、分析和归纳。
							- 复盘，除了具有总结所包含的所有动作外，还有一个重要的推演过程。
							- 推演是什么？ 推演，是指对事情推测，演练，对各种可能性及其不同后果进行审视和设计。
							- > 人们最常见到的推演，是沙盘推演。沙盘推演，最初用于军事中。沙盘推演，是事前的推演，这时还没有实践，沙盘所得出的结论，更多的是想象中的可能性。
							- 推演，亦是“棋手”之间的博弈和纵横术。“前推演”可预知更多的可能性，“后推演”则分析和归纳。
							- 而复盘中的推演，是事后的推演。是事情已经发生，已经进行了实践，所作的推演。
							- 复盘推演的目的，是探究规律，提出解决问题的方法，对未来的行动作出指导。最终的目标 —— 解决问题，追求结果。
							- 在实际应用中，对已有了执行过程和执行结果，有明确的参照物，所以复盘推演得出的结论，就不再是沙盘推演中那样只是想象的可能性，而是能够帮助解决问题的确定性方法。
							- 复盘的推演有两种方法，一种方法是对事情执行过程和结果进行推演。
							- 举例，下了一盘围棋过后，如果要做一个总结，需要总结这盘棋哪里下得好，哪里下得不好，哪里有问题，看看是否有不同的下法。需要复盘的是—— 下得好的地方，好在哪里？为什么好？下得差的地方，为什么不应该这么下？对于问题，试试有没有其他下法，新的下法会导致哪些后续变化，如何应对？
							- 另一种复盘推演方法，是对事情发生之前所作的思考和逻辑进行梳理，审视当时的思考过程以及逻辑，并对其进行评判，以确定做事的前提是否需要重新构建。
							- 举例，假设我是棋手A，今天白天跟棋手B对决，结果输给他了。这时我要梳理一下，在与棋手B对决之前是怎么思考的？我发现，我只是查看了棋手B的简历，觉得他的水平不如我，于是轻敌了。没想到对决时，自己的制胜招数，被对方破解了。而自己却没有提前研究过对手以往的制胜招数。所以，与高手对决这件事的前提需要重新构建，无论对手水平高低，都应该在事前找来对手充分的资料来研究，有备无患，不打无准备之战。
							- 我们通过复盘推演，用执行实践的过程和结果作参照比对，可以排除错误的认识和路径，找到更有效、更符合本质规律的做法。解决的就是 —— 确定哪些行为有效，可以继续做；哪些行为无效或错误，应停止做；应该开始什么新的行为。
							- 🤷不懂棋手的举例？那举例一个现在日常所见的“直播”，且不论是你刻意/随机 观看的，还是自己运营的。我再次强调「观察」的重要性，任何事情，想要清楚，一个良好的复盘很重要且不论对于新知或旧知。
							- 【直播场景描述】
							- 在直播的过程是，讲了关于“坚持的干货”；结果是只涨了一个粉，没有人在直播间买训练营或体验营。
							- 【如果是做“总结”】
							- “自认为”这次干货分享，做得好的地方，是干货讲得有价值，有料；做得不够好的地方，是语气平淡了些，像上课，略显枯燥，肢体语言不到位；结果不太好，没有出单。
							- 【如果是做“复盘”】，除了上述总结外，还需要作两种推演。
							- 1. 对事情发生的过程和结果的推演
							- 干货还是要继续讲，但是要学学别的主播的眼神、动作、肢体语言，让干货讲得更精彩。没有出单，如何才能出单？是价格高了，还是互动不够？除了讲干货，还是要注重互动，不能一味地自说自话。还要在合适的时机进行吆喝，提醒下单。要不然讲得再多也不出单。如何吆喝？看了一部分别人的直播，但看得还太少，要继续看别人的直播来学习。
							- 2. 对事情发生之前的思考和逻辑进行审视和梳理
							- 比如在直播前，探讨过我们的直播风格，决定不要自我介绍，不用管评论区，不要欢迎新人，因为那样做，干货总是被打断，而应专注为进入直播间的观众提供价值。现在通过对实践过程和结果的参照对比，发现事前的思路，就是需要调整的，需重构做这件事的前提，之前有些认识和路径是错误的。而且，之前以为，做了直播，就能面向全国观众分享，能大量引流，解决生源不足的问题。现在看来，直播还需摸索，仅靠直播引流，效果还太慢，需学习其他引流方式。
							- 于是，通过推演，得出的结果是：
							- A继续做 —— 看抖音和视频号直播，提升认知，增加见识；
							- B停止做—— 不看别人的，闭门造车，想当然地按自己的想法去做。
							- C开始做—— 看网盘中囤着的引流课，了解其他引流方法，与直播同步进行。
							- 👨‍🌾有可能这个举例并不合适，我只是提供一种复盘的“方法论”。那么在职场中、项目中，复盘的意义会更加巨大，每一次项目的完成，都有很多经验，俗话说，最大的浪费是经验的浪费！复盘的作用在于快速找到问题，总结经验的同时，找到工作的突破点，发现可以完善的方向。
							- 对结果进行核对，并不断回顾、反思、研究自己的行为，从中找出规律，指导自己每天进步。复盘的步骤可以分为：记录分析、反思原因、探究提升。
							- 常见的有效复盘方法论有6种：
							- 1. KPT复盘
							- K（Keep）：有哪些方法后面需要继续保持？
							- P（Problem）：在这次项目中，遇到了哪些问题？
							- T（Test）：有什么想法可以在下次中进行测试，尝试改进？
							- 2. 3R复盘法
							- Record记录：完完整记录事项推进的过程，以便反思每个步骤的合理性。
							- Reflect反思（ruminate 深思）：反思项目过程中做得好的，做得差的。
							- Refine提炼（react 反应）：从实践中提炼方法，形成方法论，制定统一标准。
							- 3. KISS复盘法
							- KEEP（保留）：好的方法、好的习惯和好的想法。
							- IMPROVE（改进）：优化做事流程，改进做事方法和改进工具使用。
							- START（开始）：未完成的，计划中的。
							- STOP（停止）：错误的方法、错误的习惯和不好的行为。
							- 4. PDCA复盘法
							- P (Plan) 计划：包括方针和目标的确定，以及活动规划的制定。
							- D (Do) 执行：根据已知的信息，设计具体的方法、方案和计划布局；再根据设计和布局，进行具体运作，实现计划中的内容。
							- C (Check) 检查：总结执行计划的结果，分清哪些对了，哪些错了，明确效果，找出问题。
							- A (Action)处理：对总结检查的结果进行处理，对成功的经验加以肯定，并予以标准化；对于失败的教训也要总结，引起重视。对于没有解决的问题，应提交给下一个PDCA循环中去解决。
							- 5. GRAI 复盘法
							- G（Goal）目标回放：对目标进行回顾，明确工作/项目的目标。项目目标是什么？核心阶段目标是什么？投入预算有多少？
							- R（Result）结果评估：基于目标评估是否达成，差距在哪里。目标是否完成？目标完成情况？
							- 举例，目标结果差距对比：完成结果和原定目标的差距？和原定目标相比有哪些亮点和不足？是否新增了原定没有的项目目标？
							- A（Analysis）过程分析：首先全面的回顾整个过程，尽量不要遗漏，进一步分析形成差异的原因。关键事件/动作回顾形成差距的主观原因形成差距的客观原因。
							- I（Insight）规律总结：针对差异的原因制定改善措施，并形成经验总结。通过原因分析出的对应措施是什么？把分析所得的原因总结出来，并提炼成为规律分享出去，成为自己和团队以后工作的一个参考和经验。但需要注意的是，规律并不是总结得越多越好，在一次复盘中，真正有价值的经验如果有两三个，那就很不错了。
							- 6. STAR复盘法
							- SITUATION：情境，即描述背景，你在当时所处的环境或者面临的挑战。比如你当时要做一个从来没有接触过的项目，公司没有成功的先例可以参考；或者在一次团队合作中与同事出现了意见分歧等等…尽量与工作相关，描述地尽可能详细。
							- TASK:：任务，指描述你当时的任务，或在当时环境下你所承担的职责。比如你是这个项目的组织者、策划者，需要带领团队探索未知。或者你需要解决与同事之间的分歧，试图说服他听取你的意见等；或者是达成销售目标…
							- ACTION：行动，即表述你和你的团队如何克服挑战。
							- RESULT：结果，解释所采取的行动产生了什么结果，从中学到了什么。
							- 👨‍🌾总之，通过持续的复盘，不断去看自己、看生活，慢慢地，我们看的能力会提升，这种提升依靠的不是知识的积累、广博的见闻，而是心力的提升。
							- 心明才能眼亮，人的觉察力、洞察力并非来自于眼睛，而是来自于心。
							- 除了工作复盘，生活还有很多的场景，乃至人生都可以去复盘。
							- > 《回忆录系列》属于总结，并不是复盘
							- 苏格拉底说过：未经审视的人生是不值得过的。
							- 复盘，就是给自己一个检视自己的机会。
							- 没有复盘，我们就会在同一个坑里不断跌倒，重复犯错，没有经验积累。
							- 会复盘，才能翻盘。
							- 复盘，是成长最快的方法，是破除长久坚持没有结果的利器。
							- 愿你重视起复盘，并切实开始复盘。只有不断复盘，你才能不断进步。
							- PS.
							- 所谓“[[知识管理]]”，不同软件不同筛选“颗粒度”同属于复盘。“复”的是压缩/筛选 后的精进；“盘”的是结构逻辑；两者互补均不可缺，留下的只有常青。“粗筛”的第一层工具，根本不重要。
						- 【内心敏感型】在上述场景中描述没有问题。但是在更多的场景下【内心敏感】是一种特质。它能让我们观察到常人忽略的东西，会让我们更加有同理心。但是内心敏感也会给我们带来困扰 —— 很容易因为某些事情而情绪低落，感觉受到伤害 —— 甚至有些时候在别人看来是无心的、或者轻微的扰动，都会在我们内心掀起轩然大波，久久不能平息。
						  collapsed:: true
							- 碰到这种情况，焦躁、烦扰、难以入睡等等问题，都会进一步的侵扰我们，打破我们内心的平静。
							- 从病理（精神病学）和临床上的确会认定这是一种“病”，属于“精神疾病”的一种。在超敏反应下主要表现在精神分裂症、抑郁症和焦虑症这三种精神疾病，有些则会导致 —— 边缘型人格障碍（BPD）
							- 很好的治疗，只能从临床上。但是如何自救？则是今天要废的重点。
							- 我想对敏感的同志说，其实我们只要学会理解自己，学会与自己的敏感相处，学会不因自己的敏感而过度受伤，敏感会成为我们一生的财富。
							- 如何能让自己不因自己的敏感而过度受伤，我想每个敏感的人，都有自己的方法。我把“相对有效”的方法写出来，希望对内心敏感的“你们”，有点启发。
							- 1. 学会正念（Mindfulness）和冥想（Meditation），并保持绝对的习惯。
							- 冥想最大的好处是能在大脑中创造出一片空间，能把自己的「情绪怪兽」隔离在一个玻璃瓶中，你可以从纠缠的局面中跳脱出来，浮在半空中，观察自己。这样做的最终效果有两个：第一、你可以在外界的「刺激」和「内心反应」之间建立有效的缓冲；第二、你能很快入睡 —— 我知道很多敏感的人一旦受到某种刺激，是很容易失眠。如果能很快入睡 —— 只要能睡着，就会自我疗愈、自我恢复。如果不能很快入睡，说明你还没找到正确的冥想方式，需要多加学习。
							- 这里我也简单的提及过几次，这种学习每个人的心得不一，还需要正确的心态和方法来学习。比如 [呼吸](https://t.me/talkjfh/1042)，正念（Mindfulness）白天练习比较好，尤其是在利用间隔时间，譬如工作20分休息20秒 中的20秒。 而冥想（Meditation），最好是在固定时间固定场所（比如家中），在需要聚焦时或焦躁时去练习。
							- 2. 有自己常年坚持做的事情，比如写作和运动。
							- 写作和运动不仅仅是一种有益身心的习惯，而是一种内心的支柱。只要还能坚持写作、坚持运动，内心就会相对平静。甚至在内心有扰动的时候，可以通过写作与自己进行对话，可以通过运动来恢复状态。
							- 而这样的日常习惯之所以成为内心的支柱，更在于只要你能坚持做这件事，你就在告诉自己：无论外界如何，我依然可以掌控自己的生活节奏。所以，对于内心敏感的人，最好有一到两项日常习惯，是可以长期坚持、不受任何外界影响的。在没事的时候，这些习惯有益身心；在有事的时候，只要坚持这些习惯，你就是自己心目中的「强者」。
							- 3. 有可以倾诉心声，对你毫无保留支持的人。
							- 这种“人”或许对你来说，可遇不可求。是绝对的“朋友”，不可能是泛泛之交或网络上的某一 个/种 人，“树洞解答”无非是自欺欺人。
							- 尽量在现实生活中要找到一两个可以对Ta倾诉心声，而Ta对你毫无保留支持的人。其实在遭遇问题，自己已经缠绕其中，越陷越深，无法自拔的时候，能与这样的朋友聊一会，就会有很好的效果。
							- 如果一个人遭遇了重大的心理打击，他会陷入自闭的状态，不断地自我惩罚，拒绝与外界沟通。遇到这种状态，如果他能哭出来、说出来，就是巨大的进步，就是改变的机会。
							- 其实我们日常不会遇到上述程度的心理打击，但是类似的原理同样有效：只要我们愿意把心中的不解、愤懑甚至是愤怒说出来，就能极大地释放压抑在内心的能量。
							- 如果有这样的朋友，祝贺你。如果还没有，请找到Ta。
							- 4. 有进行自我对话的 方法/方式，并持之以恒。
							- 其实无论有没有可以倾诉的真朋友，「自我对话」都是一个必须掌握，而且不需要太多条件的事情。自我对话的方式有很多，在我看来 —— 包含抄书、深度阅读、涂鸦、日记、自言自语并录音等等都是自我对话的方式。
							- 比如，“你”可以拿小朋友的手写板，把自己的遭遇、心情都写下来。这种手写板的特点类似于「小黑板」，写完之后必须一键擦除，然后才可以继续写，也没有任何保存功能。一页一页写，开始写的是现象、是情绪，逐渐会变成对自己的安慰，最后变成更加理性的分析。
							- 写完之后，一键擦除，一键清空，什么都没有留下，但内心却轻松了很多。
							- 当然数字写作亦可，坐在电脑前“写作”也是一种自我对话，很多情绪，写着写着，就释然了。[语音笔记](https://t.me/talkjfh/4390)亦然，看似一时的自我对话，对自己、对他人都能产生长久的影响，这是「自我对话」额外的收获。
							- 写到这里，我只是在解释另外一种「自我对话」的意义，非文艺式自我感动式的情绪。
							- 5. 持续运动的习惯
							- 运动本身就具备有益身心的功效，对于敏感的人而言，运动也是一种自我对话。稳定地提升心率，保持呼吸节奏，就是与自己的身心沟通、对话。
							- 如果你特别敏感的话，最好每天都能坚持运动。只要运动习惯保持，会大大降低敏感带来的内心波动，偶尔遭受打击，也会更快地从中恢复。
							- 而如何理解跑步，如何“跑”，不是简单的理解 —— 有腿就行 。那就从[ #Jfitness  运动健康类]  https://t.me/talkjfh/1940 里查看。
							- 6. 一定要重视睡眠
							- 缺乏睡眠会带来很多问题，焦虑、易怒、白天状态差等等。缺乏睡眠对于一个容易敏感的人而言，更是雪上加霜。
							- 只要能睡好、睡足，平日的状态就会好很多。如果一段时间睡不够，睡眠质量差，就容易因敏感而状态不佳。而且前文说过，敏感的人会因为内心波澜而失眠。如果在内心有波动的时候，还能比较快地入睡，睡够睡好，就能很好地恢复。
							- 7. 养成向前看的习惯，释然、放手、和解。
							- 最后一点很重要，也很难。越是敏感的人，越要学会与自己和解，与现实和解，学会放手。这需要格局的提升，也没有特别有效的方法。
							- 我只能说 ——「子在川上曰，逝者如斯夫」。只要你在一条河流边曾经待过，曾经凝望过河流，会知道这是怎样的场景。任何事情、任何人，甚至包含我们自己在内，无论我们多么在意，都与我们眼前的这条河流一样，滚滚向前，不会对于现在有片刻留恋。
							- 所以，对于内心敏感的人，终极的解决方案就是—— 「让自己成为水，像水一样温柔，又像河流一样坚定」，俗称不要脸。
							- 👨‍🌾 这7条，多是关于坚持，关于自律；关于运动、关于睡觉。犹如我一直提倡的“读写说教”，亦是一种坚持。认识、了解自己，是每个人一生的课题。我认为，每个人都应该找到「自我的闭环」，实现「自我的闭环」对自己来说非常有价值与意义。「自我的闭环」亦是 —— 自知。
							- 自知后才能自洽，没有自知的自洽，且是虚妄。
						- 《观点和决策，很早就被你选择的环境锁定》© 王川
						  collapsed:: true
							- 试图扭转一个人的观点和决策，很大程度是徒劳的，远不如改变他的环境有效。
							- 他之所以会有这样的观点和决策，很大程度上早已在几年前被他接触的信息质量，所处的环境，所建立的思维模型，行动上投入的各种沉没成本，权衡利弊使用的价值标准给锁定。
							- 而且如果他只是计较反思短期的得失，而不检讨更长期的大环境和生态上的选择，那就永远无法真正吸取教训。
							- 如果他不幸陷入某个局部极值，除非是自己的儿子，否则你不可能自己耗费那么多能量帮他跳出那个坑。
							- 你选择的环境生态，远比你的具体决策更重要。流通不自由资源贫乏的环境里，需要拼命争抢，突破重围;  流动性通畅，资源丰富的环境里，需要等待，和科学的以逸待劳的定位。
							- 把资源贫乏的环境里有效的策略，习惯性带入到资源丰富的环境里，是一个悲剧。反之亦然。
							- 始终警惕防范任何限制流动性自由的约束，才能始终让自己处于资源丰富的环境之内。
							- 资源丰富约束少的环境里，可以有多种叙事并存，互不打扰，井水不犯河水。其他群体的很多争执，可以完全无视，乐作逍遥派。
							- 资源贫乏的环境里，一些关键资源的争夺，冲突不可避免，必须先下手为强。
							- 资源丰富的环境里，你习惯性争夺的资源，大概率存在或者涌现出新的替代品。你争夺得越努力，意味着你拉着大家一起竞争杀价的压力就越大，自己活生生把自己变成耗材，还不自知。
							- 👨‍🌾 社会学之所以不是科学，是因为“人是情境性动物”。出于适应赖以生存的环境角度，思想是很难突破既定的环境。在加上大环境受限，多数人的“选择”等等。不仅环境对人有影响，就连我们表达的语言同样对我们思维有影响。这就是我一直提倡“读写说”的重要性，如果你想“脱离”，根本不改变，永远改变不了。
						- 上述6种方法论里面，其实多数用于职场当中，用什么形式还是在于是什么类型的项目或流程。但是这些用于个人的项目计划也是没什么问题的，所以我举一些更贴近真实的案例。
						  collapsed:: true
							- 老话题—— “读了很多书，却依然不知道怎么过好这一生”？
							- 虽然大家可能都有各自的理解，但是老王（王阳明）的「 知行合一 」所述 —— “知”要能“行”出来才算“真知”。
							- > 这里会有一个很大的常识性错误，此“知行合一”非彼“知行合一”。老王提出的与 现在/当代的理解是不同的，具体请看 ： https://t.me/talkjfh/2440
							- 真实生活中很多时候“知”并不一定能行（况且你“知”很有可能不是正确的），所以知与行之间还是差着些东西的，那就是 —— “反复练习”
							- 读了书上的道理，没有按照书中所说的去“行”，又或者“行了一次两次，就放弃了”，这是因为“思维的链接”要被建立的话，需要反复的过程，没有反复的练习，思维依然会链接到旧有的链条中。
							- 成年人的思维链接很多已经固定了，要想凭着看几本书（只是知道，但不去实践），是不太可能将书本的知识转化为“智慧”的，智慧要靠“实践”书本上的知识，要靠“反复练习”才能拆解旧有的思维链接，建立新的思维链接。
							- 不过关于“反复练习”，也可以借鉴IT业（研发团队）的Scrum（敏捷项目管理框架）。一个项目被划分为一系列短周期的工作单元，称为Sprints，通常持续1-4周（月度）。
							- 每个Sprint的目标是交付可工作的产品增量（每一周均有增量）。强调迭代式增量开发，旨在短周期内完成目标并持续改进产品。
							- 大致的步骤分为Sprint Planning、Daily Scrum、Sprint开发、Sprint Review和Sprint Retrospective，这些步骤从另外一个 【PDCA 方法论】中的循环角度来看是这样的：
							- 1. 计划（Plan）：在Scrum中，这个阶段对应于Sprint Planning会议。团队基于Product Backlog选择一组User Stories或其他工作项作为Sprint Backlog。在这个过程中，团队明确计划在接下来的一个Sprint（一般为两周至三周）内要完成的工作。
							- 2. 执行（Do）：这是Sprint执行阶段，团队在这个时间段内开发产品功能，进行测试并准备演示。整个Sprint期间，团队专注于实现Sprint Backlog中所定义的目标。
							- 3. 检查（Check）：主要体现在两个方面：一是通过每日站立会议（Daily Scrum）对进度进行日常检查，同步进度、协调合作并解决障碍；二是Sprint Review，在此阶段，团队展示他们已完成的功能，并与其他利益相关者一起审查产品的增量成果，收集反馈。
							- 4. 行动（Act）：对应的是Sprint回顾会议（Sprint Retrospective），在该会议上，团队成员反思过去Sprint期间的工作方式，识别出哪些地方做得好，哪些需要改进，并制定具体的行动计划以便在下一个Sprint中实施改进措施。这个过程确保了团队能够不断地从实践经验中学习并提升效能。
							- 🤷你们看这种不就出来了？其实很简单。那么用于【个人时间管理】呢？
							- 1. 计划阶段（Plan）：个人可以根据当前的项目安排情况和个人的学习，生活安排来制定详细的计划，包含时间的分配，重要的事情在什么时候做，琐碎的事情是否可以统一划分到某个时间段一起做，与人沟通的事情要看别人的时间点等等，可以明确每日、每周乃至一个时间段内的工作和个人生活的目标，以及完成每个任务大约所需的时间；
							- 2. 执行阶段（Do）：严格执行计划，可以参考敏捷开发或番茄工作法等时间管理方法，确保每个时间段都能专注于手头的任务，远离容易产生干扰的环境或事物，减少干扰和中断。这里依然推荐大家尽可能的进入“心流”中做事情，因为在心流中，“我”被放的很小，更容易集中精力在当前的事情上，从而完成这件事情的效率和质量就相对没有进入心流（有分心时）更高些；
							- 3. 检查阶段（Check）：定期评估任务完成情况，使用书面记录的方式或一些工具软件，定期检查计划和现实之间的差距，以免差距已经很大了才发现，那时候补救就更困难。推荐用有书面的形式来记录（看似最笨最土的形式），而且最好能展示在视线经常可见的范围内，这样方便及时提醒自己。
							- 4. 行动阶段（Act）：分析制定的任务被很好完成或没有按时完成的原因，思考如何才能发扬优点继续完成更多任务，或者要如何改进某项任务才能赶上进度，以后要如何避免类似事情的发生。这是一个自我思绪整理的过程，比如某项任务已经安排很久，但是一直拖延不做，就要深入分析是为什么迟迟不能开始，是因为担心会失败，还是追求完美等等。只有找到原因，才能去思考解决，才能去实践解决方案是否奏效，这是一个不断实践、修正、循环，逐渐稳定的过程。
							- PDCA本质是一种科学的质量管理和持续改进的方法论，基于迭代改进的思想，鼓励在计划、执行、检查和行动四个阶段中形成闭环管理，确保每一个改进过程都是基于上一轮的反馈和学习并有增量。
							- 基于“实践结果”，调优后循环继续实践，这也是一条从“知”走到“行”的良好路线。
							- 只有不断地实践了，基于结果去修正如何继续行，如何更好的行，如何不受外界环境影响还能行，加上不断地循环过程可以加强新思维链条的建立，从而才能更好地将书本的知识转化为自己的“智慧”。
							- 六种方法论均非常好用，完全在于个人如果嵌套自身的使用场景及需求，不论是职场、生活或个人成长，都是很好的助力。
							- 但最最关键的还是需要 —— “知”要能“行”出来才算“真知”。
							- PS.
							- 🧐与职场实战或与管理相关的话题，均会带`#VAM`，所以要去【置顶文件】-> 【二级引索及专题内容】中 查看
						- 🧐Focus on improving yourself, not proving yourself.
						  collapsed:: true
							- 🚢  [[Ship 90 for 90]]  运作了几期，关于“写”的问题一直存在。
							- 再次告诫，我们“写”，不是为了“证明自己”可以“写”或能“写”。
							- 而是，专注于提高自己的基础能力。“写”只是基础能力的一部分，仅此而已。
							- “写”的效率，一是时间；二是习惯；三是坚持。
							- 有的时候兴致特别高，每天都有想写的内容，而且一坐下来就是几个小时，完全感觉不到时间的流逝。
							- 有的时候毫无兴致，摸着酸痛的肩颈和发麻的指尖，原本每天晚上会安排时间看看书、运动，但是现在一吃完晚餐就是坐着几个小时，写完就到深夜了。
							- 是否“你”会有以上两种情况？
							- 其实
							- 一、时间至关重要
							- 时间包含两方面要素，第一是相对充裕的时间，第二是相对固定的时段。
							- 其实做到连续日更，而且每篇文章平均在1k字-3k字以上，还是要得益于时间相对充裕。其实要做到时间管理或精力管理的第一步，需要培养自身每天要有1小时左右无间断的写作时间或运动时间。
							- 比如日常工作繁忙时，很难做到？那就要及时调整自身每天的【作息时间】，而不是苦熬。
							- 这是保持高质量、高频更新，至关重要的“点”。
							- 除了相对充裕的时间，【固定时段】也很重要。其实每天24小时，看起来都是均匀流逝，但是对于每个人而言，时间其实是不均衡的，尤其是个人精力、创造力不同。有些人在日出时分有灵感，有人在夜深人静时思如泉涌。比如晚上睡前写作，发现有两个障碍。第一是晚上10点之后比较困，第二是写作用力过猛，容易造成晚上失眠，得不偿失。
							- 简而言之，如果能在固定的时段，坐在固定的地方，打开熟悉的电脑，戴上耳机，听熟悉的音乐，会自然进入高效的写作状态。
							- 别小看这些细节，就是这些细节构成了一个适合写作的「环境」，有人称之为「场」。
							- 🤷当然，对于我个人而言，这些「环境与场」我并不在意，因为我已经习惯了随时进入状态。
							- 二、日程积累
							- “粗筛”的第一层工具，根本不重要。只要能够记录，不论什么载体、不论什么格式均可，自己熟悉，自己适应，好检索好查找均可。哪怕是flomo或Apple notes均可。
							- 你需要保持任何想法都可以随时记录，而不必担心其他人的反馈。
							- 有些正在思考还没有答案的问题，无论是自己想到的问题，或是与朋交流的问题，一定要及时记录上。
							- 培养一定习惯后，至少要在这个“载体”上有过一些小作文（短文）。要很放松地把相关的想法都写下来，比如超过达到300-500字的短文。
							- 好检索或好查找的本质是在于找相关记录，提炼tags，从中整合文章。
							- 在任何“写”的动机及基础问题上，我一直强调要学会“抄”。
							- 此“抄”非彼“抄”。
							- 指的是 —— 文章其实不是写出来的，是「孕育」出来的。通过自己的“文字小库”中 寻找/检索 、提炼、整合。这样写成的文字，既丰富多彩，又非常有趣，可以避免初期阶段写作时的「枯竭感」。
							- 如果“你”可以不间断，至少保持日更365天以上，此方法无效。
							- 👨‍🌾综上，“写”的入门基础即是：
							- 1. 先习惯积累任何“素材”，素材指的是 —— 任何所见所思所想（并不要求字数限制）
							- 2. “素材”从量到质变，指的是—— “质”等同于小短文（300-500字）
							- 3. 寻找/检索 、提炼、整合 = 孕育，亦是整合文章。
							- 如果你能做到天天日更且不间断（365天），说明可以“出师”。
							- 那么第二年，就应该扩大“生产力”来增值自身的数量与质量。我自己能日均w字（频道只是一部分产出），均得益于此。渐进、成就、总结、提升、复盘 不断的循环。
							- 临时想到，🚢  [[Ship 90 for 90]]  是不是对于多数人来说“难度过大”？
							- 其实可以压缩到 Reinvent Yourself in 100 Days ？其实这个名字也不错？
							- 但是载体（软件）没有想好，而且要进阶 [Campsite Rule](https://t.me/talkjfh/1654)，每天共两段话记录+两段文字，记录自己这一天的所思所想所做？最后构成自己 生命/成长 之书的手稿？
							- 🤷还没想好，只不过这种形式，门槛极低，没有任何“质”可言。如果你喜欢可以点🍓
				- 11:04 - 14:58 *234min*
				  collapsed:: true
					- #break #回忆
					  collapsed:: true
						- [昨天]([[May 13th, 2024]])[[失眠]]了，可能是[[咖啡]]喝太多了，我这副[[身体]]真的是要好好保养了。 -> ((6641f678-3ad6-47ca-a3da-9686fab76410))
					- #log
						- [[Family]]
						  collapsed:: true
							- 11:19 - 11:20 *1min*
							  collapsed:: true
								- #review
									- ((641fdb2c-ba71-4b9b-bd6b-2f69b288de71)) => 今天[老婆]([[王露]])开始[[填报志愿]]！好严肃的感觉哈哈哈哈。
						- 11:25 - 11:37 *12min*
						  collapsed:: true
							- #continue
								- ((6641f67e-d564-499e-a469-9b67176384e3)) => 好麻烦，手机app无法登录，可能是杭州的手机号，早tm注销了。去营业厅要带上社保卡
									- #必要条件
										- TODO 今天晚上[老婆]([[王露]])帮我找下我的[[社保卡]]
										  id:: 6642dc63-5b77-4f37-9153-396086a67b01
										  SCHEDULED: <2024-08-10 Sat>
				- 15:01 - 15:02 *1min*
				  collapsed:: true
					- #质疑
						- 去[[理发/修面]]花了400多，感觉不能再这么花钱了。我觉得80块钱或100块钱比较划算。
			- 14:59 - 14:59 *0min*
			  collapsed:: true
				- #review
					- ((6641f678-d850-4deb-8a58-7732f955697f)) => 好[[枯燥]]
			- 15:04
			  collapsed:: true
				- #闪念
					- DONE 毛总，我大概22、23号左右到广州，方便我们去叨扰啵[偷笑] [[百年堂阿胶/文慧]] 
					  id:: 664dd6e5-74de-4115-ac1c-25fdc2908002
					  SCHEDULED: <2024-05-23 Thu 23:00>
			- [[Family]]
			  collapsed:: true
				- 15:14 - 16:04 *50min*
				  collapsed:: true
					- #闪念
						- DONE 阿姨的合同 
						  SCHEDULED: <2024-05-14 Tue>
							- #word
								- [家政服务合同.docx](../assets/家政服务合同_1715673840415_0.docx)
			- 17:02
			  collapsed:: true
				- #continue
					- ((65f96bc5-bfa1-4323-ad7f-1768a97d3abd))
						- #摘录
							- ![企业微信截图_1cdb7bb6-651b-46ae-9c3a-5ef903d95ef4.png](../assets/企业微信截图_1cdb7bb6-651b-46ae-9c3a-5ef903d95ef4_1715677327687_0.png)
							- 各个负责人每周同步：
							- 1.小明：所有内外部最重要的事情进度
							- 2.王露：新品研发是否在进度，变动
							- 2.绍波：每周盘点一遍教具是否即将出现断货，提前一个月生产备货；
							- 3.咏江：1.客诉集中出现的问题，尤其是产品反馈；2.店铺各个页面的商品是否上架/断货需要下架（吃瘦商品页维护可以找雪婷，中医商品页可以找贺帅）
						- #review
							- ((6641f67c-62df-4dc1-8a54-22b2f36572af))
- [[Family]]
- [[Myself]]
	- 08:40 - 08:43 *3min*
	  collapsed:: true
		- #recycle
			- ((6641f6c7-7598-4f31-843b-d131589eba51)) => 没必要了，现实一点，成年人了，有什么过不去的事，有什么接纳不了的人。