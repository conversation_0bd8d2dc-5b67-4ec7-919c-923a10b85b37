- [[Society]]
- [[Family]]
- [[Myself]]
	- 11:29
		- #continue
		  collapsed:: true
			- ((66b1da3c-81d9-4340-bf5a-b2d9e9663f36))
				- #深思
					- 随着[[logseq db版]]的即将发布，在测试的过程中，由于 [[namespace]]的不兼容引发了我对于 [[logseq工作流]]的[[反思]]，加之在 [[Readwise reader]] 中，不断调整同步的 [[property]]时发现我的摘录毫无意义，还有打开所有页面，被链接最多的标签，是[[特殊标签]] …… 由此引发了两个好问题： #特殊标签 #logseq
					  id:: 66b1da3c-4eb6-4ec3-984d-6d81b2d34de2
						- 我真的需要那么多的[[特殊标签]]吗？我是否精力花在了定位，而忘记了输出？这种定位到底是无压力的，还是无形之中，造成了压力？—— 
						  logseq.order-list-type:: number
						  id:: 66b2f26b-a35f-4569-8b47-f4d9972fa7f5
							- 我必须按某种规范来记录笔记 —— 就连一个闪念，我都要敲3下回车，输入`11 1`，`#`之类的标签，才开始……
						- 我真的在做摘录吗？明显不是，我只是把看的文章、书全文划线罢了
						  logseq.order-list-type:: number
						- 对此我深感绝望。人的惰性，通过重复某种简单的范式就达到某种收获，实际却是无意义的多巴胺刺激。
						- 因此，我需要重新来规划 [[logseq工作流]]，以及合适的[[特殊标签]]，姑且称之 [[v3.0]]。
							- 根据内容模块分几个大类的话，就是：
								- [[输入]]- [zettel]([[zettel]])：
								  logseq.order-list-type:: number
									- 这是关于笔记的部分，在para里面，属于[[资源]]。
								- [[任务管理]]- [[gtd]]
								  logseq.order-list-type:: number
									- 在para里面，属于[[项目]]
								- [[输出]]- [[output]]
								  logseq.order-list-type:: number
									- 在 [[para]]里面，这部分应该属于[[领域]]，就是你发表了某一个领域的[[洞见]]。
										- 包括短文，也是推特之类的。
								- [[保险箱]]-[[账号]]
								  logseq.order-list-type:: number
								- 这是有顺序的， [[logseq]]首先是一款[[笔记软件]]，基于[[卡片笔记]]，可以无压的输入，[[持续不断记录，意义自然浮现]]。
									- 其次是[[任务管理]]，在输入的过程中，由念头，思考，引发的各种需要执行的任务。
									- 再次是各种[[备忘]]：账号、网址、纪念日等等。
									- 最后才是输出：短文，总结，回顾，长文
							- 难点在于，需要有一套[[特殊标签]]，来辅助每个内容的坐标，它不是用来查询用的，而是起到看上下文时，直到这段文字的意义
								- 并且，在输出、输入、任务管理之间，无缝穿插