- **[[Morning-Page]]**
- [[Society]]
- [[Family]]
- [[Myself]]
	- 11:34 - 22:57 *683min*
		- #continue
			- ((66b1da3c-81d9-4340-bf5a-b2d9e9663f36)) #logseq #工作流
			  id:: 66b70aaf-3866-4732-bf86-36f24cfafc9b
			  collapsed:: true
				- #step
					- DONE [[测试]][[page]]：[[February 9th, 2023]]
					  id:: 66b4e299-08d4-4164-af79-0d810601e484
					- CANCELED 23:22 - 但如何跟gtd结合的很好还要再思考下
					  id:: 08dddb95-ca58-422d-bdbc-c165c788ee1f
					- CANCELED 23:22 - 可以用排除法，确定什么[[定位标签]]是必须要用的
					  id:: c550d47e-c123-40e5-89f7-c14479413424
					- CANCELED 23:22 - 拆掉思维的墙，可以尝试拆掉卡片盒的[[定位标签]]了
					  id:: ba6c468e-6532-491f-b32d-5bae9fd2aceb
				- #深思
				  collapsed:: true
					- 关于各种[[特殊标签]]的思考：
					  id:: 66b4d660-77d9-4f45-8d81-4c1a029197d1
						- [[摘录]]
						  id:: 66b4573a-86a8-4a46-bb7f-4123fdbc4f78
						  collapsed:: true
							- 混和了太多的 [[TODO]] 和 [[Highlights]]了，而且很多的摘录下的内容，既没有todo，也没有真正读过，仅仅是剪切而已。应该做下区分：**[[摘录]]是特别用于阅读的 [[gtd]]， [[Highlights]] 是结果，和 [[Readwise]] 共用。**
								- #then
									- CANCELED 整理 [[摘录]] 和 [[Highlights]] 以作区分：哪些是已读的，哪些是没读的
									  id:: 66b44433-a7b2-4621-a2e0-1c01723cb7b7
						- [[Comments]]
						  collapsed:: true
							- 里面就不用区分卡片头了，而是要多打内容标签，也是和 [[Readwise]] 共用。
						- [[gtd]]
						  collapsed:: true
							- todo、done、cancel等任务状态下，不能跟卡片盒。思考都用 [[block-ref]] + tag 的方式来记录。
						- [[大纲]]其实就是[[深思]]？
						  collapsed:: true
							- 当我面临一个[[选题]]或者一个 [[TODO]]之后，无论是思考怎么做，还是有其他的想法要记录，那不都是深思吗。当然，[[回忆]]、[[质疑]]、[[肯定]]也是可以并集存在的
						- 想读
						  collapsed:: true
							- 不符合思考心智，[[书单]]本身就很不错，而且todo的第一行，只要有书名就可以了，不用每次都强行插入想读这个标签，因此，[[书单]]是个[[特殊标签]]。
								- #then
									- DONE 整理[[书单]]，全部换成[[书单]]
						- 这些都是小问题 -> ((66b4d660-77d9-4f45-8d81-4c1a029197d1)) => 大[[问题]]是：我在 [[journal-page]] 上用力过猛了。
						  id:: 66b70aaf-dffe-417f-8865-c8e52827e1e7
						  collapsed:: true
							- [[Myself]]、 [[Society]]、 [[Family]]，这种都是没有必要的分类，因为 [[logseq]]就是我的[[个人笔记]]。
							- 只要在全部页面种，看起来没有查询意义（辅助筛选也不算），同时双链又很高的，都是有问题的。
							- [[常青笔记]]的理念是有用的，不整理的卡片，丢在 [[journal-page]] 里就是[[垃圾]]。
								- 而我还在强迫自己用一套框架让日志看起来美观。实际上却阻碍的了我去持续的记录。
							- [[卡片笔记]]， [[logseq]]的核心主力能力是你没有任何主力的写写写。这点不能被打破！
							- 所以不必管 [[journal-page]]排版美观不美观！[[重器轻用]]啊！
							- 我想要回到最初， [[Conor]] 那[[行云流水]]般的[[输入]]的状态，在 22:47 这一刻，我确定了[[特殊标签]]还是不能丢掉。
		- [[log]]
			- 13:58 - 14:06 *8min*
			  collapsed:: true
				- #review
					- ((6353a253-d033-4d48-aa77-77492adb4c3a))
						- #recycle
							- ((66b35ec6-c924-4a0d-b3eb-26ced6048192))
						- ((66b35ec6-9f81-4707-8536-88a556c8f19b))
						- ((66b35ec6-c8b8-4ba9-8f0e-cd0360962f78))
			- 14:06 - 16:52 *166min*
			  collapsed:: true
				- #闪念
					- 被[[于淼]]拉进听[[丽娜]]的[[用户研究与创新]]的[[转训]]
						- #书单
							- TODO [[与运气竞争]]
							- ((64db6abf-55f9-471b-84c8-e619ad6ca2b6))
							- TODO [[人人都是产品经理2.0]]
							- TODO [[启示录]]
						- [[统计学]][[概念]]：方差、、决策树
							- [[回归分析]]
			- 16:52
			  collapsed:: true
				- #continue
					- ((66209618-f3b5-41cf-9179-0db4841f6061))
				- #log
					- 17:12
						- #闪念
							- TODO 人才盘点：本次主要是盘点前后端经理级以上，以及全部内容、运营、中台职能岗位，与年中创业奖有关。内容之前已经盘点过了，本次会再拉齐一次项目之间的人才校准
			- 21:32
			  collapsed:: true
				- 晚上和[老婆]([[王露]])[[回家]]，聊起过去[[创业]]和如今[[打工]]，有什么不同，一种答案是：很不同。创业需要无限进步，无限的承担；打工则需要安全，需要迂回，在逆风时懂得明哲保身，每个人都为自己的利益考虑。 #成功 #职场 #专业 #视角 #格局 #职场 #创业 #打工 #人 #成功 #专业 #团队 #文化 #竞争 #战队 #负责人 #格局 #视角 #方法论 #布局
					- [我]([[毛义明]])想想，其实[[创业和打工的区别]]，真的不在于进取的程度还是保全自己的程度。那是因为过去我们所面对的创业都是：[[毕其功于一役]]，这本身是有问题的。换个视角看[[问题]]：[我]([[毛义明]])创业和[[朱峻修]]创业，同样是创业，我俩的区别在哪？
						- 我觉得区别就在于管理团队的方式。管理团队过去我只知道两种方式：家文化；战队文化。
						  collapsed:: true
							- 我肯定是否定家文化的。我的个性，我的经验，我都适合打造[[战队文化]]。
						- 但是，[[朱峻修]]打造的是，[[内部竞争文化]]，或者说[[负责人文化]]，这个科学的定义叫什么，我不清除，但从技术上来说，我就已经输了。
						  collapsed:: true
							- 因为我只有一支以我为核心的战队。我也许是教练，也许是赛场主力，总之，我只有一个战队。
							- 而他，手上有多支战队，时刻想着培养更多的战队，手上的菜（资源），绝不放在一个篮子里，要资源得抢才行，得靠实力证明自己才行。
								- 这种文化导致手底下的人都需要通过证明拿到资源、拿到信任。
								- 永远没有绝对的信任，也没有绝对的授权。
							- 这很凶啊！
						- 这真的是[[兴趣岛]]的[[可取之处]]：过去我有200万，我也招了20个人，也需要在1年内把钱花光。但是我只有1支战队，并且我就是这个战队的核心，所有压力、风险都我自己扛 …… 我完全可以，招3个总监，分别配他们3-5个人，要他们自己负责自己的业务，趟出一条活路来。
						  collapsed:: true
							- 这真的很妙。一支战队时，你没有选择。多支战队，你就有了选择。并且有了竞争，在资源配给不充足的情况下，优胜劣汰，自然选择，能者居之。
								- 术法道，术法道，我的就是低劣的术，他是法，是[[布局]]啊！
								- 能打赢一场仗，不代表能布赢一个局。更重要的是，自己的团队管理，组织架构也是要布局的。
									- 我的方式只能招来一群“主管”，然后逼迫他们成长，因为他们每天面对的都是[[险仗]]，所以我带的人，各个都是[[耐操]][[能打]]。
										- 而[[朱峻修]]，能把一群[[平庸之辈]]用到合适的地方，发挥群体竞争效应，就能在大局上赢。
											- 因为平庸之辈的优势在于好管稳定。而只要[[视角]]更高一点，就会发现要[[打胜仗]]并不是必须要[[险仗]]才行，走对方向，跟对时势，每个团队都去运用公司有限的资源去抢外部的机会，这tm就能赢啊。
												- 想起了我的 [[mbti]] 是 [[entj]]，视角太低，太低，处处追求刺激、追求[[富贵险中求]]，这是不行的。
						- 所以总结下，并不是过去[[创业]]和如今[[打工]]有很大的不同，而是：
						  id:: 66b4e299-ab68-4c6b-92f7-b299c630aab2
							- 从老板角度，说创造的团队文化不同：在[[兴趣岛]]，我学到的是[[内部竞争文化]]，或者叫[[负责人制]]，或者叫[[封峰制]]。这明显比[[战队文化]]更高级，看似授权实际权力都掌握在自己手上，看似给机会实际资源都是要给成绩更好的人，老板不必亲自下战场，在后台[布好内部竞争的局]([[布局]])就能开花结果。很妙！
							- 从个人角度，个性的不同带来的是格局的不同，是视角的不同。我面对商业，永远是追求冒险、极致、钻研、探索。所以我是个常胜将军。而他面对商业，视角上去一层，看的是方向，是机会，是大势。所以他是乘风将军。乘风只需要平庸的团队用到极致便可成功。
							- 乘风+团队竞争，这是他的[[方法论]]。我却还没有，所以仍然需要向外学习，向内探索。
							- 此为第一点。
					- 第二点，今天由[[于水行]]在那搞什么[[人才盘点]]想到的，我过去一直在理解的[[问题]]：一个企业里，一个项目，一件事情的成功，究竟起决定性作用的是什么？[[答案]]是[[人]]！
						- 对没错，关键是人，不是这个公司的流程、机制有多么完善，团队学到了多么先进的技术，就是在关键事情上，有一个人站出来，搞定了一些关键的事，就这么简单！
						- 但是矛盾的是，这种人明显不是你我他，我觉得这是靠运气的，老板有遇到人才的运气，人才有遇到机会的运气，恰好彼此又互相信任，时机又对，事情，成了！你成了那个改变局势，或者帮助猪起飞的人！
						- [[董宇辉]]、[[东方小孙]]莫不都是如此。
					- 所以，这得靠命，在运气没到之前，先做好第三点：先成为一个专业（职业）的人。这至少能让你能够在企业里不被讨厌的坦然的生存下去哈哈。
- 23:06
  collapsed:: true
	- #闪念
		- 思考的手术刀 #比喻 #特殊标签
- [[Comments]]
	- [[2024/08/10]]
	  collapsed:: true
		- #质疑
			- [[不折腾]]了。 => ((66b1da3c-81d9-4340-bf5a-b2d9e9663f36)) #logseq #工作流 #生产力工具
				- 在花了好几天试用 [[RoamResearch]]、 [[tana]]，我tm甚至一度删除了tana的[[邀请账号]]又去闲鱼花了160买了2个…… 忽然[[悟了]]：
				  id:: 66b71010-95a3-4add-aee5-4b185a73f6ef
					- [[RoamResearch]]是[[粉丝向]]工具，它所有的功能优化都是基于认同他说倡导的[[工作流]]展开的。
						- 底层功能是简单的，内容迁移应该也不复杂。
						- 复杂的是在于它的一套使用逻辑，你要用好，你就必须去学习它的操作流程。
					- [[logseq]]先是copy roam，现在的 [[db版]] 又是copy tana，从工作流延伸的功能来说，没有roam那么精细，但迭代到现在，也是可以用了。而未来，应该也不会完全成为 [[tana]]，它的创始人是程序员，应该会从程序的角度去考虑软件的简单。
					  collapsed:: true
						- 而且它免费，真的属于是性价比很高了。
						- 退一万步来说，目前这个版本，不更新，也够用。没有roam的优雅，但有roam没有的：gtd管理，闪卡等。
							- 这就是国人的智慧吧：未必有能力规划清楚最底层的架构（roam是page+block+property；tana是node数据库），但在中间层，拿到一套架构，不断完善、迭代这是我们最强的…… [*](((66b70afa-ed90-4e7f-947b-ad32f2b2af4f))) [*](((62ec8103-f45d-48cc-9518-299df895ca91))) [*](((66b70b0c-8267-4a79-a4b2-5185c54351d5))) #抄作业
					- [[tana]]，这个是比较终极的，说实话要不是它[[邀请账号]]还没发我，[[此时此刻]]，这篇内容我应该已经在 [[tana]]上写了。它的好处有几个：
						- 基于更细的粒度 [[node]]，开发出的一套系统，完完整整的：roam+notion，是底层架构，即可把玩，又可即是输出，可玩性太高太高。
							- 当然，这也是缺点，我觉得没有个个把月，这套东西是吃不下来的。
								- 而且不知道它目前的功能完善到什么程度了。
						- 浏览器端、移动端同步、体验都很棒，除了特效花里胡哨，体验真的秒杀 [[RoamResearch]]、[[logseq]]
					- 以上，既然看到了终点，我也看到了我在 [[logseq]]上其实[[输入]]、[[输出]]的远远不够，所以没有必要去纠结那些问题，继续用，继续写，完事儿了。[[小问题]]，都是[[小问题]]。
						- #recycle
							- ((66b70aaf-dffe-417f-8865-c8e52827e1e7))