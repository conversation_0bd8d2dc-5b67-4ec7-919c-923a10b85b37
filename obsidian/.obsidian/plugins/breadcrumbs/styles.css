.BC-trail {
  border: 1px solid var(--background-modifier-border);
  border-radius: 5px;
  padding: 5px;
  margin-bottom: 5px !important;
}

/* completely hides the trail when "no path found message" is left empty and no path is found */
.BC-trail:empty {
  display: none;
}

.BC-matrix-square li {
  text-align: left;
}

/* ensure empty headers not leaving an element  */
.BC-Matrix .BC-Matrix-square .BC-Matrix-header:empty {
  display: none;
}

.internal-link.BC-Link {
  color: var(--text-accent);
}

.internal-link.BC-Link:hover {
  color: var(--text-accent-hover, var(--text-accent));
}

.vis-view-options > * {
  padding: 5px;
}

/* Source: https://svelte.dev/repl/3153faf7584d40bd8ddebecf39f24ac1?version=3.41.0 */
[data-tooltip] {
  position: relative;
  /* z-index: 2; */
  /* display: block; */
}

[data-tooltip]:before,
[data-tooltip]:after {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
  transition: 0.2s ease-out;
  transform: translate(-50%, 5px);
}

[data-tooltip]:before {
  position: absolute;
  top: 80%;
  /* bottom: 100%; */
  left: 50%;
  margin-bottom: 5px;
  padding: 7px;
  width: fit-content;
  height: fit-content;
  min-width: 200px;
  min-height: 200px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background-color: var(--background-primary);
  color: var(--text-normal);
  content: attr(data-tooltip);
  text-align: center;
  font-size: var(--font-medium);
  line-height: 1.2;
  transition: 0.2s ease-out;
  white-space: pre-line;
  z-index: 100;
}

/* [data-tooltip]:after {
  position: absolute;
  top: 80%;
  left: 50%;
  width: 0;
  border-top: 5px solid #000;
  border-top: 5px solid hsla(0, 0%, 20%, 0.9);
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  background-color: red;
  content: " ";
  font-size: 0;
  line-height: 0;
} */

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
  visibility: visible;
  opacity: 1;
  transform: translate(-50%, 0);
}
[data-tooltip="false"]:hover:before,
[data-tooltip="false"]:hover:after {
  visibility: hidden;
  opacity: 0;
}

.thread-dir-templates .setting-item-control {
  display: flex;
  flex-direction: column;
}

.juggl-hide {
  display: none;
}

[data-type="BC-matrix"] div.view-content {
  overflow: hidden !important;
}
