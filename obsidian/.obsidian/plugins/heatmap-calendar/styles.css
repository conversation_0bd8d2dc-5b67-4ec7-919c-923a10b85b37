/* Obsidian/DataviewJS Github inspired calendar by <PERSON> - https://richard.sl/ */

.heatmap-calendar-graph>* {
  padding: 0px;
  margin: 0px;
  list-style: none;
}

.heatmap-calendar-graph {
  font-size: 0.65em;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-areas:
    'year months'
    'days boxes';
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica,
    Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';
  width: 100%;
  padding: 5px; /* 0px caused overflow and scrollbars */
}

.heatmap-calendar-graph ul {
	padding-inline-start: 0;
	margin-block-start: 0;
	margin-block-end: 0;
}

.heatmap-calendar-months {
  display: grid;
  grid-template-columns: repeat(12, minmax(0, 1fr));
  grid-area: months;
  margin-top: 0.1em;
  margin-bottom: 0.3em;
  grid-gap: 0.3em;
}

.heatmap-calendar-days {
  grid-area: days;
  margin-left: 0.1em;
  margin-right: 0.3em;
  white-space: nowrap;
}

.heatmap-calendar-boxes {
  grid-auto-flow: column;
  grid-template-columns: repeat(53, minmax(0, 1fr));
  grid-area: boxes;
}

.heatmap-calendar-days,
.heatmap-calendar-boxes {
  display: grid;
  grid-gap: 0.3em;
  grid-template-rows: repeat(7, minmax(0, 1fr));
}

.heatmap-calendar-year {
  grid-area: year;
  font-weight: bold;
  font-size: 1.2em;
}

/* only label three days of the week */
.heatmap-calendar-days li:nth-child(odd) {
  visibility: hidden;
}

.heatmap-calendar-boxes li {
  position: relative;
  font-size: 0.75em;
  background-color: #ebedf0;
  width: 100%;
  margin-inline-start: auto !important;
}

.theme-dark .heatmap-calendar-boxes .isEmpty {
  background: #333;
}

.heatmap-calendar-boxes li:not(.task-list-item)::before {
  content: unset;
}

.heatmap-calendar-boxes .internal-link {
  text-decoration: none;
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
}

.heatmap-calendar-boxes .today {
  border: solid 1px rgb(61, 61, 61);
}

/* Settings */

.heatmap-calendar-settings-colors__color-box {
    width: 10px;
    height: 10px;
    display: inline-block;
    margin: 0 5px;
}

.heatmap-calendar-settings-colors__color-box:first-child {
	margin-left: 0;
}

.heatmap-calendar-settings-colors__color-name {
    display: inline-block;
}

.heatmap-calendar-settings-colors__container {
	align-items: center;
	border-top: 1px solid var(--background-modifier-border);
	display: flex;
	justify-content: space-between;
	padding: 0.5em 0;
}

.heatmap-calendar-settings-colors__container h4 {
	margin: 0.5em 0;
}

.heatmap-calendar-settings-colors__new-color-input-container {
	display: flex;
	justify-content: space-between;
}

.heatmap-calendar-settings-colors__new-color-input-container input {
	margin-right: 1em;
}

.heatmap-calendar-settings-colors__new-color-input-container input {
	margin-right: 1em;
}

.heatmap-calendar-settings-colors__new-color-input-value {
	flex-grow: 1;
}





/* ----
/* ---- DEV Only ---- */
/* ----

/* for putting the settings window off to the side whilst taking screenshots */
/*
.modal-container {
  justify-content: left !important; 
  padding-left: 20px !important;
}
.modal-bg {
  opacity: 0% !important;
}
.modal-container.mod-dim .modal {
  box-shadow: var(--shadow-s) !important;
}
.modal.mod-sidebar-layout {
  width: 700px !important;
}
.vertical-tab-content::-webkit-scrollbar {
  display: none;
}
*/

/* ----
/* ---- DEV Only /END ---- */
/* ----