/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Dd=Object.create;var Ai=Object.defineProperty;var Id=Object.getOwnPropertyDescriptor;var Fd=Object.getOwnPropertyNames;var Ld=Object.getPrototypeOf,Pd=Object.prototype.hasOwnProperty;var z=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports),Oe=(n,e)=>{for(var t in e)Ai(n,t,{get:e[t],enumerable:!0})},cs=(n,e,t,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of Fd(e))!Pd.call(n,r)&&r!==t&&Ai(n,r,{get:()=>e[r],enumerable:!(i=Id(e,r))||i.enumerable});return n};var Jr=(n,e,t)=>(t=n!=null?Dd(Ld(n)):{},cs(e||!n||!n.__esModule?Ai(t,"default",{value:n,enumerable:!0}):t,n)),Md=n=>cs(Ai({},"__esModule",{value:!0}),n);var Po=z((fw,ql)=>{"use strict";ql.exports=Ch;function Ch(n,e){for(var t=new Array(arguments.length-1),i=0,r=2,o=!0;r<arguments.length;)t[i++]=arguments[r++];return new Promise(function(s,l){t[i]=function(p){if(o)if(o=!1,p)l(p);else{for(var m=new Array(arguments.length-1),g=0;g<m.length;)m[g++]=arguments[g];s.apply(null,m)}};try{n.apply(e||null,t)}catch(c){o&&(o=!1,l(c))}})}});var Zl=z(zl=>{"use strict";var Ji=zl;Ji.length=function(e){var t=e.length;if(!t)return 0;for(var i=0;--t%4>1&&e.charAt(t)==="=";)++i;return Math.ceil(e.length*3)/4-i};var Tn=new Array(64),Yl=new Array(123);for(Ze=0;Ze<64;)Yl[Tn[Ze]=Ze<26?Ze+65:Ze<52?Ze+71:Ze<62?Ze-4:Ze-59|43]=Ze++;var Ze;Ji.encode=function(e,t,i){for(var r=null,o=[],a=0,s=0,l;t<i;){var c=e[t++];switch(s){case 0:o[a++]=Tn[c>>2],l=(c&3)<<4,s=1;break;case 1:o[a++]=Tn[l|c>>4],l=(c&15)<<2,s=2;break;case 2:o[a++]=Tn[l|c>>6],o[a++]=Tn[c&63],s=0;break}a>8191&&((r||(r=[])).push(String.fromCharCode.apply(String,o)),a=0)}return s&&(o[a++]=Tn[l],o[a++]=61,s===1&&(o[a++]=61)),r?(a&&r.push(String.fromCharCode.apply(String,o.slice(0,a))),r.join("")):String.fromCharCode.apply(String,o.slice(0,a))};var Wl="invalid encoding";Ji.decode=function(e,t,i){for(var r=i,o=0,a,s=0;s<e.length;){var l=e.charCodeAt(s++);if(l===61&&o>1)break;if((l=Yl[l])===void 0)throw Error(Wl);switch(o){case 0:a=l,o=1;break;case 1:t[i++]=a<<2|(l&48)>>4,a=l,o=2;break;case 2:t[i++]=(a&15)<<4|(l&60)>>2,a=l,o=3;break;case 3:t[i++]=(a&3)<<6|l,o=0;break}}if(o===1)throw Error(Wl);return i-r};Ji.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)}});var Vl=z((mw,Gl)=>{"use strict";Gl.exports=Qi;function Qi(){this._listeners={}}Qi.prototype.on=function(e,t,i){return(this._listeners[e]||(this._listeners[e]=[])).push({fn:t,ctx:i||this}),this};Qi.prototype.off=function(e,t){if(e===void 0)this._listeners={};else if(t===void 0)this._listeners[e]=[];else for(var i=this._listeners[e],r=0;r<i.length;)i[r].fn===t?i.splice(r,1):++r;return this};Qi.prototype.emit=function(e){var t=this._listeners[e];if(t){for(var i=[],r=1;r<arguments.length;)i.push(arguments[r++]);for(r=0;r<t.length;)t[r].fn.apply(t[r++].ctx,i)}return this}});var nc=z((hw,tc)=>{"use strict";tc.exports=Kl(Kl);function Kl(n){return typeof Float32Array!="undefined"?function(){var e=new Float32Array([-0]),t=new Uint8Array(e.buffer),i=t[3]===128;function r(l,c,p){e[0]=l,c[p]=t[0],c[p+1]=t[1],c[p+2]=t[2],c[p+3]=t[3]}function o(l,c,p){e[0]=l,c[p]=t[3],c[p+1]=t[2],c[p+2]=t[1],c[p+3]=t[0]}n.writeFloatLE=i?r:o,n.writeFloatBE=i?o:r;function a(l,c){return t[0]=l[c],t[1]=l[c+1],t[2]=l[c+2],t[3]=l[c+3],e[0]}function s(l,c){return t[3]=l[c],t[2]=l[c+1],t[1]=l[c+2],t[0]=l[c+3],e[0]}n.readFloatLE=i?a:s,n.readFloatBE=i?s:a}():function(){function e(i,r,o,a){var s=r<0?1:0;if(s&&(r=-r),r===0)i(1/r>0?0:2147483648,o,a);else if(isNaN(r))i(2143289344,o,a);else if(r>34028234663852886e22)i((s<<31|2139095040)>>>0,o,a);else if(r<11754943508222875e-54)i((s<<31|Math.round(r/1401298464324817e-60))>>>0,o,a);else{var l=Math.floor(Math.log(r)/Math.LN2),c=Math.round(r*Math.pow(2,-l)*8388608)&8388607;i((s<<31|l+127<<23|c)>>>0,o,a)}}n.writeFloatLE=e.bind(null,Xl),n.writeFloatBE=e.bind(null,Jl);function t(i,r,o){var a=i(r,o),s=(a>>31)*2+1,l=a>>>23&255,c=a&8388607;return l===255?c?NaN:s*(1/0):l===0?s*1401298464324817e-60*c:s*Math.pow(2,l-150)*(c+8388608)}n.readFloatLE=t.bind(null,Ql),n.readFloatBE=t.bind(null,ec)}(),typeof Float64Array!="undefined"?function(){var e=new Float64Array([-0]),t=new Uint8Array(e.buffer),i=t[7]===128;function r(l,c,p){e[0]=l,c[p]=t[0],c[p+1]=t[1],c[p+2]=t[2],c[p+3]=t[3],c[p+4]=t[4],c[p+5]=t[5],c[p+6]=t[6],c[p+7]=t[7]}function o(l,c,p){e[0]=l,c[p]=t[7],c[p+1]=t[6],c[p+2]=t[5],c[p+3]=t[4],c[p+4]=t[3],c[p+5]=t[2],c[p+6]=t[1],c[p+7]=t[0]}n.writeDoubleLE=i?r:o,n.writeDoubleBE=i?o:r;function a(l,c){return t[0]=l[c],t[1]=l[c+1],t[2]=l[c+2],t[3]=l[c+3],t[4]=l[c+4],t[5]=l[c+5],t[6]=l[c+6],t[7]=l[c+7],e[0]}function s(l,c){return t[7]=l[c],t[6]=l[c+1],t[5]=l[c+2],t[4]=l[c+3],t[3]=l[c+4],t[2]=l[c+5],t[1]=l[c+6],t[0]=l[c+7],e[0]}n.readDoubleLE=i?a:s,n.readDoubleBE=i?s:a}():function(){function e(i,r,o,a,s,l){var c=a<0?1:0;if(c&&(a=-a),a===0)i(0,s,l+r),i(1/a>0?0:2147483648,s,l+o);else if(isNaN(a))i(0,s,l+r),i(2146959360,s,l+o);else if(a>17976931348623157e292)i(0,s,l+r),i((c<<31|2146435072)>>>0,s,l+o);else{var p;if(a<22250738585072014e-324)p=a/5e-324,i(p>>>0,s,l+r),i((c<<31|p/4294967296)>>>0,s,l+o);else{var m=Math.floor(Math.log(a)/Math.LN2);m===1024&&(m=1023),p=a*Math.pow(2,-m),i(p*4503599627370496>>>0,s,l+r),i((c<<31|m+1023<<20|p*1048576&1048575)>>>0,s,l+o)}}}n.writeDoubleLE=e.bind(null,Xl,0,4),n.writeDoubleBE=e.bind(null,Jl,4,0);function t(i,r,o,a,s){var l=i(a,s+r),c=i(a,s+o),p=(c>>31)*2+1,m=c>>>20&2047,g=4294967296*(c&1048575)+l;return m===2047?g?NaN:p*(1/0):m===0?p*5e-324*g:p*Math.pow(2,m-1075)*(g+4503599627370496)}n.readDoubleLE=t.bind(null,Ql,0,4),n.readDoubleBE=t.bind(null,ec,4,0)}(),n}function Xl(n,e,t){e[t]=n&255,e[t+1]=n>>>8&255,e[t+2]=n>>>16&255,e[t+3]=n>>>24}function Jl(n,e,t){e[t]=n>>>24,e[t+1]=n>>>16&255,e[t+2]=n>>>8&255,e[t+3]=n&255}function Ql(n,e){return(n[e]|n[e+1]<<8|n[e+2]<<16|n[e+3]<<24)>>>0}function ec(n,e){return(n[e]<<24|n[e+1]<<16|n[e+2]<<8|n[e+3])>>>0}});var Mo=z((exports,module)=>{"use strict";module.exports=inquire;function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(n){}return null}});var rc=z(ic=>{"use strict";var $o=ic;$o.length=function(e){for(var t=0,i=0,r=0;r<e.length;++r)i=e.charCodeAt(r),i<128?t+=1:i<2048?t+=2:(i&64512)===55296&&(e.charCodeAt(r+1)&64512)===56320?(++r,t+=4):t+=3;return t};$o.read=function(e,t,i){var r=i-t;if(r<1)return"";for(var o=null,a=[],s=0,l;t<i;)l=e[t++],l<128?a[s++]=l:l>191&&l<224?a[s++]=(l&31)<<6|e[t++]&63:l>239&&l<365?(l=((l&7)<<18|(e[t++]&63)<<12|(e[t++]&63)<<6|e[t++]&63)-65536,a[s++]=55296+(l>>10),a[s++]=56320+(l&1023)):a[s++]=(l&15)<<12|(e[t++]&63)<<6|e[t++]&63,s>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,a)),s=0);return o?(s&&o.push(String.fromCharCode.apply(String,a.slice(0,s))),o.join("")):String.fromCharCode.apply(String,a.slice(0,s))};$o.write=function(e,t,i){for(var r=i,o,a,s=0;s<e.length;++s)o=e.charCodeAt(s),o<128?t[i++]=o:o<2048?(t[i++]=o>>6|192,t[i++]=o&63|128):(o&64512)===55296&&((a=e.charCodeAt(s+1))&64512)===56320?(o=65536+((o&1023)<<10)+(a&1023),++s,t[i++]=o>>18|240,t[i++]=o>>12&63|128,t[i++]=o>>6&63|128,t[i++]=o&63|128):(t[i++]=o>>12|224,t[i++]=o>>6&63|128,t[i++]=o&63|128);return i-r}});var ac=z((yw,oc)=>{"use strict";oc.exports=Dh;function Dh(n,e,t){var i=t||8192,r=i>>>1,o=null,a=i;return function(l){if(l<1||l>r)return n(l);a+l>i&&(o=n(i),a=0);var c=e.call(o,a,a+=l);return a&7&&(a=(a|7)+1),c}}});var lc=z((bw,sc)=>{"use strict";sc.exports=ye;var ri=tt();function ye(n,e){this.lo=n>>>0,this.hi=e>>>0}var rn=ye.zero=new ye(0,0);rn.toNumber=function(){return 0};rn.zzEncode=rn.zzDecode=function(){return this};rn.length=function(){return 1};var Ih=ye.zeroHash="\0\0\0\0\0\0\0\0";ye.fromNumber=function(e){if(e===0)return rn;var t=e<0;t&&(e=-e);var i=e>>>0,r=(e-i)/4294967296>>>0;return t&&(r=~r>>>0,i=~i>>>0,++i>4294967295&&(i=0,++r>4294967295&&(r=0))),new ye(i,r)};ye.from=function(e){if(typeof e=="number")return ye.fromNumber(e);if(ri.isString(e))if(ri.Long)e=ri.Long.fromString(e);else return ye.fromNumber(parseInt(e,10));return e.low||e.high?new ye(e.low>>>0,e.high>>>0):rn};ye.prototype.toNumber=function(e){if(!e&&this.hi>>>31){var t=~this.lo+1>>>0,i=~this.hi>>>0;return t||(i=i+1>>>0),-(t+i*4294967296)}return this.lo+this.hi*4294967296};ye.prototype.toLong=function(e){return ri.Long?new ri.Long(this.lo|0,this.hi|0,!!e):{low:this.lo|0,high:this.hi|0,unsigned:!!e}};var Ct=String.prototype.charCodeAt;ye.fromHash=function(e){return e===Ih?rn:new ye((Ct.call(e,0)|Ct.call(e,1)<<8|Ct.call(e,2)<<16|Ct.call(e,3)<<24)>>>0,(Ct.call(e,4)|Ct.call(e,5)<<8|Ct.call(e,6)<<16|Ct.call(e,7)<<24)>>>0)};ye.prototype.toHash=function(){return String.fromCharCode(this.lo&255,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,this.hi&255,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)};ye.prototype.zzEncode=function(){var e=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^e)>>>0,this.lo=(this.lo<<1^e)>>>0,this};ye.prototype.zzDecode=function(){var e=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^e)>>>0,this.hi=(this.hi>>>1^e)>>>0,this};ye.prototype.length=function(){var e=this.lo,t=(this.lo>>>28|this.hi<<4)>>>0,i=this.hi>>>24;return i===0?t===0?e<16384?e<128?1:2:e<2097152?3:4:t<16384?t<128?5:6:t<2097152?7:8:i<128?9:10}});var tt=z(Bo=>{"use strict";var H=Bo;H.asPromise=Po();H.base64=Zl();H.EventEmitter=Vl();H.float=nc();H.inquire=Mo();H.utf8=rc();H.pool=ac();H.LongBits=lc();H.isNode=!!(typeof global!="undefined"&&global&&global.process&&global.process.versions&&global.process.versions.node);H.global=H.isNode&&global||typeof window!="undefined"&&window||typeof self!="undefined"&&self||Bo;H.emptyArray=Object.freeze?Object.freeze([]):[];H.emptyObject=Object.freeze?Object.freeze({}):{};H.isInteger=Number.isInteger||function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e};H.isString=function(e){return typeof e=="string"||e instanceof String};H.isObject=function(e){return e&&typeof e=="object"};H.isset=H.isSet=function(e,t){var i=e[t];return i!=null&&e.hasOwnProperty(t)?typeof i!="object"||(Array.isArray(i)?i.length:Object.keys(i).length)>0:!1};H.Buffer=function(){try{var n=H.inquire("buffer").Buffer;return n.prototype.utf8Write?n:null}catch(e){return null}}();H._Buffer_from=null;H._Buffer_allocUnsafe=null;H.newBuffer=function(e){return typeof e=="number"?H.Buffer?H._Buffer_allocUnsafe(e):new H.Array(e):H.Buffer?H._Buffer_from(e):typeof Uint8Array=="undefined"?e:new Uint8Array(e)};H.Array=typeof Uint8Array!="undefined"?Uint8Array:Array;H.Long=H.global.dcodeIO&&H.global.dcodeIO.Long||H.global.Long||H.inquire("long");H.key2Re=/^true|false|0|1$/;H.key32Re=/^-?(?:0|[1-9][0-9]*)$/;H.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;H.longToHash=function(e){return e?H.LongBits.from(e).toHash():H.LongBits.zeroHash};H.longFromHash=function(e,t){var i=H.LongBits.fromHash(e);return H.Long?H.Long.fromBits(i.lo,i.hi,t):i.toNumber(!!t)};function cc(n,e,t){for(var i=Object.keys(e),r=0;r<i.length;++r)(n[i[r]]===void 0||!t)&&(n[i[r]]=e[i[r]]);return n}H.merge=cc;H.lcFirst=function(e){return e.charAt(0).toLowerCase()+e.substring(1)};function pc(n){function e(t,i){if(!(this instanceof e))return new e(t,i);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,e):Object.defineProperty(this,"stack",{value:new Error().stack||""}),i&&cc(this,i)}return e.prototype=Object.create(Error.prototype,{constructor:{value:e,writable:!0,enumerable:!1,configurable:!0},name:{get:function(){return n},set:void 0,enumerable:!1,configurable:!0},toString:{value:function(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),e}H.newError=pc;H.ProtocolError=pc("ProtocolError");H.oneOfGetter=function(e){for(var t={},i=0;i<e.length;++i)t[e[i]]=1;return function(){for(var r=Object.keys(this),o=r.length-1;o>-1;--o)if(t[r[o]]===1&&this[r[o]]!==void 0&&this[r[o]]!==null)return r[o]}};H.oneOfSetter=function(e){return function(t){for(var i=0;i<e.length;++i)e[i]!==t&&delete this[e[i]]}};H.toJSONOptions={longs:String,enums:String,bytes:String,json:!0};H._configure=function(){var n=H.Buffer;if(!n){H._Buffer_from=H._Buffer_allocUnsafe=null;return}H._Buffer_from=n.from!==Uint8Array.from&&n.from||function(t,i){return new n(t,i)},H._Buffer_allocUnsafe=n.allocUnsafe||function(t){return new n(t)}}});var tr=z((ww,mc)=>{"use strict";mc.exports=X;var Ue=tt(),Uo,er=Ue.LongBits,uc=Ue.base64,fc=Ue.utf8;function oi(n,e,t){this.fn=n,this.len=e,this.next=void 0,this.val=t}function Ho(){}function Fh(n){this.head=n.head,this.tail=n.tail,this.len=n.len,this.next=n.states}function X(){this.len=0,this.head=new oi(Ho,0,0),this.tail=this.head,this.states=null}var dc=function(){return Ue.Buffer?function(){return(X.create=function(){return new Uo})()}:function(){return new X}};X.create=dc();X.alloc=function(e){return new Ue.Array(e)};Ue.Array!==Array&&(X.alloc=Ue.pool(X.alloc,Ue.Array.prototype.subarray));X.prototype._push=function(e,t,i){return this.tail=this.tail.next=new oi(e,t,i),this.len+=t,this};function qo(n,e,t){e[t]=n&255}function Lh(n,e,t){for(;n>127;)e[t++]=n&127|128,n>>>=7;e[t]=n}function Wo(n,e){this.len=n,this.next=void 0,this.val=e}Wo.prototype=Object.create(oi.prototype);Wo.prototype.fn=Lh;X.prototype.uint32=function(e){return this.len+=(this.tail=this.tail.next=new Wo((e=e>>>0)<128?1:e<16384?2:e<2097152?3:e<268435456?4:5,e)).len,this};X.prototype.int32=function(e){return e<0?this._push(Yo,10,er.fromNumber(e)):this.uint32(e)};X.prototype.sint32=function(e){return this.uint32((e<<1^e>>31)>>>0)};function Yo(n,e,t){for(;n.hi;)e[t++]=n.lo&127|128,n.lo=(n.lo>>>7|n.hi<<25)>>>0,n.hi>>>=7;for(;n.lo>127;)e[t++]=n.lo&127|128,n.lo=n.lo>>>7;e[t++]=n.lo}X.prototype.uint64=function(e){var t=er.from(e);return this._push(Yo,t.length(),t)};X.prototype.int64=X.prototype.uint64;X.prototype.sint64=function(e){var t=er.from(e).zzEncode();return this._push(Yo,t.length(),t)};X.prototype.bool=function(e){return this._push(qo,1,e?1:0)};function jo(n,e,t){e[t]=n&255,e[t+1]=n>>>8&255,e[t+2]=n>>>16&255,e[t+3]=n>>>24}X.prototype.fixed32=function(e){return this._push(jo,4,e>>>0)};X.prototype.sfixed32=X.prototype.fixed32;X.prototype.fixed64=function(e){var t=er.from(e);return this._push(jo,4,t.lo)._push(jo,4,t.hi)};X.prototype.sfixed64=X.prototype.fixed64;X.prototype.float=function(e){return this._push(Ue.float.writeFloatLE,4,e)};X.prototype.double=function(e){return this._push(Ue.float.writeDoubleLE,8,e)};var Ph=Ue.Array.prototype.set?function(e,t,i){t.set(e,i)}:function(e,t,i){for(var r=0;r<e.length;++r)t[i+r]=e[r]};X.prototype.bytes=function(e){var t=e.length>>>0;if(!t)return this._push(qo,1,0);if(Ue.isString(e)){var i=X.alloc(t=uc.length(e));uc.decode(e,i,0),e=i}return this.uint32(t)._push(Ph,t,e)};X.prototype.string=function(e){var t=fc.length(e);return t?this.uint32(t)._push(fc.write,t,e):this._push(qo,1,0)};X.prototype.fork=function(){return this.states=new Fh(this),this.head=this.tail=new oi(Ho,0,0),this.len=0,this};X.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new oi(Ho,0,0),this.len=0),this};X.prototype.ldelim=function(){var e=this.head,t=this.tail,i=this.len;return this.reset().uint32(i),i&&(this.tail.next=e.next,this.tail=t,this.len+=i),this};X.prototype.finish=function(){for(var e=this.head.next,t=this.constructor.alloc(this.len),i=0;e;)e.fn(e.val,t,i),i+=e.len,e=e.next;return t};X._configure=function(n){Uo=n,X.create=dc(),Uo._configure()}});var yc=z((vw,gc)=>{"use strict";gc.exports=nt;var hc=tr();(nt.prototype=Object.create(hc.prototype)).constructor=nt;var Dt=tt();function nt(){hc.call(this)}nt._configure=function(){nt.alloc=Dt._Buffer_allocUnsafe,nt.writeBytesBuffer=Dt.Buffer&&Dt.Buffer.prototype instanceof Uint8Array&&Dt.Buffer.prototype.set.name==="set"?function(e,t,i){t.set(e,i)}:function(e,t,i){if(e.copy)e.copy(t,i,0,e.length);else for(var r=0;r<e.length;)t[i++]=e[r++]}};nt.prototype.bytes=function(e){Dt.isString(e)&&(e=Dt._Buffer_from(e,"base64"));var t=e.length>>>0;return this.uint32(t),t&&this._push(nt.writeBytesBuffer,t,e),this};function Mh(n,e,t){n.length<40?Dt.utf8.write(n,e,t):e.utf8Write?e.utf8Write(n,t):e.write(n,t)}nt.prototype.string=function(e){var t=Dt.Buffer.byteLength(e);return this.uint32(t),t&&this._push(Mh,t,e),this};nt._configure()});var ir=z((Ew,Ec)=>{"use strict";Ec.exports=le;var Ge=tt(),Zo,wc=Ge.LongBits,$h=Ge.utf8;function Ve(n,e){return RangeError("index out of range: "+n.pos+" + "+(e||1)+" > "+n.len)}function le(n){this.buf=n,this.pos=0,this.len=n.length}var bc=typeof Uint8Array!="undefined"?function(e){if(e instanceof Uint8Array||Array.isArray(e))return new le(e);throw Error("illegal buffer")}:function(e){if(Array.isArray(e))return new le(e);throw Error("illegal buffer")},vc=function(){return Ge.Buffer?function(t){return(le.create=function(r){return Ge.Buffer.isBuffer(r)?new Zo(r):bc(r)})(t)}:bc};le.create=vc();le.prototype._slice=Ge.Array.prototype.subarray||Ge.Array.prototype.slice;le.prototype.uint32=function(){var e=4294967295;return function(){if(e=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128||(e=(e|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)||(e=(e|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)||(e=(e|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)||(e=(e|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128))return e;if((this.pos+=5)>this.len)throw this.pos=this.len,Ve(this,10);return e}}();le.prototype.int32=function(){return this.uint32()|0};le.prototype.sint32=function(){var e=this.uint32();return e>>>1^-(e&1)|0};function zo(){var n=new wc(0,0),e=0;if(this.len-this.pos>4){for(;e<4;++e)if(n.lo=(n.lo|(this.buf[this.pos]&127)<<e*7)>>>0,this.buf[this.pos++]<128)return n;if(n.lo=(n.lo|(this.buf[this.pos]&127)<<28)>>>0,n.hi=(n.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return n;e=0}else{for(;e<3;++e){if(this.pos>=this.len)throw Ve(this);if(n.lo=(n.lo|(this.buf[this.pos]&127)<<e*7)>>>0,this.buf[this.pos++]<128)return n}return n.lo=(n.lo|(this.buf[this.pos++]&127)<<e*7)>>>0,n}if(this.len-this.pos>4){for(;e<5;++e)if(n.hi=(n.hi|(this.buf[this.pos]&127)<<e*7+3)>>>0,this.buf[this.pos++]<128)return n}else for(;e<5;++e){if(this.pos>=this.len)throw Ve(this);if(n.hi=(n.hi|(this.buf[this.pos]&127)<<e*7+3)>>>0,this.buf[this.pos++]<128)return n}throw Error("invalid varint encoding")}le.prototype.bool=function(){return this.uint32()!==0};function nr(n,e){return(n[e-4]|n[e-3]<<8|n[e-2]<<16|n[e-1]<<24)>>>0}le.prototype.fixed32=function(){if(this.pos+4>this.len)throw Ve(this,4);return nr(this.buf,this.pos+=4)};le.prototype.sfixed32=function(){if(this.pos+4>this.len)throw Ve(this,4);return nr(this.buf,this.pos+=4)|0};function xc(){if(this.pos+8>this.len)throw Ve(this,8);return new wc(nr(this.buf,this.pos+=4),nr(this.buf,this.pos+=4))}le.prototype.float=function(){if(this.pos+4>this.len)throw Ve(this,4);var e=Ge.float.readFloatLE(this.buf,this.pos);return this.pos+=4,e};le.prototype.double=function(){if(this.pos+8>this.len)throw Ve(this,4);var e=Ge.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,e};le.prototype.bytes=function(){var e=this.uint32(),t=this.pos,i=this.pos+e;if(i>this.len)throw Ve(this,e);if(this.pos+=e,Array.isArray(this.buf))return this.buf.slice(t,i);if(t===i){var r=Ge.Buffer;return r?r.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,t,i)};le.prototype.string=function(){var e=this.bytes();return $h.read(e,0,e.length)};le.prototype.skip=function(e){if(typeof e=="number"){if(this.pos+e>this.len)throw Ve(this,e);this.pos+=e}else do if(this.pos>=this.len)throw Ve(this);while(this.buf[this.pos++]&128);return this};le.prototype.skipType=function(n){switch(n){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;(n=this.uint32()&7)!==4;)this.skipType(n);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+n+" at offset "+this.pos)}return this};le._configure=function(n){Zo=n,le.create=vc(),Zo._configure();var e=Ge.Long?"toLong":"toNumber";Ge.merge(le.prototype,{int64:function(){return zo.call(this)[e](!1)},uint64:function(){return zo.call(this)[e](!0)},sint64:function(){return zo.call(this).zzDecode()[e](!1)},fixed64:function(){return xc.call(this)[e](!0)},sfixed64:function(){return xc.call(this)[e](!1)}})}});var kc=z((_w,Ac)=>{"use strict";Ac.exports=on;var Tc=ir();(on.prototype=Object.create(Tc.prototype)).constructor=on;var _c=tt();function on(n){Tc.call(this,n)}on._configure=function(){_c.Buffer&&(on.prototype._slice=_c.Buffer.prototype.slice)};on.prototype.string=function(){var e=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+e,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+e,this.len))};on._configure()});var Sc=z((Tw,Nc)=>{"use strict";Nc.exports=ai;var Go=tt();(ai.prototype=Object.create(Go.EventEmitter.prototype)).constructor=ai;function ai(n,e,t){if(typeof n!="function")throw TypeError("rpcImpl must be a function");Go.EventEmitter.call(this),this.rpcImpl=n,this.requestDelimited=!!e,this.responseDelimited=!!t}ai.prototype.rpcCall=function n(e,t,i,r,o){if(!r)throw TypeError("request must be specified");var a=this;if(!o)return Go.asPromise(n,a,e,t,i,r);if(!a.rpcImpl){setTimeout(function(){o(Error("already ended"))},0);return}try{return a.rpcImpl(e,t[a.requestDelimited?"encodeDelimited":"encode"](r).finish(),function(l,c){if(l)return a.emit("error",l,e),o(l);if(c===null){a.end(!0);return}if(!(c instanceof i))try{c=i[a.responseDelimited?"decodeDelimited":"decode"](c)}catch(p){return a.emit("error",p,e),o(p)}return a.emit("data",c,e),o(null,c)})}catch(s){a.emit("error",s,e),setTimeout(function(){o(s)},0);return}};ai.prototype.end=function(e){return this.rpcImpl&&(e||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}});var Vo=z(Rc=>{"use strict";var Bh=Rc;Bh.Service=Sc()});var Ko=z((kw,Oc)=>{"use strict";Oc.exports={}});var Ic=z(Dc=>{"use strict";var De=Dc;De.build="minimal";De.Writer=tr();De.BufferWriter=yc();De.Reader=ir();De.BufferReader=kc();De.util=tt();De.rpc=Vo();De.roots=Ko();De.configure=Cc;function Cc(){De.util._configure(),De.Writer._configure(De.BufferWriter),De.Reader._configure(De.BufferReader)}Cc()});var Lc=z((Sw,Fc)=>{"use strict";Fc.exports=Xo;function Xo(n,e){typeof n=="string"&&(e=n,n=void 0);var t=[];function i(o){if(typeof o!="string"){var a=r();if(Xo.verbose&&console.log("codegen: "+a),a="return "+a,o){for(var s=Object.keys(o),l=new Array(s.length+1),c=new Array(s.length),p=0;p<s.length;)l[p]=s[p],c[p]=o[s[p++]];return l[p]=a,Function.apply(null,l).apply(null,c)}return Function(a)()}for(var m=new Array(arguments.length-1),g=0;g<m.length;)m[g]=arguments[++g];if(g=0,o=o.replace(/%([%dfijs])/g,function(E,f){var d=m[g++];switch(f){case"d":case"f":return String(Number(d));case"i":return String(Math.floor(d));case"j":return JSON.stringify(d);case"s":return String(d)}return"%"}),g!==m.length)throw Error("parameter count mismatch");return t.push(o),i}function r(o){return"function "+(o||e||"")+"("+(n&&n.join(",")||"")+`){
  `+t.join(`
  `)+`
}`}return i.toString=r,i}Xo.verbose=!1});var Mc=z((Rw,Pc)=>{"use strict";Pc.exports=si;var Uh=Po(),jh=Mo(),Jo=jh("fs");function si(n,e,t){return typeof e=="function"?(t=e,e={}):e||(e={}),t?!e.xhr&&Jo&&Jo.readFile?Jo.readFile(n,function(r,o){return r&&typeof XMLHttpRequest!="undefined"?si.xhr(n,e,t):r?t(r):t(null,e.binary?o:o.toString("utf8"))}):si.xhr(n,e,t):Uh(si,this,n,e)}si.xhr=function(e,t,i){var r=new XMLHttpRequest;r.onreadystatechange=function(){if(r.readyState===4){if(r.status!==0&&r.status!==200)return i(Error("status "+r.status));if(t.binary){var a=r.response;if(!a){a=[];for(var s=0;s<r.responseText.length;++s)a.push(r.responseText.charCodeAt(s)&255)}return i(null,typeof Uint8Array!="undefined"?new Uint8Array(a):a)}return i(null,r.responseText)}},t.binary&&("overrideMimeType"in r&&r.overrideMimeType("text/plain; charset=x-user-defined"),r.responseType="arraybuffer"),r.open("GET",e),r.send()}});var Uc=z(Bc=>{"use strict";var ea=Bc,$c=ea.isAbsolute=function(e){return/^(?:\/|\w+:)/.test(e)},Qo=ea.normalize=function(e){e=e.replace(/\\/g,"/").replace(/\/{2,}/g,"/");var t=e.split("/"),i=$c(e),r="";i&&(r=t.shift()+"/");for(var o=0;o<t.length;)t[o]===".."?o>0&&t[o-1]!==".."?t.splice(--o,2):i?t.splice(o,1):++o:t[o]==="."?t.splice(o,1):++o;return r+t.join("/")};ea.resolve=function(e,t,i){return i||(t=Qo(t)),$c(t)?t:(i||(e=Qo(e)),(e=e.replace(/(?:\/|^)[^/]+$/,"")).length?Qo(e+"/"+t):t)}});var an=z(jc=>{"use strict";var li=jc,Hh=be(),qh=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function ci(n,e){var t=0,i={};for(e|=0;t<n.length;)i[qh[t+e]]=n[t++];return i}li.basic=ci([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]);li.defaults=ci([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",Hh.emptyArray,null]);li.long=ci([0,0,0,1,1],7);li.mapKey=ci([0,0,0,5,5,0,0,0,1,1,0,2],2);li.packed=ci([1,5,0,0,0,5,5,0,0,0,1,1,0])});var It=z((Dw,Wc)=>{"use strict";Wc.exports=je;var rr=sn();((je.prototype=Object.create(rr.prototype)).constructor=je).className="Field";var Hc=Ke(),qc=an(),fe=be(),ta,Wh=/^required|optional|repeated$/;je.fromJSON=function(e,t){return new je(e,t.id,t.type,t.rule,t.extend,t.options,t.comment)};function je(n,e,t,i,r,o,a){if(fe.isObject(i)?(a=r,o=i,i=r=void 0):fe.isObject(r)&&(a=o,o=r,r=void 0),rr.call(this,n,o),!fe.isInteger(e)||e<0)throw TypeError("id must be a non-negative integer");if(!fe.isString(t))throw TypeError("type must be a string");if(i!==void 0&&!Wh.test(i=i.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(r!==void 0&&!fe.isString(r))throw TypeError("extend must be a string");i==="proto3_optional"&&(i="optional"),this.rule=i&&i!=="optional"?i:void 0,this.type=t,this.id=e,this.extend=r||void 0,this.required=i==="required",this.optional=!this.required,this.repeated=i==="repeated",this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=fe.Long?qc.long[t]!==void 0:!1,this.bytes=t==="bytes",this.resolvedType=null,this.extensionField=null,this.declaringField=null,this._packed=null,this.comment=a}Object.defineProperty(je.prototype,"packed",{get:function(){return this._packed===null&&(this._packed=this.getOption("packed")!==!1),this._packed}});je.prototype.setOption=function(e,t,i){return e==="packed"&&(this._packed=null),rr.prototype.setOption.call(this,e,t,i)};je.prototype.toJSON=function(e){var t=e?!!e.keepComments:!1;return fe.toObject(["rule",this.rule!=="optional"&&this.rule||void 0,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])};je.prototype.resolve=function(){if(this.resolved)return this;if((this.typeDefault=qc.defaults[this.type])===void 0?(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof ta?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]):this.options&&this.options.proto3_optional&&(this.typeDefault=null),this.options&&this.options.default!=null&&(this.typeDefault=this.options.default,this.resolvedType instanceof Hc&&typeof this.typeDefault=="string"&&(this.typeDefault=this.resolvedType.values[this.typeDefault])),this.options&&((this.options.packed===!0||this.options.packed!==void 0&&this.resolvedType&&!(this.resolvedType instanceof Hc))&&delete this.options.packed,Object.keys(this.options).length||(this.options=void 0)),this.long)this.typeDefault=fe.Long.fromNumber(this.typeDefault,this.type.charAt(0)==="u"),Object.freeze&&Object.freeze(this.typeDefault);else if(this.bytes&&typeof this.typeDefault=="string"){var e;fe.base64.test(this.typeDefault)?fe.base64.decode(this.typeDefault,e=fe.newBuffer(fe.base64.length(this.typeDefault)),0):fe.utf8.write(this.typeDefault,e=fe.newBuffer(fe.utf8.length(this.typeDefault)),0),this.typeDefault=e}return this.map?this.defaultValue=fe.emptyObject:this.repeated?this.defaultValue=fe.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof ta&&(this.parent.ctor.prototype[this.name]=this.defaultValue),rr.prototype.resolve.call(this)};je.d=function(e,t,i,r){return typeof t=="function"?t=fe.decorateType(t).name:t&&typeof t=="object"&&(t=fe.decorateEnum(t).name),function(a,s){fe.decorateType(a.constructor).add(new je(s,e,t,i,{default:r}))}};je._configure=function(e){ta=e}});var An=z((Iw,Zc)=>{"use strict";Zc.exports=He;var ar=sn();((He.prototype=Object.create(ar.prototype)).constructor=He).className="OneOf";var Yc=It(),or=be();function He(n,e,t,i){if(Array.isArray(e)||(t=e,e=void 0),ar.call(this,n,t),!(e===void 0||Array.isArray(e)))throw TypeError("fieldNames must be an Array");this.oneof=e||[],this.fieldsArray=[],this.comment=i}He.fromJSON=function(e,t){return new He(e,t.oneof,t.options,t.comment)};He.prototype.toJSON=function(e){var t=e?!!e.keepComments:!1;return or.toObject(["options",this.options,"oneof",this.oneof,"comment",t?this.comment:void 0])};function zc(n){if(n.parent)for(var e=0;e<n.fieldsArray.length;++e)n.fieldsArray[e].parent||n.parent.add(n.fieldsArray[e])}He.prototype.add=function(e){if(!(e instanceof Yc))throw TypeError("field must be a Field");return e.parent&&e.parent!==this.parent&&e.parent.remove(e),this.oneof.push(e.name),this.fieldsArray.push(e),e.partOf=this,zc(this),this};He.prototype.remove=function(e){if(!(e instanceof Yc))throw TypeError("field must be a Field");var t=this.fieldsArray.indexOf(e);if(t<0)throw Error(e+" is not a member of "+this);return this.fieldsArray.splice(t,1),t=this.oneof.indexOf(e.name),t>-1&&this.oneof.splice(t,1),e.partOf=null,this};He.prototype.onAdd=function(e){ar.prototype.onAdd.call(this,e);for(var t=this,i=0;i<this.oneof.length;++i){var r=e.get(this.oneof[i]);r&&!r.partOf&&(r.partOf=t,t.fieldsArray.push(r))}zc(this)};He.prototype.onRemove=function(e){for(var t=0,i;t<this.fieldsArray.length;++t)(i=this.fieldsArray[t]).parent&&i.parent.remove(i);ar.prototype.onRemove.call(this,e)};He.d=function(){for(var e=new Array(arguments.length),t=0;t<arguments.length;)e[t]=arguments[t++];return function(r,o){or.decorateType(r.constructor).add(new He(o,e)),Object.defineProperty(r,o,{get:or.oneOfGetter(e),set:or.oneOfSetter(e)})}}});var Sn=z((Fw,Xc)=>{"use strict";Xc.exports=J;var na=sn();((J.prototype=Object.create(na.prototype)).constructor=J).className="Namespace";var Gc=It(),sr=be(),Yh=An(),kn,pi,Nn;J.fromJSON=function(e,t){return new J(e,t.options).addJSON(t.nested)};function Vc(n,e){if(n&&n.length){for(var t={},i=0;i<n.length;++i)t[n[i].name]=n[i].toJSON(e);return t}}J.arrayToJSON=Vc;J.isReservedId=function(e,t){if(e){for(var i=0;i<e.length;++i)if(typeof e[i]!="string"&&e[i][0]<=t&&e[i][1]>t)return!0}return!1};J.isReservedName=function(e,t){if(e){for(var i=0;i<e.length;++i)if(e[i]===t)return!0}return!1};function J(n,e){na.call(this,n,e),this.nested=void 0,this._nestedArray=null}function Kc(n){return n._nestedArray=null,n}Object.defineProperty(J.prototype,"nestedArray",{get:function(){return this._nestedArray||(this._nestedArray=sr.toArray(this.nested))}});J.prototype.toJSON=function(e){return sr.toObject(["options",this.options,"nested",Vc(this.nestedArray,e)])};J.prototype.addJSON=function(e){var t=this;if(e)for(var i=Object.keys(e),r=0,o;r<i.length;++r)o=e[i[r]],t.add((o.fields!==void 0?kn.fromJSON:o.values!==void 0?Nn.fromJSON:o.methods!==void 0?pi.fromJSON:o.id!==void 0?Gc.fromJSON:J.fromJSON)(i[r],o));return this};J.prototype.get=function(e){return this.nested&&this.nested[e]||null};J.prototype.getEnum=function(e){if(this.nested&&this.nested[e]instanceof Nn)return this.nested[e].values;throw Error("no such enum: "+e)};J.prototype.add=function(e){if(!(e instanceof Gc&&e.extend!==void 0||e instanceof kn||e instanceof Yh||e instanceof Nn||e instanceof pi||e instanceof J))throw TypeError("object must be a valid nested object");if(!this.nested)this.nested={};else{var t=this.get(e.name);if(t)if(t instanceof J&&e instanceof J&&!(t instanceof kn||t instanceof pi)){for(var i=t.nestedArray,r=0;r<i.length;++r)e.add(i[r]);this.remove(t),this.nested||(this.nested={}),e.setOptions(t.options,!0)}else throw Error("duplicate name '"+e.name+"' in "+this)}return this.nested[e.name]=e,e.onAdd(this),Kc(this)};J.prototype.remove=function(e){if(!(e instanceof na))throw TypeError("object must be a ReflectionObject");if(e.parent!==this)throw Error(e+" is not a member of "+this);return delete this.nested[e.name],Object.keys(this.nested).length||(this.nested=void 0),e.onRemove(this),Kc(this)};J.prototype.define=function(e,t){if(sr.isString(e))e=e.split(".");else if(!Array.isArray(e))throw TypeError("illegal path");if(e&&e.length&&e[0]==="")throw Error("path must be relative");for(var i=this;e.length>0;){var r=e.shift();if(i.nested&&i.nested[r]){if(i=i.nested[r],!(i instanceof J))throw Error("path conflicts with non-namespace objects")}else i.add(i=new J(r))}return t&&i.addJSON(t),i};J.prototype.resolveAll=function(){for(var e=this.nestedArray,t=0;t<e.length;)e[t]instanceof J?e[t++].resolveAll():e[t++].resolve();return this.resolve()};J.prototype.lookup=function(e,t,i){if(typeof t=="boolean"?(i=t,t=void 0):t&&!Array.isArray(t)&&(t=[t]),sr.isString(e)&&e.length){if(e===".")return this.root;e=e.split(".")}else if(!e.length)return this;if(e[0]==="")return this.root.lookup(e.slice(1),t);var r=this.get(e[0]);if(r){if(e.length===1){if(!t||t.indexOf(r.constructor)>-1)return r}else if(r instanceof J&&(r=r.lookup(e.slice(1),t,!0)))return r}else for(var o=0;o<this.nestedArray.length;++o)if(this._nestedArray[o]instanceof J&&(r=this._nestedArray[o].lookup(e,t,!0)))return r;return this.parent===null||i?null:this.parent.lookup(e,t)};J.prototype.lookupType=function(e){var t=this.lookup(e,[kn]);if(!t)throw Error("no such type: "+e);return t};J.prototype.lookupEnum=function(e){var t=this.lookup(e,[Nn]);if(!t)throw Error("no such Enum '"+e+"' in "+this);return t};J.prototype.lookupTypeOrEnum=function(e){var t=this.lookup(e,[kn,Nn]);if(!t)throw Error("no such Type or Enum '"+e+"' in "+this);return t};J.prototype.lookupService=function(e){var t=this.lookup(e,[pi]);if(!t)throw Error("no such Service '"+e+"' in "+this);return t};J._configure=function(n,e,t){kn=n,pi=e,Nn=t}});var lr=z((Lw,Jc)=>{"use strict";Jc.exports=mt;var ia=It();((mt.prototype=Object.create(ia.prototype)).constructor=mt).className="MapField";var zh=an(),ui=be();function mt(n,e,t,i,r,o){if(ia.call(this,n,e,i,void 0,void 0,r,o),!ui.isString(t))throw TypeError("keyType must be a string");this.keyType=t,this.resolvedKeyType=null,this.map=!0}mt.fromJSON=function(e,t){return new mt(e,t.id,t.keyType,t.type,t.options,t.comment)};mt.prototype.toJSON=function(e){var t=e?!!e.keepComments:!1;return ui.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])};mt.prototype.resolve=function(){if(this.resolved)return this;if(zh.mapKey[this.keyType]===void 0)throw Error("invalid key type: "+this.keyType);return ia.prototype.resolve.call(this)};mt.d=function(e,t,i){return typeof i=="function"?i=ui.decorateType(i).name:i&&typeof i=="object"&&(i=ui.decorateEnum(i).name),function(o,a){ui.decorateType(o.constructor).add(new mt(a,e,t,i))}}});var cr=z((Pw,Qc)=>{"use strict";Qc.exports=ln;var ra=sn();((ln.prototype=Object.create(ra.prototype)).constructor=ln).className="Method";var Rn=be();function ln(n,e,t,i,r,o,a,s,l){if(Rn.isObject(r)?(a=r,r=o=void 0):Rn.isObject(o)&&(a=o,o=void 0),!(e===void 0||Rn.isString(e)))throw TypeError("type must be a string");if(!Rn.isString(t))throw TypeError("requestType must be a string");if(!Rn.isString(i))throw TypeError("responseType must be a string");ra.call(this,n,a),this.type=e||"rpc",this.requestType=t,this.requestStream=r?!0:void 0,this.responseType=i,this.responseStream=o?!0:void 0,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=s,this.parsedOptions=l}ln.fromJSON=function(e,t){return new ln(e,t.type,t.requestType,t.responseType,t.requestStream,t.responseStream,t.options,t.comment,t.parsedOptions)};ln.prototype.toJSON=function(e){var t=e?!!e.keepComments:!1;return Rn.toObject(["type",this.type!=="rpc"&&this.type||void 0,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",t?this.comment:void 0,"parsedOptions",this.parsedOptions])};ln.prototype.resolve=function(){return this.resolved?this:(this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),ra.prototype.resolve.call(this))}});var pr=z((Mw,tp)=>{"use strict";tp.exports=qe;var Ft=Sn();((qe.prototype=Object.create(Ft.prototype)).constructor=qe).className="Service";var oa=cr(),fi=be(),Zh=Vo();function qe(n,e){Ft.call(this,n,e),this.methods={},this._methodsArray=null}qe.fromJSON=function(e,t){var i=new qe(e,t.options);if(t.methods)for(var r=Object.keys(t.methods),o=0;o<r.length;++o)i.add(oa.fromJSON(r[o],t.methods[r[o]]));return t.nested&&i.addJSON(t.nested),i.comment=t.comment,i};qe.prototype.toJSON=function(e){var t=Ft.prototype.toJSON.call(this,e),i=e?!!e.keepComments:!1;return fi.toObject(["options",t&&t.options||void 0,"methods",Ft.arrayToJSON(this.methodsArray,e)||{},"nested",t&&t.nested||void 0,"comment",i?this.comment:void 0])};Object.defineProperty(qe.prototype,"methodsArray",{get:function(){return this._methodsArray||(this._methodsArray=fi.toArray(this.methods))}});function ep(n){return n._methodsArray=null,n}qe.prototype.get=function(e){return this.methods[e]||Ft.prototype.get.call(this,e)};qe.prototype.resolveAll=function(){for(var e=this.methodsArray,t=0;t<e.length;++t)e[t].resolve();return Ft.prototype.resolve.call(this)};qe.prototype.add=function(e){if(this.get(e.name))throw Error("duplicate name '"+e.name+"' in "+this);return e instanceof oa?(this.methods[e.name]=e,e.parent=this,ep(this)):Ft.prototype.add.call(this,e)};qe.prototype.remove=function(e){if(e instanceof oa){if(this.methods[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.methods[e.name],e.parent=null,ep(this)}return Ft.prototype.remove.call(this,e)};qe.prototype.create=function(e,t,i){for(var r=new Zh.Service(e,t,i),o=0,a;o<this.methodsArray.length;++o){var s=fi.lcFirst((a=this._methodsArray[o]).resolve().name).replace(/[^$\w_]/g,"");r[s]=fi.codegen(["r","c"],fi.isReserved(s)?s+"_":s)("return this.rpcCall(m,q,s,r,c)")({m:a,q:a.resolvedRequestType.ctor,s:a.resolvedResponseType.ctor})}return r}});var ur=z(($w,np)=>{"use strict";np.exports=it;var Gh=tt();function it(n){if(n)for(var e=Object.keys(n),t=0;t<e.length;++t)this[e[t]]=n[e[t]]}it.create=function(e){return this.$type.create(e)};it.encode=function(e,t){return this.$type.encode(e,t)};it.encodeDelimited=function(e,t){return this.$type.encodeDelimited(e,t)};it.decode=function(e){return this.$type.decode(e)};it.decodeDelimited=function(e){return this.$type.decodeDelimited(e)};it.verify=function(e){return this.$type.verify(e)};it.fromObject=function(e){return this.$type.fromObject(e)};it.toObject=function(e,t){return this.$type.toObject(e,t)};it.prototype.toJSON=function(){return this.$type.toObject(this,Gh.toJSONOptions)}});var aa=z((Bw,rp)=>{"use strict";rp.exports=Xh;var Vh=Ke(),ht=an(),ip=be();function Kh(n){return"missing required '"+n.name+"'"}function Xh(n){var e=ip.codegen(["r","l"],n.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(n.fieldsArray.filter(function(s){return s.map}).length?",k,value":""))("while(r.pos<c){")("var t=r.uint32()");n.group&&e("if((t&7)===4)")("break"),e("switch(t>>>3){");for(var t=0;t<n.fieldsArray.length;++t){var i=n._fieldsArray[t].resolve(),r=i.resolvedType instanceof Vh?"int32":i.type,o="m"+ip.safeProp(i.name);e("case %i: {",i.id),i.map?(e("if(%s===util.emptyObject)",o)("%s={}",o)("var c2 = r.uint32()+r.pos"),ht.defaults[i.keyType]!==void 0?e("k=%j",ht.defaults[i.keyType]):e("k=null"),ht.defaults[r]!==void 0?e("value=%j",ht.defaults[r]):e("value=null"),e("while(r.pos<c2){")("var tag2=r.uint32()")("switch(tag2>>>3){")("case 1: k=r.%s(); break",i.keyType)("case 2:"),ht.basic[r]===void 0?e("value=types[%i].decode(r,r.uint32())",t):e("value=r.%s()",r),e("break")("default:")("r.skipType(tag2&7)")("break")("}")("}"),ht.long[i.keyType]!==void 0?e('%s[typeof k==="object"?util.longToHash(k):k]=value',o):e("%s[k]=value",o)):i.repeated?(e("if(!(%s&&%s.length))",o,o)("%s=[]",o),ht.packed[r]!==void 0&&e("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",o,r)("}else"),ht.basic[r]===void 0?e(i.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",o,t):e("%s.push(r.%s())",o,r)):ht.basic[r]===void 0?e(i.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",o,t):e("%s=r.%s()",o,r),e("break")("}")}for(e("default:")("r.skipType(t&7)")("break")("}")("}"),t=0;t<n._fieldsArray.length;++t){var a=n._fieldsArray[t];a.required&&e("if(!m.hasOwnProperty(%j))",a.name)("throw util.ProtocolError(%j,{instance:m})",Kh(a))}return e("return m")}});var ca=z((Uw,op)=>{"use strict";op.exports=eg;var Jh=Ke(),sa=be();function We(n,e){return n.name+": "+e+(n.repeated&&e!=="array"?"[]":n.map&&e!=="object"?"{k:"+n.keyType+"}":"")+" expected"}function la(n,e,t,i){if(e.resolvedType)if(e.resolvedType instanceof Jh){n("switch(%s){",i)("default:")("return%j",We(e,"enum value"));for(var r=Object.keys(e.resolvedType.values),o=0;o<r.length;++o)n("case %i:",e.resolvedType.values[r[o]]);n("break")("}")}else n("{")("var e=types[%i].verify(%s);",t,i)("if(e)")("return%j+e",e.name+".")("}");else switch(e.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":n("if(!util.isInteger(%s))",i)("return%j",We(e,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":n("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",i,i,i,i)("return%j",We(e,"integer|Long"));break;case"float":case"double":n('if(typeof %s!=="number")',i)("return%j",We(e,"number"));break;case"bool":n('if(typeof %s!=="boolean")',i)("return%j",We(e,"boolean"));break;case"string":n("if(!util.isString(%s))",i)("return%j",We(e,"string"));break;case"bytes":n('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',i,i,i)("return%j",We(e,"buffer"));break}return n}function Qh(n,e,t){switch(e.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":n("if(!util.key32Re.test(%s))",t)("return%j",We(e,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":n("if(!util.key64Re.test(%s))",t)("return%j",We(e,"integer|Long key"));break;case"bool":n("if(!util.key2Re.test(%s))",t)("return%j",We(e,"boolean key"));break}return n}function eg(n){var e=sa.codegen(["m"],n.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),t=n.oneofsArray,i={};t.length&&e("var p={}");for(var r=0;r<n.fieldsArray.length;++r){var o=n._fieldsArray[r].resolve(),a="m"+sa.safeProp(o.name);if(o.optional&&e("if(%s!=null&&m.hasOwnProperty(%j)){",a,o.name),o.map)e("if(!util.isObject(%s))",a)("return%j",We(o,"object"))("var k=Object.keys(%s)",a)("for(var i=0;i<k.length;++i){"),Qh(e,o,"k[i]"),la(e,o,r,a+"[k[i]]")("}");else if(o.repeated)e("if(!Array.isArray(%s))",a)("return%j",We(o,"array"))("for(var i=0;i<%s.length;++i){",a),la(e,o,r,a+"[i]")("}");else{if(o.partOf){var s=sa.safeProp(o.partOf.name);i[o.partOf.name]===1&&e("if(p%s===1)",s)("return%j",o.partOf.name+": multiple values"),i[o.partOf.name]=1,e("p%s=1",s)}la(e,o,r,a)}o.optional&&e("}")}return e("return null")}});var fa=z(sp=>{"use strict";var ap=sp,di=Ke(),rt=be();function pa(n,e,t,i){var r=!1;if(e.resolvedType)if(e.resolvedType instanceof di){n("switch(d%s){",i);for(var o=e.resolvedType.values,a=Object.keys(o),s=0;s<a.length;++s)o[a[s]]===e.typeDefault&&!r&&(n("default:")('if(typeof(d%s)==="number"){m%s=d%s;break}',i,i,i),e.repeated||n("break"),r=!0),n("case%j:",a[s])("case %i:",o[a[s]])("m%s=%j",i,o[a[s]])("break");n("}")}else n('if(typeof d%s!=="object")',i)("throw TypeError(%j)",e.fullName+": object expected")("m%s=types[%i].fromObject(d%s)",i,t,i);else{var l=!1;switch(e.type){case"double":case"float":n("m%s=Number(d%s)",i,i);break;case"uint32":case"fixed32":n("m%s=d%s>>>0",i,i);break;case"int32":case"sint32":case"sfixed32":n("m%s=d%s|0",i,i);break;case"uint64":l=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":n("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j",i,i,l)('else if(typeof d%s==="string")',i)("m%s=parseInt(d%s,10)",i,i)('else if(typeof d%s==="number")',i)("m%s=d%s",i,i)('else if(typeof d%s==="object")',i)("m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)",i,i,i,l?"true":"");break;case"bytes":n('if(typeof d%s==="string")',i)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",i,i,i)("else if(d%s.length >= 0)",i)("m%s=d%s",i,i);break;case"string":n("m%s=String(d%s)",i,i);break;case"bool":n("m%s=Boolean(d%s)",i,i);break}}return n}ap.fromObject=function(e){var t=e.fieldsArray,i=rt.codegen(["d"],e.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!t.length)return i("return new this.ctor");i("var m=new this.ctor");for(var r=0;r<t.length;++r){var o=t[r].resolve(),a=rt.safeProp(o.name);o.map?(i("if(d%s){",a)('if(typeof d%s!=="object")',a)("throw TypeError(%j)",o.fullName+": object expected")("m%s={}",a)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",a),pa(i,o,r,a+"[ks[i]]")("}")("}")):o.repeated?(i("if(d%s){",a)("if(!Array.isArray(d%s))",a)("throw TypeError(%j)",o.fullName+": array expected")("m%s=[]",a)("for(var i=0;i<d%s.length;++i){",a),pa(i,o,r,a+"[i]")("}")("}")):(o.resolvedType instanceof di||i("if(d%s!=null){",a),pa(i,o,r,a),o.resolvedType instanceof di||i("}"))}return i("return m")};function ua(n,e,t,i){if(e.resolvedType)e.resolvedType instanceof di?n("d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s",i,t,i,i,t,i,i):n("d%s=types[%i].toObject(m%s,o)",i,t,i);else{var r=!1;switch(e.type){case"double":case"float":n("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",i,i,i,i);break;case"uint64":r=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":n('if(typeof m%s==="number")',i)("d%s=o.longs===String?String(m%s):m%s",i,i,i)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",i,i,i,i,r?"true":"",i);break;case"bytes":n("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",i,i,i,i,i);break;default:n("d%s=m%s",i,i);break}}return n}ap.toObject=function(e){var t=e.fieldsArray.slice().sort(rt.compareFieldsById);if(!t.length)return rt.codegen()("return {}");for(var i=rt.codegen(["m","o"],e.name+"$toObject")("if(!o)")("o={}")("var d={}"),r=[],o=[],a=[],s=0;s<t.length;++s)t[s].partOf||(t[s].resolve().repeated?r:t[s].map?o:a).push(t[s]);if(r.length){for(i("if(o.arrays||o.defaults){"),s=0;s<r.length;++s)i("d%s=[]",rt.safeProp(r[s].name));i("}")}if(o.length){for(i("if(o.objects||o.defaults){"),s=0;s<o.length;++s)i("d%s={}",rt.safeProp(o[s].name));i("}")}if(a.length){for(i("if(o.defaults){"),s=0;s<a.length;++s){var l=a[s],c=rt.safeProp(l.name);if(l.resolvedType instanceof di)i("d%s=o.enums===String?%j:%j",c,l.resolvedType.valuesById[l.typeDefault],l.typeDefault);else if(l.long)i("if(util.Long){")("var n=new util.Long(%i,%i,%j)",l.typeDefault.low,l.typeDefault.high,l.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n",c)("}else")("d%s=o.longs===String?%j:%i",c,l.typeDefault.toString(),l.typeDefault.toNumber());else if(l.bytes){var p="["+Array.prototype.slice.call(l.typeDefault).join(",")+"]";i("if(o.bytes===String)d%s=%j",c,String.fromCharCode.apply(String,l.typeDefault))("else{")("d%s=%s",c,p)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)",c,c)("}")}else i("d%s=%j",c,l.typeDefault)}i("}")}var m=!1;for(s=0;s<t.length;++s){var l=t[s],g=e._fieldsArray.indexOf(l),c=rt.safeProp(l.name);l.map?(m||(m=!0,i("var ks2")),i("if(m%s&&(ks2=Object.keys(m%s)).length){",c,c)("d%s={}",c)("for(var j=0;j<ks2.length;++j){"),ua(i,l,g,c+"[ks2[j]]")("}")):l.repeated?(i("if(m%s&&m%s.length){",c,c)("d%s=[]",c)("for(var j=0;j<m%s.length;++j){",c),ua(i,l,g,c+"[j]")("}")):(i("if(m%s!=null&&m.hasOwnProperty(%j)){",c,l.name),ua(i,l,g,c),l.partOf&&i("if(o.oneofs)")("d%s=%j",rt.safeProp(l.partOf.name),l.name)),i("}")}return i("return d")}});var da=z(lp=>{"use strict";var tg=lp,ng=ur();tg[".google.protobuf.Any"]={fromObject:function(n){if(n&&n["@type"]){var e=n["@type"].substring(n["@type"].lastIndexOf("/")+1),t=this.lookup(e);if(t){var i=n["@type"].charAt(0)==="."?n["@type"].slice(1):n["@type"];return i.indexOf("/")===-1&&(i="/"+i),this.create({type_url:i,value:t.encode(t.fromObject(n)).finish()})}}return this.fromObject(n)},toObject:function(n,e){var t="type.googleapis.com/",i="",r="";if(e&&e.json&&n.type_url&&n.value){r=n.type_url.substring(n.type_url.lastIndexOf("/")+1),i=n.type_url.substring(0,n.type_url.lastIndexOf("/")+1);var o=this.lookup(r);o&&(n=o.decode(n.value))}if(!(n instanceof this.ctor)&&n instanceof ng){var a=n.$type.toObject(n,e),s=n.$type.fullName[0]==="."?n.$type.fullName.slice(1):n.$type.fullName;return i===""&&(i=t),r=i+s,a["@type"]=r,a}return this.toObject(n,e)}}});var mr=z((qw,pp)=>{"use strict";pp.exports=ie;var Xe=Sn();((ie.prototype=Object.create(Xe.prototype)).constructor=ie).className="Type";var ig=Ke(),ga=An(),fr=It(),rg=lr(),og=pr(),ma=ur(),ha=ir(),ag=tr(),Ee=be(),sg=ya(),lg=aa(),cg=ca(),cp=fa(),pg=da();function ie(n,e){Xe.call(this,n,e),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this._fieldsById=null,this._fieldsArray=null,this._oneofsArray=null,this._ctor=null}Object.defineProperties(ie.prototype,{fieldsById:{get:function(){if(this._fieldsById)return this._fieldsById;this._fieldsById={};for(var n=Object.keys(this.fields),e=0;e<n.length;++e){var t=this.fields[n[e]],i=t.id;if(this._fieldsById[i])throw Error("duplicate id "+i+" in "+this);this._fieldsById[i]=t}return this._fieldsById}},fieldsArray:{get:function(){return this._fieldsArray||(this._fieldsArray=Ee.toArray(this.fields))}},oneofsArray:{get:function(){return this._oneofsArray||(this._oneofsArray=Ee.toArray(this.oneofs))}},ctor:{get:function(){return this._ctor||(this.ctor=ie.generateConstructor(this)())},set:function(n){var e=n.prototype;e instanceof ma||((n.prototype=new ma).constructor=n,Ee.merge(n.prototype,e)),n.$type=n.prototype.$type=this,Ee.merge(n,ma,!0),this._ctor=n;for(var t=0;t<this.fieldsArray.length;++t)this._fieldsArray[t].resolve();var i={};for(t=0;t<this.oneofsArray.length;++t)i[this._oneofsArray[t].resolve().name]={get:Ee.oneOfGetter(this._oneofsArray[t].oneof),set:Ee.oneOfSetter(this._oneofsArray[t].oneof)};t&&Object.defineProperties(n.prototype,i)}}});ie.generateConstructor=function(e){for(var t=Ee.codegen(["p"],e.name),i=0,r;i<e.fieldsArray.length;++i)(r=e._fieldsArray[i]).map?t("this%s={}",Ee.safeProp(r.name)):r.repeated&&t("this%s=[]",Ee.safeProp(r.name));return t("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")};function dr(n){return n._fieldsById=n._fieldsArray=n._oneofsArray=null,delete n.encode,delete n.decode,delete n.verify,n}ie.fromJSON=function(e,t){var i=new ie(e,t.options);i.extensions=t.extensions,i.reserved=t.reserved;for(var r=Object.keys(t.fields),o=0;o<r.length;++o)i.add((typeof t.fields[r[o]].keyType!="undefined"?rg.fromJSON:fr.fromJSON)(r[o],t.fields[r[o]]));if(t.oneofs)for(r=Object.keys(t.oneofs),o=0;o<r.length;++o)i.add(ga.fromJSON(r[o],t.oneofs[r[o]]));if(t.nested)for(r=Object.keys(t.nested),o=0;o<r.length;++o){var a=t.nested[r[o]];i.add((a.id!==void 0?fr.fromJSON:a.fields!==void 0?ie.fromJSON:a.values!==void 0?ig.fromJSON:a.methods!==void 0?og.fromJSON:Xe.fromJSON)(r[o],a))}return t.extensions&&t.extensions.length&&(i.extensions=t.extensions),t.reserved&&t.reserved.length&&(i.reserved=t.reserved),t.group&&(i.group=!0),t.comment&&(i.comment=t.comment),i};ie.prototype.toJSON=function(e){var t=Xe.prototype.toJSON.call(this,e),i=e?!!e.keepComments:!1;return Ee.toObject(["options",t&&t.options||void 0,"oneofs",Xe.arrayToJSON(this.oneofsArray,e),"fields",Xe.arrayToJSON(this.fieldsArray.filter(function(r){return!r.declaringField}),e)||{},"extensions",this.extensions&&this.extensions.length?this.extensions:void 0,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"group",this.group||void 0,"nested",t&&t.nested||void 0,"comment",i?this.comment:void 0])};ie.prototype.resolveAll=function(){for(var e=this.fieldsArray,t=0;t<e.length;)e[t++].resolve();var i=this.oneofsArray;for(t=0;t<i.length;)i[t++].resolve();return Xe.prototype.resolveAll.call(this)};ie.prototype.get=function(e){return this.fields[e]||this.oneofs&&this.oneofs[e]||this.nested&&this.nested[e]||null};ie.prototype.add=function(e){if(this.get(e.name))throw Error("duplicate name '"+e.name+"' in "+this);if(e instanceof fr&&e.extend===void 0){if(this._fieldsById?this._fieldsById[e.id]:this.fieldsById[e.id])throw Error("duplicate id "+e.id+" in "+this);if(this.isReservedId(e.id))throw Error("id "+e.id+" is reserved in "+this);if(this.isReservedName(e.name))throw Error("name '"+e.name+"' is reserved in "+this);return e.parent&&e.parent.remove(e),this.fields[e.name]=e,e.message=this,e.onAdd(this),dr(this)}return e instanceof ga?(this.oneofs||(this.oneofs={}),this.oneofs[e.name]=e,e.onAdd(this),dr(this)):Xe.prototype.add.call(this,e)};ie.prototype.remove=function(e){if(e instanceof fr&&e.extend===void 0){if(!this.fields||this.fields[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.fields[e.name],e.parent=null,e.onRemove(this),dr(this)}if(e instanceof ga){if(!this.oneofs||this.oneofs[e.name]!==e)throw Error(e+" is not a member of "+this);return delete this.oneofs[e.name],e.parent=null,e.onRemove(this),dr(this)}return Xe.prototype.remove.call(this,e)};ie.prototype.isReservedId=function(e){return Xe.isReservedId(this.reserved,e)};ie.prototype.isReservedName=function(e){return Xe.isReservedName(this.reserved,e)};ie.prototype.create=function(e){return new this.ctor(e)};ie.prototype.setup=function(){for(var e=this.fullName,t=[],i=0;i<this.fieldsArray.length;++i)t.push(this._fieldsArray[i].resolve().resolvedType);this.encode=sg(this)({Writer:ag,types:t,util:Ee}),this.decode=lg(this)({Reader:ha,types:t,util:Ee}),this.verify=cg(this)({types:t,util:Ee}),this.fromObject=cp.fromObject(this)({types:t,util:Ee}),this.toObject=cp.toObject(this)({types:t,util:Ee});var r=pg[e];if(r){var o=Object.create(this);o.fromObject=this.fromObject,this.fromObject=r.fromObject.bind(o),o.toObject=this.toObject,this.toObject=r.toObject.bind(o)}return this};ie.prototype.encode=function(e,t){return this.setup().encode(e,t)};ie.prototype.encodeDelimited=function(e,t){return this.encode(e,t&&t.len?t.fork():t).ldelim()};ie.prototype.decode=function(e,t){return this.setup().decode(e,t)};ie.prototype.decodeDelimited=function(e){return e instanceof ha||(e=ha.create(e)),this.decode(e,e.uint32())};ie.prototype.verify=function(e){return this.setup().verify(e)};ie.prototype.fromObject=function(e){return this.setup().fromObject(e)};ie.prototype.toObject=function(e,t){return this.setup().toObject(e,t)};ie.d=function(e){return function(i){Ee.decorateType(i,e)}}});var yr=z((Ww,hp)=>{"use strict";hp.exports=Le;var gr=Sn();((Le.prototype=Object.create(gr.prototype)).constructor=Le).className="Root";var xa=It(),fp=Ke(),ug=An(),Lt=be(),dp,ba,mi;function Le(n){gr.call(this,"",n),this.deferred=[],this.files=[]}Le.fromJSON=function(e,t){return t||(t=new Le),e.options&&t.setOptions(e.options),t.addJSON(e.nested)};Le.prototype.resolvePath=Lt.path.resolve;Le.prototype.fetch=Lt.fetch;function mp(){}Le.prototype.load=function n(e,t,i){typeof t=="function"&&(i=t,t=void 0);var r=this;if(!i)return Lt.asPromise(n,r,e,t);var o=i===mp;function a(b,E){if(i){var f=i;if(i=null,o)throw b;f(b,E)}}function s(b){var E=b.lastIndexOf("google/protobuf/");if(E>-1){var f=b.substring(E);if(f in mi)return f}return null}function l(b,E){try{if(Lt.isString(E)&&E.charAt(0)==="{"&&(E=JSON.parse(E)),!Lt.isString(E))r.setOptions(E.options).addJSON(E.nested);else{ba.filename=b;var f=ba(E,r,t),d,h=0;if(f.imports)for(;h<f.imports.length;++h)(d=s(f.imports[h])||r.resolvePath(b,f.imports[h]))&&c(d);if(f.weakImports)for(h=0;h<f.weakImports.length;++h)(d=s(f.weakImports[h])||r.resolvePath(b,f.weakImports[h]))&&c(d,!0)}}catch(v){a(v)}!o&&!p&&a(null,r)}function c(b,E){if(b=s(b)||b,!(r.files.indexOf(b)>-1)){if(r.files.push(b),b in mi){o?l(b,mi[b]):(++p,setTimeout(function(){--p,l(b,mi[b])}));return}if(o){var f;try{f=Lt.fs.readFileSync(b).toString("utf8")}catch(d){E||a(d);return}l(b,f)}else++p,r.fetch(b,function(d,h){if(--p,!!i){if(d){E?p||a(null,r):a(d);return}l(b,h)}})}}var p=0;Lt.isString(e)&&(e=[e]);for(var m=0,g;m<e.length;++m)(g=r.resolvePath("",e[m]))&&c(g);if(o)return r;p||a(null,r)};Le.prototype.loadSync=function(e,t){if(!Lt.isNode)throw Error("not supported");return this.load(e,t,mp)};Le.prototype.resolveAll=function(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map(function(e){return"'extend "+e.extend+"' in "+e.parent.fullName}).join(", "));return gr.prototype.resolveAll.call(this)};var hr=/^[A-Z]/;function up(n,e){var t=e.parent.lookup(e.extend);if(t){var i=new xa(e.fullName,e.id,e.type,e.rule,void 0,e.options);return t.get(i.name)||(i.declaringField=e,e.extensionField=i,t.add(i)),!0}return!1}Le.prototype._handleAdd=function(e){if(e instanceof xa)e.extend!==void 0&&!e.extensionField&&(up(this,e)||this.deferred.push(e));else if(e instanceof fp)hr.test(e.name)&&(e.parent[e.name]=e.values);else if(!(e instanceof ug)){if(e instanceof dp)for(var t=0;t<this.deferred.length;)up(this,this.deferred[t])?this.deferred.splice(t,1):++t;for(var i=0;i<e.nestedArray.length;++i)this._handleAdd(e._nestedArray[i]);hr.test(e.name)&&(e.parent[e.name]=e)}};Le.prototype._handleRemove=function(e){if(e instanceof xa){if(e.extend!==void 0)if(e.extensionField)e.extensionField.parent.remove(e.extensionField),e.extensionField=null;else{var t=this.deferred.indexOf(e);t>-1&&this.deferred.splice(t,1)}}else if(e instanceof fp)hr.test(e.name)&&delete e.parent[e.name];else if(e instanceof gr){for(var i=0;i<e.nestedArray.length;++i)this._handleRemove(e._nestedArray[i]);hr.test(e.name)&&delete e.parent[e.name]}};Le._configure=function(n,e,t){dp=n,ba=e,mi=t}});var be=z((Yw,yp)=>{"use strict";var ce=yp.exports=tt(),gp=Ko(),wa,va;ce.codegen=Lc();ce.fetch=Mc();ce.path=Uc();ce.fs=ce.inquire("fs");ce.toArray=function(e){if(e){for(var t=Object.keys(e),i=new Array(t.length),r=0;r<t.length;)i[r]=e[t[r++]];return i}return[]};ce.toObject=function(e){for(var t={},i=0;i<e.length;){var r=e[i++],o=e[i++];o!==void 0&&(t[r]=o)}return t};var fg=/\\/g,dg=/"/g;ce.isReserved=function(e){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(e)};ce.safeProp=function(e){return!/^[$\w_]+$/.test(e)||ce.isReserved(e)?'["'+e.replace(fg,"\\\\").replace(dg,'\\"')+'"]':"."+e};ce.ucFirst=function(e){return e.charAt(0).toUpperCase()+e.substring(1)};var mg=/_([a-z])/g;ce.camelCase=function(e){return e.substring(0,1)+e.substring(1).replace(mg,function(t,i){return i.toUpperCase()})};ce.compareFieldsById=function(e,t){return e.id-t.id};ce.decorateType=function(e,t){if(e.$type)return t&&e.$type.name!==t&&(ce.decorateRoot.remove(e.$type),e.$type.name=t,ce.decorateRoot.add(e.$type)),e.$type;wa||(wa=mr());var i=new wa(t||e.name);return ce.decorateRoot.add(i),i.ctor=e,Object.defineProperty(e,"$type",{value:i,enumerable:!1}),Object.defineProperty(e.prototype,"$type",{value:i,enumerable:!1}),i};var hg=0;ce.decorateEnum=function(e){if(e.$type)return e.$type;va||(va=Ke());var t=new va("Enum"+hg++,e);return ce.decorateRoot.add(t),Object.defineProperty(e,"$type",{value:t,enumerable:!1}),t};ce.setProperty=function(e,t,i){function r(o,a,s){var l=a.shift();if(l==="__proto__"||l==="prototype")return o;if(a.length>0)o[l]=r(o[l]||{},a,s);else{var c=o[l];c&&(s=[].concat(c).concat(s)),o[l]=s}return o}if(typeof e!="object")throw TypeError("dst must be an object");if(!t)throw TypeError("path must be specified");return t=t.split("."),r(e,t,i)};Object.defineProperty(ce,"decorateRoot",{get:function(){return gp.decorated||(gp.decorated=new(yr()))}})});var sn=z((zw,bp)=>{"use strict";bp.exports=Pe;Pe.className="ReflectionObject";var br=be(),xr;function Pe(n,e){if(!br.isString(n))throw TypeError("name must be a string");if(e&&!br.isObject(e))throw TypeError("options must be an object");this.options=e,this.parsedOptions=null,this.name=n,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}Object.defineProperties(Pe.prototype,{root:{get:function(){for(var n=this;n.parent!==null;)n=n.parent;return n}},fullName:{get:function(){for(var n=[this.name],e=this.parent;e;)n.unshift(e.name),e=e.parent;return n.join(".")}}});Pe.prototype.toJSON=function(){throw Error()};Pe.prototype.onAdd=function(e){this.parent&&this.parent!==e&&this.parent.remove(this),this.parent=e,this.resolved=!1;var t=e.root;t instanceof xr&&t._handleAdd(this)};Pe.prototype.onRemove=function(e){var t=e.root;t instanceof xr&&t._handleRemove(this),this.parent=null,this.resolved=!1};Pe.prototype.resolve=function(){return this.resolved?this:(this.root instanceof xr&&(this.resolved=!0),this)};Pe.prototype.getOption=function(e){if(this.options)return this.options[e]};Pe.prototype.setOption=function(e,t,i){return(!i||!this.options||this.options[e]===void 0)&&((this.options||(this.options={}))[e]=t),this};Pe.prototype.setParsedOption=function(e,t,i){this.parsedOptions||(this.parsedOptions=[]);var r=this.parsedOptions;if(i){var o=r.find(function(l){return Object.prototype.hasOwnProperty.call(l,e)});if(o){var a=o[e];br.setProperty(a,i,t)}else o={},o[e]=br.setProperty({},i,t),r.push(o)}else{var s={};s[e]=t,r.push(s)}return this};Pe.prototype.setOptions=function(e,t){if(e)for(var i=Object.keys(e),r=0;r<i.length;++r)this.setOption(i[r],e[i[r]],t);return this};Pe.prototype.toString=function(){var e=this.constructor.className,t=this.fullName;return t.length?e+" "+t:e};Pe._configure=function(n){xr=n}});var Ke=z((Zw,vp)=>{"use strict";vp.exports=ot;var xp=sn();((ot.prototype=Object.create(xp.prototype)).constructor=ot).className="Enum";var wp=Sn(),wr=be();function ot(n,e,t,i,r,o){if(xp.call(this,n,t),e&&typeof e!="object")throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=i,this.comments=r||{},this.valuesOptions=o,this.reserved=void 0,e)for(var a=Object.keys(e),s=0;s<a.length;++s)typeof e[a[s]]=="number"&&(this.valuesById[this.values[a[s]]=e[a[s]]]=a[s])}ot.fromJSON=function(e,t){var i=new ot(e,t.values,t.options,t.comment,t.comments);return i.reserved=t.reserved,i};ot.prototype.toJSON=function(e){var t=e?!!e.keepComments:!1;return wr.toObject(["options",this.options,"valuesOptions",this.valuesOptions,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"comment",t?this.comment:void 0,"comments",t?this.comments:void 0])};ot.prototype.add=function(e,t,i,r){if(!wr.isString(e))throw TypeError("name must be a string");if(!wr.isInteger(t))throw TypeError("id must be an integer");if(this.values[e]!==void 0)throw Error("duplicate name '"+e+"' in "+this);if(this.isReservedId(t))throw Error("id "+t+" is reserved in "+this);if(this.isReservedName(e))throw Error("name '"+e+"' is reserved in "+this);if(this.valuesById[t]!==void 0){if(!(this.options&&this.options.allow_alias))throw Error("duplicate id "+t+" in "+this);this.values[e]=t}else this.valuesById[this.values[e]=t]=e;return r&&(this.valuesOptions===void 0&&(this.valuesOptions={}),this.valuesOptions[e]=r||null),this.comments[e]=i||null,this};ot.prototype.remove=function(e){if(!wr.isString(e))throw TypeError("name must be a string");var t=this.values[e];if(t==null)throw Error("name '"+e+"' does not exist in "+this);return delete this.valuesById[t],delete this.values[e],delete this.comments[e],this.valuesOptions&&delete this.valuesOptions[e],this};ot.prototype.isReservedId=function(e){return wp.isReservedId(this.reserved,e)};ot.prototype.isReservedName=function(e){return wp.isReservedName(this.reserved,e)}});var ya=z((Gw,_p)=>{"use strict";_p.exports=yg;var gg=Ke(),Ea=an(),_a=be();function Ep(n,e,t,i){return e.resolvedType.group?n("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",t,i,(e.id<<3|3)>>>0,(e.id<<3|4)>>>0):n("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",t,i,(e.id<<3|2)>>>0)}function yg(n){for(var e=_a.codegen(["m","w"],n.name+"$encode")("if(!w)")("w=Writer.create()"),t,i,r=n.fieldsArray.slice().sort(_a.compareFieldsById),t=0;t<r.length;++t){var o=r[t].resolve(),a=n._fieldsArray.indexOf(o),s=o.resolvedType instanceof gg?"int32":o.type,l=Ea.basic[s];i="m"+_a.safeProp(o.name),o.map?(e("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){",i,o.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",i)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(o.id<<3|2)>>>0,8|Ea.mapKey[o.keyType],o.keyType),l===void 0?e("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",a,i):e(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|l,s,i),e("}")("}")):o.repeated?(e("if(%s!=null&&%s.length){",i,i),o.packed&&Ea.packed[s]!==void 0?e("w.uint32(%i).fork()",(o.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",i)("w.%s(%s[i])",s,i)("w.ldelim()"):(e("for(var i=0;i<%s.length;++i)",i),l===void 0?Ep(e,o,a,i+"[i]"):e("w.uint32(%i).%s(%s[i])",(o.id<<3|l)>>>0,s,i)),e("}")):(o.optional&&e("if(%s!=null&&Object.hasOwnProperty.call(m,%j))",i,o.name),l===void 0?Ep(e,o,a,i):e("w.uint32(%i).%s(%s)",(o.id<<3|l)>>>0,s,i))}return e("return w")}});var Ap=z((Vw,Tp)=>{"use strict";var V=Tp.exports=Ic();V.build="light";function bg(n,e,t){return typeof e=="function"?(t=e,e=new V.Root):e||(e=new V.Root),e.load(n,t)}V.load=bg;function xg(n,e){return e||(e=new V.Root),e.loadSync(n)}V.loadSync=xg;V.encoder=ya();V.decoder=aa();V.verifier=ca();V.converter=fa();V.ReflectionObject=sn();V.Namespace=Sn();V.Root=yr();V.Enum=Ke();V.Type=mr();V.Field=It();V.OneOf=An();V.MapField=lr();V.Service=pr();V.Method=cr();V.Message=ur();V.wrappers=da();V.types=an();V.util=be();V.ReflectionObject._configure(V.Root);V.Namespace._configure(V.Type,V.Service,V.Enum);V.Root._configure(V.Type);V.Field._configure(V.Type)});var Aa=z((Kw,Sp)=>{"use strict";Sp.exports=Np;var Ta=/[\s{}=;:[\],'"()<>]/g,wg=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,vg=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,Eg=/^ *[*/]+ */,_g=/^\s*\*?\/*/,Tg=/\n/g,Ag=/\s/,kg=/\\(.?)/g,Ng={0:"\0",r:"\r",n:`
`,t:"	"};function kp(n){return n.replace(kg,function(e,t){switch(t){case"\\":case"":return t;default:return Ng[t]||""}})}Np.unescape=kp;function Np(n,e){n=n.toString();var t=0,i=n.length,r=1,o=0,a={},s=[],l=null;function c(A){return Error("illegal "+A+" (line "+r+")")}function p(){var A=l==="'"?vg:wg;A.lastIndex=t-1;var O=A.exec(n);if(!O)throw c("string");return t=A.lastIndex,d(l),l=null,kp(O[1])}function m(A){return n.charAt(A)}function g(A,O,T){var k={type:n.charAt(A++),lineEmpty:!1,leading:T},w;e?w=2:w=3;var D=A-w,C;do if(--D<0||(C=n.charAt(D))===`
`){k.lineEmpty=!0;break}while(C===" "||C==="	");for(var N=n.substring(A,O).split(Tg),P=0;P<N.length;++P)N[P]=N[P].replace(e?_g:Eg,"").trim();k.text=N.join(`
`).trim(),a[r]=k,o=r}function b(A){var O=E(A),T=n.substring(A,O),k=/^\s*\/\//.test(T);return k}function E(A){for(var O=A;O<i&&m(O)!==`
`;)O++;return O}function f(){if(s.length>0)return s.shift();if(l)return p();var A,O,T,k,w,D=t===0;do{if(t===i)return null;for(A=!1;Ag.test(T=m(t));)if(T===`
`&&(D=!0,++r),++t===i)return null;if(m(t)==="/"){if(++t===i)throw c("comment");if(m(t)==="/")if(e){if(k=t,w=!1,b(t-1)){w=!0;do if(t=E(t),t===i||(t++,!D))break;while(b(t))}else t=Math.min(i,E(t)+1);w&&(g(k,t,D),D=!0),r++,A=!0}else{for(w=m(k=t+1)==="/";m(++t)!==`
`;)if(t===i)return null;++t,w&&(g(k,t-1,D),D=!0),++r,A=!0}else if((T=m(t))==="*"){k=t+1,w=e||m(k)==="*";do{if(T===`
`&&++r,++t===i)throw c("comment");O=T,T=m(t)}while(O!=="*"||T!=="/");++t,w&&(g(k,t-2,D),D=!0),A=!0}else return"/"}}while(A);var C=t;Ta.lastIndex=0;var N=Ta.test(m(C++));if(!N)for(;C<i&&!Ta.test(m(C));)++C;var P=n.substring(t,t=C);return(P==='"'||P==="'")&&(l=P),P}function d(A){s.push(A)}function h(){if(!s.length){var A=f();if(A===null)return null;d(A)}return s[0]}function v(A,O){var T=h(),k=T===A;if(k)return f(),!0;if(!O)throw c("token '"+T+"', '"+A+"' expected");return!1}function _(A){var O=null,T;return A===void 0?(T=a[r-1],delete a[r-1],T&&(e||T.type==="*"||T.lineEmpty)&&(O=T.leading?T.text:null)):(o<A&&h(),T=a[A],delete a[A],T&&!T.lineEmpty&&(e||T.type==="/")&&(O=T.leading?null:T.text)),O}return Object.defineProperty({next:f,peek:h,push:d,skip:v,cmnt:_},"line",{get:function(){return r}})}});var Fp=z((Xw,Ip)=>{"use strict";Ip.exports=gt;gt.filename=null;gt.defaults={keepCase:!1};var Sg=Aa(),Rp=yr(),Op=mr(),Cp=It(),Rg=lr(),Dp=An(),Og=Ke(),Cg=pr(),Dg=cr(),ka=an(),Na=be(),Ig=/^[1-9][0-9]*$/,Fg=/^-?[1-9][0-9]*$/,Lg=/^0[x][0-9a-fA-F]+$/,Pg=/^-?0[x][0-9a-fA-F]+$/,Mg=/^0[0-7]+$/,$g=/^-?0[0-7]+$/,Bg=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,at=/^[a-zA-Z_][a-zA-Z_0-9]*$/,st=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,Ug=/^(?:\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;function gt(n,e,t){e instanceof Rp||(t=e,e=new Rp),t||(t=gt.defaults);var i=t.preferTrailingComment||!1,r=Sg(n,t.alternateCommentMode||!1),o=r.next,a=r.push,s=r.peek,l=r.skip,c=r.cmnt,p=!0,m,g,b,E,f=!1,d=e,h=t.keepCase?function(x){return x}:Na.camelCase;function v(x,S,I){var F=gt.filename;return I||(gt.filename=null),Error("illegal "+(S||"token")+" '"+x+"' ("+(F?F+", ":"")+"line "+r.line+")")}function _(){var x=[],S;do{if((S=o())!=='"'&&S!=="'")throw v(S);x.push(o()),l(S),S=s()}while(S==='"'||S==="'");return x.join("")}function A(x){var S=o();switch(S){case"'":case'"':return a(S),_();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return T(S,!0)}catch(I){if(x&&st.test(S))return S;throw v(S,"value")}}function O(x,S){var I,F;do S&&((I=s())==='"'||I==="'")?x.push(_()):x.push([F=k(o()),l("to",!0)?k(o()):F]);while(l(",",!0));l(";")}function T(x,S){var I=1;switch(x.charAt(0)==="-"&&(I=-1,x=x.substring(1)),x){case"inf":case"INF":case"Inf":return I*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(Ig.test(x))return I*parseInt(x,10);if(Lg.test(x))return I*parseInt(x,16);if(Mg.test(x))return I*parseInt(x,8);if(Bg.test(x))return I*parseFloat(x);throw v(x,"number",S)}function k(x,S){switch(x){case"max":case"MAX":case"Max":return *********;case"0":return 0}if(!S&&x.charAt(0)==="-")throw v(x,"id");if(Fg.test(x))return parseInt(x,10);if(Pg.test(x))return parseInt(x,16);if($g.test(x))return parseInt(x,8);throw v(x,"id")}function w(){if(m!==void 0)throw v("package");if(m=o(),!st.test(m))throw v(m,"name");d=d.define(m),l(";")}function D(){var x=s(),S;switch(x){case"weak":S=b||(b=[]),o();break;case"public":o();default:S=g||(g=[]);break}x=_(),l(";"),S.push(x)}function C(){if(l("="),E=_(),f=E==="proto3",!f&&E!=="proto2")throw v(E,"syntax");l(";")}function N(x,S){switch(S){case"option":return U(x,S),l(";"),!0;case"message":return L(x,S),!0;case"enum":return Z(x,S),!0;case"service":return Yt(x,S),!0;case"extend":return u(x,S),!0}return!1}function P(x,S,I){var F=r.line;if(x&&(typeof x.comment!="string"&&(x.comment=c()),x.filename=gt.filename),l("{",!0)){for(var $;($=o())!=="}";)S($);l(";",!0)}else I&&I(),l(";"),x&&(typeof x.comment!="string"||i)&&(x.comment=c(F)||x.comment)}function L(x,S){if(!at.test(S=o()))throw v(S,"type name");var I=new Op(S);P(I,function($){if(!N(I,$))switch($){case"map":M(I,$);break;case"required":case"repeated":j(I,$);break;case"optional":f?j(I,"proto3_optional"):j(I,"optional");break;case"oneof":ne(I,$);break;case"extensions":O(I.extensions||(I.extensions=[]));break;case"reserved":O(I.reserved||(I.reserved=[]),!0);break;default:if(!f||!st.test($))throw v($);a($),j(I,"optional");break}}),x.add(I)}function j(x,S,I){var F=o();if(F==="group"){Y(x,S);return}for(;F.endsWith(".")||s().startsWith(".");)F+=o();if(!st.test(F))throw v(F,"type");var $=o();if(!at.test($))throw v($,"name");$=h($),l("=");var W=new Cp($,k(o()),F,S,I);if(P(W,function(re){if(re==="option")U(W,re),l(";");else throw v(re)},function(){ct(W)}),S==="proto3_optional"){var te=new Dp("_"+$);W.setOption("proto3_optional",!0),te.add(W),x.add(te)}else x.add(W);!f&&W.repeated&&(ka.packed[F]!==void 0||ka.basic[F]===void 0)&&W.setOption("packed",!1,!0)}function Y(x,S){var I=o();if(!at.test(I))throw v(I,"name");var F=Na.lcFirst(I);I===F&&(I=Na.ucFirst(I)),l("=");var $=k(o()),W=new Op(I);W.group=!0;var te=new Cp(F,$,I,S);te.filename=gt.filename,P(W,function(re){switch(re){case"option":U(W,re),l(";");break;case"required":case"repeated":j(W,re);break;case"optional":f?j(W,"proto3_optional"):j(W,"optional");break;case"message":L(W,re);break;case"enum":Z(W,re);break;default:throw v(re)}}),x.add(W).add(te)}function M(x){l("<");var S=o();if(ka.mapKey[S]===void 0)throw v(S,"type");l(",");var I=o();if(!st.test(I))throw v(I,"type");l(">");var F=o();if(!at.test(F))throw v(F,"name");l("=");var $=new Rg(h(F),k(o()),S,I);P($,function(te){if(te==="option")U($,te),l(";");else throw v(te)},function(){ct($)}),x.add($)}function ne(x,S){if(!at.test(S=o()))throw v(S,"name");var I=new Dp(h(S));P(I,function($){$==="option"?(U(I,$),l(";")):(a($),j(I,"optional"))}),x.add(I)}function Z(x,S){if(!at.test(S=o()))throw v(S,"name");var I=new Og(S);P(I,function($){switch($){case"option":U(I,$),l(";");break;case"reserved":O(I.reserved||(I.reserved=[]),!0);break;default:pe(I,$)}}),x.add(I)}function pe(x,S){if(!at.test(S))throw v(S,"name");l("=");var I=k(o(),!0),F={options:void 0};F.setOption=function($,W){this.options===void 0&&(this.options={}),this.options[$]=W},P(F,function(W){if(W==="option")U(F,W),l(";");else throw v(W)},function(){ct(F)}),x.add(S,I,F.comment,F.options)}function U(x,S){var I=l("(",!0);if(!st.test(S=o()))throw v(S,"name");var F=S,$=F,W;I&&(l(")"),F="("+F+")",$=F,S=s(),Ug.test(S)&&(W=S.slice(1),F+=S,o())),l("=");var te=Re(x,F);Q(x,$,te,W)}function Re(x,S){if(l("{",!0)){for(var I={};!l("}",!0);){if(!at.test(R=o()))throw v(R,"name");if(R===null)throw v(R,"end of input");var F,$=R;if(l(":",!0),s()==="{")F=Re(x,S+"."+R);else if(s()==="["){F=[];var W;if(l("[",!0)){do W=A(!0),F.push(W);while(l(",",!0));l("]"),typeof W!="undefined"&&G(x,S+"."+R,W)}}else F=A(!0),G(x,S+"."+R,F);var te=I[$];te&&(F=[].concat(te).concat(F)),I[$]=F,l(",",!0),l(";",!0)}return I}var ee=A(!0);return G(x,S,ee),ee}function G(x,S,I){x.setOption&&x.setOption(S,I)}function Q(x,S,I,F){x.setParsedOption&&x.setParsedOption(S,I,F)}function ct(x){if(l("[",!0)){do U(x,"option");while(l(",",!0));l("]")}return x}function Yt(x,S){if(!at.test(S=o()))throw v(S,"service name");var I=new Cg(S);P(I,function($){if(!N(I,$))if($==="rpc")y(I,$);else throw v($)}),x.add(I)}function y(x,S){var I=c(),F=S;if(!at.test(S=o()))throw v(S,"name");var $=S,W,te,ee,re;if(l("("),l("stream",!0)&&(te=!0),!st.test(S=o())||(W=S,l(")"),l("returns"),l("("),l("stream",!0)&&(re=!0),!st.test(S=o())))throw v(S);ee=S,l(")");var gn=new Dg($,F,W,ee,te,re);gn.comment=I,P(gn,function(zt){if(zt==="option")U(gn,zt),l(";");else throw v(zt)}),x.add(gn)}function u(x,S){if(!st.test(S=o()))throw v(S,"reference");var I=S;P(null,function($){switch($){case"required":case"repeated":j(x,$,I);break;case"optional":f?j(x,"proto3_optional",I):j(x,"optional",I);break;default:if(!f||!st.test($))throw v($);a($),j(x,"optional",I);break}})}for(var R;(R=o())!==null;)switch(R){case"package":if(!p)throw v(R);w();break;case"import":if(!p)throw v(R);D();break;case"syntax":if(!p)throw v(R);C();break;case"option":U(d,R),l(";");break;default:if(N(d,R)){p=!1;continue}throw v(R)}return gt.filename=null,{package:m,imports:g,weakImports:b,syntax:E,root:e}}});var Mp=z((Jw,Pp)=>{"use strict";Pp.exports=Je;var jg=/\/|\./;function Je(n,e){jg.test(n)||(n="google/protobuf/"+n+".proto",e={nested:{google:{nested:{protobuf:{nested:e}}}}}),Je[n]=e}Je("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}});var Lp;Je("duration",{Duration:Lp={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}});Je("timestamp",{Timestamp:Lp});Je("empty",{Empty:{fields:{}}});Je("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}});Je("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}});Je("field_mask",{FieldMask:{fields:{paths:{rule:"repeated",type:"string",id:1}}}});Je.get=function(e){return Je[e]||null}});var Bp=z((Qw,$p)=>{"use strict";var Pt=$p.exports=Ap();Pt.build="full";Pt.tokenize=Aa();Pt.parse=Fp();Pt.common=Mp();Pt.Root._configure(Pt.Type,Pt.parse,Pt.common)});var jp=z((ev,Up)=>{"use strict";Up.exports=Bp()});var mu=z(cn=>{"use strict";Object.defineProperty(cn,"__esModule",{value:!0});var eu=/highlight-(?:text|source)-([a-z0-9]+)/;function nu(n){n.addRule("highlightedCodeBlock",{filter:function(e){var t=e.firstChild;return e.nodeName==="DIV"&&eu.test(e.className)&&t&&t.nodeName==="PRE"},replacement:function(e,t,i){var r=t.className||"",o=(r.match(eu)||[null,""])[1];return`

`+i.fence+o+`
`+t.firstChild.textContent+`
`+i.fence+`

`}})}function iu(n){n.addRule("strikethrough",{filter:["del","s","strike"],replacement:function(e){return"~~"+e+"~~"}})}var n0=Array.prototype.indexOf,i0=Array.prototype.every,Mt={},r0={left:":---",right:"---:",center:":---:"},La=null,ru=null,tu=new WeakMap;function o0(n){return n?(n.getAttribute("align")||n.style.textAlign||"").toLowerCase():""}function ou(n){return n?r0[n]:"---"}function au(n,e){for(var t={left:0,right:0,center:0,"":0},i="",r=0;r<n.rows.length;++r){var o=n.rows[r];if(e<o.childNodes.length){var a=o0(o.childNodes[e]);++t[a],t[a]>t[i]&&(i=a)}}return i}Mt.tableCell={filter:["th","td"],replacement:function(n,e){return Ma(pu(e))?n:su(n,e)}};Mt.tableRow={filter:"tr",replacement:function(n,e){let t=pu(e);if(Ma(t))return n;var i="";if(a0(e)){let a=uu(t);for(var r=0;r<a;r++){let s=r<e.childNodes.length?e.childNodes[r]:null;var o=ou(au(t,r));i+=su(o,s,r)}}return`
`+n+(i?`
`+i:"")}};Mt.table={filter:function(n,e){return n.nodeName==="TABLE"},replacement:function(n,e){if(cu(e,ru)){let s=e.outerHTML,l=c0(e);return l===null||!l.classList.contains("joplin-table-wrapper")?`

<div class="joplin-table-wrapper">${s}</div>

`:s}else{if(Ma(e))return n;n=n.replace(/\n+/g,`
`);var t=n.trim().split(`
`);t.length>=2&&(t=t[1]);var i=/\| :?---/.test(t),r=uu(e),o="";if(r&&!i){o="|"+"     |".repeat(r)+`
|`;for(var a=0;a<r;++a)o+=" "+ou(au(e,a))+" |"}let s=e.caption&&e.caption.textContent||"",l=s?`${s}

`:"",c=`${o}${n}`.trimStart();return`

${l}${c}

`}}};Mt.tableCaption={filter:["caption"],replacement:()=>""};Mt.tableColgroup={filter:["colgroup","col"],replacement:()=>""};Mt.tableSection={filter:["thead","tbody","tfoot"],replacement:function(n){return n}};function a0(n){var e=n.parentNode;return e.nodeName==="THEAD"||e.firstChild===n&&(e.nodeName==="TABLE"||s0(e))&&i0.call(n.childNodes,function(t){return t.nodeName==="TH"})}function s0(n){var e=n.previousSibling;return n.nodeName==="TBODY"&&(!e||e.nodeName==="THEAD"&&/^\s*$/i.test(e.textContent))}function su(n,e=null,t=null){t===null&&(t=n0.call(e.parentNode.childNodes,e));var i=" ";t===0&&(i="| ");let r=n.trim().replace(/\n\r/g,"<br>").replace(/\n/g,"<br>");for(r=r.replace(/\|+/g,"\\|");r.length<3;)r+=" ";return e&&(r=p0(r,e," ")),i+r+" |"}function lu(n){if(!n.childNodes)return!1;for(let e=0;e<n.childNodes.length;e++){let t=n.childNodes[e];if(t.nodeName==="TABLE"||lu(t))return!0}return!1}var Pa=(n,e)=>{if(!n.childNodes)return!1;for(let t=0;t<n.childNodes.length;t++){let i=n.childNodes[t];if(e==="code"&&La&&La(i)||e.includes(i.nodeName)||Pa(i,e))return!0}return!1},cu=(n,e)=>{let t=["UL","OL","H1","H2","H3","H4","H5","H6","HR","BLOCKQUOTE"];return e.preserveNestedTables&&t.push("TABLE"),Pa(n,"code")||Pa(n,t)};function Ma(n){let e=tu.get(n);if(e!==void 0)return e;let t=l0(n);return tu.set(n,t),t}function l0(n){return!!(!n||!n.rows||n.rows.length===1&&n.rows[0].childNodes.length<=1||lu(n))}function c0(n){let e=n.parentNode;for(;e.nodeName!=="DIV";)if(e=e.parentNode,!e)return null;return e}function pu(n){let e=n.parentNode;for(;e.nodeName!=="TABLE";)if(e=e.parentNode,!e)return null;return e}function p0(n,e,t){let i=e.getAttribute("colspan")||1;for(let r=1;r<i;r++)n+=" | "+t.repeat(3);return n}function uu(n){let e=0;for(let t=0;t<n.rows.length;t++){let r=n.rows[t].childNodes.length;r>e&&(e=r)}return e}function fu(n){La=n.isCodeBlock,ru=n.options,n.keep(function(t){return!!(t.nodeName==="TABLE"&&cu(t,n.options))});for(var e in Mt)n.addRule(e,Mt[e])}function du(n){n.addRule("taskListItems",{filter:function(e){return e.type==="checkbox"&&e.parentNode.nodeName==="LI"},replacement:function(e,t){return(t.checked?"[x]":"[ ]")+" "}})}function u0(n){n.use([nu,iu,fu,du])}cn.gfm=u0;cn.highlightedCodeBlock=nu;cn.strikethrough=iu;cn.tables=fu;cn.taskListItems=du});var Gf=z(Fr=>{(function(n){n.parser=function(y,u){return new t(y,u)},n.SAXParser=t,n.SAXStream=c,n.createStream=l,n.MAX_BUFFER_LENGTH=64*1024;var e=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];n.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function t(y,u){if(!(this instanceof t))return new t(y,u);var R=this;r(R),R.q=R.c="",R.bufferCheckPosition=n.MAX_BUFFER_LENGTH,R.opt=u||{},R.opt.lowercase=R.opt.lowercase||R.opt.lowercasetags,R.looseCase=R.opt.lowercase?"toLowerCase":"toUpperCase",R.tags=[],R.closed=R.closedRoot=R.sawRoot=!1,R.tag=R.error=null,R.strict=!!y,R.noscript=!!(y||R.opt.noscript),R.state=w.BEGIN,R.strictEntities=R.opt.strictEntities,R.ENTITIES=R.strictEntities?Object.create(n.XML_ENTITIES):Object.create(n.ENTITIES),R.attribList=[],R.opt.xmlns&&(R.ns=Object.create(E)),R.trackPosition=R.opt.position!==!1,R.trackPosition&&(R.position=R.line=R.column=0),C(R,"onready")}Object.create||(Object.create=function(y){function u(){}u.prototype=y;var R=new u;return R}),Object.keys||(Object.keys=function(y){var u=[];for(var R in y)y.hasOwnProperty(R)&&u.push(R);return u});function i(y){for(var u=Math.max(n.MAX_BUFFER_LENGTH,10),R=0,x=0,S=e.length;x<S;x++){var I=y[e[x]].length;if(I>u)switch(e[x]){case"textNode":P(y);break;case"cdata":N(y,"oncdata",y.cdata),y.cdata="";break;case"script":N(y,"onscript",y.script),y.script="";break;default:j(y,"Max buffer length exceeded: "+e[x])}R=Math.max(R,I)}var F=n.MAX_BUFFER_LENGTH-R;y.bufferCheckPosition=F+y.position}function r(y){for(var u=0,R=e.length;u<R;u++)y[e[u]]=""}function o(y){P(y),y.cdata!==""&&(N(y,"oncdata",y.cdata),y.cdata=""),y.script!==""&&(N(y,"onscript",y.script),y.script="")}t.prototype={end:function(){Y(this)},write:Yt,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){o(this)}};var a;try{a=require("stream").Stream}catch(y){a=function(){}}a||(a=function(){});var s=n.EVENTS.filter(function(y){return y!=="error"&&y!=="end"});function l(y,u){return new c(y,u)}function c(y,u){if(!(this instanceof c))return new c(y,u);a.apply(this),this._parser=new t(y,u),this.writable=!0,this.readable=!0;var R=this;this._parser.onend=function(){R.emit("end")},this._parser.onerror=function(x){R.emit("error",x),R._parser.error=null},this._decoder=null,s.forEach(function(x){Object.defineProperty(R,"on"+x,{get:function(){return R._parser["on"+x]},set:function(S){if(!S)return R.removeAllListeners(x),R._parser["on"+x]=S,S;R.on(x,S)},enumerable:!0,configurable:!1})})}c.prototype=Object.create(a.prototype,{constructor:{value:c}}),c.prototype.write=function(y){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(y)){if(!this._decoder){var u=require("string_decoder").StringDecoder;this._decoder=new u("utf8")}y=this._decoder.write(y)}return this._parser.write(y.toString()),this.emit("data",y),!0},c.prototype.end=function(y){return y&&y.length&&this.write(y),this._parser.end(),!0},c.prototype.on=function(y,u){var R=this;return!R._parser["on"+y]&&s.indexOf(y)!==-1&&(R._parser["on"+y]=function(){var x=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);x.splice(0,0,y),R.emit.apply(R,x)}),a.prototype.on.call(R,y,u)};var p="[CDATA[",m="DOCTYPE",g="http://www.w3.org/XML/1998/namespace",b="http://www.w3.org/2000/xmlns/",E={xml:g,xmlns:b},f=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,d=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,h=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,v=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function _(y){return y===" "||y===`
`||y==="\r"||y==="	"}function A(y){return y==='"'||y==="'"}function O(y){return y===">"||_(y)}function T(y,u){return y.test(u)}function k(y,u){return!T(y,u)}var w=0;n.STATE={BEGIN:w++,BEGIN_WHITESPACE:w++,TEXT:w++,TEXT_ENTITY:w++,OPEN_WAKA:w++,SGML_DECL:w++,SGML_DECL_QUOTED:w++,DOCTYPE:w++,DOCTYPE_QUOTED:w++,DOCTYPE_DTD:w++,DOCTYPE_DTD_QUOTED:w++,COMMENT_STARTING:w++,COMMENT:w++,COMMENT_ENDING:w++,COMMENT_ENDED:w++,CDATA:w++,CDATA_ENDING:w++,CDATA_ENDING_2:w++,PROC_INST:w++,PROC_INST_BODY:w++,PROC_INST_ENDING:w++,OPEN_TAG:w++,OPEN_TAG_SLASH:w++,ATTRIB:w++,ATTRIB_NAME:w++,ATTRIB_NAME_SAW_WHITE:w++,ATTRIB_VALUE:w++,ATTRIB_VALUE_QUOTED:w++,ATTRIB_VALUE_CLOSED:w++,ATTRIB_VALUE_UNQUOTED:w++,ATTRIB_VALUE_ENTITY_Q:w++,ATTRIB_VALUE_ENTITY_U:w++,CLOSE_TAG:w++,CLOSE_TAG_SAW_WHITE:w++,SCRIPT:w++,SCRIPT_ENDING:w++},n.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},n.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(n.ENTITIES).forEach(function(y){var u=n.ENTITIES[y],R=typeof u=="number"?String.fromCharCode(u):u;n.ENTITIES[y]=R});for(var D in n.STATE)n.STATE[n.STATE[D]]=D;w=n.STATE;function C(y,u,R){y[u]&&y[u](R)}function N(y,u,R){y.textNode&&P(y),C(y,u,R)}function P(y){y.textNode=L(y.opt,y.textNode),y.textNode&&C(y,"ontext",y.textNode),y.textNode=""}function L(y,u){return y.trim&&(u=u.trim()),y.normalize&&(u=u.replace(/\s+/g," ")),u}function j(y,u){return P(y),y.trackPosition&&(u+=`
Line: `+y.line+`
Column: `+y.column+`
Char: `+y.c),u=new Error(u),y.error=u,C(y,"onerror",u),y}function Y(y){return y.sawRoot&&!y.closedRoot&&M(y,"Unclosed root tag"),y.state!==w.BEGIN&&y.state!==w.BEGIN_WHITESPACE&&y.state!==w.TEXT&&j(y,"Unexpected end"),P(y),y.c="",y.closed=!0,C(y,"onend"),t.call(y,y.strict,y.opt),y}function M(y,u){if(typeof y!="object"||!(y instanceof t))throw new Error("bad call to strictFail");y.strict&&j(y,u)}function ne(y){y.strict||(y.tagName=y.tagName[y.looseCase]());var u=y.tags[y.tags.length-1]||y,R=y.tag={name:y.tagName,attributes:{}};y.opt.xmlns&&(R.ns=u.ns),y.attribList.length=0,N(y,"onopentagstart",R)}function Z(y,u){var R=y.indexOf(":"),x=R<0?["",y]:y.split(":"),S=x[0],I=x[1];return u&&y==="xmlns"&&(S="xmlns",I=""),{prefix:S,local:I}}function pe(y){if(y.strict||(y.attribName=y.attribName[y.looseCase]()),y.attribList.indexOf(y.attribName)!==-1||y.tag.attributes.hasOwnProperty(y.attribName)){y.attribName=y.attribValue="";return}if(y.opt.xmlns){var u=Z(y.attribName,!0),R=u.prefix,x=u.local;if(R==="xmlns")if(x==="xml"&&y.attribValue!==g)M(y,"xml: prefix must be bound to "+g+`
Actual: `+y.attribValue);else if(x==="xmlns"&&y.attribValue!==b)M(y,"xmlns: prefix must be bound to "+b+`
Actual: `+y.attribValue);else{var S=y.tag,I=y.tags[y.tags.length-1]||y;S.ns===I.ns&&(S.ns=Object.create(I.ns)),S.ns[x]=y.attribValue}y.attribList.push([y.attribName,y.attribValue])}else y.tag.attributes[y.attribName]=y.attribValue,N(y,"onattribute",{name:y.attribName,value:y.attribValue});y.attribName=y.attribValue=""}function U(y,u){if(y.opt.xmlns){var R=y.tag,x=Z(y.tagName);R.prefix=x.prefix,R.local=x.local,R.uri=R.ns[x.prefix]||"",R.prefix&&!R.uri&&(M(y,"Unbound namespace prefix: "+JSON.stringify(y.tagName)),R.uri=x.prefix);var S=y.tags[y.tags.length-1]||y;R.ns&&S.ns!==R.ns&&Object.keys(R.ns).forEach(function(ls){N(y,"onopennamespace",{prefix:ls,uri:R.ns[ls]})});for(var I=0,F=y.attribList.length;I<F;I++){var $=y.attribList[I],W=$[0],te=$[1],ee=Z(W,!0),re=ee.prefix,gn=ee.local,Xr=re===""?"":R.ns[re]||"",zt={name:W,value:te,prefix:re,local:gn,uri:Xr};re&&re!=="xmlns"&&!Xr&&(M(y,"Unbound namespace prefix: "+JSON.stringify(re)),zt.uri=re),y.tag.attributes[W]=zt,N(y,"onattribute",zt)}y.attribList.length=0}y.tag.isSelfClosing=!!u,y.sawRoot=!0,y.tags.push(y.tag),N(y,"onopentag",y.tag),u||(!y.noscript&&y.tagName.toLowerCase()==="script"?y.state=w.SCRIPT:y.state=w.TEXT,y.tag=null,y.tagName=""),y.attribName=y.attribValue="",y.attribList.length=0}function Re(y){if(!y.tagName){M(y,"Weird empty close tag."),y.textNode+="</>",y.state=w.TEXT;return}if(y.script){if(y.tagName!=="script"){y.script+="</"+y.tagName+">",y.tagName="",y.state=w.SCRIPT;return}N(y,"onscript",y.script),y.script=""}var u=y.tags.length,R=y.tagName;y.strict||(R=R[y.looseCase]());for(var x=R;u--;){var S=y.tags[u];if(S.name!==x)M(y,"Unexpected close tag");else break}if(u<0){M(y,"Unmatched closing tag: "+y.tagName),y.textNode+="</"+y.tagName+">",y.state=w.TEXT;return}y.tagName=R;for(var I=y.tags.length;I-- >u;){var F=y.tag=y.tags.pop();y.tagName=y.tag.name,N(y,"onclosetag",y.tagName);var $={};for(var W in F.ns)$[W]=F.ns[W];var te=y.tags[y.tags.length-1]||y;y.opt.xmlns&&F.ns!==te.ns&&Object.keys(F.ns).forEach(function(ee){var re=F.ns[ee];N(y,"onclosenamespace",{prefix:ee,uri:re})})}u===0&&(y.closedRoot=!0),y.tagName=y.attribValue=y.attribName="",y.attribList.length=0,y.state=w.TEXT}function G(y){var u=y.entity,R=u.toLowerCase(),x,S="";return y.ENTITIES[u]?y.ENTITIES[u]:y.ENTITIES[R]?y.ENTITIES[R]:(u=R,u.charAt(0)==="#"&&(u.charAt(1)==="x"?(u=u.slice(2),x=parseInt(u,16),S=x.toString(16)):(u=u.slice(1),x=parseInt(u,10),S=x.toString(10))),u=u.replace(/^0+/,""),isNaN(x)||S.toLowerCase()!==u?(M(y,"Invalid character entity"),"&"+y.entity+";"):String.fromCodePoint(x))}function Q(y,u){u==="<"?(y.state=w.OPEN_WAKA,y.startTagPosition=y.position):_(u)||(M(y,"Non-whitespace before first tag."),y.textNode=u,y.state=w.TEXT)}function ct(y,u){var R="";return u<y.length&&(R=y.charAt(u)),R}function Yt(y){var u=this;if(this.error)throw this.error;if(u.closed)return j(u,"Cannot write after close. Assign an onready handler.");if(y===null)return Y(u);typeof y=="object"&&(y=y.toString());for(var R=0,x="";x=ct(y,R++),u.c=x,!!x;)switch(u.trackPosition&&(u.position++,x===`
`?(u.line++,u.column=0):u.column++),u.state){case w.BEGIN:if(u.state=w.BEGIN_WHITESPACE,x==="\uFEFF")continue;Q(u,x);continue;case w.BEGIN_WHITESPACE:Q(u,x);continue;case w.TEXT:if(u.sawRoot&&!u.closedRoot){for(var S=R-1;x&&x!=="<"&&x!=="&";)x=ct(y,R++),x&&u.trackPosition&&(u.position++,x===`
`?(u.line++,u.column=0):u.column++);u.textNode+=y.substring(S,R-1)}x==="<"&&!(u.sawRoot&&u.closedRoot&&!u.strict)?(u.state=w.OPEN_WAKA,u.startTagPosition=u.position):(!_(x)&&(!u.sawRoot||u.closedRoot)&&M(u,"Text data outside of root node."),x==="&"?u.state=w.TEXT_ENTITY:u.textNode+=x);continue;case w.SCRIPT:x==="<"?u.state=w.SCRIPT_ENDING:u.script+=x;continue;case w.SCRIPT_ENDING:x==="/"?u.state=w.CLOSE_TAG:(u.script+="<"+x,u.state=w.SCRIPT);continue;case w.OPEN_WAKA:if(x==="!")u.state=w.SGML_DECL,u.sgmlDecl="";else if(!_(x))if(T(f,x))u.state=w.OPEN_TAG,u.tagName=x;else if(x==="/")u.state=w.CLOSE_TAG,u.tagName="";else if(x==="?")u.state=w.PROC_INST,u.procInstName=u.procInstBody="";else{if(M(u,"Unencoded <"),u.startTagPosition+1<u.position){var I=u.position-u.startTagPosition;x=new Array(I).join(" ")+x}u.textNode+="<"+x,u.state=w.TEXT}continue;case w.SGML_DECL:(u.sgmlDecl+x).toUpperCase()===p?(N(u,"onopencdata"),u.state=w.CDATA,u.sgmlDecl="",u.cdata=""):u.sgmlDecl+x==="--"?(u.state=w.COMMENT,u.comment="",u.sgmlDecl=""):(u.sgmlDecl+x).toUpperCase()===m?(u.state=w.DOCTYPE,(u.doctype||u.sawRoot)&&M(u,"Inappropriately located doctype declaration"),u.doctype="",u.sgmlDecl=""):x===">"?(N(u,"onsgmldeclaration",u.sgmlDecl),u.sgmlDecl="",u.state=w.TEXT):(A(x)&&(u.state=w.SGML_DECL_QUOTED),u.sgmlDecl+=x);continue;case w.SGML_DECL_QUOTED:x===u.q&&(u.state=w.SGML_DECL,u.q=""),u.sgmlDecl+=x;continue;case w.DOCTYPE:x===">"?(u.state=w.TEXT,N(u,"ondoctype",u.doctype),u.doctype=!0):(u.doctype+=x,x==="["?u.state=w.DOCTYPE_DTD:A(x)&&(u.state=w.DOCTYPE_QUOTED,u.q=x));continue;case w.DOCTYPE_QUOTED:u.doctype+=x,x===u.q&&(u.q="",u.state=w.DOCTYPE);continue;case w.DOCTYPE_DTD:u.doctype+=x,x==="]"?u.state=w.DOCTYPE:A(x)&&(u.state=w.DOCTYPE_DTD_QUOTED,u.q=x);continue;case w.DOCTYPE_DTD_QUOTED:u.doctype+=x,x===u.q&&(u.state=w.DOCTYPE_DTD,u.q="");continue;case w.COMMENT:x==="-"?u.state=w.COMMENT_ENDING:u.comment+=x;continue;case w.COMMENT_ENDING:x==="-"?(u.state=w.COMMENT_ENDED,u.comment=L(u.opt,u.comment),u.comment&&N(u,"oncomment",u.comment),u.comment=""):(u.comment+="-"+x,u.state=w.COMMENT);continue;case w.COMMENT_ENDED:x!==">"?(M(u,"Malformed comment"),u.comment+="--"+x,u.state=w.COMMENT):u.state=w.TEXT;continue;case w.CDATA:x==="]"?u.state=w.CDATA_ENDING:u.cdata+=x;continue;case w.CDATA_ENDING:x==="]"?u.state=w.CDATA_ENDING_2:(u.cdata+="]"+x,u.state=w.CDATA);continue;case w.CDATA_ENDING_2:x===">"?(u.cdata&&N(u,"oncdata",u.cdata),N(u,"onclosecdata"),u.cdata="",u.state=w.TEXT):x==="]"?u.cdata+="]":(u.cdata+="]]"+x,u.state=w.CDATA);continue;case w.PROC_INST:x==="?"?u.state=w.PROC_INST_ENDING:_(x)?u.state=w.PROC_INST_BODY:u.procInstName+=x;continue;case w.PROC_INST_BODY:if(!u.procInstBody&&_(x))continue;x==="?"?u.state=w.PROC_INST_ENDING:u.procInstBody+=x;continue;case w.PROC_INST_ENDING:x===">"?(N(u,"onprocessinginstruction",{name:u.procInstName,body:u.procInstBody}),u.procInstName=u.procInstBody="",u.state=w.TEXT):(u.procInstBody+="?"+x,u.state=w.PROC_INST_BODY);continue;case w.OPEN_TAG:T(d,x)?u.tagName+=x:(ne(u),x===">"?U(u):x==="/"?u.state=w.OPEN_TAG_SLASH:(_(x)||M(u,"Invalid character in tag name"),u.state=w.ATTRIB));continue;case w.OPEN_TAG_SLASH:x===">"?(U(u,!0),Re(u)):(M(u,"Forward-slash in opening tag not followed by >"),u.state=w.ATTRIB);continue;case w.ATTRIB:if(_(x))continue;x===">"?U(u):x==="/"?u.state=w.OPEN_TAG_SLASH:T(f,x)?(u.attribName=x,u.attribValue="",u.state=w.ATTRIB_NAME):M(u,"Invalid attribute name");continue;case w.ATTRIB_NAME:x==="="?u.state=w.ATTRIB_VALUE:x===">"?(M(u,"Attribute without value"),u.attribValue=u.attribName,pe(u),U(u)):_(x)?u.state=w.ATTRIB_NAME_SAW_WHITE:T(d,x)?u.attribName+=x:M(u,"Invalid attribute name");continue;case w.ATTRIB_NAME_SAW_WHITE:if(x==="=")u.state=w.ATTRIB_VALUE;else{if(_(x))continue;M(u,"Attribute without value"),u.tag.attributes[u.attribName]="",u.attribValue="",N(u,"onattribute",{name:u.attribName,value:""}),u.attribName="",x===">"?U(u):T(f,x)?(u.attribName=x,u.state=w.ATTRIB_NAME):(M(u,"Invalid attribute name"),u.state=w.ATTRIB)}continue;case w.ATTRIB_VALUE:if(_(x))continue;A(x)?(u.q=x,u.state=w.ATTRIB_VALUE_QUOTED):(M(u,"Unquoted attribute value"),u.state=w.ATTRIB_VALUE_UNQUOTED,u.attribValue=x);continue;case w.ATTRIB_VALUE_QUOTED:if(x!==u.q){x==="&"?u.state=w.ATTRIB_VALUE_ENTITY_Q:u.attribValue+=x;continue}pe(u),u.q="",u.state=w.ATTRIB_VALUE_CLOSED;continue;case w.ATTRIB_VALUE_CLOSED:_(x)?u.state=w.ATTRIB:x===">"?U(u):x==="/"?u.state=w.OPEN_TAG_SLASH:T(f,x)?(M(u,"No whitespace between attributes"),u.attribName=x,u.attribValue="",u.state=w.ATTRIB_NAME):M(u,"Invalid attribute name");continue;case w.ATTRIB_VALUE_UNQUOTED:if(!O(x)){x==="&"?u.state=w.ATTRIB_VALUE_ENTITY_U:u.attribValue+=x;continue}pe(u),x===">"?U(u):u.state=w.ATTRIB;continue;case w.CLOSE_TAG:if(u.tagName)x===">"?Re(u):T(d,x)?u.tagName+=x:u.script?(u.script+="</"+u.tagName,u.tagName="",u.state=w.SCRIPT):(_(x)||M(u,"Invalid tagname in closing tag"),u.state=w.CLOSE_TAG_SAW_WHITE);else{if(_(x))continue;k(f,x)?u.script?(u.script+="</"+x,u.state=w.SCRIPT):M(u,"Invalid tagname in closing tag."):u.tagName=x}continue;case w.CLOSE_TAG_SAW_WHITE:if(_(x))continue;x===">"?Re(u):M(u,"Invalid characters in closing tag");continue;case w.TEXT_ENTITY:case w.ATTRIB_VALUE_ENTITY_Q:case w.ATTRIB_VALUE_ENTITY_U:var F,$;switch(u.state){case w.TEXT_ENTITY:F=w.TEXT,$="textNode";break;case w.ATTRIB_VALUE_ENTITY_Q:F=w.ATTRIB_VALUE_QUOTED,$="attribValue";break;case w.ATTRIB_VALUE_ENTITY_U:F=w.ATTRIB_VALUE_UNQUOTED,$="attribValue";break}if(x===";")if(u.opt.unparsedEntities){var W=G(u);u.entity="",u.state=F,u.write(W)}else u[$]+=G(u),u.entity="",u.state=F;else T(u.entity.length?v:h,x)?u.entity+=x:(M(u,"Invalid character in entity name"),u[$]+="&"+u.entity+x,u.entity="",u.state=F);continue;default:throw new Error(u,"Unknown state: "+u.state)}return u.position>=u.bufferCheckPosition&&i(u),u}String.fromCodePoint||function(){var y=String.fromCharCode,u=Math.floor,R=function(){var x=16384,S=[],I,F,$=-1,W=arguments.length;if(!W)return"";for(var te="";++$<W;){var ee=Number(arguments[$]);if(!isFinite(ee)||ee<0||ee>1114111||u(ee)!==ee)throw RangeError("Invalid code point: "+ee);ee<=65535?S.push(ee):(ee-=65536,I=(ee>>10)+55296,F=ee%1024+56320,S.push(I,F)),($+1===W||S.length>x)&&(te+=y.apply(null,S),S.length=0)}return te};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:R,configurable:!0,writable:!0}):String.fromCodePoint=R}()})(typeof Fr=="undefined"?Fr.sax={}:Fr)});var Jf=z((I2,Xf)=>{var Vf=/[&<>"]/g,Hy=RegExp(Vf.source),qy={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"},Wy=n=>{let e=String(n);return e&&Hy.test(e)?e.replace(Vf,t=>qy[t]):e},Yy=(n,e)=>typeof n=="string"&&typeof e=="string"||n&&n[0].$name===e.$name,zy=(n,e)=>typeof n=="string"?n+e:n.concat(e),Zy=n=>n.reduce((e,t)=>{let i=e.length-1,r=e[i];return Yy(r,t)?e[i]=zy(r,t):e.push(typeof t=="string"?t:[t]),e},[]),Gy=(n,e,t)=>{let i=null,r={};return Object.keys(n).forEach(o=>{let a=Lr(n[o])?n[o]:null;if(a)switch(o){case"$name":!e&&a&&(r.$name=a);break;case"$attrs":i=a;break;case"$markup":r.$markup=Kf(a);break;default:a&&(r[o]=t(a))}}),{stripped:r,$attrs:i}},Vy=n=>Array.isArray(n)&&n.length===1?n[0]:n,Ky=(n,e)=>Lr(e)?n?Object.keys(e).length===1&&e.$name?Object.assign(e,n):Object.assign(e,{$attrs:n}):e:n,Xy=n=>{if(!n)return;let e=Object.keys(n),[t]=e;return e.length===1&&t!=="$name"&&t!=="id"?n[e[0]]:n},Xa=(n,e,t)=>{let i=t?l=>l:Vy,r=i(n);if(!Lr(r))return null;if(typeof r!="object"||Array.isArray(r))return r;let{$attrs:o,stripped:a}=Gy(r,e,i),s=Ky(o,a);return Xy(s)},Jy=n=>{let e,t={},i=!0;return n.every(r=>(typeof r=="string"?e="$text":e=r[0].$name,t[e]?(i=!1,!1):(t[e]=!0,!0))),i},Ja=(n=[],e=[],t=!1)=>{let i=Array.isArray(n)?n:[n],r=Array.isArray(e)?e:[e],o=i.concat(r);return o.length===1&&!t?o[0]:o},Kf=(n,e)=>{let t=[];return n!==null&&typeof n=="object"&&n.constructor===Array?(n.forEach(i=>{t.push(Xa(i,e))}),t):Xa(n,e)},Qy=(n,e)=>{let t,i={};return Object.keys(n).forEach(r=>{r!=="$markup"&&(i[r]=n[r])}),n.$markup&&n.$markup.forEach(r=>{typeof r=="string"?i.$text=Ja(i.$text,r,e):typeof r=="object"&&(r.constructor===Array?t=r[0].$name:t=r.$name,i[t]=Ja(i[t],Kf(r,!0),e))}),i},Lr=n=>n!=null&&(n.length==null||n.length!==0)&&(typeof n!="object"||Object.keys(n).length!==0);Xf.exports={condenseArray:Zy,escape:Wy,isSomething:Lr,moosh:Ja,objectifyMarkup:Qy,shouldObjectifyMarkup:Jy,simplifyNode:Xa}});var td=z((F2,ed)=>{var{EventEmitter:e1}=require("events"),t1=Gf(),Ut=Jf(),n1=1,Pn=0,jt=-1,Qf={preserveMarkup:Pn,simplifyNodes:!0,useArrays:Pn,lowercase:!0,trim:!0,normalize:!0,cdataAsText:!1,strict:!1},Ei=function(e,t=Qf){let i=new e1,r=[],o=Object.assign({},Qf,t),{preserveMarkup:a,simplifyNodes:s,useArrays:l,lowercase:c,trim:p,normalize:m,cdataAsText:g,strict:b}=o,E=null,f=null,d=t1.createStream(b,{lowercase:c,trim:p,normalize:m,cdataAsText:g});return d.on("opentag",h=>{r.length===0&&!i.listeners(`tag:${h.name}`).length||(E={$name:h.name,$attrs:h.attributes},l>jt&&(E.$markup=[]),r.push(E))}),d.on("text",h=>{E&&(l>jt?E.$markup.push(h):E.$text?E.$text+=h:E.$text=h)}),d.on("opencdata",()=>{E&&(g||(f={$name:"$cdata",text:""},l>jt?E.$markup.push(f):E.$cdata=f))}),d.on("cdata",h=>{E&&(f!==null?f.text+=h:l>jt?E.$markup.push(h):E.$text?E.$text+=h:E.$text=h)}),d.on("closecdata",()=>{f=null}),d.on("script",h=>{E&&(E.$script=h)}),d.on("closetag",h=>{let v,_=null,A=l>Pn;r.length!==0&&(l>jt&&(a<=jt?(E.$markup=Ut.condenseArray(E.$markup),E=Ut.objectifyMarkup(E,A)):a===Pn&&(v=Ut.condenseArray(E.$markup),Ut.shouldObjectifyMarkup(v)&&(E.$markup=v,E=Ut.objectifyMarkup(E,A)))),i.listeners(`tag:${h}`).length&&i.emit(`tag:${h}`,s?Ut.simplifyNode(E,!1,l>Pn):E),r.pop(),r.length>0&&(_=r[r.length-1],l>jt?_.$markup.push(E):_[h]||(_[h]=Ut.simplifyNode(E,!0))),E=_)}),d.on("end",()=>{i.emit("end")}),d.on("error",h=>{i.emit("error",h)}),e.pipe(d),i.pause=function(){e.pause()},i.resume=function(){e.resume()},i};Ei.ALWAYS=n1;Ei.SOMETIMES=Pn;Ei.NEVER=jt;Ei.toXml=function(e,{indent:t="",selfClosing:i=!0,escape:r=Ut.escape,nodeName:o}={}){let a=t?`
`:"";function s(l,c,p){let m="",g,b=c,E=p?a+p:"",f=p+t,d="";return l=l||"",l.constructor===Array?(l.forEach(h=>{m+=s(h,b,p)}),m):(!b&&l.$name&&(b=l.$name),b&&(m=`${E}<${b}`,l.$attrs&&typeof l.$attrs=="object"&&(g=Object.keys(l.$attrs),g.forEach(h=>{m+=` ${h}=${JSON.stringify(String(l.$attrs[h]))}`}))),l==null||l===""||(typeof l=="object"?(g=Object.keys(l),g.forEach(h=>{let v=l[h];switch(h){case"$name":case"$attrs":break;case"$text":case"$markup":d+=s(v,null,f);break;case"$script":d+=s(v,"script",f);break;case"$cdata":d+=`${E}<![CDATA[${v}]]>`;break;default:d+=s(v,h,f)}})):d+=E+r(l)),b?d?m+=`>${d}${a}${p}</${b}>`:i?m+="/>":m+=`></${b}>`:m+=d,m)}return s(e,o,"")};ed.exports=Ei});var ob={};Oe(ob,{ATTACHMENT_EXTS:()=>$n,AUTH_REDIRECT_URI:()=>qr,ImportContext:()=>Vr,ImporterModal:()=>Ti,default:()=>Kr});module.exports=Md(ob);var Wt=require("obsidian");var lt=require("obsidian");var vt=class{constructor(e){this.importer=e,this.app=e.app}};var Un=class extends vt{constructor(e,t){super(e),this.scan=t.mergableDataObject,this.objects=this.scan.mergeableDataObjectData.mergeableDataObjectEntry}async format(){let e=[];for(let t of this.objects){if(!t.customMap)continue;let i=t.customMap.mapEntry[0].value.stringValue,r=await this.importer.database.get`
				SELECT z_pk, zmedia, ztypeuti FROM ziccloudsyncingobject 
				WHERE zidentifier = ${i}`,o=await this.importer.resolveAttachment(r.Z_PK,"com.apple.notes.gallery");if(o||(o=await this.importer.resolveAttachment(r.ZMEDIA,r.ZTYPEUTI)),o)e.push(this.importer.app.fileManager.generateMarkdownLink(o,"/"));else return"**Cannot decode scan**"}return`
${e.join(`
`)}
`}};Un.protobufType="ciofecaforensics.MergableDataProto";var jn=class extends vt{constructor(t,i){super(t);this.rowLocations={};this.columnLocations={};this.table=i.mergableDataObject;let r=this.table.mergeableDataObjectData;this.keys=r.mergeableDataObjectKeyItem,this.types=r.mergeableDataObjectTypeItem,this.uuids=r.mergeableDataObjectUuidItem.map(this.uuidToString),this.objects=r.mergeableDataObjectEntry}async parse(){let t=this.objects.find(r=>r.customMap&&this.types[r.customMap.type]=="com.apple.notes.ICTable");if(!t)return null;let i=null;for(let r of t.customMap.mapEntry){let o=this.objects[r.value.objectIndex];switch(this.keys[r.key]){case"crRows":[this.rowLocations,this.rowCount]=this.findLocations(o);break;case"crColumns":[this.columnLocations,this.columnCount]=this.findLocations(o);break;case"cellColumns":i=o;break}}return i?await this.computeCells(i):null}findLocations(t){let i=[],r={};for(let o of t.orderedSet.ordering.array.attachment)i.push(this.uuidToString(o.uuid));for(let o of t.orderedSet.ordering.contents.element){let a=this.getTargetUuid(o.key),s=this.getTargetUuid(o.value);r[s]=i.indexOf(a)}return[r,i.length]}async computeCells(t){let i=Array(this.rowCount).fill(0).map(()=>Array(this.columnCount));for(let r of t.dictionary.element){let o=this.columnLocations[this.getTargetUuid(r.key)],a=this.objects[r.value.objectIndex];for(let s of a.dictionary.element){let l=this.rowLocations[this.getTargetUuid(s.key)],c=this.objects[s.value.objectIndex];if(!(l in i)||!c)continue;let p=new Zt(this.importer,c);i[l][o]=await p.format(!0)}}return i}async format(){let t=await this.parse();if(!t)return"";let i=`
`;for(let r=0;r<t.length;r++)i+=`| ${t[r].join(" | ")} |
`,r==0&&(i+=`|${" -- |".repeat(t[0].length)}
`);return i+`
`}getTargetUuid(t){let r=this.objects[t.objectIndex].customMap.mapEntry[0].value.unsignedIntegerValue;return this.uuids[r]}uuidToString(t){return Buffer.from(t).toString("hex")}};jn.protobufType="ciofecaforensics.MergableDataProto";var $d=/(^\s+|(?:\s+)?\n(?:\s+)?|\s+$)/,Qr=/applenotes:note\/([-0-9a-f]+)(?:\?ownerIdentifier=.*)?/,Bd=".AppleColorEmojiUI",us=[100,101,102,103],Zt=class extends vt{constructor(t,i){super(t);this.listNumber=0;this.listIndent=0;this.multiRun=0;this.note=i.note}parseTokens(){let t=0,i=0,r=0,o=[];for(;t<this.note.attributeRun.length;){let a,s="",l=!0;do a=this.note.attributeRun[t],r=r+a.length,s+=this.note.noteText.substring(i,r),i=r,l=t==this.note.attributeRun.length-1?!1:fs(a,this.note.attributeRun[t+1]),t++;while(l);for(let c of s.split($d))c&&o.push({attr:a,fragment:c})}return o}async format(t=!1){var a;let i=this.parseTokens(),r=!t&&this.importer.omitFirstLine&&this.note.noteText.contains(`
`),o="";for(let s=0;s<i.length;s++){let{attr:l,fragment:c}=i[s];if(r)if(c.contains(`
`)||l.attachmentInfo)r=!1;else continue;l.fragment=c,l.atLineStart=s==0?!0:(a=i[s-1])==null?void 0:a.fragment.contains(`
`),o+=this.formatMultiRun(l),!/\S/.test(l.fragment)||this.multiRun==1?o+=l.fragment:l.attachmentInfo?o+=await this.formatAttachment(l):l.superscript||l.underlined||l.color||l.font||this.multiRun==2?o+=await this.formatHtmlAttr(l):o+=await this.formatAttr(l)}return this.multiRun!=0&&(o+=this.formatMultiRun({})),t&&o.replace(`
`,"<br>").replace("|","&#124;"),o.trim()}formatMultiRun(t){var o,a,s,l,c,p;let i=(o=t.paragraphStyle)==null?void 0:o.styleType,r="";switch(this.multiRun){case 3:(((a=t.paragraphStyle)==null?void 0:a.indentAmount)==0&&!us.includes(i)||Ud(t))&&(this.multiRun=0);break;case 1:i!=4&&(this.multiRun=0,r+="```\n");break;case 2:(s=t.paragraphStyle)!=null&&s.alignment||(this.multiRun=0,r+=`</p>
`);break}if(this.multiRun==0){if(i==4)this.multiRun=1,r+="\n```\n";else if(us.includes(i))this.multiRun=3,(l=t.paragraphStyle)!=null&&l.indentAmount&&(r+=`
- &nbsp;
`);else if((c=t.paragraphStyle)!=null&&c.alignment){this.multiRun=2;let m=this.convertAlign((p=t==null?void 0:t.paragraphStyle)==null?void 0:p.alignment);r+=`
<p style="text-align:${m};margin:0">`}}return r}async formatHtmlAttr(t){var r,o;t.strikethrough&&(t.fragment=`<s>${t.fragment}</s>`),t.underlined&&(t.fragment=`<u>${t.fragment}</u>`),t.superscript==1&&(t.fragment=`<sup>${t.fragment}</sup>`),t.superscript==-1&&(t.fragment=`<sub>${t.fragment}</sub>`);let i="";switch(t.fontWeight){case 1:t.fragment=`<b>${t.fragment}</b>`;break;case 2:t.fragment=`<i>${t.fragment}</i>`;break;case 3:t.fragment=`<b><i>${t.fragment}</i></b>`;break}return(r=t.font)!=null&&r.fontName&&t.font.fontName!==Bd&&(i+=`font-family:${t.font.fontName};`),(o=t.font)!=null&&o.pointSize&&(i+=`font-size:${t.font.pointSize}pt;`),t.color&&(i+=`color:${this.convertColor(t.color)};`),t.link&&!Qr.test(t.link)?(i&&(i=` style="${i}"`),t.fragment=`<a href="${t.link}" rel="noopener" class="external-link" target="_blank"${i}>${t.fragment}</a>`):i&&(t.link&&(t.fragment=await this.getInternalLink(t.link,t.fragment)),t.fragment=`<span style="${i}">${t.fragment}</span>`),t.atLineStart?this.formatParagraph(t):t.fragment}async formatAttr(t){switch(t.fontWeight){case 1:t.fragment=`**${t.fragment}**`;break;case 2:t.fragment=`*${t.fragment}*`;break;case 3:t.fragment=`***${t.fragment}***`;break}return t.strikethrough&&(t.fragment=`~~${t.fragment}~~`),t.link&&t.link!=t.fragment&&(Qr.test(t.link)?t.fragment=await this.getInternalLink(t.link,t.fragment):t.fragment=`[${t.fragment}](${t.link})`),t.atLineStart?this.formatParagraph(t):t.fragment}formatParagraph(t){var a,s,l,c,p,m;let i="	".repeat(((a=t.paragraphStyle)==null?void 0:a.indentAmount)||0),r=(s=t.paragraphStyle)==null?void 0:s.styleType,o=(l=t.paragraphStyle)!=null&&l.blockquote?"> ":"";switch(this.listNumber!=0&&(r!==102||this.listIndent!==((c=t.paragraphStyle)==null?void 0:c.indentAmount))&&(this.listIndent=((p=t.paragraphStyle)==null?void 0:p.indentAmount)||0,this.listNumber=0),r){case 0:return`${o}# ${t.fragment}`;case 1:return`${o}## ${t.fragment}`;case 2:return`${o}### ${t.fragment}`;case 101:case 100:return`${o}${i}- ${t.fragment}`;case 102:return this.listNumber++,`${o}${i}${this.listNumber}. ${t.fragment}`;case 103:let g=(m=t.paragraphStyle.checklist)!=null&&m.done?"[x]":"[ ]";return`${o}${i}- ${g} ${t.fragment}`}return this.multiRun==3&&(o+=i),`${o}${t.fragment}`}async formatAttachment(t){var l,c,p;let i,r,o;switch((l=t.attachmentInfo)==null?void 0:l.typeUti){case"com.apple.notes.inlinetextattachment.hashtag":case"com.apple.notes.inlinetextattachment.mention":return i=await this.importer.database.get`
					SELECT zalttext FROM ziccloudsyncingobject 
					WHERE zidentifier = ${t.attachmentInfo.attachmentIdentifier}`,i.ZALTTEXT;case"com.apple.notes.inlinetextattachment.link":return i=await this.importer.database.get`
					SELECT ztokencontentidentifier FROM ziccloudsyncingobject 
					WHERE zidentifier = ${t.attachmentInfo.attachmentIdentifier}`,await this.getInternalLink(i.ZTOKENCONTENTIDENTIFIER);case"com.apple.notes.table":return i=await this.importer.database.get`
					SELECT hex(zmergeabledata1) as zhexdata FROM ziccloudsyncingobject 
					WHERE zidentifier = ${t.attachmentInfo.attachmentIdentifier}`,o=this.importer.decodeData(i.zhexdata,jn),await o.format();case"public.url":return i=await this.importer.database.get`
					SELECT ztitle, zurlstring FROM ziccloudsyncingobject 
					WHERE zidentifier = ${t.attachmentInfo.attachmentIdentifier}`,`[**${i.ZTITLE}**](${i.ZURLSTRING})`;case"com.apple.notes.gallery":return i=await this.importer.database.get`
					SELECT hex(zmergeabledata1) as zhexdata FROM ziccloudsyncingobject 
					WHERE zidentifier = ${t.attachmentInfo.attachmentIdentifier}`,o=this.importer.decodeData(i.zhexdata,Un),await o.format();case"com.apple.paper.doc.scan":case"com.apple.drawing":case"com.apple.drawing.2":case"com.apple.paper":i=await this.importer.database.get`
					SELECT z_pk, zhandwritingsummary 
					FROM (SELECT *, NULL AS zhandwritingsummary FROM ziccloudsyncingobject) 
					WHERE zidentifier = ${t.attachmentInfo.attachmentIdentifier}`,r=i==null?void 0:i.Z_PK;break;default:i=await this.importer.database.get`
					SELECT zmedia FROM ziccloudsyncingobject 
					WHERE zidentifier = ${(c=t.attachmentInfo)==null?void 0:c.attachmentIdentifier}`,r=i==null?void 0:i.ZMEDIA;break}if(!r)return` **(unknown attachment: ${(p=t.attachmentInfo)==null?void 0:p.typeUti})** `;let a=await this.importer.resolveAttachment(r,t.attachmentInfo.typeUti),s=a?`
${this.app.fileManager.generateMarkdownLink(a,"/")}
`:" **(error reading attachment)**";return this.importer.includeHandwriting&&i.ZHANDWRITINGSUMMARY&&(s=`
> [!Handwriting]-
> ${i.ZHANDWRITINGSUMMARY.replace(`
`,`
> `)}${s}`),s}async getInternalLink(t,i=void 0){let r=t.match(Qr)[1],o=await this.importer.database.get`
			SELECT z_pk FROM ziccloudsyncingobject 
			WHERE zidentifier = ${r.toUpperCase()}`,a=await this.importer.resolveNote(o.Z_PK);return a?this.app.fileManager.generateMarkdownLink(a,this.importer.rootFolder.path,void 0,i):"(unknown file link)"}convertColor(t){let i="#";for(let r of Object.values(t))i+=Math.floor(r*255).toString(16);return i}convertAlign(t){switch(t){default:return"left";case 1:return"center";case 2:return"right";case 3:return"justify"}}};Zt.protobufType="ciofecaforensics.Document";function Ud(n){return n.attachmentInfo?!n.attachmentInfo.typeUti.includes("com.apple.notes.inlinetextattachment"):!1}function fs(n,e){var t,i;if(!e||n.$type!=e.$type)return!1;for(let r of n.$type.fieldsArray)if(r.name!="length"){if((t=n[r.name])!=null&&t.$type&&((i=e[r.name])!=null&&i.$type)){if(!fs(n[r.name],e[r.name]))return!1}else if(n[r.name]!=e[r.name])return!1}return!0}var ds={nested:{ciofecaforensics:{nested:{Color:{fields:{red:{type:"float",id:1},green:{type:"float",id:2},blue:{type:"float",id:3},alpha:{type:"float",id:4}}},AttachmentInfo:{fields:{attachmentIdentifier:{type:"string",id:1},typeUti:{type:"string",id:2}}},Font:{fields:{fontName:{type:"string",id:1},pointSize:{type:"float",id:2},fontHints:{type:"int32",id:3}}},ParagraphStyle:{fields:{styleType:{type:"int32",id:1,options:{default:-1}},alignment:{type:"int32",id:2},indentAmount:{type:"int32",id:4},checklist:{type:"Checklist",id:5},blockquote:{type:"int32",id:8}}},Checklist:{fields:{uuid:{type:"bytes",id:1},done:{type:"int32",id:2}}},DictionaryElement:{fields:{key:{type:"ObjectID",id:1},value:{type:"ObjectID",id:2}}},Dictionary:{fields:{element:{rule:"repeated",type:"DictionaryElement",id:1,options:{packed:!1}}}},ObjectID:{fields:{unsignedIntegerValue:{type:"uint64",id:2},stringValue:{type:"string",id:4},objectIndex:{type:"int32",id:6}}},RegisterLatest:{fields:{contents:{type:"ObjectID",id:2}}},MapEntry:{fields:{key:{type:"int32",id:1},value:{type:"ObjectID",id:2}}},AttributeRun:{fields:{length:{type:"int32",id:1},paragraphStyle:{type:"ParagraphStyle",id:2},font:{type:"Font",id:3},fontWeight:{type:"int32",id:5},underlined:{type:"int32",id:6},strikethrough:{type:"int32",id:7},superscript:{type:"int32",id:8},link:{type:"string",id:9},color:{type:"Color",id:10},attachmentInfo:{type:"AttachmentInfo",id:12}}},NoteStoreProto:{fields:{document:{type:"Document",id:2}}},Document:{fields:{version:{type:"int32",id:2},note:{type:"Note",id:3}}},Note:{fields:{noteText:{type:"string",id:2},attributeRun:{rule:"repeated",type:"AttributeRun",id:5,options:{packed:!1}}}},MergableDataProto:{fields:{mergableDataObject:{type:"MergableDataObject",id:2}}},MergableDataObject:{fields:{version:{type:"int32",id:2},mergeableDataObjectData:{type:"MergeableDataObjectData",id:3}}},MergeableDataObjectData:{fields:{mergeableDataObjectEntry:{rule:"repeated",type:"MergeableDataObjectEntry",id:3,options:{packed:!1}},mergeableDataObjectKeyItem:{rule:"repeated",type:"string",id:4},mergeableDataObjectTypeItem:{rule:"repeated",type:"string",id:5},mergeableDataObjectUuidItem:{rule:"repeated",type:"bytes",id:6}}},MergeableDataObjectEntry:{fields:{registerLatest:{type:"RegisterLatest",id:1},list:{type:"List",id:5},dictionary:{type:"Dictionary",id:6},unknownMessage:{type:"UnknownMergeableDataObjectEntryMessage",id:9},note:{type:"Note",id:10},customMap:{type:"MergeableDataObjectMap",id:13},orderedSet:{type:"OrderedSet",id:16}}},UnknownMergeableDataObjectEntryMessage:{fields:{unknownEntry:{type:"UnknownMergeableDataObjectEntryMessageEntry",id:1}}},UnknownMergeableDataObjectEntryMessageEntry:{fields:{unknownInt1:{type:"int32",id:1},unknownInt2:{type:"int64",id:2}}},MergeableDataObjectMap:{fields:{type:{type:"int32",id:1},mapEntry:{rule:"repeated",type:"MapEntry",id:3,options:{packed:!1}}}},OrderedSet:{fields:{ordering:{type:"OrderedSetOrdering",id:1},elements:{type:"Dictionary",id:2}}},OrderedSetOrdering:{fields:{array:{type:"OrderedSetOrderingArray",id:1},contents:{type:"Dictionary",id:2}}},OrderedSetOrderingArray:{fields:{contents:{type:"Note",id:1},attachment:{rule:"repeated",type:"OrderedSetOrderingArrayAttachment",id:2,options:{packed:!1}}}},OrderedSetOrderingArrayAttachment:{fields:{index:{type:"int32",id:1},uuid:{type:"bytes",id:2}}},List:{fields:{listEntry:{rule:"repeated",type:"ListEntry",id:1,options:{packed:!1}}}},ListEntry:{fields:{id:{type:"ObjectID",id:2},details:{type:"ListEntryDetails",id:3},additionalDetails:{type:"ListEntryDetails",id:4}}},ListEntryDetails:{fields:{listEntryDetailsKey:{type:"ListEntryDetailsKey",id:1},id:{type:"ObjectID",id:2}}},ListEntryDetailsKey:{fields:{listEntryDetailsTypeIndex:{type:"int32",id:1},listEntryDetailsKey:{type:"int32",id:2}}}}}}};var Ie=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],Is=1440,jd=0,Hd=4,qd=9,Wd=5,Yd=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],zd=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],Zd=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],Gd=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],Vd=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],Kd=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],Et=15;function io(){let n=this,e,t,i,r,o,a;function s(c,p,m,g,b,E,f,d,h,v,_){let A,O,T,k,w,D,C,N,P,L,j,Y,M,ne,Z;L=0,w=m;do i[c[p+L]]++,L++,w--;while(w!==0);if(i[0]==m)return f[0]=-1,d[0]=0,0;for(N=d[0],D=1;D<=Et&&i[D]===0;D++);for(C=D,N<D&&(N=D),w=Et;w!==0&&i[w]===0;w--);for(T=w,N>w&&(N=w),d[0]=N,ne=1<<D;D<w;D++,ne<<=1)if((ne-=i[D])<0)return-3;if((ne-=i[w])<0)return-3;for(i[w]+=ne,a[1]=D=0,L=1,M=2;--w!==0;)a[M]=D+=i[L],M++,L++;w=0,L=0;do(D=c[p+L])!==0&&(_[a[D]++]=w),L++;while(++w<m);for(m=a[T],a[0]=w=0,L=0,k=-1,Y=-N,o[0]=0,j=0,Z=0;C<=T;C++)for(A=i[C];A--!==0;){for(;C>Y+N;){if(k++,Y+=N,Z=T-Y,Z=Z>N?N:Z,(O=1<<(D=C-Y))>A+1&&(O-=A+1,M=C,D<Z))for(;++D<Z&&!((O<<=1)<=i[++M]);)O-=i[M];if(Z=1<<D,v[0]+Z>Is)return-3;o[k]=j=v[0],v[0]+=Z,k!==0?(a[k]=w,r[0]=D,r[1]=N,D=w>>>Y-N,r[2]=j-o[k-1]-D,h.set(r,(o[k-1]+D)*3)):f[0]=j}for(r[1]=C-Y,L>=m?r[0]=192:_[L]<g?(r[0]=_[L]<256?0:96,r[2]=_[L++]):(r[0]=E[_[L]-g]+16+64,r[2]=b[_[L++]-g]),O=1<<C-Y,D=w>>>Y;D<Z;D+=O)h.set(r,(j+D)*3);for(D=1<<C-1;(w&D)!==0;D>>>=1)w^=D;for(w^=D,P=(1<<Y)-1;(w&P)!=a[k];)k--,Y-=N,P=(1<<Y)-1}return ne!==0&&T!=1?-5:0}function l(c){let p;for(e||(e=[],t=[],i=new Int32Array(Et+1),r=[],o=new Int32Array(Et),a=new Int32Array(Et+1)),t.length<c&&(t=[]),p=0;p<c;p++)t[p]=0;for(p=0;p<Et+1;p++)i[p]=0;for(p=0;p<3;p++)r[p]=0;o.set(i.subarray(0,Et),0),a.set(i.subarray(0,Et+1),0)}n.inflate_trees_bits=function(c,p,m,g,b){let E;return l(19),e[0]=0,E=s(c,0,19,19,null,null,m,p,g,e,t),E==-3?b.msg="oversubscribed dynamic bit lengths tree":(E==-5||p[0]===0)&&(b.msg="incomplete dynamic bit lengths tree",E=-3),E},n.inflate_trees_dynamic=function(c,p,m,g,b,E,f,d,h){let v;return l(288),e[0]=0,v=s(m,0,c,257,Zd,Gd,E,g,d,e,t),v!=0||g[0]===0?(v==-3?h.msg="oversubscribed literal/length tree":v!=-4&&(h.msg="incomplete literal/length tree",v=-3),v):(l(288),v=s(m,c,p,0,Vd,Kd,f,b,d,e,t),v!=0||b[0]===0&&c>257?(v==-3?h.msg="oversubscribed distance tree":v==-5?(h.msg="incomplete distance tree",v=-3):v!=-4&&(h.msg="empty distance tree with lengths",v=-3),v):0)}}io.inflate_trees_fixed=function(n,e,t,i){return n[0]=qd,e[0]=Wd,t[0]=Yd,i[0]=zd,0};var ki=0,ms=1,hs=2,gs=3,ys=4,bs=5,xs=6,eo=7,ws=8,Ni=9;function Xd(){let n=this,e,t=0,i,r=0,o=0,a=0,s=0,l=0,c=0,p=0,m,g=0,b,E=0;function f(d,h,v,_,A,O,T,k){let w,D,C,N,P,L,j,Y,M,ne,Z,pe,U,Re,G,Q;j=k.next_in_index,Y=k.avail_in,P=T.bitb,L=T.bitk,M=T.write,ne=M<T.read?T.read-M-1:T.end-M,Z=Ie[d],pe=Ie[h];do{for(;L<20;)Y--,P|=(k.read_byte(j++)&255)<<L,L+=8;if(w=P&Z,D=v,C=_,Q=(C+w)*3,(N=D[Q])===0){P>>=D[Q+1],L-=D[Q+1],T.win[M++]=D[Q+2],ne--;continue}do{if(P>>=D[Q+1],L-=D[Q+1],(N&16)!==0){for(N&=15,U=D[Q+2]+(P&Ie[N]),P>>=N,L-=N;L<15;)Y--,P|=(k.read_byte(j++)&255)<<L,L+=8;w=P&pe,D=A,C=O,Q=(C+w)*3,N=D[Q];do if(P>>=D[Q+1],L-=D[Q+1],(N&16)!==0){for(N&=15;L<N;)Y--,P|=(k.read_byte(j++)&255)<<L,L+=8;if(Re=D[Q+2]+(P&Ie[N]),P>>=N,L-=N,ne-=U,M>=Re)G=M-Re,M-G>0&&2>M-G?(T.win[M++]=T.win[G++],T.win[M++]=T.win[G++],U-=2):(T.win.set(T.win.subarray(G,G+2),M),M+=2,G+=2,U-=2);else{G=M-Re;do G+=T.end;while(G<0);if(N=T.end-G,U>N){if(U-=N,M-G>0&&N>M-G)do T.win[M++]=T.win[G++];while(--N!==0);else T.win.set(T.win.subarray(G,G+N),M),M+=N,G+=N,N=0;G=0}}if(M-G>0&&U>M-G)do T.win[M++]=T.win[G++];while(--U!==0);else T.win.set(T.win.subarray(G,G+U),M),M+=U,G+=U,U=0;break}else if((N&64)===0)w+=D[Q+2],w+=P&Ie[N],Q=(C+w)*3,N=D[Q];else return k.msg="invalid distance code",U=k.avail_in-Y,U=L>>3<U?L>>3:U,Y+=U,j-=U,L-=U<<3,T.bitb=P,T.bitk=L,k.avail_in=Y,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=M,-3;while(!0);break}if((N&64)===0){if(w+=D[Q+2],w+=P&Ie[N],Q=(C+w)*3,(N=D[Q])===0){P>>=D[Q+1],L-=D[Q+1],T.win[M++]=D[Q+2],ne--;break}}else return(N&32)!==0?(U=k.avail_in-Y,U=L>>3<U?L>>3:U,Y+=U,j-=U,L-=U<<3,T.bitb=P,T.bitk=L,k.avail_in=Y,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=M,1):(k.msg="invalid literal/length code",U=k.avail_in-Y,U=L>>3<U?L>>3:U,Y+=U,j-=U,L-=U<<3,T.bitb=P,T.bitk=L,k.avail_in=Y,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=M,-3)}while(!0)}while(ne>=258&&Y>=10);return U=k.avail_in-Y,U=L>>3<U?L>>3:U,Y+=U,j-=U,L-=U<<3,T.bitb=P,T.bitk=L,k.avail_in=Y,k.total_in+=j-k.next_in_index,k.next_in_index=j,T.write=M,0}n.init=function(d,h,v,_,A,O){e=ki,c=d,p=h,m=v,g=_,b=A,E=O,i=null},n.proc=function(d,h,v){let _,A,O,T=0,k=0,w=0,D,C,N,P;for(w=h.next_in_index,D=h.avail_in,T=d.bitb,k=d.bitk,C=d.write,N=C<d.read?d.read-C-1:d.end-C;;)switch(e){case ki:if(N>=258&&D>=10&&(d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,v=f(c,p,m,g,b,E,d,h),w=h.next_in_index,D=h.avail_in,T=d.bitb,k=d.bitk,C=d.write,N=C<d.read?d.read-C-1:d.end-C,v!=0)){e=v==1?eo:Ni;break}o=c,i=m,r=g,e=ms;case ms:for(_=o;k<_;){if(D!==0)v=0;else return d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);D--,T|=(h.read_byte(w++)&255)<<k,k+=8}if(A=(r+(T&Ie[_]))*3,T>>>=i[A+1],k-=i[A+1],O=i[A],O===0){a=i[A+2],e=xs;break}if((O&16)!==0){s=O&15,t=i[A+2],e=hs;break}if((O&64)===0){o=O,r=A/3+i[A+2];break}if((O&32)!==0){e=eo;break}return e=Ni,h.msg="invalid literal/length code",v=-3,d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);case hs:for(_=s;k<_;){if(D!==0)v=0;else return d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);D--,T|=(h.read_byte(w++)&255)<<k,k+=8}t+=T&Ie[_],T>>=_,k-=_,o=p,i=b,r=E,e=gs;case gs:for(_=o;k<_;){if(D!==0)v=0;else return d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);D--,T|=(h.read_byte(w++)&255)<<k,k+=8}if(A=(r+(T&Ie[_]))*3,T>>=i[A+1],k-=i[A+1],O=i[A],(O&16)!==0){s=O&15,l=i[A+2],e=ys;break}if((O&64)===0){o=O,r=A/3+i[A+2];break}return e=Ni,h.msg="invalid distance code",v=-3,d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);case ys:for(_=s;k<_;){if(D!==0)v=0;else return d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);D--,T|=(h.read_byte(w++)&255)<<k,k+=8}l+=T&Ie[_],T>>=_,k-=_,e=bs;case bs:for(P=C-l;P<0;)P+=d.end;for(;t!==0;){if(N===0&&(C==d.end&&d.read!==0&&(C=0,N=C<d.read?d.read-C-1:d.end-C),N===0&&(d.write=C,v=d.inflate_flush(h,v),C=d.write,N=C<d.read?d.read-C-1:d.end-C,C==d.end&&d.read!==0&&(C=0,N=C<d.read?d.read-C-1:d.end-C),N===0)))return d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);d.win[C++]=d.win[P++],N--,P==d.end&&(P=0),t--}e=ki;break;case xs:if(N===0&&(C==d.end&&d.read!==0&&(C=0,N=C<d.read?d.read-C-1:d.end-C),N===0&&(d.write=C,v=d.inflate_flush(h,v),C=d.write,N=C<d.read?d.read-C-1:d.end-C,C==d.end&&d.read!==0&&(C=0,N=C<d.read?d.read-C-1:d.end-C),N===0)))return d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);v=0,d.win[C++]=a,N--,e=ki;break;case eo:if(k>7&&(k-=8,D++,w--),d.write=C,v=d.inflate_flush(h,v),C=d.write,N=C<d.read?d.read-C-1:d.end-C,d.read!=d.write)return d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);e=ws;case ws:return v=1,d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);case Ni:return v=-3,d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v);default:return v=-2,d.bitb=T,d.bitk=k,h.avail_in=D,h.total_in+=w-h.next_in_index,h.next_in_index=w,d.write=C,d.inflate_flush(h,v)}},n.free=function(){}}var vs=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],yn=0,to=1,Es=2,_s=3,Ts=4,As=5,Si=6,Ri=7,ks=8,Gt=9;function Jd(n,e){let t=this,i=yn,r=0,o=0,a=0,s,l=[0],c=[0],p=new Xd,m=0,g=new Int32Array(Is*3),b=0,E=new io;t.bitk=0,t.bitb=0,t.win=new Uint8Array(e),t.end=e,t.read=0,t.write=0,t.reset=function(f,d){d&&(d[0]=b),i==Si&&p.free(f),i=yn,t.bitk=0,t.bitb=0,t.read=t.write=0},t.reset(n,null),t.inflate_flush=function(f,d){let h,v,_;return v=f.next_out_index,_=t.read,h=(_<=t.write?t.write:t.end)-_,h>f.avail_out&&(h=f.avail_out),h!==0&&d==-5&&(d=0),f.avail_out-=h,f.total_out+=h,f.next_out.set(t.win.subarray(_,_+h),v),v+=h,_+=h,_==t.end&&(_=0,t.write==t.end&&(t.write=0),h=t.write-_,h>f.avail_out&&(h=f.avail_out),h!==0&&d==-5&&(d=0),f.avail_out-=h,f.total_out+=h,f.next_out.set(t.win.subarray(_,_+h),v),v+=h,_+=h),f.next_out_index=v,t.read=_,d},t.proc=function(f,d){let h,v,_,A,O,T,k,w;for(A=f.next_in_index,O=f.avail_in,v=t.bitb,_=t.bitk,T=t.write,k=T<t.read?t.read-T-1:t.end-T;;){let D,C,N,P,L,j,Y,M;switch(i){case yn:for(;_<3;){if(O!==0)d=0;else return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);O--,v|=(f.read_byte(A++)&255)<<_,_+=8}switch(h=v&7,m=h&1,h>>>1){case 0:v>>>=3,_-=3,h=_&7,v>>>=h,_-=h,i=to;break;case 1:D=[],C=[],N=[[]],P=[[]],io.inflate_trees_fixed(D,C,N,P),p.init(D[0],C[0],N[0],0,P[0],0),v>>>=3,_-=3,i=Si;break;case 2:v>>>=3,_-=3,i=_s;break;case 3:return v>>>=3,_-=3,i=Gt,f.msg="invalid block type",d=-3,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d)}break;case to:for(;_<32;){if(O!==0)d=0;else return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);O--,v|=(f.read_byte(A++)&255)<<_,_+=8}if((~v>>>16&65535)!=(v&65535))return i=Gt,f.msg="invalid stored block lengths",d=-3,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);r=v&65535,v=_=0,i=r!==0?Es:m!==0?Ri:yn;break;case Es:if(O===0||k===0&&(T==t.end&&t.read!==0&&(T=0,k=T<t.read?t.read-T-1:t.end-T),k===0&&(t.write=T,d=t.inflate_flush(f,d),T=t.write,k=T<t.read?t.read-T-1:t.end-T,T==t.end&&t.read!==0&&(T=0,k=T<t.read?t.read-T-1:t.end-T),k===0)))return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);if(d=0,h=r,h>O&&(h=O),h>k&&(h=k),t.win.set(f.read_buf(A,h),T),A+=h,O-=h,T+=h,k-=h,(r-=h)!==0)break;i=m!==0?Ri:yn;break;case _s:for(;_<14;){if(O!==0)d=0;else return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);O--,v|=(f.read_byte(A++)&255)<<_,_+=8}if(o=h=v&16383,(h&31)>29||(h>>5&31)>29)return i=Gt,f.msg="too many length or distance symbols",d=-3,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);if(h=258+(h&31)+(h>>5&31),!s||s.length<h)s=[];else for(w=0;w<h;w++)s[w]=0;v>>>=14,_-=14,a=0,i=Ts;case Ts:for(;a<4+(o>>>10);){for(;_<3;){if(O!==0)d=0;else return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);O--,v|=(f.read_byte(A++)&255)<<_,_+=8}s[vs[a++]]=v&7,v>>>=3,_-=3}for(;a<19;)s[vs[a++]]=0;if(l[0]=7,h=E.inflate_trees_bits(s,l,c,g,f),h!=0)return d=h,d==-3&&(s=null,i=Gt),t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);a=0,i=As;case As:for(;h=o,!(a>=258+(h&31)+(h>>5&31));){let ne,Z;for(h=l[0];_<h;){if(O!==0)d=0;else return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);O--,v|=(f.read_byte(A++)&255)<<_,_+=8}if(h=g[(c[0]+(v&Ie[h]))*3+1],Z=g[(c[0]+(v&Ie[h]))*3+2],Z<16)v>>>=h,_-=h,s[a++]=Z;else{for(w=Z==18?7:Z-14,ne=Z==18?11:3;_<h+w;){if(O!==0)d=0;else return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);O--,v|=(f.read_byte(A++)&255)<<_,_+=8}if(v>>>=h,_-=h,ne+=v&Ie[w],v>>>=w,_-=w,w=a,h=o,w+ne>258+(h&31)+(h>>5&31)||Z==16&&w<1)return s=null,i=Gt,f.msg="invalid bit length repeat",d=-3,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);Z=Z==16?s[w-1]:0;do s[w++]=Z;while(--ne!==0);a=w}}if(c[0]=-1,L=[],j=[],Y=[],M=[],L[0]=9,j[0]=6,h=o,h=E.inflate_trees_dynamic(257+(h&31),1+(h>>5&31),s,L,j,Y,M,g,f),h!=0)return h==-3&&(s=null,i=Gt),d=h,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);p.init(L[0],j[0],g,Y[0],g,M[0]),i=Si;case Si:if(t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,(d=p.proc(t,f,d))!=1)return t.inflate_flush(f,d);if(d=0,p.free(f),A=f.next_in_index,O=f.avail_in,v=t.bitb,_=t.bitk,T=t.write,k=T<t.read?t.read-T-1:t.end-T,m===0){i=yn;break}i=Ri;case Ri:if(t.write=T,d=t.inflate_flush(f,d),T=t.write,k=T<t.read?t.read-T-1:t.end-T,t.read!=t.write)return t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);i=ks;case ks:return d=1,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);case Gt:return d=-3,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d);default:return d=-2,t.bitb=v,t.bitk=_,f.avail_in=O,f.total_in+=A-f.next_in_index,f.next_in_index=A,t.write=T,t.inflate_flush(f,d)}}},t.free=function(f){t.reset(f,null),t.win=null,g=null},t.set_dictionary=function(f,d,h){t.win.set(f.subarray(d,d+h),0),t.read=t.write=h},t.sync_point=function(){return i==to?1:0}}var Qd=32,em=8,tm=0,Ns=1,Ss=2,Rs=3,Os=4,Cs=5,no=6,Hn=7,Ds=12,_t=13,nm=[0,0,255,255];function im(){let n=this;n.mode=0,n.method=0,n.was=[0],n.need=0,n.marker=0,n.wbits=0;function e(t){return!t||!t.istate?-2:(t.total_in=t.total_out=0,t.msg=null,t.istate.mode=Hn,t.istate.blocks.reset(t,null),0)}n.inflateEnd=function(t){return n.blocks&&n.blocks.free(t),n.blocks=null,0},n.inflateInit=function(t,i){return t.msg=null,n.blocks=null,i<8||i>15?(n.inflateEnd(t),-2):(n.wbits=i,t.istate.blocks=new Jd(t,1<<i),e(t),0)},n.inflate=function(t,i){let r,o;if(!t||!t.istate||!t.next_in)return-2;let a=t.istate;for(i=i==Hd?-5:0,r=-5;;)switch(a.mode){case tm:if(t.avail_in===0)return r;if(r=i,t.avail_in--,t.total_in++,((a.method=t.read_byte(t.next_in_index++))&15)!=em){a.mode=_t,t.msg="unknown compression method",a.marker=5;break}if((a.method>>4)+8>a.wbits){a.mode=_t,t.msg="invalid win size",a.marker=5;break}a.mode=Ns;case Ns:if(t.avail_in===0)return r;if(r=i,t.avail_in--,t.total_in++,o=t.read_byte(t.next_in_index++)&255,((a.method<<8)+o)%31!==0){a.mode=_t,t.msg="incorrect header check",a.marker=5;break}if((o&Qd)===0){a.mode=Hn;break}a.mode=Ss;case Ss:if(t.avail_in===0)return r;r=i,t.avail_in--,t.total_in++,a.need=(t.read_byte(t.next_in_index++)&255)<<24&4278190080,a.mode=Rs;case Rs:if(t.avail_in===0)return r;r=i,t.avail_in--,t.total_in++,a.need+=(t.read_byte(t.next_in_index++)&255)<<16&16711680,a.mode=Os;case Os:if(t.avail_in===0)return r;r=i,t.avail_in--,t.total_in++,a.need+=(t.read_byte(t.next_in_index++)&255)<<8&65280,a.mode=Cs;case Cs:return t.avail_in===0?r:(r=i,t.avail_in--,t.total_in++,a.need+=t.read_byte(t.next_in_index++)&255,a.mode=no,2);case no:return a.mode=_t,t.msg="need dictionary",a.marker=0,-2;case Hn:if(r=a.blocks.proc(t,r),r==-3){a.mode=_t,a.marker=0;break}if(r==0&&(r=i),r!=1)return r;r=i,a.blocks.reset(t,a.was),a.mode=Ds;case Ds:return t.avail_in=0,1;case _t:return-3;default:return-2}},n.inflateSetDictionary=function(t,i,r){let o=0,a=r;if(!t||!t.istate||t.istate.mode!=no)return-2;let s=t.istate;return a>=1<<s.wbits&&(a=(1<<s.wbits)-1,o=r-a),s.blocks.set_dictionary(i,o,a),s.mode=Hn,0},n.inflateSync=function(t){let i,r,o,a,s;if(!t||!t.istate)return-2;let l=t.istate;if(l.mode!=_t&&(l.mode=_t,l.marker=0),(i=t.avail_in)===0)return-5;for(r=t.next_in_index,o=l.marker;i!==0&&o<4;)t.read_byte(r)==nm[o]?o++:t.read_byte(r)!==0?o=0:o=4-o,r++,i--;return t.total_in+=r-t.next_in_index,t.next_in_index=r,t.avail_in=i,l.marker=o,o!=4?-3:(a=t.total_in,s=t.total_out,e(t),t.total_in=a,t.total_out=s,l.mode=Hn,0)},n.inflateSyncPoint=function(t){return!t||!t.istate||!t.istate.blocks?-2:t.istate.blocks.sync_point()}}function Fs(){}Fs.prototype={inflateInit(n){let e=this;return e.istate=new im,n||(n=15),e.istate.inflateInit(e,n)},inflate(n){let e=this;return e.istate?e.istate.inflate(e,n):-2},inflateEnd(){let n=this;if(!n.istate)return-2;let e=n.istate.inflateEnd(n);return n.istate=null,e},inflateSync(){let n=this;return n.istate?n.istate.inflateSync(n):-2},inflateSetDictionary(n,e){let t=this;return t.istate?t.istate.inflateSetDictionary(t,n,e):-2},read_byte(n){return this.next_in[n]},read_buf(n,e){return this.next_in.subarray(n,n+e)}};function Ls(n){let e=this,t=new Fs,i=n&&n.chunkSize?Math.floor(n.chunkSize*2):128*1024,r=jd,o=new Uint8Array(i),a=!1;t.inflateInit(),t.next_out=o,e.append=function(s,l){let c=[],p,m,g=0,b=0,E=0;if(s.length!==0){t.next_in_index=0,t.next_in=s,t.avail_in=s.length;do{if(t.next_out_index=0,t.avail_out=i,t.avail_in===0&&!a&&(t.next_in_index=0,a=!0),p=t.inflate(r),a&&p===-5){if(t.avail_in!==0)throw new Error("inflating: bad input")}else if(p!==0&&p!==1)throw new Error("inflating: "+t.msg);if((a||p===1)&&t.avail_in===s.length)throw new Error("inflating: bad input");t.next_out_index&&(t.next_out_index===i?c.push(new Uint8Array(o)):c.push(o.subarray(0,t.next_out_index))),E+=t.next_out_index,l&&t.next_in_index>0&&t.next_in_index!=g&&(l(t.next_in_index),g=t.next_in_index)}while(t.avail_in>0||t.avail_out===0);return c.length>1?(m=new Uint8Array(E),c.forEach(function(f){m.set(f,b),b+=f.length})):m=c[0]?new Uint8Array(c[0]):new Uint8Array,m}},e.flush=function(){t.inflateEnd()}}var ro="/",Bb=new Date(2107,11,31),Ub=new Date(1980,0,1),ae=void 0,Fe="undefined",pt="function";var qn=class{constructor(e){return class extends TransformStream{constructor(t,i){let r=new e(i);super({transform(o,a){a.enqueue(r.append(o))},flush(o){let a=r.flush();a&&o.enqueue(a)}})}}}};var rm=64,Ps=2;try{typeof navigator!=Fe&&navigator.hardwareConcurrency&&(Ps=navigator.hardwareConcurrency)}catch(n){}var om={chunkSize:512*1024,maxWorkers:Ps,terminateWorkerTimeout:5e3,useWebWorkers:!0,useCompressionStream:!0,workerScripts:ae,CompressionStreamNative:typeof CompressionStream!=Fe&&CompressionStream,DecompressionStreamNative:typeof DecompressionStream!=Fe&&DecompressionStream},At=Object.assign({},om);function oo(){return At}function Ms(n){return Math.max(n.chunkSize,rm)}function Oi(n){let{baseURL:e,chunkSize:t,maxWorkers:i,terminateWorkerTimeout:r,useCompressionStream:o,useWebWorkers:a,Deflate:s,Inflate:l,CompressionStream:c,DecompressionStream:p,workerScripts:m}=n;if(Tt("baseURL",e),Tt("chunkSize",t),Tt("maxWorkers",i),Tt("terminateWorkerTimeout",r),Tt("useCompressionStream",o),Tt("useWebWorkers",a),s&&(At.CompressionStream=new qn(s)),l&&(At.DecompressionStream=new qn(l)),Tt("CompressionStream",c),Tt("DecompressionStream",p),m!==ae){let{deflate:g,inflate:b}=m;if((g||b)&&(At.workerScripts||(At.workerScripts={})),g){if(!Array.isArray(g))throw new Error("workerScripts.deflate must be an array");At.workerScripts.deflate=g}if(b){if(!Array.isArray(b))throw new Error("workerScripts.inflate must be an array");At.workerScripts.inflate=b}}}function Tt(n,e){e!==ae&&(At[n]=e)}var $s=[];for(let n=0;n<256;n++){let e=n;for(let t=0;t<8;t++)e&1?e=e>>>1^3988292384:e=e>>>1;$s[n]=e}var ut=class{constructor(e){this.crc=e||-1}append(e){let t=this.crc|0;for(let i=0,r=e.length|0;i<r;i++)t=t>>>8^$s[(t^e[i])&255];this.crc=t}get(){return~this.crc}};var Wn=class extends TransformStream{constructor(){let e,t=new ut;super({transform(i,r){t.append(i),r.enqueue(i)},flush(){let i=new Uint8Array(4);new DataView(i.buffer).setUint32(0,t.get()),e.value=i}}),e=this}};function Bs(n){if(typeof TextEncoder==Fe){n=unescape(encodeURIComponent(n));let e=new Uint8Array(n.length);for(let t=0;t<e.length;t++)e[t]=n.charCodeAt(t);return e}else return new TextEncoder().encode(n)}var Te={concat(n,e){if(n.length===0||e.length===0)return n.concat(e);let t=n[n.length-1],i=Te.getPartial(t);return i===32?n.concat(e):Te._shiftRight(e,i,t|0,n.slice(0,n.length-1))},bitLength(n){let e=n.length;if(e===0)return 0;let t=n[e-1];return(e-1)*32+Te.getPartial(t)},clamp(n,e){if(n.length*32<e)return n;n=n.slice(0,Math.ceil(e/32));let t=n.length;return e=e&31,t>0&&e&&(n[t-1]=Te.partial(e,n[t-1]&2147483648>>e-1,1)),n},partial(n,e,t){return n===32?e:(t?e|0:e<<32-n)+n*1099511627776},getPartial(n){return Math.round(n/1099511627776)||32},_shiftRight(n,e,t,i){for(i===void 0&&(i=[]);e>=32;e-=32)i.push(t),t=0;if(e===0)return i.concat(n);for(let a=0;a<n.length;a++)i.push(t|n[a]>>>e),t=n[a]<<32-e;let r=n.length?n[n.length-1]:0,o=Te.getPartial(r);return i.push(Te.partial(e+o&31,e+o>32?t:i.pop(),1)),i}},Yn={bytes:{fromBits(n){let t=Te.bitLength(n)/8,i=new Uint8Array(t),r;for(let o=0;o<t;o++)(o&3)===0&&(r=n[o/4]),i[o]=r>>>24,r<<=8;return i},toBits(n){let e=[],t,i=0;for(t=0;t<n.length;t++)i=i<<8|n[t],(t&3)===3&&(e.push(i),i=0);return t&3&&e.push(Te.partial(8*(t&3),i)),e}}},Us={};Us.sha1=class{constructor(n){let e=this;e.blockSize=512,e._init=[1732584193,4023233417,2562383102,271733878,3285377520],e._key=[1518500249,1859775393,2400959708,3395469782],n?(e._h=n._h.slice(0),e._buffer=n._buffer.slice(0),e._length=n._length):e.reset()}reset(){let n=this;return n._h=n._init.slice(0),n._buffer=[],n._length=0,n}update(n){let e=this;typeof n=="string"&&(n=Yn.utf8String.toBits(n));let t=e._buffer=Te.concat(e._buffer,n),i=e._length,r=e._length=i+Te.bitLength(n);if(r>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");let o=new Uint32Array(t),a=0;for(let s=e.blockSize+i-(e.blockSize+i&e.blockSize-1);s<=r;s+=e.blockSize)e._block(o.subarray(16*a,16*(a+1))),a+=1;return t.splice(0,16*a),e}finalize(){let n=this,e=n._buffer,t=n._h;e=Te.concat(e,[Te.partial(1,1)]);for(let i=e.length+2;i&15;i++)e.push(0);for(e.push(Math.floor(n._length/4294967296)),e.push(n._length|0);e.length;)n._block(e.splice(0,16));return n.reset(),t}_f(n,e,t,i){if(n<=19)return e&t|~e&i;if(n<=39)return e^t^i;if(n<=59)return e&t|e&i|t&i;if(n<=79)return e^t^i}_S(n,e){return e<<n|e>>>32-n}_block(n){let e=this,t=e._h,i=Array(80);for(let c=0;c<16;c++)i[c]=n[c];let r=t[0],o=t[1],a=t[2],s=t[3],l=t[4];for(let c=0;c<=79;c++){c>=16&&(i[c]=e._S(1,i[c-3]^i[c-8]^i[c-14]^i[c-16]));let p=e._S(5,r)+e._f(c,o,a,s)+l+i[c]+e._key[Math.floor(c/20)]|0;l=s,s=a,a=e._S(30,o),o=r,r=p}t[0]=t[0]+r|0,t[1]=t[1]+o|0,t[2]=t[2]+a|0,t[3]=t[3]+s|0,t[4]=t[4]+l|0}};var ao={};ao.aes=class{constructor(n){let e=this;e._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],e._tables[0][0][0]||e._precompute();let t=e._tables[0][4],i=e._tables[1],r=n.length,o,a,s,l=1;if(r!==4&&r!==6&&r!==8)throw new Error("invalid aes key size");for(e._key=[a=n.slice(0),s=[]],o=r;o<4*r+28;o++){let c=a[o-1];(o%r===0||r===8&&o%r===4)&&(c=t[c>>>24]<<24^t[c>>16&255]<<16^t[c>>8&255]<<8^t[c&255],o%r===0&&(c=c<<8^c>>>24^l<<24,l=l<<1^(l>>7)*283)),a[o]=a[o-r]^c}for(let c=0;o;c++,o--){let p=a[c&3?o:o-4];o<=4||c<4?s[c]=p:s[c]=i[0][t[p>>>24]]^i[1][t[p>>16&255]]^i[2][t[p>>8&255]]^i[3][t[p&255]]}}encrypt(n){return this._crypt(n,0)}decrypt(n){return this._crypt(n,1)}_precompute(){let n=this._tables[0],e=this._tables[1],t=n[4],i=e[4],r=[],o=[],a,s,l,c;for(let p=0;p<256;p++)o[(r[p]=p<<1^(p>>7)*283)^p]=p;for(let p=a=0;!t[p];p^=s||1,a=o[a]||1){let m=a^a<<1^a<<2^a<<3^a<<4;m=m>>8^m&255^99,t[p]=m,i[m]=p,c=r[l=r[s=r[p]]];let g=c*16843009^l*65537^s*257^p*16843008,b=r[m]*257^m*16843008;for(let E=0;E<4;E++)n[E][p]=b=b<<24^b>>>8,e[E][m]=g=g<<24^g>>>8}for(let p=0;p<5;p++)n[p]=n[p].slice(0),e[p]=e[p].slice(0)}_crypt(n,e){if(n.length!==4)throw new Error("invalid aes block size");let t=this._key[e],i=t.length/4-2,r=[0,0,0,0],o=this._tables[e],a=o[0],s=o[1],l=o[2],c=o[3],p=o[4],m=n[0]^t[0],g=n[e?3:1]^t[1],b=n[2]^t[2],E=n[e?1:3]^t[3],f=4,d,h,v;for(let _=0;_<i;_++)d=a[m>>>24]^s[g>>16&255]^l[b>>8&255]^c[E&255]^t[f],h=a[g>>>24]^s[b>>16&255]^l[E>>8&255]^c[m&255]^t[f+1],v=a[b>>>24]^s[E>>16&255]^l[m>>8&255]^c[g&255]^t[f+2],E=a[E>>>24]^s[m>>16&255]^l[g>>8&255]^c[b&255]^t[f+3],f+=4,m=d,g=h,b=v;for(let _=0;_<4;_++)r[e?3&-_:_]=p[m>>>24]<<24^p[g>>16&255]<<16^p[b>>8&255]<<8^p[E&255]^t[f++],d=m,m=g,g=b,b=E,E=d;return r}};var js={getRandomValues(n){let e=new Uint32Array(n.buffer),t=i=>{let r=987654321,o=4294967295;return function(){return r=36969*(r&65535)+(r>>16)&o,i=18e3*(i&65535)+(i>>16)&o,(((r<<16)+i&o)/4294967296+.5)*(Math.random()>.5?1:-1)}};for(let i=0,r;i<n.length;i+=4){let o=t((r||Math.random())*4294967296);r=o()*987654071,e[i/4]=o()*4294967296|0}return n}},so={};so.ctrGladman=class{constructor(n,e){this._prf=n,this._initIv=e,this._iv=e}reset(){this._iv=this._initIv}update(n){return this.calculate(this._prf,n,this._iv)}incWord(n){if((n>>24&255)===255){let e=n>>16&255,t=n>>8&255,i=n&255;e===255?(e=0,t===255?(t=0,i===255?i=0:++i):++t):++e,n=0,n+=e<<16,n+=t<<8,n+=i}else n+=1<<24;return n}incCounter(n){(n[0]=this.incWord(n[0]))===0&&(n[1]=this.incWord(n[1]))}calculate(n,e,t){let i;if(!(i=e.length))return[];let r=Te.bitLength(e);for(let o=0;o<i;o+=4){this.incCounter(t);let a=n.encrypt(t);e[o]^=a[0],e[o+1]^=a[1],e[o+2]^=a[2],e[o+3]^=a[3]}return Te.clamp(e,r)}};var kt={importKey(n){return new kt.hmacSha1(Yn.bytes.toBits(n))},pbkdf2(n,e,t,i){if(t=t||1e4,i<0||t<0)throw new Error("invalid params to pbkdf2");let r=(i>>5)+1<<2,o,a,s,l,c,p=new ArrayBuffer(r),m=new DataView(p),g=0,b=Te;for(e=Yn.bytes.toBits(e),c=1;g<(r||1);c++){for(o=a=n.encrypt(b.concat(e,[c])),s=1;s<t;s++)for(a=n.encrypt(a),l=0;l<a.length;l++)o[l]^=a[l];for(s=0;g<(r||1)&&s<o.length;s++)m.setInt32(g,o[s]),g+=4}return p.slice(0,i/8)}};kt.hmacSha1=class{constructor(n){let e=this,t=e._hash=Us.sha1,i=[[],[]];e._baseHash=[new t,new t];let r=e._baseHash[0].blockSize/32;n.length>r&&(n=new t().update(n).finalize());for(let o=0;o<r;o++)i[0][o]=n[o]^909522486,i[1][o]=n[o]^1549556828;e._baseHash[0].update(i[0]),e._baseHash[1].update(i[1]),e._resultHash=new t(e._baseHash[0])}reset(){let n=this;n._resultHash=new n._hash(n._baseHash[0]),n._updated=!1}update(n){let e=this;e._updated=!0,e._resultHash.update(n)}digest(){let n=this,e=n._resultHash.finalize(),t=new n._hash(n._baseHash[1]).update(e).finalize();return n.reset(),t}encrypt(n){if(this._updated)throw new Error("encrypt on already updated hmac called!");return this.update(n),this.digest(n)}};var am=typeof crypto!=Fe&&typeof crypto.getRandomValues==pt,Nt="Invalid password",Vt="Invalid signature",ft="zipjs-abort-check-password";function Ci(n){return am?crypto.getRandomValues(n):js.getRandomValues(n)}var bn=16,sm="raw",Ws={name:"PBKDF2"},lm={name:"HMAC"},cm="SHA-1",pm=Object.assign({hash:lm},Ws),lo=Object.assign({iterations:1e3,hash:{name:cm}},Ws),um=["deriveBits"],Zn=[8,12,16],zn=[16,24,32],St=10,fm=[0,0,0,0],Fi=typeof crypto!=Fe,Kn=Fi&&crypto.subtle,Ys=Fi&&typeof Kn!=Fe,Qe=Yn.bytes,dm=ao.aes,mm=so.ctrGladman,hm=kt.hmacSha1,Hs=Fi&&Ys&&typeof Kn.importKey==pt,qs=Fi&&Ys&&typeof Kn.deriveBits==pt,Di=class extends TransformStream{constructor({password:e,rawPassword:t,signed:i,encryptionStrength:r,checkPasswordOnly:o}){super({start(){Object.assign(this,{ready:new Promise(a=>this.resolveReady=a),password:Gs(e,t),signed:i,strength:r-1,pending:new Uint8Array})},async transform(a,s){let l=this,{password:c,strength:p,resolveReady:m,ready:g}=l;c?(await gm(l,p,c,Me(a,0,Zn[p]+2)),a=Me(a,Zn[p]+2),o?s.error(new Error(ft)):m()):await g;let b=new Uint8Array(a.length-St-(a.length-St)%bn);s.enqueue(zs(l,a,b,0,St,!0))},async flush(a){let{signed:s,ctr:l,hmac:c,pending:p,ready:m}=this;if(c&&l){await m;let g=Me(p,0,p.length-St),b=Me(p,p.length-St),E=new Uint8Array;if(g.length){let f=Vn(Qe,g);c.update(f);let d=l.update(f);E=Gn(Qe,d)}if(s){let f=Me(Gn(Qe,c.digest()),0,St);for(let d=0;d<St;d++)if(f[d]!=b[d])throw new Error(Vt)}a.enqueue(E)}}})}},Ii=class extends TransformStream{constructor({password:e,rawPassword:t,encryptionStrength:i}){let r;super({start(){Object.assign(this,{ready:new Promise(o=>this.resolveReady=o),password:Gs(e,t),strength:i-1,pending:new Uint8Array})},async transform(o,a){let s=this,{password:l,strength:c,resolveReady:p,ready:m}=s,g=new Uint8Array;l?(g=await ym(s,c,l),p()):await m;let b=new Uint8Array(g.length+o.length-o.length%bn);b.set(g,0),a.enqueue(zs(s,o,b,g.length,0))},async flush(o){let{ctr:a,hmac:s,pending:l,ready:c}=this;if(s&&a){await c;let p=new Uint8Array;if(l.length){let m=a.update(Vn(Qe,l));s.update(m),p=Gn(Qe,m)}r.signature=Gn(Qe,s.digest()).slice(0,St),o.enqueue(co(p,r.signature))}}}),r=this}};function zs(n,e,t,i,r,o){let{ctr:a,hmac:s,pending:l}=n,c=e.length-r;l.length&&(e=co(l,e),t=wm(t,c-c%bn));let p;for(p=0;p<=c-bn;p+=bn){let m=Vn(Qe,Me(e,p,p+bn));o&&s.update(m);let g=a.update(m);o||s.update(g),t.set(Gn(Qe,g),p+i)}return n.pending=Me(e,p),t}async function gm(n,e,t,i){let r=await Zs(n,e,t,Me(i,0,Zn[e])),o=Me(i,Zn[e]);if(r[0]!=o[0]||r[1]!=o[1])throw new Error(Nt)}async function ym(n,e,t){let i=Ci(new Uint8Array(Zn[e])),r=await Zs(n,e,t,i);return co(i,r)}async function Zs(n,e,t,i){n.password=null;let r=await bm(sm,t,pm,!1,um),o=await xm(Object.assign({salt:i},lo),r,8*(zn[e]*2+2)),a=new Uint8Array(o),s=Vn(Qe,Me(a,0,zn[e])),l=Vn(Qe,Me(a,zn[e],zn[e]*2)),c=Me(a,zn[e]*2);return Object.assign(n,{keys:{key:s,authentication:l,passwordVerification:c},ctr:new mm(new dm(s),Array.from(fm)),hmac:new hm(l)}),c}async function bm(n,e,t,i,r){if(Hs)try{return await Kn.importKey(n,e,t,i,r)}catch(o){return Hs=!1,kt.importKey(e)}else return kt.importKey(e)}async function xm(n,e,t){if(qs)try{return await Kn.deriveBits(n,e,t)}catch(i){return qs=!1,kt.pbkdf2(e,n.salt,lo.iterations,t)}else return kt.pbkdf2(e,n.salt,lo.iterations,t)}function Gs(n,e){return e===ae?Bs(n):e}function co(n,e){let t=n;return n.length+e.length&&(t=new Uint8Array(n.length+e.length),t.set(n,0),t.set(e,n.length)),t}function wm(n,e){if(e&&e>n.length){let t=n;n=new Uint8Array(e),n.set(t,0)}return n}function Me(n,e,t){return n.subarray(e,t)}function Gn(n,e){return n.fromBits(e)}function Vn(n,e){return n.toBits(e)}var xn=12,Li=class extends TransformStream{constructor({password:e,passwordVerification:t,checkPasswordOnly:i}){super({start(){Object.assign(this,{password:e,passwordVerification:t}),Js(this,e)},transform(r,o){let a=this;if(a.password){let s=Vs(a,r.subarray(0,xn));if(a.password=null,s[xn-1]!=a.passwordVerification)throw new Error(Nt);r=r.subarray(xn)}i?o.error(new Error(ft)):o.enqueue(Vs(a,r))}})}},Pi=class extends TransformStream{constructor({password:e,passwordVerification:t}){super({start(){Object.assign(this,{password:e,passwordVerification:t}),Js(this,e)},transform(i,r){let o=this,a,s;if(o.password){o.password=null;let l=Ci(new Uint8Array(xn));l[xn-1]=o.passwordVerification,a=new Uint8Array(i.length+l.length),a.set(Ks(o,l),0),s=xn}else a=new Uint8Array(i.length),s=0;a.set(Ks(o,i),s),r.enqueue(a)}})}};function Vs(n,e){let t=new Uint8Array(e.length);for(let i=0;i<e.length;i++)t[i]=Qs(n)^e[i],po(n,t[i]);return t}function Ks(n,e){let t=new Uint8Array(e.length);for(let i=0;i<e.length;i++)t[i]=Qs(n)^e[i],po(n,e[i]);return t}function Js(n,e){let t=[305419896,591751049,878082192];Object.assign(n,{keys:t,crcKey0:new ut(t[0]),crcKey2:new ut(t[2])});for(let i=0;i<e.length;i++)po(n,e.charCodeAt(i))}function po(n,e){let[t,i,r]=n.keys;n.crcKey0.append([e]),t=~n.crcKey0.get(),i=Xs(Math.imul(Xs(i+el(t)),134775813)+1),n.crcKey2.append([i>>>24]),r=~n.crcKey2.get(),n.keys=[t,i,r]}function Qs(n){let e=n.keys[2]|2;return el(Math.imul(e,e^1)>>>8)}function el(n){return n&255}function Xs(n){return n&4294967295}var tl="deflate-raw",Mi=class extends TransformStream{constructor(e,{chunkSize:t,CompressionStream:i,CompressionStreamNative:r}){super({});let{compressed:o,encrypted:a,useCompressionStream:s,zipCrypto:l,signed:c,level:p}=e,m=this,g,b,E=nl(super.readable);(!a||l)&&c&&(g=new Wn,E=et(E,g)),o&&(E=rl(E,s,{level:p,chunkSize:t},r,i)),a&&(l?E=et(E,new Pi(e)):(b=new Ii(e),E=et(E,b))),il(m,E,()=>{let f;a&&!l&&(f=b.signature),(!a||l)&&c&&(f=new DataView(g.value.buffer).getUint32(0)),m.signature=f})}},$i=class extends TransformStream{constructor(e,{chunkSize:t,DecompressionStream:i,DecompressionStreamNative:r}){super({});let{zipCrypto:o,encrypted:a,signed:s,signature:l,compressed:c,useCompressionStream:p}=e,m,g,b=nl(super.readable);a&&(o?b=et(b,new Li(e)):(g=new Di(e),b=et(b,g))),c&&(b=rl(b,p,{chunkSize:t},r,i)),(!a||o)&&s&&(m=new Wn,b=et(b,m)),il(this,b,()=>{if((!a||o)&&s){let E=new DataView(m.value.buffer);if(l!=E.getUint32(0,!1))throw new Error(Vt)}})}};function nl(n){return et(n,new TransformStream({transform(e,t){e&&e.length&&t.enqueue(e)}}))}function il(n,e,t){e=et(e,new TransformStream({flush:t})),Object.defineProperty(n,"readable",{get(){return e}})}function rl(n,e,t,i,r){try{let o=e&&i?i:r;n=et(n,new o(tl,t))}catch(o){if(e)try{n=et(n,new r(tl,t))}catch(a){return n}else return n}return n}function et(n,e){return n.pipeThrough(e)}var ol="message",al="start",sl="pull",uo="data",ll="ack",fo="close",cl="deflate",ji="inflate";var Bi=class extends TransformStream{constructor(e,t){super({});let i=this,{codecType:r}=e,o;r.startsWith(cl)?o=Mi:r.startsWith(ji)&&(o=$i);let a=0,s=0,l=new o(e,t),c=super.readable,p=new TransformStream({transform(g,b){g&&g.length&&(s+=g.length,b.enqueue(g))},flush(){Object.assign(i,{inputSize:s})}}),m=new TransformStream({transform(g,b){g&&g.length&&(a+=g.length,b.enqueue(g))},flush(){let{signature:g}=l;Object.assign(i,{signature:g,outputSize:a,inputSize:s})}});Object.defineProperty(i,"readable",{get(){return c.pipeThrough(p).pipeThrough(l).pipeThrough(m)}})}},Ui=class extends TransformStream{constructor(e){let t;super({transform:i,flush(r){t&&t.length&&r.enqueue(t)}});function i(r,o){if(t){let a=new Uint8Array(t.length+r.length);a.set(t),a.set(r,t.length),r=a,t=null}r.length>e?(o.enqueue(r.slice(0,e)),i(r.slice(e),o)):t=r}}};var fl=typeof Worker!=Fe;var wn=class{constructor(e,{readable:t,writable:i},{options:r,config:o,streamOptions:a,useWebWorkers:s,transferStreams:l,scripts:c},p){let{signal:m}=a;return Object.assign(e,{busy:!0,readable:t.pipeThrough(new Ui(o.chunkSize)).pipeThrough(new ho(t,a),{signal:m}),writable:i,options:Object.assign({},r),scripts:c,transferStreams:l,terminate(){return new Promise(g=>{let{worker:b,busy:E}=e;b?(E?e.resolveTerminated=g:(b.terminate(),g()),e.interface=null):g()})},onTaskFinished(){let{resolveTerminated:g}=e;g&&(e.resolveTerminated=null,e.terminated=!0,e.worker.terminate(),g()),e.busy=!1,p(e)}}),(s&&fl?vm:dl)(e,o)}},ho=class extends TransformStream{constructor(e,{onstart:t,onprogress:i,size:r,onend:o}){let a=0;super({async start(){t&&await mo(t,r)},async transform(s,l){a+=s.length,i&&await mo(i,a,r),l.enqueue(s)},async flush(){e.size=a,o&&await mo(o,a)}})}};async function mo(n,...e){try{await n(...e)}catch(t){}}function dl(n,e){return{run:()=>Em(n,e)}}function vm(n,e){let{baseURL:t,chunkSize:i}=e;if(!n.interface){let r;try{r=Am(n.scripts[0],t,n)}catch(o){return fl=!1,dl(n,e)}Object.assign(n,{worker:r,interface:{run:()=>_m(n,{chunkSize:i})}})}return n.interface}async function Em({options:n,readable:e,writable:t,onTaskFinished:i},r){try{let o=new Bi(n,r);await e.pipeThrough(o).pipeTo(t,{preventClose:!0,preventAbort:!0});let{signature:a,inputSize:s,outputSize:l}=o;return{signature:a,inputSize:s,outputSize:l}}finally{i()}}async function _m(n,e){let t,i,r=new Promise((g,b)=>{t=g,i=b});Object.assign(n,{reader:null,writer:null,resolveResult:t,rejectResult:i,result:r});let{readable:o,options:a,scripts:s}=n,{writable:l,closed:c}=Tm(n.writable),p=Hi({type:al,scripts:s.slice(1),options:a,config:e,readable:o,writable:l},n);p||Object.assign(n,{reader:o.getReader(),writer:l.getWriter()});let m=await r;return p||await l.getWriter().close(),await c,m}function Tm(n){let e,t=new Promise(r=>e=r);return{writable:new WritableStream({async write(r){let o=n.getWriter();await o.ready,await o.write(r),o.releaseLock()},close(){e()},abort(r){return n.getWriter().abort(r)}}),closed:t}}var pl=!0,ul=!0;function Am(n,e,t){let i={type:"module"},r,o;typeof n==pt&&(n=n());try{r=new URL(n,e)}catch(a){r=n}if(pl)try{o=new Worker(r)}catch(a){pl=!1,o=new Worker(r,i)}else o=new Worker(r,i);return o.addEventListener(ol,a=>km(a,t)),o}function Hi(n,{worker:e,writer:t,onTaskFinished:i,transferStreams:r}){try{let{value:o,readable:a,writable:s}=n,l=[];if(o&&(o.byteLength<o.buffer.byteLength?n.value=o.buffer.slice(0,o.byteLength):n.value=o.buffer,l.push(n.value)),r&&ul?(a&&l.push(a),s&&l.push(s)):n.readable=n.writable=null,l.length)try{return e.postMessage(n,l),!0}catch(c){ul=!1,n.readable=n.writable=null,e.postMessage(n)}else e.postMessage(n)}catch(o){throw t&&t.releaseLock(),i(),o}}async function km({data:n},e){let{type:t,value:i,messageId:r,result:o,error:a}=n,{reader:s,writer:l,resolveResult:c,rejectResult:p,onTaskFinished:m}=e;try{if(a){let{message:b,stack:E,code:f,name:d}=a,h=new Error(b);Object.assign(h,{stack:E,code:f,name:d}),g(h)}else{if(t==sl){let{value:b,done:E}=await s.read();Hi({type:uo,value:b,done:E,messageId:r},e)}t==uo&&(await l.ready,await l.write(new Uint8Array(i)),Hi({type:ll,messageId:r},e)),t==fo&&g(null,o)}}catch(b){Hi({type:fo,messageId:r},e),g(b)}function g(b,E){b?p(b):c(E),l&&l.releaseLock(),m()}}var Kt=[],go=[];var ml=0;async function gl(n,e){let{options:t,config:i}=e,{transferStreams:r,useWebWorkers:o,useCompressionStream:a,codecType:s,compressed:l,signed:c,encrypted:p}=t,{workerScripts:m,maxWorkers:g}=i;e.transferStreams=r||r===ae;let b=!l&&!c&&!p&&!e.transferStreams;return e.useWebWorkers=!b&&(o||o===ae&&i.useWebWorkers),e.scripts=e.useWebWorkers&&m?m[s]:[],t.useCompressionStream=a||a===ae&&i.useCompressionStream,(await E()).run();async function E(){let d=Kt.find(h=>!h.busy);if(d)return hl(d),new wn(d,n,e,f);if(Kt.length<g){let h={indexWorker:ml};return ml++,Kt.push(h),new wn(h,n,e,f)}else return new Promise(h=>go.push({resolve:h,stream:n,workerOptions:e}))}function f(d){if(go.length){let[{resolve:h,stream:v,workerOptions:_}]=go.splice(0,1);h(new wn(d,v,_,f))}else d.worker?(hl(d),Nm(d,e)):Kt=Kt.filter(h=>h!=d)}}function Nm(n,e){let{config:t}=e,{terminateWorkerTimeout:i}=t;Number.isFinite(i)&&i>=0&&(n.terminated?n.terminated=!1:n.terminateTimeout=setTimeout(async()=>{Kt=Kt.filter(r=>r!=n);try{await n.terminate()}catch(r){}},i))}function hl(n){let{terminateTimeout:e}=n;e&&(clearTimeout(e),n.terminateTimeout=null)}var Sm="Writer iterator completed too soon";var Rm="Content-Type";var Om=64*1024,yl="writable",Xn=class{constructor(){this.size=0}init(){this.initialized=!0}},vn=class extends Xn{get readable(){let e=this,{chunkSize:t=Om}=e,i=new ReadableStream({start(){this.chunkOffset=0},async pull(r){let{offset:o=0,size:a,diskNumberStart:s}=i,{chunkOffset:l}=this,c=a===ae?t:Math.min(t,a-l),p=await we(e,o+l,c,s);r.enqueue(p),l+t>a||a===ae&&!p.length&&c?r.close():this.chunkOffset+=t}});return i}};var Rt=class extends vn{constructor(e){super(),Object.assign(this,{blob:e,size:e.size})}async readUint8Array(e,t){let i=this,r=e+t,a=await(e||r<i.size?i.blob.slice(e,r):i.blob).arrayBuffer();return a.byteLength>t&&(a=a.slice(e,r)),new Uint8Array(a)}},Jn=class extends Xn{constructor(e){super();let t=this,i=new TransformStream,r=[];e&&r.push([Rm,e]),Object.defineProperty(t,yl,{get(){return i.writable}}),t.blob=new Response(i.readable,{headers:r}).blob()}getData(){return this.blob}};var qi=class extends Jn{constructor(e){super(e),Object.assign(this,{encoding:e,utf8:!e||e.toLowerCase()=="utf-8"})}async getData(){let{encoding:e,utf8:t}=this,i=await super.getData();if(i.text&&t)return i.text();{let r=new FileReader;return new Promise((o,a)=>{Object.assign(r,{onload:({target:s})=>o(s.result),onerror:()=>a(r.error)}),r.readAsText(i,e)})}}};var yo=class extends vn{constructor(e){super(),this.readers=e}async init(){let e=this,{readers:t}=e;e.lastDiskNumber=0,e.lastDiskOffset=0,await Promise.all(t.map(async(i,r)=>{await i.init(),r!=t.length-1&&(e.lastDiskOffset+=i.size),e.size+=i.size})),super.init()}async readUint8Array(e,t,i=0){let r=this,{readers:o}=this,a,s=i;s==-1&&(s=o.length-1);let l=e;for(;l>=o[s].size;)l-=o[s].size,s++;let c=o[s],p=c.size;if(l+t<=p)a=await we(c,l,t);else{let m=p-l;a=new Uint8Array(t),a.set(await we(c,l,m)),a.set(await r.readUint8Array(e+m,t-m,i),m)}return r.lastDiskNumber=Math.max(s,r.lastDiskNumber),a}},Wi=class extends Xn{constructor(e,t=4294967295){super();let i=this;Object.assign(i,{diskNumber:0,diskOffset:0,size:0,maxSize:t,availableSize:t});let r,o,a,s=new WritableStream({async write(p){let{availableSize:m}=i;if(a)p.length>=m?(await l(p.slice(0,m)),await c(),i.diskOffset+=r.size,i.diskNumber++,a=null,await this.write(p.slice(m))):await l(p);else{let{value:g,done:b}=await e.next();if(b&&!g)throw new Error(Sm);r=g,r.size=0,r.maxSize&&(i.maxSize=r.maxSize),i.availableSize=i.maxSize,await Qn(r),o=g.writable,a=o.getWriter(),await this.write(p)}},async close(){await a.ready,await c()}});Object.defineProperty(i,yl,{get(){return s}});async function l(p){let m=p.length;m&&(await a.ready,await a.write(p),r.size+=m,i.size+=m,i.availableSize-=m)}async function c(){o.size=r.size,await a.close()}}};async function Qn(n,e){if(n.init&&!n.initialized)await n.init(e);else return Promise.resolve()}function bl(n){return Array.isArray(n)&&(n=new yo(n)),n instanceof ReadableStream&&(n={readable:n}),n}function xl(n){n.writable===ae&&typeof n.next==pt&&(n=new Wi(n)),n instanceof WritableStream&&(n={writable:n});let{writable:e}=n;return e.size===ae&&(e.size=0),n instanceof Wi||Object.assign(n,{diskNumber:0,diskOffset:0,availableSize:1/0,maxSize:1/0}),n}function we(n,e,t,i){return n.readUint8Array(e,t,i)}var wl="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0 ".split(""),Cm=wl.length==256;function vl(n){if(Cm){let e="";for(let t=0;t<n.length;t++)e+=wl[n[t]];return e}else return new TextDecoder().decode(n)}function ei(n,e){return e&&e.trim().toLowerCase()=="cp437"?vl(n):new TextDecoder(e).decode(n)}var bo="filename",xo="rawFilename",wo="comment",vo="rawComment",Eo="uncompressedSize",_o="compressedSize",To="offset",Yi="diskNumberStart",zi="lastModDate",Zi="rawLastModDate",Ao="lastAccessDate",El="rawLastAccessDate",ko="creationDate",_l="rawCreationDate",Dm="internalFileAttribute",Im="internalFileAttributes",Fm="externalFileAttribute",Lm="externalFileAttributes",Pm="msDosCompatible",Mm="zip64",$m="encrypted",Bm="version",Um="versionMadeBy",jm="zipCrypto",Hm="directory",qm="executable",Wm=[bo,xo,_o,Eo,zi,Zi,wo,vo,Ao,ko,To,Yi,Yi,Dm,Im,Fm,Lm,Pm,Mm,$m,Bm,Um,jm,Hm,qm,"bitFlag","signature","filenameUTF8","commentUTF8","compressionMethod","extraField","rawExtraField","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","extraFieldNTFS","extraFieldExtendedTimestamp"],ti=class{constructor(e){Wm.forEach(t=>this[t]=e[t])}};var Ro="File format is not recognized",sh="End of central directory not found",lh="End of Zip64 central directory locator not found",ch="Central directory header not found",ph="Local file header not found",uh="Zip64 extra field not found",fh="File contains encrypted entry",dh="Encryption method not supported",Dl="Compression method not supported",Il="Split zip file",Fl="utf-8",Ll="cp437",mh=[[Eo,4294967295],[_o,4294967295],[To,4294967295],[Yi,65535]],hh={[65535]:{getValue:se,bytes:4},[4294967295]:{getValue:Gi,bytes:8}},Qt=class{constructor(e,t={}){Object.assign(this,{reader:bl(e),options:t,config:oo()})}async*getEntriesGenerator(e={}){let t=this,{reader:i}=t,{config:r}=t;if(await Qn(i),(i.size===ae||!i.readUint8Array)&&(i=new Rt(await new Response(i.readable).blob()),await Qn(i)),i.size<22)throw new Error(Ro);i.chunkSize=Ms(r);let o=await wh(i,101010256,i.size,22,65535*16);if(!o){let C=await we(i,0,4),N=ge(C);throw se(N)==134695760?new Error(Il):new Error(sh)}let a=ge(o),s=se(a,12),l=se(a,16),c=o.offset,p=he(a,20),m=c+22+p,g=he(a,4),b=i.lastDiskNumber||0,E=he(a,6),f=he(a,8),d=0,h=0;if(l==4294967295||s==4294967295||f==65535||E==65535){let C=await we(i,o.offset-20,20),N=ge(C);if(se(N,0)==117853008){l=Gi(N,8);let P=await we(i,l,56,-1),L=ge(P),j=o.offset-20-56;if(se(L,0)!=101075792&&l!=j){let Y=l;l=j,d=l-Y,P=await we(i,l,56,-1),L=ge(P)}if(se(L,0)!=101075792)throw new Error(lh);g==65535&&(g=se(L,16)),E==65535&&(E=se(L,20)),f==65535&&(f=Gi(L,32)),s==4294967295&&(s=Gi(L,40)),l-=s}}if(l>=i.size&&(d=i.size-l-s-22,l=i.size-s-22),b!=g)throw new Error(Il);if(l<0)throw new Error(Ro);let v=0,_=await we(i,l,s,E),A=ge(_);if(s){let C=o.offset-s;if(se(A,v)!=33639248&&l!=C){let N=l;l=C,d+=l-N,_=await we(i,l,s,E),A=ge(_)}}let O=o.offset-l-(i.lastDiskOffset||0);if(s!=O&&O>=0&&(s=O,_=await we(i,l,s,E),A=ge(_)),l<0||l>=i.size)throw new Error(Ro);let T=Ae(t,e,"filenameEncoding"),k=Ae(t,e,"commentEncoding");for(let C=0;C<f;C++){let N=new Co(i,r,t.options);if(se(A,v)!=33639248)throw new Error(ch);Ml(N,A,v+6);let P=!!N.bitFlag.languageEncodingFlag,L=v+46,j=L+N.filenameLength,Y=j+N.extraFieldLength,M=he(A,v+4),ne=M>>8==0,Z=M>>8==3,pe=_.subarray(L,j),U=he(A,v+32),Re=Y+U,G=_.subarray(Y,Re),Q=P,ct=P,Yt=se(A,v+38),y=ne&&(En(A,v+38)&16)==16||Z&&(Yt>>16&16384)==16384||pe.length&&pe[pe.length-1]==ro.charCodeAt(0),u=Z&&(Yt>>16&73)==73,R=se(A,v+42)+d;Object.assign(N,{versionMadeBy:M,msDosCompatible:ne,compressedSize:0,uncompressedSize:0,commentLength:U,directory:y,offset:R,diskNumberStart:he(A,v+34),internalFileAttributes:he(A,v+36),externalFileAttributes:Yt,rawFilename:pe,filenameUTF8:Q,commentUTF8:ct,rawExtraField:_.subarray(j,Y),executable:u}),N.internalFileAttribute=N.internalFileAttributes,N.externalFileAttribute=N.externalFileAttributes;let x=Ae(t,e,"decodeText")||ei,S=Q?Fl:T||Ll,I=ct?Fl:k||Ll,F=x(pe,S);F===ae&&(F=ei(pe,S));let $=x(G,I);$===ae&&($=ei(G,I)),Object.assign(N,{rawComment:G,filename:F,comment:$,directory:y||F.endsWith(ro)}),h=Math.max(R,h),$l(N,N,A,v+6),N.zipCrypto=N.encrypted&&!N.extraFieldAES;let W=new ti(N);W.getData=(ee,re)=>N.getData(ee,W,re),v=Re;let{onprogress:te}=e;if(te)try{await te(C+1,f,new ti(N))}catch(ee){}yield W}let w=Ae(t,e,"extractPrependedData"),D=Ae(t,e,"extractAppendedData");return w&&(t.prependedData=h>0?await we(i,0,h):new Uint8Array),t.comment=p?await we(i,c+22,p):new Uint8Array,D&&(t.appendedData=m<i.size?await we(i,m,i.size-m):new Uint8Array),!0}async getEntries(e={}){let t=[];for await(let i of this.getEntriesGenerator(e))t.push(i);return t}async close(){}};var Co=class{constructor(e,t,i){Object.assign(this,{reader:e,config:t,options:i})}async getData(e,t,i={}){let r=this,{reader:o,offset:a,diskNumberStart:s,extraFieldAES:l,compressionMethod:c,config:p,bitFlag:m,signature:g,rawLastModDate:b,uncompressedSize:E,compressedSize:f}=r,d=t.localDirectory={},h=await we(o,a,30,s),v=ge(h),_=Ae(r,i,"password"),A=Ae(r,i,"rawPassword"),O=Ae(r,i,"passThrough");if(_=_&&_.length&&_,A=A&&A.length&&A,l&&l.originalCompressionMethod!=99)throw new Error(Dl);if(c!=0&&c!=8&&!O)throw new Error(Dl);if(se(v,0)!=67324752)throw new Error(ph);Ml(d,v,4),d.rawExtraField=d.extraFieldLength?await we(o,a+30+d.filenameLength,d.extraFieldLength,s):new Uint8Array,$l(r,d,v,4,!0),Object.assign(t,{lastAccessDate:d.lastAccessDate,creationDate:d.creationDate});let T=r.encrypted&&d.encrypted&&!O,k=T&&!l;if(O||(t.zipCrypto=k),T){if(!k&&l.strength===ae)throw new Error(dh);if(!_&&!A)throw new Error(fh)}let w=a+30+d.filenameLength+d.extraFieldLength,D=f,C=o.readable;Object.assign(C,{diskNumberStart:s,offset:w,size:D});let N=Ae(r,i,"signal"),P=Ae(r,i,"checkPasswordOnly");P&&(e=new WritableStream),e=xl(e),await Qn(e,O?f:E);let{writable:L}=e,{onstart:j,onprogress:Y,onend:M}=i,ne={options:{codecType:ji,password:_,rawPassword:A,zipCrypto:k,encryptionStrength:l&&l.strength,signed:Ae(r,i,"checkSignature")&&!O,passwordVerification:k&&(m.dataDescriptor?b>>>8&255:g>>>24&255),signature:g,compressed:c!=0&&!O,encrypted:r.encrypted&&!O,useWebWorkers:Ae(r,i,"useWebWorkers"),useCompressionStream:Ae(r,i,"useCompressionStream"),transferStreams:Ae(r,i,"transferStreams"),checkPasswordOnly:P},config:p,streamOptions:{signal:N,size:D,onstart:j,onprogress:Y,onend:M}},Z=0;try{({outputSize:Z}=await gl({readable:C,writable:L},ne))}catch(pe){if(!P||pe.message!=ft)throw pe}finally{let pe=Ae(r,i,"preventClose");L.size+=Z,!pe&&!L.locked&&await L.getWriter().close()}return P?ae:e.getData?e.getData():L}};function Ml(n,e,t){let i=n.rawBitFlag=he(e,t+2),r=(i&1)==1,o=se(e,t+6);Object.assign(n,{encrypted:r,version:he(e,t),bitFlag:{level:(i&6)>>1,dataDescriptor:(i&8)==8,languageEncodingFlag:(i&2048)==2048},rawLastModDate:o,lastModDate:vh(o),filenameLength:he(e,t+22),extraFieldLength:he(e,t+24)})}function $l(n,e,t,i,r){let{rawExtraField:o}=e,a=e.extraField=new Map,s=ge(new Uint8Array(o)),l=0;try{for(;l<o.length;){let h=he(s,l),v=he(s,l+2);a.set(h,{type:h,data:o.slice(l+4,l+4+v)}),l+=4+v}}catch(h){}let c=he(t,i+4);Object.assign(e,{signature:se(t,i+10),uncompressedSize:se(t,i+18),compressedSize:se(t,i+14)});let p=a.get(1);p&&(gh(p,e),e.extraFieldZip64=p);let m=a.get(28789);m&&(Pl(m,bo,xo,e,n),e.extraFieldUnicodePath=m);let g=a.get(25461);g&&(Pl(g,wo,vo,e,n),e.extraFieldUnicodeComment=g);let b=a.get(39169);b?(yh(b,e,c),e.extraFieldAES=b):e.compressionMethod=c;let E=a.get(10);E&&(bh(E,e),e.extraFieldNTFS=E);let f=a.get(21589);f&&(xh(f,e,r),e.extraFieldExtendedTimestamp=f);let d=a.get(6534);d&&(e.extraFieldUSDZ=d)}function gh(n,e){e.zip64=!0;let t=ge(n.data),i=mh.filter(([r,o])=>e[r]==o);for(let r=0,o=0;r<i.length;r++){let[a,s]=i[r];if(e[a]==s){let l=hh[s];e[a]=n[a]=l.getValue(t,o),o+=l.bytes}else if(n[a])throw new Error(uh)}}function Pl(n,e,t,i,r){let o=ge(n.data),a=new ut;a.append(r[t]);let s=ge(new Uint8Array(4));s.setUint32(0,a.get(),!0);let l=se(o,1);Object.assign(n,{version:En(o,0),[e]:ei(n.data.subarray(5)),valid:!r.bitFlag.languageEncodingFlag&&l==se(s,0)}),n.valid&&(i[e]=n[e],i[e+"UTF8"]=!0)}function yh(n,e,t){let i=ge(n.data),r=En(i,4);Object.assign(n,{vendorVersion:En(i,0),vendorId:En(i,2),strength:r,originalCompressionMethod:t,compressionMethod:he(i,5)}),e.compressionMethod=n.compressionMethod}function bh(n,e){let t=ge(n.data),i=4,r;try{for(;i<n.data.length&&!r;){let o=he(t,i),a=he(t,i+2);o==1&&(r=n.data.slice(i+4,i+4+a)),i+=4+a}}catch(o){}try{if(r&&r.length==24){let o=ge(r),a=o.getBigUint64(0,!0),s=o.getBigUint64(8,!0),l=o.getBigUint64(16,!0);Object.assign(n,{rawLastModDate:a,rawLastAccessDate:s,rawCreationDate:l});let c=Oo(a),p=Oo(s),m=Oo(l),g={lastModDate:c,lastAccessDate:p,creationDate:m};Object.assign(n,g),Object.assign(e,g)}}catch(o){}}function xh(n,e,t){let i=ge(n.data),r=En(i,0),o=[],a=[];t?((r&1)==1&&(o.push(zi),a.push(Zi)),(r&2)==2&&(o.push(Ao),a.push(El)),(r&4)==4&&(o.push(ko),a.push(_l))):n.data.length>=5&&(o.push(zi),a.push(Zi));let s=1;o.forEach((l,c)=>{if(n.data.length>=s+4){let p=se(i,s);e[l]=n[l]=new Date(p*1e3);let m=a[c];n[m]=p}s+=4})}async function wh(n,e,t,i,r){let o=new Uint8Array(4),a=ge(o);Eh(a,0,e);let s=i+r;return await l(i)||await l(Math.min(s,t));async function l(c){let p=t-c,m=await we(n,p,c);for(let g=m.length-i;g>=0;g--)if(m[g]==o[0]&&m[g+1]==o[1]&&m[g+2]==o[2]&&m[g+3]==o[3])return{offset:p+g,buffer:m.slice(g,g+i).buffer}}}function Ae(n,e,t){return e[t]===ae?n.options[t]:e[t]}function vh(n){let e=(n&4294901760)>>16,t=n&65535;try{return new Date(1980+((e&65024)>>9),((e&480)>>5)-1,e&31,(t&63488)>>11,(t&2016)>>5,(t&31)*2,0)}catch(i){}}function Oo(n){return new Date(Number(n/BigInt(1e4)-BigInt(116444736e5)))}function En(n,e){return n.getUint8(e)}function he(n,e){return n.getUint16(e,!0)}function se(n,e){return n.getUint32(e,!0)}function Gi(n,e){return Number(n.getBigUint64(e,!0))}function Eh(n,e,t){n.setUint32(e,t,!0)}function ge(n){return new DataView(n.buffer)}Oi({Inflate:Ls});var tn=require("obsidian");function Bl(n){let e=()=>URL.createObjectURL(new Blob([`const{Array:e,Object:t,Number:n,Math:r,Error:s,Uint8Array:i,Uint16Array:o,Uint32Array:c,Int32Array:f,Map:a,DataView:l,Promise:u,TextEncoder:w,crypto:h,postMessage:d,TransformStream:p,ReadableStream:y,WritableStream:m,CompressionStream:b,DecompressionStream:g}=self;class k{constructor(e){return class extends p{constructor(t,n){const r=new e(n);super({transform(e,t){t.enqueue(r.append(e))},flush(e){const t=r.flush();t&&e.enqueue(t)}})}}}}const v=[];for(let e=0;256>e;e++){let t=e;for(let e=0;8>e;e++)1&t?t=t>>>1^3988292384:t>>>=1;v[e]=t}class S{constructor(e){this.t=e||-1}append(e){let t=0|this.t;for(let n=0,r=0|e.length;r>n;n++)t=t>>>8^v[255&(t^e[n])];this.t=t}get(){return~this.t}}class z extends p{constructor(){let e;const t=new S;super({transform(e,n){t.append(e),n.enqueue(e)},flush(){const n=new i(4);new l(n.buffer).setUint32(0,t.get()),e.value=n}}),e=this}}const C={concat(e,t){if(0===e.length||0===t.length)return e.concat(t);const n=e[e.length-1],r=C.i(n);return 32===r?e.concat(t):C.o(t,r,0|n,e.slice(0,e.length-1))},l(e){const t=e.length;if(0===t)return 0;const n=e[t-1];return 32*(t-1)+C.i(n)},u(e,t){if(32*e.length<t)return e;const n=(e=e.slice(0,r.ceil(t/32))).length;return t&=31,n>0&&t&&(e[n-1]=C.h(t,e[n-1]&2147483648>>t-1,1)),e},h:(e,t,n)=>32===e?t:(n?0|t:t<<32-e)+1099511627776*e,i:e=>r.round(e/1099511627776)||32,o(e,t,n,r){for(void 0===r&&(r=[]);t>=32;t-=32)r.push(n),n=0;if(0===t)return r.concat(e);for(let s=0;s<e.length;s++)r.push(n|e[s]>>>t),n=e[s]<<32-t;const s=e.length?e[e.length-1]:0,i=C.i(s);return r.push(C.h(t+i&31,t+i>32?n:r.pop(),1)),r}},x={p:{m(e){const t=C.l(e)/8,n=new i(t);let r;for(let s=0;t>s;s++)0==(3&s)&&(r=e[s/4]),n[s]=r>>>24,r<<=8;return n},g(e){const t=[];let n,r=0;for(n=0;n<e.length;n++)r=r<<8|e[n],3==(3&n)&&(t.push(r),r=0);return 3&n&&t.push(C.h(8*(3&n),r)),t}}},_=class{constructor(e){const t=this;t.blockSize=512,t.k=[1732584193,4023233417,2562383102,271733878,3285377520],t.v=[1518500249,1859775393,2400959708,3395469782],e?(t.S=e.S.slice(0),t.C=e.C.slice(0),t._=e._):t.reset()}reset(){const e=this;return e.S=e.k.slice(0),e.C=[],e._=0,e}update(e){const t=this;"string"==typeof e&&(e=x.A.g(e));const n=t.C=C.concat(t.C,e),r=t._,i=t._=r+C.l(e);if(i>9007199254740991)throw new s("Cannot hash more than 2^53 - 1 bits");const o=new c(n);let f=0;for(let e=t.blockSize+r-(t.blockSize+r&t.blockSize-1);i>=e;e+=t.blockSize)t.I(o.subarray(16*f,16*(f+1))),f+=1;return n.splice(0,16*f),t}D(){const e=this;let t=e.C;const n=e.S;t=C.concat(t,[C.h(1,1)]);for(let e=t.length+2;15&e;e++)t.push(0);for(t.push(r.floor(e._/4294967296)),t.push(0|e._);t.length;)e.I(t.splice(0,16));return e.reset(),n}V(e,t,n,r){return e>19?e>39?e>59?e>79?void 0:t^n^r:t&n|t&r|n&r:t^n^r:t&n|~t&r}P(e,t){return t<<e|t>>>32-e}I(t){const n=this,s=n.S,i=e(80);for(let e=0;16>e;e++)i[e]=t[e];let o=s[0],c=s[1],f=s[2],a=s[3],l=s[4];for(let e=0;79>=e;e++){16>e||(i[e]=n.P(1,i[e-3]^i[e-8]^i[e-14]^i[e-16]));const t=n.P(5,o)+n.V(e,c,f,a)+l+i[e]+n.v[r.floor(e/20)]|0;l=a,a=f,f=n.P(30,c),c=o,o=t}s[0]=s[0]+o|0,s[1]=s[1]+c|0,s[2]=s[2]+f|0,s[3]=s[3]+a|0,s[4]=s[4]+l|0}},A={getRandomValues(e){const t=new c(e.buffer),n=e=>{let t=987654321;const n=4294967295;return()=>(t=36969*(65535&t)+(t>>16)&n,(((t<<16)+(e=18e3*(65535&e)+(e>>16)&n)&n)/4294967296+.5)*(r.random()>.5?1:-1))};for(let s,i=0;i<e.length;i+=4){const e=n(4294967296*(s||r.random()));s=987654071*e(),t[i/4]=4294967296*e()|0}return e}},I={importKey:e=>new I.R(x.p.g(e)),B(e,t,n,r){if(n=n||1e4,0>r||0>n)throw new s("invalid params to pbkdf2");const i=1+(r>>5)<<2;let o,c,f,a,u;const w=new ArrayBuffer(i),h=new l(w);let d=0;const p=C;for(t=x.p.g(t),u=1;(i||1)>d;u++){for(o=c=e.encrypt(p.concat(t,[u])),f=1;n>f;f++)for(c=e.encrypt(c),a=0;a<c.length;a++)o[a]^=c[a];for(f=0;(i||1)>d&&f<o.length;f++)h.setInt32(d,o[f]),d+=4}return w.slice(0,r/8)},R:class{constructor(e){const t=this,n=t.M=_,r=[[],[]];t.K=[new n,new n];const s=t.K[0].blockSize/32;e.length>s&&(e=(new n).update(e).D());for(let t=0;s>t;t++)r[0][t]=909522486^e[t],r[1][t]=1549556828^e[t];t.K[0].update(r[0]),t.K[1].update(r[1]),t.U=new n(t.K[0])}reset(){const e=this;e.U=new e.M(e.K[0]),e.N=!1}update(e){this.N=!0,this.U.update(e)}digest(){const e=this,t=e.U.D(),n=new e.M(e.K[1]).update(t).D();return e.reset(),n}encrypt(e){if(this.N)throw new s("encrypt on already updated hmac called!");return this.update(e),this.digest(e)}}},D=void 0!==h&&"function"==typeof h.getRandomValues,V="Invalid password",P="Invalid signature",R="zipjs-abort-check-password";function B(e){return D?h.getRandomValues(e):A.getRandomValues(e)}const E=16,M={name:"PBKDF2"},K=t.assign({hash:{name:"HMAC"}},M),U=t.assign({iterations:1e3,hash:{name:"SHA-1"}},M),N=["deriveBits"],O=[8,12,16],T=[16,24,32],W=10,j=[0,0,0,0],H="undefined",L="function",F=typeof h!=H,q=F&&h.subtle,G=F&&typeof q!=H,J=x.p,Q=class{constructor(e){const t=this;t.O=[[[],[],[],[],[]],[[],[],[],[],[]]],t.O[0][0][0]||t.T();const n=t.O[0][4],r=t.O[1],i=e.length;let o,c,f,a=1;if(4!==i&&6!==i&&8!==i)throw new s("invalid aes key size");for(t.v=[c=e.slice(0),f=[]],o=i;4*i+28>o;o++){let e=c[o-1];(o%i==0||8===i&&o%i==4)&&(e=n[e>>>24]<<24^n[e>>16&255]<<16^n[e>>8&255]<<8^n[255&e],o%i==0&&(e=e<<8^e>>>24^a<<24,a=a<<1^283*(a>>7))),c[o]=c[o-i]^e}for(let e=0;o;e++,o--){const t=c[3&e?o:o-4];f[e]=4>=o||4>e?t:r[0][n[t>>>24]]^r[1][n[t>>16&255]]^r[2][n[t>>8&255]]^r[3][n[255&t]]}}encrypt(e){return this.W(e,0)}decrypt(e){return this.W(e,1)}T(){const e=this.O[0],t=this.O[1],n=e[4],r=t[4],s=[],i=[];let o,c,f,a;for(let e=0;256>e;e++)i[(s[e]=e<<1^283*(e>>7))^e]=e;for(let l=o=0;!n[l];l^=c||1,o=i[o]||1){let i=o^o<<1^o<<2^o<<3^o<<4;i=i>>8^255&i^99,n[l]=i,r[i]=l,a=s[f=s[c=s[l]]];let u=16843009*a^65537*f^257*c^16843008*l,w=257*s[i]^16843008*i;for(let n=0;4>n;n++)e[n][l]=w=w<<24^w>>>8,t[n][i]=u=u<<24^u>>>8}for(let n=0;5>n;n++)e[n]=e[n].slice(0),t[n]=t[n].slice(0)}W(e,t){if(4!==e.length)throw new s("invalid aes block size");const n=this.v[t],r=n.length/4-2,i=[0,0,0,0],o=this.O[t],c=o[0],f=o[1],a=o[2],l=o[3],u=o[4];let w,h,d,p=e[0]^n[0],y=e[t?3:1]^n[1],m=e[2]^n[2],b=e[t?1:3]^n[3],g=4;for(let e=0;r>e;e++)w=c[p>>>24]^f[y>>16&255]^a[m>>8&255]^l[255&b]^n[g],h=c[y>>>24]^f[m>>16&255]^a[b>>8&255]^l[255&p]^n[g+1],d=c[m>>>24]^f[b>>16&255]^a[p>>8&255]^l[255&y]^n[g+2],b=c[b>>>24]^f[p>>16&255]^a[y>>8&255]^l[255&m]^n[g+3],g+=4,p=w,y=h,m=d;for(let e=0;4>e;e++)i[t?3&-e:e]=u[p>>>24]<<24^u[y>>16&255]<<16^u[m>>8&255]<<8^u[255&b]^n[g++],w=p,p=y,y=m,m=b,b=w;return i}},X=class{constructor(e,t){this.j=e,this.H=t,this.L=t}reset(){this.L=this.H}update(e){return this.F(this.j,e,this.L)}q(e){if(255==(e>>24&255)){let t=e>>16&255,n=e>>8&255,r=255&e;255===t?(t=0,255===n?(n=0,255===r?r=0:++r):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=r}else e+=1<<24;return e}G(e){0===(e[0]=this.q(e[0]))&&(e[1]=this.q(e[1]))}F(e,t,n){let r;if(!(r=t.length))return[];const s=C.l(t);for(let s=0;r>s;s+=4){this.G(n);const r=e.encrypt(n);t[s]^=r[0],t[s+1]^=r[1],t[s+2]^=r[2],t[s+3]^=r[3]}return C.u(t,s)}},Y=I.R;let Z=F&&G&&typeof q.importKey==L,$=F&&G&&typeof q.deriveBits==L;class ee extends p{constructor({password:e,signed:n,encryptionStrength:r,checkPasswordOnly:o}){super({start(){t.assign(this,{ready:new u((e=>this.J=e)),password:e,signed:n,X:r-1,pending:new i})},async transform(e,t){const n=this,{password:r,X:c,J:f,ready:a}=n;r?(await(async(e,t,n,r)=>{const i=await re(e,t,n,ie(r,0,O[t])),o=ie(r,O[t]);if(i[0]!=o[0]||i[1]!=o[1])throw new s(V)})(n,c,r,ie(e,0,O[c]+2)),e=ie(e,O[c]+2),o?t.error(new s(R)):f()):await a;const l=new i(e.length-W-(e.length-W)%E);t.enqueue(ne(n,e,l,0,W,!0))},async flush(e){const{signed:t,Y:n,Z:r,pending:o,ready:c}=this;await c;const f=ie(o,0,o.length-W),a=ie(o,o.length-W);let l=new i;if(f.length){const e=ce(J,f);r.update(e);const t=n.update(e);l=oe(J,t)}if(t){const e=ie(oe(J,r.digest()),0,W);for(let t=0;W>t;t++)if(e[t]!=a[t])throw new s(P)}e.enqueue(l)}})}}class te extends p{constructor({password:e,encryptionStrength:n}){let r;super({start(){t.assign(this,{ready:new u((e=>this.J=e)),password:e,X:n-1,pending:new i})},async transform(e,t){const n=this,{password:r,X:s,J:o,ready:c}=n;let f=new i;r?(f=await(async(e,t,n)=>{const r=B(new i(O[t]));return se(r,await re(e,t,n,r))})(n,s,r),o()):await c;const a=new i(f.length+e.length-e.length%E);a.set(f,0),t.enqueue(ne(n,e,a,f.length,0))},async flush(e){const{Y:t,Z:n,pending:s,ready:o}=this;await o;let c=new i;if(s.length){const e=t.update(ce(J,s));n.update(e),c=oe(J,e)}r.signature=oe(J,n.digest()).slice(0,W),e.enqueue(se(c,r.signature))}}),r=this}}function ne(e,t,n,r,s,o){const{Y:c,Z:f,pending:a}=e,l=t.length-s;let u;for(a.length&&(t=se(a,t),n=((e,t)=>{if(t&&t>e.length){const n=e;(e=new i(t)).set(n,0)}return e})(n,l-l%E)),u=0;l-E>=u;u+=E){const e=ce(J,ie(t,u,u+E));o&&f.update(e);const s=c.update(e);o||f.update(s),n.set(oe(J,s),u+r)}return e.pending=ie(t,u),n}async function re(n,r,s,o){n.password=null;const c=(e=>{if(void 0===w){const t=new i((e=unescape(encodeURIComponent(e))).length);for(let n=0;n<t.length;n++)t[n]=e.charCodeAt(n);return t}return(new w).encode(e)})(s),f=await(async(e,t,n,r,s)=>{if(!Z)return I.importKey(t);try{return await q.importKey("raw",t,n,!1,s)}catch(e){return Z=!1,I.importKey(t)}})(0,c,K,0,N),a=await(async(e,t,n)=>{if(!$)return I.B(t,e.salt,U.iterations,n);try{return await q.deriveBits(e,t,n)}catch(r){return $=!1,I.B(t,e.salt,U.iterations,n)}})(t.assign({salt:o},U),f,8*(2*T[r]+2)),l=new i(a),u=ce(J,ie(l,0,T[r])),h=ce(J,ie(l,T[r],2*T[r])),d=ie(l,2*T[r]);return t.assign(n,{keys:{key:u,$:h,passwordVerification:d},Y:new X(new Q(u),e.from(j)),Z:new Y(h)}),d}function se(e,t){let n=e;return e.length+t.length&&(n=new i(e.length+t.length),n.set(e,0),n.set(t,e.length)),n}function ie(e,t,n){return e.subarray(t,n)}function oe(e,t){return e.m(t)}function ce(e,t){return e.g(t)}class fe extends p{constructor({password:e,passwordVerification:n,checkPasswordOnly:r}){super({start(){t.assign(this,{password:e,passwordVerification:n}),we(this,e)},transform(e,t){const n=this;if(n.password){const t=le(n,e.subarray(0,12));if(n.password=null,t[11]!=n.passwordVerification)throw new s(V);e=e.subarray(12)}r?t.error(new s(R)):t.enqueue(le(n,e))}})}}class ae extends p{constructor({password:e,passwordVerification:n}){super({start(){t.assign(this,{password:e,passwordVerification:n}),we(this,e)},transform(e,t){const n=this;let r,s;if(n.password){n.password=null;const t=B(new i(12));t[11]=n.passwordVerification,r=new i(e.length+t.length),r.set(ue(n,t),0),s=12}else r=new i(e.length),s=0;r.set(ue(n,e),s),t.enqueue(r)}})}}function le(e,t){const n=new i(t.length);for(let r=0;r<t.length;r++)n[r]=de(e)^t[r],he(e,n[r]);return n}function ue(e,t){const n=new i(t.length);for(let r=0;r<t.length;r++)n[r]=de(e)^t[r],he(e,t[r]);return n}function we(e,n){const r=[305419896,591751049,878082192];t.assign(e,{keys:r,ee:new S(r[0]),te:new S(r[2])});for(let t=0;t<n.length;t++)he(e,n.charCodeAt(t))}function he(e,t){let[n,s,i]=e.keys;e.ee.append([t]),n=~e.ee.get(),s=ye(r.imul(ye(s+pe(n)),134775813)+1),e.te.append([s>>>24]),i=~e.te.get(),e.keys=[n,s,i]}function de(e){const t=2|e.keys[2];return pe(r.imul(t,1^t)>>>8)}function pe(e){return 255&e}function ye(e){return 4294967295&e}const me="deflate-raw";class be extends p{constructor(e,{chunkSize:t,CompressionStream:n,CompressionStreamNative:r}){super({});const{compressed:s,encrypted:i,useCompressionStream:o,zipCrypto:c,signed:f,level:a}=e,u=this;let w,h,d=ke(super.readable);i&&!c||!f||(w=new z,d=ze(d,w)),s&&(d=Se(d,o,{level:a,chunkSize:t},r,n)),i&&(c?d=ze(d,new ae(e)):(h=new te(e),d=ze(d,h))),ve(u,d,(async()=>{let e;i&&!c&&(e=h.signature),i&&!c||!f||(e=new l(w.value.buffer).getUint32(0)),u.signature=e}))}}class ge extends p{constructor(e,{chunkSize:t,DecompressionStream:n,DecompressionStreamNative:r}){super({});const{zipCrypto:i,encrypted:o,signed:c,signature:f,compressed:a,useCompressionStream:u}=e;let w,h,d=ke(super.readable);o&&(i?d=ze(d,new fe(e)):(h=new ee(e),d=ze(d,h))),a&&(d=Se(d,u,{chunkSize:t},r,n)),o&&!i||!c||(w=new z,d=ze(d,w)),ve(this,d,(async()=>{if((!o||i)&&c){const e=new l(w.value.buffer);if(f!=e.getUint32(0,!1))throw new s(P)}}))}}function ke(e){return ze(e,new p({transform(e,t){e&&e.length&&t.enqueue(e)}}))}function ve(e,n,r){n=ze(n,new p({flush:r})),t.defineProperty(e,"readable",{get:()=>n})}function Se(e,t,n,r,s){try{e=ze(e,new(t&&r?r:s)(me,n))}catch(r){if(!t)throw r;e=ze(e,new s(me,n))}return e}function ze(e,t){return e.pipeThrough(t)}const Ce="data";class xe extends p{constructor(e,n){super({});const r=this,{codecType:s}=e;let i;s.startsWith("deflate")?i=be:s.startsWith("inflate")&&(i=ge);let o=0;const c=new i(e,n),f=super.readable,a=new p({transform(e,t){e&&e.length&&(o+=e.length,t.enqueue(e))},flush(){const{signature:e}=c;t.assign(r,{signature:e,size:o})}});t.defineProperty(r,"readable",{get:()=>f.pipeThrough(c).pipeThrough(a)})}}const _e=new a,Ae=new a;let Ie=0;async function De(e){try{const{options:t,scripts:r,config:s}=e;r&&r.length&&importScripts.apply(void 0,r),self.initCodec&&self.initCodec(),s.CompressionStreamNative=self.CompressionStream,s.DecompressionStreamNative=self.DecompressionStream,self.Deflate&&(s.CompressionStream=new k(self.Deflate)),self.Inflate&&(s.DecompressionStream=new k(self.Inflate));const i={highWaterMark:1,size:()=>s.chunkSize},o=e.readable||new y({async pull(e){const t=new u((e=>_e.set(Ie,e)));Ve({type:"pull",messageId:Ie}),Ie=(Ie+1)%n.MAX_SAFE_INTEGER;const{value:r,done:s}=await t;e.enqueue(r),s&&e.close()}},i),c=e.writable||new m({async write(e){let t;const r=new u((e=>t=e));Ae.set(Ie,t),Ve({type:Ce,value:e,messageId:Ie}),Ie=(Ie+1)%n.MAX_SAFE_INTEGER,await r}},i),f=new xe(t,s);await o.pipeThrough(f).pipeTo(c,{preventClose:!0,preventAbort:!0});try{await c.getWriter().close()}catch(e){}const{signature:a,size:l}=f;Ve({type:"close",result:{signature:a,size:l}})}catch(e){Pe(e)}}function Ve(e){let{value:t}=e;if(t)if(t.length)try{t=new i(t),e.value=t.buffer,d(e,[e.value])}catch(t){d(e)}else d(e);else d(e)}function Pe(e){const{message:t,stack:n,code:r,name:s}=e;d({error:{message:t,stack:n,code:r,name:s}})}addEventListener("message",(({data:e})=>{const{type:t,messageId:n,value:r,done:s}=e;try{if("start"==t&&De(e),t==Ce){const e=_e.get(n);_e.delete(n),e({value:new i(r),done:s})}if("ack"==t){const e=Ae.get(n);Ae.delete(n),e()}}catch(e){Pe(e)}}));const Re=-2;function Be(t){return Ee(t.map((([t,n])=>new e(t).fill(n,0,t))))}function Ee(t){return t.reduce(((t,n)=>t.concat(e.isArray(n)?Ee(n):n)),[])}const Me=[0,1,2,3].concat(...Be([[2,4],[2,5],[4,6],[4,7],[8,8],[8,9],[16,10],[16,11],[32,12],[32,13],[64,14],[64,15],[2,0],[1,16],[1,17],[2,18],[2,19],[4,20],[4,21],[8,22],[8,23],[16,24],[16,25],[32,26],[32,27],[64,28],[64,29]]));function Ke(){const e=this;function t(e,t){let n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}e.ne=n=>{const s=e.re,i=e.ie.se,o=e.ie.oe;let c,f,a,l=-1;for(n.ce=0,n.fe=573,c=0;o>c;c++)0!==s[2*c]?(n.ae[++n.ce]=l=c,n.le[c]=0):s[2*c+1]=0;for(;2>n.ce;)a=n.ae[++n.ce]=2>l?++l:0,s[2*a]=1,n.le[a]=0,n.ue--,i&&(n.we-=i[2*a+1]);for(e.he=l,c=r.floor(n.ce/2);c>=1;c--)n.de(s,c);a=o;do{c=n.ae[1],n.ae[1]=n.ae[n.ce--],n.de(s,1),f=n.ae[1],n.ae[--n.fe]=c,n.ae[--n.fe]=f,s[2*a]=s[2*c]+s[2*f],n.le[a]=r.max(n.le[c],n.le[f])+1,s[2*c+1]=s[2*f+1]=a,n.ae[1]=a++,n.de(s,1)}while(n.ce>=2);n.ae[--n.fe]=n.ae[1],(t=>{const n=e.re,r=e.ie.se,s=e.ie.pe,i=e.ie.ye,o=e.ie.me;let c,f,a,l,u,w,h=0;for(l=0;15>=l;l++)t.be[l]=0;for(n[2*t.ae[t.fe]+1]=0,c=t.fe+1;573>c;c++)f=t.ae[c],l=n[2*n[2*f+1]+1]+1,l>o&&(l=o,h++),n[2*f+1]=l,f>e.he||(t.be[l]++,u=0,i>f||(u=s[f-i]),w=n[2*f],t.ue+=w*(l+u),r&&(t.we+=w*(r[2*f+1]+u)));if(0!==h){do{for(l=o-1;0===t.be[l];)l--;t.be[l]--,t.be[l+1]+=2,t.be[o]--,h-=2}while(h>0);for(l=o;0!==l;l--)for(f=t.be[l];0!==f;)a=t.ae[--c],a>e.he||(n[2*a+1]!=l&&(t.ue+=(l-n[2*a+1])*n[2*a],n[2*a+1]=l),f--)}})(n),((e,n,r)=>{const s=[];let i,o,c,f=0;for(i=1;15>=i;i++)s[i]=f=f+r[i-1]<<1;for(o=0;n>=o;o++)c=e[2*o+1],0!==c&&(e[2*o]=t(s[c]++,c))})(s,e.he,n.be)}}function Ue(e,t,n,r,s){const i=this;i.se=e,i.pe=t,i.ye=n,i.oe=r,i.me=s}Ke.ge=[0,1,2,3,4,5,6,7].concat(...Be([[2,8],[2,9],[2,10],[2,11],[4,12],[4,13],[4,14],[4,15],[8,16],[8,17],[8,18],[8,19],[16,20],[16,21],[16,22],[16,23],[32,24],[32,25],[32,26],[31,27],[1,28]])),Ke.ke=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],Ke.ve=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],Ke.Se=e=>256>e?Me[e]:Me[256+(e>>>7)],Ke.ze=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],Ke.Ce=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],Ke.xe=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Ke._e=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];const Ne=Be([[144,8],[112,9],[24,7],[8,8]]);Ue.Ae=Ee([12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254,1,129,65,193,33,161,97,225,17,145,81,209,49,177,113,241,9,137,73,201,41,169,105,233,25,153,89,217,57,185,121,249,5,133,69,197,37,165,101,229,21,149,85,213,53,181,117,245,13,141,77,205,45,173,109,237,29,157,93,221,61,189,125,253,19,275,147,403,83,339,211,467,51,307,179,435,115,371,243,499,11,267,139,395,75,331,203,459,43,299,171,427,107,363,235,491,27,283,155,411,91,347,219,475,59,315,187,443,123,379,251,507,7,263,135,391,71,327,199,455,39,295,167,423,103,359,231,487,23,279,151,407,87,343,215,471,55,311,183,439,119,375,247,503,15,271,143,399,79,335,207,463,47,303,175,431,111,367,239,495,31,287,159,415,95,351,223,479,63,319,191,447,127,383,255,511,0,64,32,96,16,80,48,112,8,72,40,104,24,88,56,120,4,68,36,100,20,84,52,116,3,131,67,195,35,163,99,227].map(((e,t)=>[e,Ne[t]])));const Oe=Be([[30,5]]);function Te(e,t,n,r,s){const i=this;i.Ie=e,i.De=t,i.Ve=n,i.Pe=r,i.Re=s}Ue.Be=Ee([0,16,8,24,4,20,12,28,2,18,10,26,6,22,14,30,1,17,9,25,5,21,13,29,3,19,11,27,7,23].map(((e,t)=>[e,Oe[t]]))),Ue.Ee=new Ue(Ue.Ae,Ke.ze,257,286,15),Ue.Me=new Ue(Ue.Be,Ke.Ce,0,30,15),Ue.Ke=new Ue(null,Ke.xe,0,19,7);const We=[new Te(0,0,0,0,0),new Te(4,4,8,4,1),new Te(4,5,16,8,1),new Te(4,6,32,32,1),new Te(4,4,16,16,2),new Te(8,16,32,32,2),new Te(8,16,128,128,2),new Te(8,32,128,256,2),new Te(32,128,258,1024,2),new Te(32,258,258,4096,2)],je=["need dictionary","stream end","","","stream error","data error","","buffer error","",""],He=113,Le=666,Fe=262;function qe(e,t,n,r){const s=e[2*t],i=e[2*n];return i>s||s==i&&r[t]<=r[n]}function Ge(){const e=this;let t,n,s,c,f,a,l,u,w,h,d,p,y,m,b,g,k,v,S,z,C,x,_,A,I,D,V,P,R,B,E,M,K;const U=new Ke,N=new Ke,O=new Ke;let T,W,j,H,L,F;function q(){let t;for(t=0;286>t;t++)E[2*t]=0;for(t=0;30>t;t++)M[2*t]=0;for(t=0;19>t;t++)K[2*t]=0;E[512]=1,e.ue=e.we=0,W=j=0}function G(e,t){let n,r=-1,s=e[1],i=0,o=7,c=4;0===s&&(o=138,c=3),e[2*(t+1)+1]=65535;for(let f=0;t>=f;f++)n=s,s=e[2*(f+1)+1],++i<o&&n==s||(c>i?K[2*n]+=i:0!==n?(n!=r&&K[2*n]++,K[32]++):i>10?K[36]++:K[34]++,i=0,r=n,0===s?(o=138,c=3):n==s?(o=6,c=3):(o=7,c=4))}function J(t){e.Ue[e.pending++]=t}function Q(e){J(255&e),J(e>>>8&255)}function X(e,t){let n;const r=t;F>16-r?(n=e,L|=n<<F&65535,Q(L),L=n>>>16-F,F+=r-16):(L|=e<<F&65535,F+=r)}function Y(e,t){const n=2*e;X(65535&t[n],65535&t[n+1])}function Z(e,t){let n,r,s=-1,i=e[1],o=0,c=7,f=4;for(0===i&&(c=138,f=3),n=0;t>=n;n++)if(r=i,i=e[2*(n+1)+1],++o>=c||r!=i){if(f>o)do{Y(r,K)}while(0!=--o);else 0!==r?(r!=s&&(Y(r,K),o--),Y(16,K),X(o-3,2)):o>10?(Y(18,K),X(o-11,7)):(Y(17,K),X(o-3,3));o=0,s=r,0===i?(c=138,f=3):r==i?(c=6,f=3):(c=7,f=4)}}function $(){16==F?(Q(L),L=0,F=0):8>F||(J(255&L),L>>>=8,F-=8)}function ee(t,n){let s,i,o;if(e.Ne[W]=t,e.Oe[W]=255&n,W++,0===t?E[2*n]++:(j++,t--,E[2*(Ke.ge[n]+256+1)]++,M[2*Ke.Se(t)]++),0==(8191&W)&&V>2){for(s=8*W,i=C-k,o=0;30>o;o++)s+=M[2*o]*(5+Ke.Ce[o]);if(s>>>=3,j<r.floor(W/2)&&s<r.floor(i/2))return!0}return W==T-1}function te(t,n){let r,s,i,o,c=0;if(0!==W)do{r=e.Ne[c],s=e.Oe[c],c++,0===r?Y(s,t):(i=Ke.ge[s],Y(i+256+1,t),o=Ke.ze[i],0!==o&&(s-=Ke.ke[i],X(s,o)),r--,i=Ke.Se(r),Y(i,n),o=Ke.Ce[i],0!==o&&(r-=Ke.ve[i],X(r,o)))}while(W>c);Y(256,t),H=t[513]}function ne(){F>8?Q(L):F>0&&J(255&L),L=0,F=0}function re(t,n,r){X(0+(r?1:0),3),((t,n)=>{ne(),H=8,Q(n),Q(~n),e.Ue.set(u.subarray(t,t+n),e.pending),e.pending+=n})(t,n)}function se(n){((t,n,r)=>{let s,i,o=0;V>0?(U.ne(e),N.ne(e),o=(()=>{let t;for(G(E,U.he),G(M,N.he),O.ne(e),t=18;t>=3&&0===K[2*Ke._e[t]+1];t--);return e.ue+=14+3*(t+1),t})(),s=e.ue+3+7>>>3,i=e.we+3+7>>>3,i>s||(s=i)):s=i=n+5,n+4>s||-1==t?i==s?(X(2+(r?1:0),3),te(Ue.Ae,Ue.Be)):(X(4+(r?1:0),3),((e,t,n)=>{let r;for(X(e-257,5),X(t-1,5),X(n-4,4),r=0;n>r;r++)X(K[2*Ke._e[r]+1],3);Z(E,e-1),Z(M,t-1)})(U.he+1,N.he+1,o+1),te(E,M)):re(t,n,r),q(),r&&ne()})(0>k?-1:k,C-k,n),k=C,t.Te()}function ie(){let e,n,r,s;do{if(s=w-_-C,0===s&&0===C&&0===_)s=f;else if(-1==s)s--;else if(C>=f+f-Fe){u.set(u.subarray(f,f+f),0),x-=f,C-=f,k-=f,e=y,r=e;do{n=65535&d[--r],d[r]=f>n?0:n-f}while(0!=--e);e=f,r=e;do{n=65535&h[--r],h[r]=f>n?0:n-f}while(0!=--e);s+=f}if(0===t.We)return;e=t.je(u,C+_,s),_+=e,3>_||(p=255&u[C],p=(p<<g^255&u[C+1])&b)}while(Fe>_&&0!==t.We)}function oe(e){let t,n,r=I,s=C,i=A;const o=C>f-Fe?C-(f-Fe):0;let c=B;const a=l,w=C+258;let d=u[s+i-1],p=u[s+i];R>A||(r>>=2),c>_&&(c=_);do{if(t=e,u[t+i]==p&&u[t+i-1]==d&&u[t]==u[s]&&u[++t]==u[s+1]){s+=2,t++;do{}while(u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&u[++s]==u[++t]&&w>s);if(n=258-(w-s),s=w-258,n>i){if(x=e,i=n,n>=c)break;d=u[s+i-1],p=u[s+i]}}}while((e=65535&h[e&a])>o&&0!=--r);return i>_?_:i}e.le=[],e.be=[],e.ae=[],E=[],M=[],K=[],e.de=(t,n)=>{const r=e.ae,s=r[n];let i=n<<1;for(;i<=e.ce&&(i<e.ce&&qe(t,r[i+1],r[i],e.le)&&i++,!qe(t,s,r[i],e.le));)r[n]=r[i],n=i,i<<=1;r[n]=s},e.He=(t,S,x,W,j,G)=>(W||(W=8),j||(j=8),G||(G=0),t.Le=null,-1==S&&(S=6),1>j||j>9||8!=W||9>x||x>15||0>S||S>9||0>G||G>2?Re:(t.Fe=e,a=x,f=1<<a,l=f-1,m=j+7,y=1<<m,b=y-1,g=r.floor((m+3-1)/3),u=new i(2*f),h=[],d=[],T=1<<j+6,e.Ue=new i(4*T),s=4*T,e.Ne=new o(T),e.Oe=new i(T),V=S,P=G,(t=>(t.qe=t.Ge=0,t.Le=null,e.pending=0,e.Je=0,n=He,c=0,U.re=E,U.ie=Ue.Ee,N.re=M,N.ie=Ue.Me,O.re=K,O.ie=Ue.Ke,L=0,F=0,H=8,q(),(()=>{w=2*f,d[y-1]=0;for(let e=0;y-1>e;e++)d[e]=0;D=We[V].De,R=We[V].Ie,B=We[V].Ve,I=We[V].Pe,C=0,k=0,_=0,v=A=2,z=0,p=0})(),0))(t))),e.Qe=()=>42!=n&&n!=He&&n!=Le?Re:(e.Oe=null,e.Ne=null,e.Ue=null,d=null,h=null,u=null,e.Fe=null,n==He?-3:0),e.Xe=(e,t,n)=>{let r=0;return-1==t&&(t=6),0>t||t>9||0>n||n>2?Re:(We[V].Re!=We[t].Re&&0!==e.qe&&(r=e.Ye(1)),V!=t&&(V=t,D=We[V].De,R=We[V].Ie,B=We[V].Ve,I=We[V].Pe),P=n,r)},e.Ze=(e,t,r)=>{let s,i=r,o=0;if(!t||42!=n)return Re;if(3>i)return 0;for(i>f-Fe&&(i=f-Fe,o=r-i),u.set(t.subarray(o,o+i),0),C=i,k=i,p=255&u[0],p=(p<<g^255&u[1])&b,s=0;i-3>=s;s++)p=(p<<g^255&u[s+2])&b,h[s&l]=d[p],d[p]=s;return 0},e.Ye=(r,i)=>{let o,w,m,I,R;if(i>4||0>i)return Re;if(!r.$e||!r.et&&0!==r.We||n==Le&&4!=i)return r.Le=je[4],Re;if(0===r.tt)return r.Le=je[7],-5;var B;if(t=r,I=c,c=i,42==n&&(w=8+(a-8<<4)<<8,m=(V-1&255)>>1,m>3&&(m=3),w|=m<<6,0!==C&&(w|=32),w+=31-w%31,n=He,J((B=w)>>8&255),J(255&B)),0!==e.pending){if(t.Te(),0===t.tt)return c=-1,0}else if(0===t.We&&I>=i&&4!=i)return t.Le=je[7],-5;if(n==Le&&0!==t.We)return r.Le=je[7],-5;if(0!==t.We||0!==_||0!=i&&n!=Le){switch(R=-1,We[V].Re){case 0:R=(e=>{let n,r=65535;for(r>s-5&&(r=s-5);;){if(1>=_){if(ie(),0===_&&0==e)return 0;if(0===_)break}if(C+=_,_=0,n=k+r,(0===C||C>=n)&&(_=C-n,C=n,se(!1),0===t.tt))return 0;if(C-k>=f-Fe&&(se(!1),0===t.tt))return 0}return se(4==e),0===t.tt?4==e?2:0:4==e?3:1})(i);break;case 1:R=(e=>{let n,r=0;for(;;){if(Fe>_){if(ie(),Fe>_&&0==e)return 0;if(0===_)break}if(3>_||(p=(p<<g^255&u[C+2])&b,r=65535&d[p],h[C&l]=d[p],d[p]=C),0===r||(C-r&65535)>f-Fe||2!=P&&(v=oe(r)),3>v)n=ee(0,255&u[C]),_--,C++;else if(n=ee(C-x,v-3),_-=v,v>D||3>_)C+=v,v=0,p=255&u[C],p=(p<<g^255&u[C+1])&b;else{v--;do{C++,p=(p<<g^255&u[C+2])&b,r=65535&d[p],h[C&l]=d[p],d[p]=C}while(0!=--v);C++}if(n&&(se(!1),0===t.tt))return 0}return se(4==e),0===t.tt?4==e?2:0:4==e?3:1})(i);break;case 2:R=(e=>{let n,r,s=0;for(;;){if(Fe>_){if(ie(),Fe>_&&0==e)return 0;if(0===_)break}if(3>_||(p=(p<<g^255&u[C+2])&b,s=65535&d[p],h[C&l]=d[p],d[p]=C),A=v,S=x,v=2,0!==s&&D>A&&f-Fe>=(C-s&65535)&&(2!=P&&(v=oe(s)),5>=v&&(1==P||3==v&&C-x>4096)&&(v=2)),3>A||v>A)if(0!==z){if(n=ee(0,255&u[C-1]),n&&se(!1),C++,_--,0===t.tt)return 0}else z=1,C++,_--;else{r=C+_-3,n=ee(C-1-S,A-3),_-=A-1,A-=2;do{++C>r||(p=(p<<g^255&u[C+2])&b,s=65535&d[p],h[C&l]=d[p],d[p]=C)}while(0!=--A);if(z=0,v=2,C++,n&&(se(!1),0===t.tt))return 0}}return 0!==z&&(n=ee(0,255&u[C-1]),z=0),se(4==e),0===t.tt?4==e?2:0:4==e?3:1})(i)}if(2!=R&&3!=R||(n=Le),0==R||2==R)return 0===t.tt&&(c=-1),0;if(1==R){if(1==i)X(2,3),Y(256,Ue.Ae),$(),9>1+H+10-F&&(X(2,3),Y(256,Ue.Ae),$()),H=7;else if(re(0,0,!1),3==i)for(o=0;y>o;o++)d[o]=0;if(t.Te(),0===t.tt)return c=-1,0}}return 4!=i?0:1}}function Je(){const e=this;e.nt=0,e.rt=0,e.We=0,e.qe=0,e.tt=0,e.Ge=0}function Qe(e){const t=new Je,n=(o=e&&e.chunkSize?e.chunkSize:65536)+5*(r.floor(o/16383)+1);var o;const c=new i(n);let f=e?e.level:-1;void 0===f&&(f=-1),t.He(f),t.$e=c,this.append=(e,r)=>{let o,f,a=0,l=0,u=0;const w=[];if(e.length){t.nt=0,t.et=e,t.We=e.length;do{if(t.rt=0,t.tt=n,o=t.Ye(0),0!=o)throw new s("deflating: "+t.Le);t.rt&&(t.rt==n?w.push(new i(c)):w.push(c.subarray(0,t.rt))),u+=t.rt,r&&t.nt>0&&t.nt!=a&&(r(t.nt),a=t.nt)}while(t.We>0||0===t.tt);return w.length>1?(f=new i(u),w.forEach((e=>{f.set(e,l),l+=e.length}))):f=w[0]?new i(w[0]):new i,f}},this.flush=()=>{let e,r,o=0,f=0;const a=[];do{if(t.rt=0,t.tt=n,e=t.Ye(4),1!=e&&0!=e)throw new s("deflating: "+t.Le);n-t.tt>0&&a.push(c.slice(0,t.rt)),f+=t.rt}while(t.We>0||0===t.tt);return t.Qe(),r=new i(f),a.forEach((e=>{r.set(e,o),o+=e.length})),r}}Je.prototype={He(e,t){const n=this;return n.Fe=new Ge,t||(t=15),n.Fe.He(n,e,t)},Ye(e){const t=this;return t.Fe?t.Fe.Ye(t,e):Re},Qe(){const e=this;if(!e.Fe)return Re;const t=e.Fe.Qe();return e.Fe=null,t},Xe(e,t){const n=this;return n.Fe?n.Fe.Xe(n,e,t):Re},Ze(e,t){const n=this;return n.Fe?n.Fe.Ze(n,e,t):Re},je(e,t,n){const r=this;let s=r.We;return s>n&&(s=n),0===s?0:(r.We-=s,e.set(r.et.subarray(r.nt,r.nt+s),t),r.nt+=s,r.qe+=s,s)},Te(){const e=this;let t=e.Fe.pending;t>e.tt&&(t=e.tt),0!==t&&(e.$e.set(e.Fe.Ue.subarray(e.Fe.Je,e.Fe.Je+t),e.rt),e.rt+=t,e.Fe.Je+=t,e.Ge+=t,e.tt-=t,e.Fe.pending-=t,0===e.Fe.pending&&(e.Fe.Je=0))}};const Xe=-2,Ye=-3,Ze=-5,$e=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],et=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],tt=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],nt=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],rt=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],st=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],it=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];function ot(){let e,t,n,r,s,i;function o(e,t,o,c,f,a,l,u,w,h,d){let p,y,m,b,g,k,v,S,z,C,x,_,A,I,D;C=0,g=o;do{n[e[t+C]]++,C++,g--}while(0!==g);if(n[0]==o)return l[0]=-1,u[0]=0,0;for(S=u[0],k=1;15>=k&&0===n[k];k++);for(v=k,k>S&&(S=k),g=15;0!==g&&0===n[g];g--);for(m=g,S>g&&(S=g),u[0]=S,I=1<<k;g>k;k++,I<<=1)if(0>(I-=n[k]))return Ye;if(0>(I-=n[g]))return Ye;for(n[g]+=I,i[1]=k=0,C=1,A=2;0!=--g;)i[A]=k+=n[C],A++,C++;g=0,C=0;do{0!==(k=e[t+C])&&(d[i[k]++]=g),C++}while(++g<o);for(o=i[m],i[0]=g=0,C=0,b=-1,_=-S,s[0]=0,x=0,D=0;m>=v;v++)for(p=n[v];0!=p--;){for(;v>_+S;){if(b++,_+=S,D=m-_,D=D>S?S:D,(y=1<<(k=v-_))>p+1&&(y-=p+1,A=v,D>k))for(;++k<D&&(y<<=1)>n[++A];)y-=n[A];if(D=1<<k,h[0]+D>1440)return Ye;s[b]=x=h[0],h[0]+=D,0!==b?(i[b]=g,r[0]=k,r[1]=S,k=g>>>_-S,r[2]=x-s[b-1]-k,w.set(r,3*(s[b-1]+k))):l[0]=x}for(r[1]=v-_,o>C?d[C]<c?(r[0]=256>d[C]?0:96,r[2]=d[C++]):(r[0]=a[d[C]-c]+16+64,r[2]=f[d[C++]-c]):r[0]=192,y=1<<v-_,k=g>>>_;D>k;k+=y)w.set(r,3*(x+k));for(k=1<<v-1;0!=(g&k);k>>>=1)g^=k;for(g^=k,z=(1<<_)-1;(g&z)!=i[b];)b--,_-=S,z=(1<<_)-1}return 0!==I&&1!=m?Ze:0}function c(o){let c;for(e||(e=[],t=[],n=new f(16),r=[],s=new f(15),i=new f(16)),t.length<o&&(t=[]),c=0;o>c;c++)t[c]=0;for(c=0;16>c;c++)n[c]=0;for(c=0;3>c;c++)r[c]=0;s.set(n.subarray(0,15),0),i.set(n.subarray(0,16),0)}this.st=(n,r,s,i,f)=>{let a;return c(19),e[0]=0,a=o(n,0,19,19,null,null,s,r,i,e,t),a==Ye?f.Le="oversubscribed dynamic bit lengths tree":a!=Ze&&0!==r[0]||(f.Le="incomplete dynamic bit lengths tree",a=Ye),a},this.it=(n,r,s,i,f,a,l,u,w)=>{let h;return c(288),e[0]=0,h=o(s,0,n,257,nt,rt,a,i,u,e,t),0!=h||0===i[0]?(h==Ye?w.Le="oversubscribed literal/length tree":-4!=h&&(w.Le="incomplete literal/length tree",h=Ye),h):(c(288),h=o(s,n,r,0,st,it,l,f,u,e,t),0!=h||0===f[0]&&n>257?(h==Ye?w.Le="oversubscribed distance tree":h==Ze?(w.Le="incomplete distance tree",h=Ye):-4!=h&&(w.Le="empty distance tree with lengths",h=Ye),h):0)}}function ct(){const e=this;let t,n,r,s,i=0,o=0,c=0,f=0,a=0,l=0,u=0,w=0,h=0,d=0;function p(e,t,n,r,s,i,o,c){let f,a,l,u,w,h,d,p,y,m,b,g,k,v,S,z;d=c.nt,p=c.We,w=o.ot,h=o.ct,y=o.write,m=y<o.read?o.read-y-1:o.end-y,b=$e[e],g=$e[t];do{for(;20>h;)p--,w|=(255&c.ft(d++))<<h,h+=8;if(f=w&b,a=n,l=r,z=3*(l+f),0!==(u=a[z]))for(;;){if(w>>=a[z+1],h-=a[z+1],0!=(16&u)){for(u&=15,k=a[z+2]+(w&$e[u]),w>>=u,h-=u;15>h;)p--,w|=(255&c.ft(d++))<<h,h+=8;for(f=w&g,a=s,l=i,z=3*(l+f),u=a[z];;){if(w>>=a[z+1],h-=a[z+1],0!=(16&u)){for(u&=15;u>h;)p--,w|=(255&c.ft(d++))<<h,h+=8;if(v=a[z+2]+(w&$e[u]),w>>=u,h-=u,m-=k,v>y){S=y-v;do{S+=o.end}while(0>S);if(u=o.end-S,k>u){if(k-=u,y-S>0&&u>y-S)do{o.lt[y++]=o.lt[S++]}while(0!=--u);else o.lt.set(o.lt.subarray(S,S+u),y),y+=u,S+=u,u=0;S=0}}else S=y-v,y-S>0&&2>y-S?(o.lt[y++]=o.lt[S++],o.lt[y++]=o.lt[S++],k-=2):(o.lt.set(o.lt.subarray(S,S+2),y),y+=2,S+=2,k-=2);if(y-S>0&&k>y-S)do{o.lt[y++]=o.lt[S++]}while(0!=--k);else o.lt.set(o.lt.subarray(S,S+k),y),y+=k,S+=k,k=0;break}if(0!=(64&u))return c.Le="invalid distance code",k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,Ye;f+=a[z+2],f+=w&$e[u],z=3*(l+f),u=a[z]}break}if(0!=(64&u))return 0!=(32&u)?(k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,1):(c.Le="invalid literal/length code",k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,Ye);if(f+=a[z+2],f+=w&$e[u],z=3*(l+f),0===(u=a[z])){w>>=a[z+1],h-=a[z+1],o.lt[y++]=a[z+2],m--;break}}else w>>=a[z+1],h-=a[z+1],o.lt[y++]=a[z+2],m--}while(m>=258&&p>=10);return k=c.We-p,k=k>h>>3?h>>3:k,p+=k,d-=k,h-=k<<3,o.ot=w,o.ct=h,c.We=p,c.qe+=d-c.nt,c.nt=d,o.write=y,0}e.init=(e,i,o,c,f,a)=>{t=0,u=e,w=i,r=o,h=c,s=f,d=a,n=null},e.ut=(e,y,m)=>{let b,g,k,v,S,z,C,x=0,_=0,A=0;for(A=y.nt,v=y.We,x=e.ot,_=e.ct,S=e.write,z=S<e.read?e.read-S-1:e.end-S;;)switch(t){case 0:if(z>=258&&v>=10&&(e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,m=p(u,w,r,h,s,d,e,y),A=y.nt,v=y.We,x=e.ot,_=e.ct,S=e.write,z=S<e.read?e.read-S-1:e.end-S,0!=m)){t=1==m?7:9;break}c=u,n=r,o=h,t=1;case 1:for(b=c;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}if(g=3*(o+(x&$e[b])),x>>>=n[g+1],_-=n[g+1],k=n[g],0===k){f=n[g+2],t=6;break}if(0!=(16&k)){a=15&k,i=n[g+2],t=2;break}if(0==(64&k)){c=k,o=g/3+n[g+2];break}if(0!=(32&k)){t=7;break}return t=9,y.Le="invalid literal/length code",m=Ye,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);case 2:for(b=a;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}i+=x&$e[b],x>>=b,_-=b,c=w,n=s,o=d,t=3;case 3:for(b=c;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}if(g=3*(o+(x&$e[b])),x>>=n[g+1],_-=n[g+1],k=n[g],0!=(16&k)){a=15&k,l=n[g+2],t=4;break}if(0==(64&k)){c=k,o=g/3+n[g+2];break}return t=9,y.Le="invalid distance code",m=Ye,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);case 4:for(b=a;b>_;){if(0===v)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,v--,x|=(255&y.ft(A++))<<_,_+=8}l+=x&$e[b],x>>=b,_-=b,t=5;case 5:for(C=S-l;0>C;)C+=e.end;for(;0!==i;){if(0===z&&(S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z&&(e.write=S,m=e.wt(y,m),S=e.write,z=S<e.read?e.read-S-1:e.end-S,S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z)))return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);e.lt[S++]=e.lt[C++],z--,C==e.end&&(C=0),i--}t=0;break;case 6:if(0===z&&(S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z&&(e.write=S,m=e.wt(y,m),S=e.write,z=S<e.read?e.read-S-1:e.end-S,S==e.end&&0!==e.read&&(S=0,z=S<e.read?e.read-S-1:e.end-S),0===z)))return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);m=0,e.lt[S++]=f,z--,t=0;break;case 7:if(_>7&&(_-=8,v++,A--),e.write=S,m=e.wt(y,m),S=e.write,z=S<e.read?e.read-S-1:e.end-S,e.read!=e.write)return e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);t=8;case 8:return m=1,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);case 9:return m=Ye,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m);default:return m=Xe,e.ot=x,e.ct=_,y.We=v,y.qe+=A-y.nt,y.nt=A,e.write=S,e.wt(y,m)}},e.ht=()=>{}}ot.dt=(e,t,n,r)=>(e[0]=9,t[0]=5,n[0]=et,r[0]=tt,0);const ft=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function at(e,t){const n=this;let r,s=0,o=0,c=0,a=0;const l=[0],u=[0],w=new ct;let h=0,d=new f(4320);const p=new ot;n.ct=0,n.ot=0,n.lt=new i(t),n.end=t,n.read=0,n.write=0,n.reset=(e,t)=>{t&&(t[0]=0),6==s&&w.ht(e),s=0,n.ct=0,n.ot=0,n.read=n.write=0},n.reset(e,null),n.wt=(e,t)=>{let r,s,i;return s=e.rt,i=n.read,r=(i>n.write?n.end:n.write)-i,r>e.tt&&(r=e.tt),0!==r&&t==Ze&&(t=0),e.tt-=r,e.Ge+=r,e.$e.set(n.lt.subarray(i,i+r),s),s+=r,i+=r,i==n.end&&(i=0,n.write==n.end&&(n.write=0),r=n.write-i,r>e.tt&&(r=e.tt),0!==r&&t==Ze&&(t=0),e.tt-=r,e.Ge+=r,e.$e.set(n.lt.subarray(i,i+r),s),s+=r,i+=r),e.rt=s,n.read=i,t},n.ut=(e,t)=>{let i,f,y,m,b,g,k,v;for(m=e.nt,b=e.We,f=n.ot,y=n.ct,g=n.write,k=g<n.read?n.read-g-1:n.end-g;;){let S,z,C,x,_,A,I,D;switch(s){case 0:for(;3>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}switch(i=7&f,h=1&i,i>>>1){case 0:f>>>=3,y-=3,i=7&y,f>>>=i,y-=i,s=1;break;case 1:S=[],z=[],C=[[]],x=[[]],ot.dt(S,z,C,x),w.init(S[0],z[0],C[0],0,x[0],0),f>>>=3,y-=3,s=6;break;case 2:f>>>=3,y-=3,s=3;break;case 3:return f>>>=3,y-=3,s=9,e.Le="invalid block type",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t)}break;case 1:for(;32>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if((~f>>>16&65535)!=(65535&f))return s=9,e.Le="invalid stored block lengths",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);o=65535&f,f=y=0,s=0!==o?2:0!==h?7:0;break;case 2:if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);if(0===k&&(g==n.end&&0!==n.read&&(g=0,k=g<n.read?n.read-g-1:n.end-g),0===k&&(n.write=g,t=n.wt(e,t),g=n.write,k=g<n.read?n.read-g-1:n.end-g,g==n.end&&0!==n.read&&(g=0,k=g<n.read?n.read-g-1:n.end-g),0===k)))return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);if(t=0,i=o,i>b&&(i=b),i>k&&(i=k),n.lt.set(e.je(m,i),g),m+=i,b-=i,g+=i,k-=i,0!=(o-=i))break;s=0!==h?7:0;break;case 3:for(;14>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if(c=i=16383&f,(31&i)>29||(i>>5&31)>29)return s=9,e.Le="too many length or distance symbols",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);if(i=258+(31&i)+(i>>5&31),!r||r.length<i)r=[];else for(v=0;i>v;v++)r[v]=0;f>>>=14,y-=14,a=0,s=4;case 4:for(;4+(c>>>10)>a;){for(;3>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}r[ft[a++]]=7&f,f>>>=3,y-=3}for(;19>a;)r[ft[a++]]=0;if(l[0]=7,i=p.st(r,l,u,d,e),0!=i)return(t=i)==Ye&&(r=null,s=9),n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);a=0,s=5;case 5:for(;i=c,258+(31&i)+(i>>5&31)>a;){let o,w;for(i=l[0];i>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if(i=d[3*(u[0]+(f&$e[i]))+1],w=d[3*(u[0]+(f&$e[i]))+2],16>w)f>>>=i,y-=i,r[a++]=w;else{for(v=18==w?7:w-14,o=18==w?11:3;i+v>y;){if(0===b)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);t=0,b--,f|=(255&e.ft(m++))<<y,y+=8}if(f>>>=i,y-=i,o+=f&$e[v],f>>>=v,y-=v,v=a,i=c,v+o>258+(31&i)+(i>>5&31)||16==w&&1>v)return r=null,s=9,e.Le="invalid bit length repeat",t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);w=16==w?r[v-1]:0;do{r[v++]=w}while(0!=--o);a=v}}if(u[0]=-1,_=[],A=[],I=[],D=[],_[0]=9,A[0]=6,i=c,i=p.it(257+(31&i),1+(i>>5&31),r,_,A,I,D,d,e),0!=i)return i==Ye&&(r=null,s=9),t=i,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);w.init(_[0],A[0],d,I[0],d,D[0]),s=6;case 6:if(n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,1!=(t=w.ut(n,e,t)))return n.wt(e,t);if(t=0,w.ht(e),m=e.nt,b=e.We,f=n.ot,y=n.ct,g=n.write,k=g<n.read?n.read-g-1:n.end-g,0===h){s=0;break}s=7;case 7:if(n.write=g,t=n.wt(e,t),g=n.write,k=g<n.read?n.read-g-1:n.end-g,n.read!=n.write)return n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);s=8;case 8:return t=1,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);case 9:return t=Ye,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t);default:return t=Xe,n.ot=f,n.ct=y,e.We=b,e.qe+=m-e.nt,e.nt=m,n.write=g,n.wt(e,t)}}},n.ht=e=>{n.reset(e,null),n.lt=null,d=null},n.yt=(e,t,r)=>{n.lt.set(e.subarray(t,t+r),0),n.read=n.write=r},n.bt=()=>1==s?1:0}const lt=13,ut=[0,0,255,255];function wt(){const e=this;function t(e){return e&&e.gt?(e.qe=e.Ge=0,e.Le=null,e.gt.mode=7,e.gt.kt.reset(e,null),0):Xe}e.mode=0,e.method=0,e.vt=[0],e.St=0,e.marker=0,e.zt=0,e.Ct=t=>(e.kt&&e.kt.ht(t),e.kt=null,0),e.xt=(n,r)=>(n.Le=null,e.kt=null,8>r||r>15?(e.Ct(n),Xe):(e.zt=r,n.gt.kt=new at(n,1<<r),t(n),0)),e._t=(e,t)=>{let n,r;if(!e||!e.gt||!e.et)return Xe;const s=e.gt;for(t=4==t?Ze:0,n=Ze;;)switch(s.mode){case 0:if(0===e.We)return n;if(n=t,e.We--,e.qe++,8!=(15&(s.method=e.ft(e.nt++)))){s.mode=lt,e.Le="unknown compression method",s.marker=5;break}if(8+(s.method>>4)>s.zt){s.mode=lt,e.Le="invalid win size",s.marker=5;break}s.mode=1;case 1:if(0===e.We)return n;if(n=t,e.We--,e.qe++,r=255&e.ft(e.nt++),((s.method<<8)+r)%31!=0){s.mode=lt,e.Le="incorrect header check",s.marker=5;break}if(0==(32&r)){s.mode=7;break}s.mode=2;case 2:if(0===e.We)return n;n=t,e.We--,e.qe++,s.St=(255&e.ft(e.nt++))<<24&4278190080,s.mode=3;case 3:if(0===e.We)return n;n=t,e.We--,e.qe++,s.St+=(255&e.ft(e.nt++))<<16&16711680,s.mode=4;case 4:if(0===e.We)return n;n=t,e.We--,e.qe++,s.St+=(255&e.ft(e.nt++))<<8&65280,s.mode=5;case 5:return 0===e.We?n:(n=t,e.We--,e.qe++,s.St+=255&e.ft(e.nt++),s.mode=6,2);case 6:return s.mode=lt,e.Le="need dictionary",s.marker=0,Xe;case 7:if(n=s.kt.ut(e,n),n==Ye){s.mode=lt,s.marker=0;break}if(0==n&&(n=t),1!=n)return n;n=t,s.kt.reset(e,s.vt),s.mode=12;case 12:return e.We=0,1;case lt:return Ye;default:return Xe}},e.At=(e,t,n)=>{let r=0,s=n;if(!e||!e.gt||6!=e.gt.mode)return Xe;const i=e.gt;return s<1<<i.zt||(s=(1<<i.zt)-1,r=n-s),i.kt.yt(t,r,s),i.mode=7,0},e.It=e=>{let n,r,s,i,o;if(!e||!e.gt)return Xe;const c=e.gt;if(c.mode!=lt&&(c.mode=lt,c.marker=0),0===(n=e.We))return Ze;for(r=e.nt,s=c.marker;0!==n&&4>s;)e.ft(r)==ut[s]?s++:s=0!==e.ft(r)?0:4-s,r++,n--;return e.qe+=r-e.nt,e.nt=r,e.We=n,c.marker=s,4!=s?Ye:(i=e.qe,o=e.Ge,t(e),e.qe=i,e.Ge=o,c.mode=7,0)},e.Dt=e=>e&&e.gt&&e.gt.kt?e.gt.kt.bt():Xe}function ht(){}function dt(e){const t=new ht,n=e&&e.chunkSize?r.floor(2*e.chunkSize):131072,o=new i(n);let c=!1;t.xt(),t.$e=o,this.append=(e,r)=>{const f=[];let a,l,u=0,w=0,h=0;if(0!==e.length){t.nt=0,t.et=e,t.We=e.length;do{if(t.rt=0,t.tt=n,0!==t.We||c||(t.nt=0,c=!0),a=t._t(0),c&&a===Ze){if(0!==t.We)throw new s("inflating: bad input")}else if(0!==a&&1!==a)throw new s("inflating: "+t.Le);if((c||1===a)&&t.We===e.length)throw new s("inflating: bad input");t.rt&&(t.rt===n?f.push(new i(o)):f.push(o.subarray(0,t.rt))),h+=t.rt,r&&t.nt>0&&t.nt!=u&&(r(t.nt),u=t.nt)}while(t.We>0||0===t.tt);return f.length>1?(l=new i(h),f.forEach((e=>{l.set(e,w),w+=e.length}))):l=f[0]?new i(f[0]):new i,l}},this.flush=()=>{t.Ct()}}ht.prototype={xt(e){const t=this;return t.gt=new wt,e||(e=15),t.gt.xt(t,e)},_t(e){const t=this;return t.gt?t.gt._t(t,e):Xe},Ct(){const e=this;if(!e.gt)return Xe;const t=e.gt.Ct(e);return e.gt=null,t},It(){const e=this;return e.gt?e.gt.It(e):Xe},At(e,t){const n=this;return n.gt?n.gt.At(n,e,t):Xe},ft(e){return this.et[e]},je(e,t){return this.et.subarray(e,e+t)}},self.initCodec=()=>{self.Deflate=Qe,self.Inflate=dt};
`],{type:"text/javascript"}));n({workerScripts:{inflate:[e],deflate:[e]}})}Bl(Oi);var K=tn.Platform.isDesktopApp?window.require("node:original-fs"):null,$e=tn.Platform.isDesktopApp?K.promises:null,nn=tn.Platform.isDesktopApp?window.require("node:os"):null,q=tn.Platform.isDesktopApp?window.require("node:path"):null,ni=tn.Platform.isDesktopApp?window.require("node:url"):null,Ul=tn.Platform.isDesktopApp?window.require("node:zlib"):null;function Ki(n,e=0,t=n.byteLength){return n.buffer.slice(n.byteOffset+e,n.byteOffset+e+t)}var Be=class{constructor(e){this.type="file";this.filepath=e;let t=this.name=q.basename(e);this.fullpath=t;let i=q.extname(t);this.extension=i.substring(1).toLowerCase(),this.basename=q.basename(t,i)}async readText(){return $e.readFile(this.filepath,"utf8")}async read(){let e=await $e.readFile(this.filepath);return Ki(e)}async readZip(e){let t=null;try{t=await $e.open(this.filepath,"r");let i=await t.stat();return await e(new Qt(new Do(t,i.size)))}finally{await(t==null?void 0:t.close())}}createReadStream(){return K.createReadStream(this.filepath)}toString(){return this.filepath}},en=class n{constructor(e){this.type="folder";this.filepath=e,this.name=q.basename(e)}async list(){let{filepath:e}=this,t=await $e.readdir(e,{withFileTypes:!0}),i=[];for(let r of t)r.isFile()?i.push(new Be(q.join(e,r.name))):r.isDirectory()&&i.push(new n(q.join(e,r.name)));return i}toString(){return this.filepath}},Vi=class{constructor(e){this.type="file";this.file=e;let t=this.name=e.name;this.fullpath=t;let{basename:i,extension:r}=oe(t);this.basename=i,this.extension=r}readText(){let{file:e}=this;return e.text?e.text():new Promise((t,i)=>{let r=new FileReader;r.addEventListener("load",()=>t(r.result)),r.addEventListener("error",i),r.readAsText(this.file)})}async read(){let{file:e}=this;return e.arrayBuffer?e.arrayBuffer():new Promise((t,i)=>{let r=new FileReader;r.addEventListener("load",()=>t(r.result)),r.addEventListener("error",i),r.readAsArrayBuffer(this.file)})}async readZip(e){return e(new Qt(new Rt(this.file)))}toString(){return this.file.toString()}};async function Io(n,e){let t=[];for(let i of n)try{i.type==="folder"?t.push(...await Io(await i.list(),e)):i.type==="file"&&(!e||e(i))&&t.push(i)}catch(r){console.log("Skipping path: ",i.name,r)}return t}function oe(n){let e=Math.max(n.lastIndexOf("/"),n.lastIndexOf("\\")),t=n,i="";e>=0&&(t=n.substring(e+1),i=n.substring(0,e));let[r,o]=Fo(t);return{parent:i,name:t,basename:r,extension:o}}function Fo(n){let e=n.lastIndexOf("."),t=n,i="";return e>0&&(t=n.substring(0,e),i=n.substring(e+1).toLowerCase()),[t,i]}var Do=class extends vn{constructor(e,t){super(e),this.fd=e,this.size=t}async readUint8Array(e,t){let i=Buffer.alloc(t),r=await this.fd.read(i,0,t,e);return new Uint8Array(Ki(i,0,r.bytesRead))}};var jl=require("obsidian"),_h=/[\/\?<>\\:\*\|"]/g,Th=/[\x00-\x1f\x80-\x9f]/g,Ah=/^\.+$/,kh=/^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i,Nh=/[\. ]+$/,Sh=/^\./,Rh=/[\[\]#|^]/g;function ze(n){return n.replace(_h,"").replace(Th,"").replace(Ah,"").replace(kh,"").replace(Nh,"").replace(Sh,"").replace(Rh,"")}function _n(n){let e=[];for(let t=0;t<n;t++)e.push((Math.random()*16|0).toString(16));return e.join("")}function dt(n){return new DOMParser().parseFromString(n,"text/html").documentElement}function Oh(n){return n.buffer.slice(n.byteOffset,n.byteOffset+n.byteLength)}function Lo(n){return Oh(new TextEncoder().encode(n))}function Xi(n){return Object.isEmpty(n)?"":`---
`+(0,jl.stringifyYaml)(n)+`---
`}function ii(n,e,t="..."){return n.length<e?n:n.substring(0,e)+t}var Ce=require("obsidian");var Hl=300,ue=class{constructor(e,t){this.files=[];this.outputLocation="";this.notAvailable=!1;this.outputFolder=null;this.app=e,this.vault=e.vault,this.modal=t,this.init()}registerAuthCallback(e){this.modal.plugin.registerAuthCallback(e)}addFileChooserSetting(e,t,i=!1){let r=new Ce.Setting(this.modal.contentEl).setName("Files to import").setDesc("Pick the files that you want to import.").addButton(a=>a.setButtonText(i?"Choose files":"Choose file").onClick(async()=>{if(Ce.Platform.isDesktopApp){let s=["openFile","dontAddToRecent"];i&&s.push("multiSelections");let l=window.electron.remote.dialog.showOpenDialogSync({title:"Pick files to import",properties:s,filters:[{name:e,extensions:t}]});l&&l.length>0&&(this.files=l.map(c=>new Be(c)),o())}else{let s=createEl("input");s.type="file",s.accept=t.map(l=>"."+l.toLowerCase()).join(","),s.addEventListener("change",()=>{if(!s.files)return;let l=Array.from(s.files);l.length>0&&(this.files=l.map(c=>new Vi(c)).filter(c=>t.contains(c.extension)),o())}),s.click()}}));i&&Ce.Platform.isDesktopApp&&r.addButton(a=>a.setButtonText("Choose folders").onClick(async()=>{if(Ce.Platform.isDesktopApp){let s=window.electron.remote.dialog.showOpenDialogSync({title:"Pick folders to import",properties:["openDirectory","multiSelections","dontAddToRecent"]});if(s&&s.length>0){r.setDesc("Reading folders...");let l=s.map(c=>new en(c));this.files=await Io(l,c=>t.contains(c.extension)),o()}}}));let o=()=>{let a=document.createDocumentFragment(),s=this.files.length,l=this.files.map(c=>c.name).join(", ");l.length>Hl&&(l=l.substring(0,Hl)+"..."),a.createEl("span",{text:`These ${s} files will be imported: `}),a.createEl("br"),a.createEl("span",{cls:"u-pop",text:l}),r.setDesc(a)}}addOutputLocationSetting(e){this.outputLocation=e,new Ce.Setting(this.modal.contentEl).setName("Output folder").setDesc("Choose a folder in the vault to put the imported files. Leave empty to output to vault root.").addText(t=>t.setValue(e).onChange(i=>{this.outputLocation=i,this.outputFolder=null}))}async getOutputFolder(){if(this.outputFolder)return this.outputFolder;let{vault:e}=this.app,t=this.outputLocation;t===""&&(t="/");let i=e.getAbstractFileByPath(t);return(i===null||!(i instanceof Ce.TFolder))&&(await e.createFolder(t),i=e.getAbstractFileByPath(t)),i instanceof Ce.TFolder?(this.outputFolder=i,i):null}async getAvailablePathForAttachment(e,t){let i=await this.getOutputFolder(),r=i?{parent:i}:null,{basename:o,extension:a}=oe(e),s=await this.vault.getAvailablePathForAttachments(o,a,r),l=oe(s),c=l.extension?"."+l.extension:"."+a,p=1,m=s;for(;t.includes(m)||this.vault.getAbstractFileByPath(m);)m=q.join(l.parent,`${l.name} ${p}${c}`),p++;return m}sanitizeFilePath(e){return e.replace(/[:|?<>*\\]/g,"")}async createFolders(e){let t=e.split("/").map(o=>o.replace(/^\.+/,"")).join("/"),i=(0,Ce.normalizePath)(t),r=this.vault.getAbstractFileByPathInsensitive(i);if(r&&r instanceof Ce.TFolder)return r;if(await this.vault.createFolder(i),r=this.vault.getAbstractFileByPathInsensitive(i),!(r instanceof Ce.TFolder))throw new Error(`Failed to create folder at "${e}"`);return r}async saveAsMarkdownFile(e,t,i){let r=ze(t);return await this.app.fileManager.createNewMarkdownFile(e,r,i)}};var Xp=Jr(jp());var Ca=require("obsidian");var Hp=n=>{let t=vr.execSync(`${n} --version`).toString().toString().match(/(\d+)\.(\d+).(\d+)/);return(t==null?void 0:t[1])>3||(t==null?void 0:t[2])>32},qp=n=>{let e=[];if(!n)return e;let t=[],i=0,r=-1,o={};for(;i<n.length;){let a="";if(n[i]==="'")for(i++;i<n.length;)if(n[i]!=="'"){let s=n.indexOf("'",i);a+=n.substring(i,s),i=s}else if(n[i+1]==="'")a+=n[i],i+=2;else{i++;break}else if(n[i]==="N")a=null,i+=4;else{let s=Math.min(...[n.indexOf(",",i),n.indexOf(`
`,i)].filter(l=>l>0),n.length-1);a=parseFloat(n.substring(i,s)),i=s}r==-1?t.push(a):(o[t[r]]=a,r++),(n[i]==`
`||t.length<r)&&(r!==-1&&e.push(o),r=0,o={}),i++}return e};function Wp(n){for(var e=n[0],t=1,i=arguments.length;t<i;t++)e+=arguments[t]+n[t];return e}var{isArray:Hg}=Array,Er=class extends String{},Yp=(n,...e)=>{let t=[n[0]],i=[t];for(let r=0;r<e.length;r++)e[r]instanceof Er?t[t.length-1]+=e[r]+n[r+1]:(Hg(e[r])?(t.push(...e[r].slice(1).map(o=>",")),i.push(...e[r].length?e[r]:[""])):i.push(e[r]),t.push(n[r+1]));return i},zp=n=>new Er(n);var hi=(n,e)=>{let t="SQLITE_ERROR",i=new Error(t+": "+e);return i.code=t,n(i),""},Zp=(...n)=>zp(Wp(...n)),{from:qg}=Array,Wg=/'/g,Yg=n=>n.toString(16).padStart(2,"0"),zg=n=>`x'${qg(n,Yg).join("")}'`,Zg=n=>{switch(typeof n){case"string":return"'"+n.replace(Wg,"''")+"'";case"number":if(!isFinite(n))return;case"boolean":return+n;case"object":case"undefined":switch(!0){case!n:return"NULL";case n instanceof Date:return"'"+n.toISOString()+"'";case n instanceof Buffer:case n instanceof ArrayBuffer:n=new Uint8Array(n);case n instanceof Uint8Array:case n instanceof Uint8ClampedArray:return zg(n)}}},Sa=(n,e)=>{let[t,...i]=Yp(...e),r=[t[0]];for(let a=0;a<i.length;a++){let s=Zg(i[a]);if(s===void 0)return hi(n,"incompatible "+typeof s+"value");r.push(s,t[a+1])}let o=r.join("").trim();return o.length?o:hi(n,"empty query")};var Ra=Ca.Platform.isDesktopApp?window.require("node:crypto"):null,vr=Ca.Platform.isDesktopApp?window.require("node:child_process"):null,Da=Ra==null?void 0:Ra.randomUUID(),Gg=`[{"_":"${Da}"}]
`,Vg=`'_'
'${Da}'
`,{isArray:Kg}=Array,{parse:Vp}=JSON,{defineProperty:Xg}=Object,Jg=()=>{},Qg=(n,e,t,i,r,o)=>{let a=[],{stdout:s,stderr:l}=vr.spawn(i,r,o).on("close",p=>{if(c||p!==0){p!==0&&hi(e,"busy DB or query too slow");return}let m=a.join("").trim();if(t==="query")n(m);else{let g=Vp(m||"[]");n(t==="get"&&Kg(g)?g.shift():g)}});s.on("data",p=>{a.push(p)});let c=!1;l.on("data",p=>{c=!0,hi(e,"".trim.call(p))})},e0=(n,e,t)=>{let i=Hp(n),r=i?Gg:Vg,{stdin:o,stdout:a,stderr:s}=vr.spawn(n,e);i?o.write(`.mode json
`):o.write(`.mode quote
.headers on
`),t&&o.write(`.timeout ${t}
`);let l=Promise.resolve();return(c,p,m,g,b)=>{m==="close"?(o.write(`.quit
`),l=null):l&&(l=l.then(()=>new Promise(E=>{let f="",d=_=>{f+=_;let A=!1;for(;f.endsWith(r);)A=!0,f=f.slice(0,-r.length);if(A){for(v();f.startsWith(r);)f=f.slice(r.length);if(m==="query")c(f);else{let O=i?Vp(f||"[]"):qp(f);c(m==="get"?O.shift():O)}}},h=_=>{v(),p(new Error(_))},v=()=>{E(),a.removeListener("data",d),s.removeListener("data",h)};a.on("data",d),s.once("data",h),o.write(`${b[b.length-1]};
`),o.write(`SELECT '${Da}' as _;
`)})))}},Oa=(n,e,t,i,r)=>(...o)=>new Promise((a,s)=>{let l=Sa(s,o);l.length&&(n==="get"&&/^SELECT\s+/i.test(l)&&!/\s+LIMIT\s+\d+$/i.test(l)&&(l+=" LIMIT 1"),t(a,s,n,e,i.concat(l),r))}),Gp="";function Ia(n,e={}){n===":memory:"&&(n=Gp||(Gp=q.join(nn.tmpdir(),randomUUID())));let t=e.timeout||0,i=e.bin||"sqlite3",r=[n,"-bail"],o={timeout:t};e.readonly&&r.push("-readonly"),t&&r.push("-cmd",".timeout "+t);let a=r.concat("-json"),s=e.exec||(e.persistent?e0(i,r,t):Qg);return{transaction(){let l=[];return Xg((...c)=>{l.push(c)},"commit",{value(){return new Promise((c,p)=>{let m=["BEGIN TRANSACTION"];for(let g of l){let b=Sa(p,g);if(!b.length)return;m.push(b)}m.push("COMMIT"),s(c,p,"query",i,r.concat(m.join(";")),o)})}})},query:Oa("query",i,s,r,o),get:Oa("get",i,s,a,o),all:Oa("all",i,s,a,o),close:e.persistent?()=>s(null,null,"close"):Jg,raw:Zp}}var Fa="Library/Group Containers/group.com.apple.notes",Kp="NoteStore.sqlite",t0=*********,_r=class extends ue{constructor(){super(...arguments);this.owners={};this.resolvedAccounts={};this.resolvedFiles={};this.resolvedFolders={};this.multiAccount=!1;this.noteCount=0;this.parsedNotes=0;this.omitFirstLine=!0;this.importTrashed=!1;this.includeHandwriting=!1;this.trashFolders=[]}init(){if(!lt.Platform.isMacOS||!lt.Platform.isDesktop){this.modal.contentEl.createEl("p",{text:"Due to platform limitations, Apple Notes cannot be exported from this device. Open your vault on a Mac to export from Apple Notes."}),this.notAvailable=!0;return}this.addOutputLocationSetting("Apple Notes"),new lt.Setting(this.modal.contentEl).setName("Import recently deleted notes").setDesc('Import notes in the "Recently Deleted" folder. Unlike in Apple Notes, they will not be automatically removed after a set amount of time.').addToggle(t=>t.setValue(!1).onChange(async i=>this.importTrashed=i)),new lt.Setting(this.modal.contentEl).setName("Omit first line").setDesc("Don't include the first line in the text, since Apple Notes uses it as the title. It will still be used as the note name.").addToggle(t=>t.setValue(!0).onChange(async i=>this.omitFirstLine=i)),new lt.Setting(this.modal.contentEl).setName("Include handwriting text").setDesc("When Apple Notes has detected handwriting in drawings, include it as text before the drawing.").addToggle(t=>t.setValue(!1).onChange(async i=>this.includeHandwriting=i))}async getNotesDatabase(){let t=q.join(nn.homedir(),Fa),i=window.electron.remote.dialog.showOpenDialogSync({defaultPath:t,properties:["openDirectory"],message:'Select the "group.com.apple.notes" folder to allow Obsidian to read Apple Notes data.'});if(!(i!=null&&i.includes(t)))return new lt.Notice("Data import failed. Ensure you have selected the correct Apple Notes data folder."),null;let r=q.join(t,Kp),o=q.join(nn.tmpdir(),Kp);return await $e.copyFile(r,o),await $e.copyFile(r+"-shm",o+"-shm"),await $e.copyFile(r+"-wal",o+"-wal"),new Ia(o,{readonly:!0,persistent:!0})}async import(t){if(this.ctx=t,this.protobufRoot=Xp.Root.fromJSON(ds),this.rootFolder=await this.getOutputFolder(),!this.rootFolder){new lt.Notice("Please select a location to export to.");return}if(this.database=await this.getNotesDatabase(),!this.database)return;this.keys=Object.fromEntries((await this.database.all`SELECT z_ent, z_name FROM z_primarykey`).map(a=>[a.Z_NAME,a.Z_ENT]));let i=await this.database.all`
			SELECT z_pk FROM ziccloudsyncingobject WHERE z_ent = ${this.keys.ICAccount}
		`,r=await this.database.all`
			SELECT z_pk, ztitle2 FROM ziccloudsyncingobject WHERE z_ent = ${this.keys.ICFolder}
		`;for(let a of i)await this.resolveAccount(a.Z_PK);for(let a of r)try{await this.resolveFolder(a.Z_PK)}catch(s){this.ctx.reportFailed(a.ZTITLE2,s==null?void 0:s.message),console.error(s)}let o=await this.database.all`
			SELECT
				z_pk, zfolder, ztitle1 FROM ziccloudsyncingobject
			WHERE
				z_ent = ${this.keys.ICNote}
				AND ztitle1 IS NOT NULL
				AND zfolder NOT IN (${this.trashFolders})
		`;this.noteCount=o.length;for(let a of o)try{await this.resolveNote(a.Z_PK)}catch(s){this.ctx.reportFailed(a.ZTITLE1,s==null?void 0:s.message),console.error(s)}this.database.close()}async resolveAccount(t){!this.multiAccount&&Object.keys(this.resolvedAccounts).length&&(this.multiAccount=!0);let i=await this.database.get`
			SELECT zname, zidentifier FROM ziccloudsyncingobject
			WHERE z_ent = ${this.keys.ICAccount} AND z_pk = ${t}
		`;this.resolvedAccounts[t]={name:i.ZNAME,uuid:i.ZIDENTIFIER,path:q.join(nn.homedir(),Fa,"Accounts",i.ZIDENTIFIER)}}async resolveFolder(t){var a;if(t in this.resolvedFiles)return this.resolvedFolders[t];let i=await this.database.get`
			SELECT ztitle2, zparent, zidentifier, zfoldertype, zowner
			FROM ziccloudsyncingobject
			WHERE z_ent = ${this.keys.ICFolder} AND z_pk = ${t}
		`,r;if(i.ZFOLDERTYPE==3)return null;if(!this.importTrashed&&i.ZFOLDERTYPE==1)return this.trashFolders.push(t),null;if(i.ZPARENT!==null)r=((a=await this.resolveFolder(i.ZPARENT))==null?void 0:a.path)+"/";else if(this.multiAccount){let s=this.resolvedAccounts[i.ZOWNER].name;r=`${this.rootFolder.path}/${s}/`}else r=`${this.rootFolder.path}/`;i.ZIDENTIFIER.startsWith("DefaultFolder")||(r+=ze(i.ZTITLE2));let o=await this.createFolders(r);return this.resolvedFolders[t]=o,this.owners[t]=i.ZOWNER,o}async resolveNote(t){if(t in this.resolvedFiles)return this.resolvedFiles[t];let i=await this.database.get`
			SELECT
				nd.z_pk, hex(nd.zdata) as zhexdata, zcso.ztitle1, zfolder,
				zcreationdate1, zcreationdate2, zcreationdate3, zmodificationdate1, zispasswordprotected
			FROM
				zicnotedata AS nd,
				(SELECT
					*, NULL AS zcreationdate3, NULL AS zcreationdate2,
					NULL AS zispasswordprotected FROM ziccloudsyncingobject
				) AS zcso
			WHERE
				zcso.z_pk = nd.znote
				AND zcso.z_pk = ${t}
		`;if(i.ZISPASSWORDPROTECTED)return this.ctx.reportSkipped(i.ZTITLE1,"note is password protected"),null;let r=this.resolvedFolders[i.ZFOLDER]||this.rootFolder,o=`${i.ZTITLE1}.md`,a=await this.saveAsMarkdownFile(r,o,"");this.ctx.status(`Importing note ${o}`),this.resolvedFiles[t]=a,this.owners[t]=this.owners[i.ZFOLDER];let s=this.decodeData(i.zhexdata,Zt);return this.vault.modify(a,await s.format(),{ctime:this.decodeTime(i.ZCREATIONDATE3||i.ZCREATIONDATE2||i.ZCREATIONDATE1),mtime:this.decodeTime(i.ZMODIFICATIONDATE1)}),this.parsedNotes++,this.ctx.reportProgress(this.parsedNotes,this.noteCount),a}async resolveAttachment(t,i){if(t in this.resolvedFiles)return this.resolvedFiles[t];let r,o,a,s,l;switch(i){case"com.apple.paper.doc.scan":s=await this.database.get`
					SELECT
						zidentifier, zfallbackpdfgeneration, zcreationdate, zmodificationdate, znote
					FROM
						(SELECT *, NULL AS zfallbackpdfgeneration FROM ziccloudsyncingobject)
					WHERE
						z_ent = ${this.keys.ICAttachment}
						AND z_pk = ${t}
				`,r=q.join("FallbackPDFs",s.ZIDENTIFIER,s.ZFALLBACKPDFGENERATION||"","FallbackPDF.pdf"),o="Scan",a="pdf";break;case"com.apple.notes.gallery":s=await this.database.get`
					SELECT
						zidentifier, zsizeheight, zsizewidth, zcreationdate, zmodificationdate, znote
					FROM ziccloudsyncingobject
					WHERE
						z_ent = ${this.keys.ICAttachment}
						AND z_pk = ${t}
				`,r=q.join("Previews",`${s.ZIDENTIFIER}-1-${s.ZSIZEWIDTH}x${s.ZSIZEHEIGHT}-0.jpeg`),o="Scan Page",a="jpg";break;case"com.apple.paper":s=await this.database.get`
					SELECT
						zidentifier, zfallbackimagegeneration, zcreationdate, zmodificationdate,
						znote, zhandwritingsummary
					FROM
						(SELECT *, NULL AS zfallbackimagegeneration FROM ziccloudsyncingobject)
					WHERE
						z_ent = ${this.keys.ICAttachment}
						AND z_pk = ${t}
				`,s.ZFALLBACKIMAGEGENERATION?r=q.join("FallbackImages",s.ZIDENTIFIER,s.ZFALLBACKIMAGEGENERATION,"FallbackImage.png"):r=q.join("FallbackImages",`${s.ZIDENTIFIER}.jpg`),o="Drawing",a="png";break;default:s=await this.database.get`
					SELECT
						a.zidentifier, a.zfilename,
						a.zgeneration1, b.zcreationdate, b.zmodificationdate, b.znote
					FROM
						(SELECT *, NULL AS zgeneration1 FROM ziccloudsyncingobject) AS a,
						ziccloudsyncingobject AS b
					WHERE
						a.z_ent = ${this.keys.ICMedia}
						AND a.z_pk = ${t}
						AND a.z_pk = b.zmedia
				`,r=q.join("Media",s.ZIDENTIFIER,s.ZGENERATION1||"",s.ZFILENAME),[o,a]=Fo(s.ZFILENAME);break}try{let c=await this.getAttachmentSource(this.resolvedAccounts[this.owners[s.ZNOTE]],r),p=await this.getAvailablePathForAttachment(`${o}.${a}`,[]);l=await this.vault.createBinary(p,c,{ctime:this.decodeTime(s.ZCREATIONDATE),mtime:this.decodeTime(s.ZMODIFICATIONDATE)})}catch(c){return this.ctx.reportFailed(r),console.error(c),null}return this.resolvedFiles[t]=l,this.ctx.reportAttachmentSuccess(this.resolvedFiles[t].path),l}decodeData(t,i){let r=Ul.gunzipSync(Buffer.from(t,"hex")),o=this.protobufRoot.lookupType(i.protobufType).decode(r);return new i(this,o)}decodeTime(t){return!t||t<1?new Date().getTime():Math.floor((t+t0)*1e3)}async getAttachmentSource(t,i){try{return await $e.readFile(q.join(t.path,i))}catch(r){return await $e.readFile(q.join(nn.homedir(),Fa,i))}}};var On=require("obsidian");var gi=class{constructor(e,t){this.type="file";this.entry=t,this.fullpath=e.fullpath+"/"+t.filename;let{parent:i,name:r,basename:o,extension:a}=oe(t.filename);this.parent=i,this.name=r,this.basename=o,this.extension=a}async readText(){return this.entry.getData(new qi)}async read(){return(await this.entry.getData(new Jn)).arrayBuffer()}get filepath(){return this.entry.filename}get size(){return this.entry.uncompressedSize}get ctime(){return this.entry.creationDate}get mtime(){return this.entry.lastModDate}async readZip(e){return e(new Qt(new Rt(new Blob([await this.read()]))))}};async function yt(n,e){await n.readZip(async t=>{let r=(await t.getEntries()).filter(o=>!o.directory&&!!o.getData).map(o=>new gi(n,o));return e(t,r)})}var Tr=class extends ue{constructor(){super(...arguments);this.attachmentMap={}}init(){this.addFileChooserSetting("Bear2bk",["bear2bk"]),this.addOutputLocationSetting("Bear")}async import(t){let{files:i}=this;if(i.length===0){new On.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new On.Notice("Please select a location to export to.");return}let o=r,a=new RegExp("\\[[^\\]]*\\]\\((assets/[^\\)]+)\\)","gm"),s=await this.createFolders(`${r.path}/archive`),l=await this.createFolders(`${r.path}/trash`);for(let c of i){if(t.isCancelled())return;t.status("Processing "+c.name),await yt(c,async(p,m)=>{let g=await this.collectMetadata(t,m);for(let b of m){if(t.isCancelled())return;let{fullpath:E,filepath:f,parent:d,name:h,extension:v}=b;if(!(h==="info.json"||h==="tags.json")){t.status("Processing "+h);try{if(v==="md"||v==="markdown"){let _=oe(d).basename;t.status("Importing note "+_);let A=await b.readText();A=this.removeMarkdownHeader(_,A);let O=[...A.matchAll(a)];if(O.length>0)for(let C of O){let[N,P]=C,L=q.join(d,decodeURI(P)),j=await this.getAttachmentStoragePath(L);j=encodeURI(j);let Y=N.replace(P,j);A=A.replace(N,Y)}let T=(0,On.normalizePath)(_),k=g[d],w=o;k!=null&&k.archived?w=s:k!=null&&k.trashed&&(w=l);let D=await this.saveAsMarkdownFile(w,T,A);k!=null&&k.ctime&&(k!=null&&k.mtime)&&await this.modifFileTimestamps(k,D),t.reportNoteSuccess(_)}else if(f.match(/\/assets\//g)){t.status("Importing asset "+b.name);let _=await this.getAttachmentStoragePath(b.filepath),A=await b.read();await this.vault.createBinary(_,A),t.reportAttachmentSuccess(b.fullpath)}else t.reportSkipped(E,"unknown type of file")}catch(_){t.reportFailed(E,_)}}}})}}async modifFileTimestamps(t,i){let r={ctime:t.ctime,mtime:t.mtime};await this.vault.append(i,"",r)}async collectMetadata(t,i){let r={};for(let o of i){if(t.isCancelled())return r;if(o.name!=="info.json")continue;let a=await o.readText(),l=JSON.parse(a)["net.shinyfrog.bear"],c=Date.parse(l.creationDate),p=Date.parse(l.modificationDate);r[o.parent]={ctime:isNaN(c)?void 0:c,mtime:isNaN(p)?void 0:p,archived:l.archived===1,trashed:l.trashed===1}}return r}async getAttachmentStoragePath(t){let i=(0,On.normalizePath)(t);if(this.attachmentMap[i])return this.attachmentMap[i];let r=Object.values(this.attachmentMap),o=await this.getAvailablePathForAttachment(i,r);return o=o.replace(/:/g,""),this.attachmentMap[i]=o,o}removeMarkdownHeader(t,i){if(!i.startsWith("# "))return i;let r=i.indexOf(`
`),o=r>0?i.substring(2,r):i.substring(2);return o=o.trim(),o!==t.trim()&&o!==""?i:r>0?i.substring(r+1):""}};var _i=require("obsidian");var nd=require("obsidian");var Jp=require("obsidian");var Qp=n=>({...n,created:yi(n.created),statusupdated:yi(n.statusupdated),updated:yi(n.updated),duedate:yi(n.duedate),taskflag:n.taskflag==="true",reminderdate:n.reminder?yi(n.reminder.reminderdate):void 0,sortweight:n.sortweight}),yi=n=>n?(0,Jp.moment)(n,"YYYYMMDDThhmmssZ").toDate():void 0;var Lu=Jr(mu());var de=n=>{let e={get(t,i){return t[i]}};return new Proxy(n.attributes,e)};var hu="\n```\n",$a=n=>{let e=de(n);return e.style&&e.style.value.indexOf("-en-codeblock:true")>=0},f0=n=>{let e=de(n),t="padding-left:",i=0;return e.style&&e.style.value.indexOf(t)>=0&&(i=Math.floor(e.style.value.split(t)[1].split("px")[0]/20)),i},Ba=n=>n.replace(/\\(.)/g,"$1"),gu=(n,e)=>{let t=f0(e);return n=`${"	".repeat(t)}${n}`,$a(e)?(n=Ba(n),`${hu}${n}${hu}`):e.parentElement&&$a(e.parentElement)&&e.parentElement.firstElementChild===e?`${n}`:e.parentElement&&$a(e.parentElement)?`
${n}`:e.isBlock?`
${n}
`:n};var Ar="\n```\n",d0="-en-codeblock:true",m0=/\b(Courier|Mono|Consolas|Console|Inconsolata|Pitch|Monaco|monospace)\b/,yu=n=>{var o,a;if(n.nodeType!==1)return null;let e=n.childNodes,t=e.length;if(t>1)return"mixed";if(t===1){let s=yu(e[0]);if(s)return s}let i=de(n);if(n.tagName==="FONT")return(o=i.face)==null?void 0:o.value;let r=(a=i.style)==null?void 0:a.value;if(r){let s=r.match(/font-family:([^;]+)/);if(s)return s[1]}return null},bi=n=>{var r;let t=(r=de(n).style)==null?void 0:r.value;if(t&&t.includes(d0))return!0;let i=yu(n);return!!i&&m0.test(i)},bu=(n,e)=>{if(bi(e)){let t=e.previousSibling,i=t&&t.tagName===e.tagName&&bi(t),r=e.nextSibling,o=r&&r.tagName===e.tagName&&bi(r);return i||o?(n=i?`
${n}`:`${Ar}${n}`,n=o?`${n}
`:`${n}${Ar}`,n):(n=Ba(n),n.trim()?`${Ar}${n}${Ar}`:n)}return e.parentElement&&bi(e.parentElement)&&e.parentElement.firstElementChild===e?n:e.parentElement&&bi(e.parentElement)?`
${n}`:e.isBlock?`
${n}
`:n};var Ye=n=>e=>e.nodeName===n||e.nodeName.toLowerCase()===n;var xu={filter:Ye("IMG"),replacement:(n,e)=>{let t=de(e);if(!t.src)return"";let i=t.src.value,r=e.width||"",o=e.height||"",a=i;B.sanitizeResourceNameSpaces?a=a.replace(/ /g,B.replacementChar):B.urlEncodeFileNamesAndLinks&&(a=encodeURI(a));let s=r||o?` =${r}x${o}`:"";if(B.keepImageSize)return s=r||o?`|${r||0}x${o||0}`:"",a.startsWith("./")?`![[${a}${s}]]`:`![${s}](${a})`;if(!i.match(/^[a-z]+:/))return`![[${a}]]`;let l=t.src.value.split("/");return`![${l[l.length-1]}](${a})`}};var ke=class n{constructor(){this.noteIdNameMap={},this.noteIdNameTOCMap={}}static getInstance(){return n.instance||(n.instance=new n),n.instance}addItemToMap(e){this.noteIdNameMap[e.url]={...this.noteIdNameMap[e.url],title:e.title,noteName:this.currentNoteName,notebookName:this.currentNotebookName,uniqueEnd:e.uniqueEnd}}addItemToTOCMap(e){this.noteIdNameTOCMap[e.url]={...this.noteIdNameMap[e.url],title:e.title,noteName:this.currentNoteName,notebookName:this.currentNotebookName,uniqueEnd:e.uniqueEnd}}getNoteIdNameMap(){return this.noteIdNameMap}getNoteIdNameTOCMap(){return this.noteIdNameTOCMap}getAllNoteIdNameMap(){return{...this.noteIdNameMap,...this.noteIdNameTOCMap}}getNoteIdNameMapByNoteTitle(e){return Object.values(this.getAllNoteIdNameMap()).filter(t=>t.title===e)}setCurrentNotebookName(e){this.currentNotebookName=e}getCurrentNotebookName(){return this.currentNotebookName}setCurrentNotebookFullpath(e){this.currentNotebookFullpath=e}setCurrentNoteName(e){this.currentNoteName=e}getCurrentNoteName(){return this.currentNoteName}getCurrentNotePath(){return this.currentNotePath}setCurrentNotePath(e){this.currentNotePath=e}getCurrentNotebookFullpath(){return this.currentNotebookFullpath}};var wu=require("obsidian");var Cn=n=>n.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&");var h0={"application/andrew-inset":"ez","application/applixware":"aw","application/atom+xml":"atom","application/atomcat+xml":"atomcat","application/atomdeleted+xml":"atomdeleted","application/atomsvc+xml":"atomsvc","application/atsc-dwd+xml":"dwd","application/atsc-held+xml":"held","application/atsc-rsat+xml":"rsat","application/bdoc":"bdoc","application/calendar+xml":"xcs","application/ccxml+xml":"ccxml","application/cdfx+xml":"cdfx","application/cdmi-capability":"cdmia","application/cdmi-container":"cdmic","application/cdmi-domain":"cdmid","application/cdmi-object":"cdmio","application/cdmi-queue":"cdmiq","application/cpl+xml":"cpl","application/cu-seeme":"cu","application/dash+xml":"mpd","application/dash-patch+xml":"mpp","application/davmount+xml":"davmount","application/docbook+xml":"dbk","application/dssc+der":"dssc","application/dssc+xml":"xdssc","application/ecmascript":"es","application/emma+xml":"emma","application/emotionml+xml":"emotionml","application/epub+zip":"epub","application/exi":"exi","application/express":"exp","application/fdt+xml":"fdt","application/font-tdpfr":"pfr","application/geo+json":"geojson","application/gml+xml":"gml","application/gpx+xml":"gpx","application/gxf":"gxf","application/gzip":"gz","application/hjson":"hjson","application/hyperstudio":"stk","application/inkml+xml":"ink","application/ipfix":"ipfix","application/its+xml":"its","application/java-archive":"jar","application/java-serialized-object":"ser","application/java-vm":"class","application/javascript":"js","application/json":"json","application/json5":"json5","application/jsonml+json":"jsonml","application/ld+json":"jsonld","application/lgr+xml":"lgr","application/lost+xml":"lostxml","application/mac-binhex40":"hqx","application/mac-compactpro":"cpt","application/mads+xml":"mads","application/manifest+json":"webmanifest","application/marc":"mrc","application/marcxml+xml":"mrcx","application/mathematica":"ma","application/mathml+xml":"mathml","application/mbox":"mbox","application/media-policy-dataset+xml":"mpf","application/mediaservercontrol+xml":"mscml","application/metalink+xml":"metalink","application/metalink4+xml":"meta4","application/mets+xml":"mets","application/mmt-aei+xml":"maei","application/mmt-usd+xml":"musd","application/mods+xml":"mods","application/mp21":"m21","application/mp4":"mp4s","application/msword":"doc","application/mxf":"mxf","application/n-quads":"nq","application/n-triples":"nt","application/node":"cjs","application/octet-stream":"bin","application/oda":"oda","application/oebps-package+xml":"opf","application/ogg":"ogx","application/omdoc+xml":"omdoc","application/onenote":"onetoc","application/oxps":"oxps","application/p2p-overlay+xml":"relo","application/patch-ops-error+xml":"xer","application/pdf":"pdf","application/pgp-encrypted":"pgp","application/pgp-keys":"asc","application/pgp-signature":"asc","application/pics-rules":"prf","application/pkcs10":"p10","application/pkcs7-mime":"p7m","application/pkcs7-signature":"p7s","application/pkcs8":"p8","application/pkix-attr-cert":"ac","application/pkix-cert":"cer","application/pkix-crl":"crl","application/pkix-pkipath":"pkipath","application/pkixcmp":"pki","application/pls+xml":"pls","application/postscript":"ai","application/provenance+xml":"provx","application/prs.cww":"cww","application/pskc+xml":"pskcxml","application/raml+yaml":"raml","application/rdf+xml":"rdf","application/reginfo+xml":"rif","application/relax-ng-compact-syntax":"rnc","application/resource-lists+xml":"rl","application/resource-lists-diff+xml":"rld","application/rls-services+xml":"rs","application/route-apd+xml":"rapd","application/route-s-tsid+xml":"sls","application/route-usd+xml":"rusd","application/rpki-ghostbusters":"gbr","application/rpki-manifest":"mft","application/rpki-roa":"roa","application/rsd+xml":"rsd","application/rss+xml":"rss","application/rtf":"rtf","application/sbml+xml":"sbml","application/scvp-cv-request":"scq","application/scvp-cv-response":"scs","application/scvp-vp-request":"spq","application/scvp-vp-response":"spp","application/sdp":"sdp","application/senml+xml":"senmlx","application/sensml+xml":"sensmlx","application/set-payment-initiation":"setpay","application/set-registration-initiation":"setreg","application/shf+xml":"shf","application/sieve":"siv","application/smil+xml":"smi","application/sparql-query":"rq","application/sparql-results+xml":"srx","application/srgs":"gram","application/srgs+xml":"grxml","application/sru+xml":"sru","application/ssdl+xml":"ssdl","application/ssml+xml":"ssml","application/swid+xml":"swidtag","application/tei+xml":"tei","application/thraud+xml":"tfi","application/timestamped-data":"tsd","application/toml":"toml","application/trig":"trig","application/ttml+xml":"ttml","application/ubjson":"ubj","application/urc-ressheet+xml":"rsheet","application/urc-targetdesc+xml":"td","application/vnd.1000minds.decision-model+xml":"1km","application/vnd.3gpp.pic-bw-large":"plb","application/vnd.3gpp.pic-bw-small":"psb","application/vnd.3gpp.pic-bw-var":"pvb","application/vnd.3gpp2.tcap":"tcap","application/vnd.3m.post-it-notes":"pwn","application/vnd.accpac.simply.aso":"aso","application/vnd.accpac.simply.imp":"imp","application/vnd.acucobol":"acu","application/vnd.acucorp":"atc","application/vnd.adobe.air-application-installer-package+zip":"air","application/vnd.adobe.formscentral.fcdt":"fcdt","application/vnd.adobe.fxp":"fxp","application/vnd.adobe.xdp+xml":"xdp","application/vnd.adobe.xfdf":"xfdf","application/vnd.age":"age","application/vnd.ahead.space":"ahead","application/vnd.airzip.filesecure.azf":"azf","application/vnd.airzip.filesecure.azs":"azs","application/vnd.amazon.ebook":"azw","application/vnd.americandynamics.acc":"acc","application/vnd.amiga.ami":"ami","application/vnd.android.package-archive":"apk","application/vnd.anser-web-certificate-issue-initiation":"cii","application/vnd.anser-web-funds-transfer-initiation":"fti","application/vnd.antix.game-component":"atx","application/vnd.apple.installer+xml":"mpkg","application/vnd.apple.keynote":"key","application/vnd.apple.mpegurl":"m3u8","application/vnd.apple.numbers":"numbers","application/vnd.apple.pages":"pages","application/vnd.apple.pkpass":"pkpass","application/vnd.aristanetworks.swi":"swi","application/vnd.astraea-software.iota":"iota","application/vnd.audiograph":"aep","application/vnd.balsamiq.bmml+xml":"bmml","application/vnd.blueice.multipass":"mpm","application/vnd.bmi":"bmi","application/vnd.businessobjects":"rep","application/vnd.chemdraw+xml":"cdxml","application/vnd.chipnuts.karaoke-mmd":"mmd","application/vnd.cinderella":"cdy","application/vnd.citationstyles.style+xml":"csl","application/vnd.claymore":"cla","application/vnd.cloanto.rp9":"rp9","application/vnd.clonk.c4group":"c4g","application/vnd.cluetrust.cartomobile-config":"c11amc","application/vnd.cluetrust.cartomobile-config-pkg":"c11amz","application/vnd.commonspace":"csp","application/vnd.contact.cmsg":"cdbcmsg","application/vnd.cosmocaller":"cmc","application/vnd.crick.clicker":"clkx","application/vnd.crick.clicker.keyboard":"clkk","application/vnd.crick.clicker.palette":"clkp","application/vnd.crick.clicker.template":"clkt","application/vnd.crick.clicker.wordbank":"clkw","application/vnd.criticaltools.wbs+xml":"wbs","application/vnd.ctc-posml":"pml","application/vnd.cups-ppd":"ppd","application/vnd.curl.car":"car","application/vnd.curl.pcurl":"pcurl","application/vnd.dart":"dart","application/vnd.data-vision.rdz":"rdz","application/vnd.dbf":"dbf","application/vnd.dece.data":"uvf","application/vnd.dece.ttml+xml":"uvt","application/vnd.dece.unspecified":"uvx","application/vnd.dece.zip":"uvz","application/vnd.denovo.fcselayout-link":"fe_launch","application/vnd.dna":"dna","application/vnd.dolby.mlp":"mlp","application/vnd.dpgraph":"dpg","application/vnd.dreamfactory":"dfac","application/vnd.ds-keypoint":"kpxx","application/vnd.dvb.ait":"ait","application/vnd.dvb.service":"svc","application/vnd.dynageo":"geo","application/vnd.ecowin.chart":"mag","application/vnd.enliven":"nml","application/vnd.epson.esf":"esf","application/vnd.epson.msf":"msf","application/vnd.epson.quickanime":"qam","application/vnd.epson.salt":"slt","application/vnd.epson.ssf":"ssf","application/vnd.eszigno3+xml":"es3","application/vnd.ezpix-album":"ez2","application/vnd.ezpix-package":"ez3","application/vnd.fdf":"fdf","application/vnd.fdsn.mseed":"mseed","application/vnd.fdsn.seed":"seed","application/vnd.flographit":"gph","application/vnd.fluxtime.clip":"ftc","application/vnd.framemaker":"fm","application/vnd.frogans.fnc":"fnc","application/vnd.frogans.ltf":"ltf","application/vnd.fsc.weblaunch":"fsc","application/vnd.fujitsu.oasys":"oas","application/vnd.fujitsu.oasys2":"oa2","application/vnd.fujitsu.oasys3":"oa3","application/vnd.fujitsu.oasysgp":"fg5","application/vnd.fujitsu.oasysprs":"bh2","application/vnd.fujixerox.ddd":"ddd","application/vnd.fujixerox.docuworks":"xdw","application/vnd.fujixerox.docuworks.binder":"xbd","application/vnd.fuzzysheet":"fzs","application/vnd.genomatix.tuxedo":"txd","application/vnd.geogebra.file":"ggb","application/vnd.geogebra.tool":"ggt","application/vnd.geometry-explorer":"gex","application/vnd.geonext":"gxt","application/vnd.geoplan":"g2w","application/vnd.geospace":"g3w","application/vnd.gmx":"gmx","application/vnd.google-apps.document":"gdoc","application/vnd.google-apps.presentation":"gslides","application/vnd.google-apps.spreadsheet":"gsheet","application/vnd.google-earth.kml+xml":"kml","application/vnd.google-earth.kmz":"kmz","application/vnd.grafeq":"gqf","application/vnd.groove-account":"gac","application/vnd.groove-help":"ghf","application/vnd.groove-identity-message":"gim","application/vnd.groove-injector":"grv","application/vnd.groove-tool-message":"gtm","application/vnd.groove-tool-template":"tpl","application/vnd.groove-vcard":"vcg","application/vnd.hal+xml":"hal","application/vnd.handheld-entertainment+xml":"zmm","application/vnd.hbci":"hbci","application/vnd.hhe.lesson-player":"les","application/vnd.hp-hpgl":"hpgl","application/vnd.hp-hpid":"hpid","application/vnd.hp-hps":"hps","application/vnd.hp-jlyt":"jlt","application/vnd.hp-pcl":"pcl","application/vnd.hp-pclxl":"pclxl","application/vnd.hydrostatix.sof-data":"sfd-hdstx","application/vnd.ibm.minipay":"mpy","application/vnd.ibm.modcap":"afp","application/vnd.ibm.rights-management":"irm","application/vnd.ibm.secure-container":"sc","application/vnd.iccprofile":"icc","application/vnd.igloader":"igl","application/vnd.immervision-ivp":"ivp","application/vnd.immervision-ivu":"ivu","application/vnd.insors.igm":"igm","application/vnd.intercon.formnet":"xpw","application/vnd.intergeo":"i2g","application/vnd.intu.qbo":"qbo","application/vnd.intu.qfx":"qfx","application/vnd.ipunplugged.rcprofile":"rcprofile","application/vnd.irepository.package+xml":"irp","application/vnd.is-xpr":"xpr","application/vnd.isac.fcs":"fcs","application/vnd.jam":"jam","application/vnd.jcp.javame.midlet-rms":"rms","application/vnd.jisp":"jisp","application/vnd.joost.joda-archive":"joda","application/vnd.kahootz":"ktz","application/vnd.kde.karbon":"karbon","application/vnd.kde.kchart":"chrt","application/vnd.kde.kformula":"kfo","application/vnd.kde.kivio":"flw","application/vnd.kde.kontour":"kon","application/vnd.kde.kpresenter":"kpr","application/vnd.kde.kspread":"ksp","application/vnd.kde.kword":"kwd","application/vnd.kenameaapp":"htke","application/vnd.kidspiration":"kia","application/vnd.kinar":"kne","application/vnd.koan":"skp","application/vnd.kodak-descriptor":"sse","application/vnd.las.las+xml":"lasxml","application/vnd.llamagraphics.life-balance.desktop":"lbd","application/vnd.llamagraphics.life-balance.exchange+xml":"lbe","application/vnd.lotus-1-2-3":"123","application/vnd.lotus-approach":"apr","application/vnd.lotus-freelance":"pre","application/vnd.lotus-notes":"nsf","application/vnd.lotus-organizer":"org","application/vnd.lotus-screencam":"scm","application/vnd.lotus-wordpro":"lwp","application/vnd.macports.portpkg":"portpkg","application/vnd.mapbox-vector-tile":"mvt","application/vnd.mcd":"mcd","application/vnd.medcalcdata":"mc1","application/vnd.mediastation.cdkey":"cdkey","application/vnd.mfer":"mwf","application/vnd.mfmp":"mfm","application/vnd.micrografx.flo":"flo","application/vnd.micrografx.igx":"igx","application/vnd.mif":"mif","application/vnd.mobius.daf":"daf","application/vnd.mobius.dis":"dis","application/vnd.mobius.mbk":"mbk","application/vnd.mobius.mqy":"mqy","application/vnd.mobius.msl":"msl","application/vnd.mobius.plc":"plc","application/vnd.mobius.txf":"txf","application/vnd.mophun.application":"mpn","application/vnd.mophun.certificate":"mpc","application/vnd.mozilla.xul+xml":"xul","application/vnd.ms-artgalry":"cil","application/vnd.ms-cab-compressed":"cab","application/vnd.ms-excel":"xls","application/vnd.ms-excel.addin.macroenabled.12":"xlam","application/vnd.ms-excel.sheet.binary.macroenabled.12":"xlsb","application/vnd.ms-excel.sheet.macroenabled.12":"xlsm","application/vnd.ms-excel.template.macroenabled.12":"xltm","application/vnd.ms-fontobject":"eot","application/vnd.ms-htmlhelp":"chm","application/vnd.ms-ims":"ims","application/vnd.ms-lrm":"lrm","application/vnd.ms-officetheme":"thmx","application/vnd.ms-outlook":"msg","application/vnd.ms-pki.seccat":"cat","application/vnd.ms-pki.stl":"stl","application/vnd.ms-powerpoint":"ppt","application/vnd.ms-powerpoint.addin.macroenabled.12":"ppam","application/vnd.ms-powerpoint.presentation.macroenabled.12":"pptm","application/vnd.ms-powerpoint.slide.macroenabled.12":"sldm","application/vnd.ms-powerpoint.slideshow.macroenabled.12":"ppsm","application/vnd.ms-powerpoint.template.macroenabled.12":"potm","application/vnd.ms-project":"mpp","application/vnd.ms-word.document.macroenabled.12":"docm","application/vnd.ms-word.template.macroenabled.12":"dotm","application/vnd.ms-works":"wps","application/vnd.ms-wpl":"wpl","application/vnd.ms-xpsdocument":"xps","application/vnd.mseq":"mseq","application/vnd.musician":"mus","application/vnd.muvee.style":"msty","application/vnd.mynfc":"taglet","application/vnd.neurolanguage.nlu":"nlu","application/vnd.nitf":"ntf","application/vnd.noblenet-directory":"nnd","application/vnd.noblenet-sealer":"nns","application/vnd.noblenet-web":"nnw","application/vnd.nokia.n-gage.ac+xml":"ac","application/vnd.nokia.n-gage.data":"ngdat","application/vnd.nokia.n-gage.symbian.install":"n-gage","application/vnd.nokia.radio-preset":"rpst","application/vnd.nokia.radio-presets":"rpss","application/vnd.novadigm.edm":"edm","application/vnd.novadigm.edx":"edx","application/vnd.novadigm.ext":"ext","application/vnd.oasis.opendocument.chart":"odc","application/vnd.oasis.opendocument.chart-template":"otc","application/vnd.oasis.opendocument.database":"odb","application/vnd.oasis.opendocument.formula":"odf","application/vnd.oasis.opendocument.formula-template":"odft","application/vnd.oasis.opendocument.graphics":"odg","application/vnd.oasis.opendocument.graphics-template":"otg","application/vnd.oasis.opendocument.image":"odi","application/vnd.oasis.opendocument.image-template":"oti","application/vnd.oasis.opendocument.presentation":"odp","application/vnd.oasis.opendocument.presentation-template":"otp","application/vnd.oasis.opendocument.spreadsheet":"ods","application/vnd.oasis.opendocument.spreadsheet-template":"ots","application/vnd.oasis.opendocument.text":"odt","application/vnd.oasis.opendocument.text-master":"odm","application/vnd.oasis.opendocument.text-template":"ott","application/vnd.oasis.opendocument.text-web":"oth","application/vnd.olpc-sugar":"xo","application/vnd.oma.dd2+xml":"dd2","application/vnd.openblox.game+xml":"obgx","application/vnd.openofficeorg.extension":"oxt","application/vnd.openstreetmap.data+xml":"osm","application/vnd.openxmlformats-officedocument.presentationml.presentation":"pptx","application/vnd.openxmlformats-officedocument.presentationml.slide":"sldx","application/vnd.openxmlformats-officedocument.presentationml.slideshow":"ppsx","application/vnd.openxmlformats-officedocument.presentationml.template":"potx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":"xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.template":"xltx","application/vnd.openxmlformats-officedocument.wordprocessingml.document":"docx","application/vnd.openxmlformats-officedocument.wordprocessingml.template":"dotx","application/vnd.osgeo.mapguide.package":"mgp","application/vnd.osgi.dp":"dp","application/vnd.osgi.subsystem":"esa","application/vnd.palm":"pdb","application/vnd.pawaafile":"paw","application/vnd.pg.format":"str","application/vnd.pg.osasli":"ei6","application/vnd.picsel":"efif","application/vnd.pmi.widget":"wg","application/vnd.pocketlearn":"plf","application/vnd.powerbuilder6":"pbd","application/vnd.previewsystems.box":"box","application/vnd.proteus.magazine":"mgz","application/vnd.publishare-delta-tree":"qps","application/vnd.pvi.ptid1":"ptid","application/vnd.quark.quarkxpress":"qxd","application/vnd.rar":"rar","application/vnd.realvnc.bed":"bed","application/vnd.recordare.musicxml":"mxl","application/vnd.recordare.musicxml+xml":"musicxml","application/vnd.rig.cryptonote":"cryptonote","application/vnd.rim.cod":"cod","application/vnd.rn-realmedia":"rm","application/vnd.rn-realmedia-vbr":"rmvb","application/vnd.route66.link66+xml":"link66","application/vnd.sailingtracker.track":"st","application/vnd.seemail":"see","application/vnd.sema":"sema","application/vnd.semd":"semd","application/vnd.semf":"semf","application/vnd.shana.informed.formdata":"ifm","application/vnd.shana.informed.formtemplate":"itp","application/vnd.shana.informed.interchange":"iif","application/vnd.shana.informed.package":"ipk","application/vnd.simtech-mindmapper":"twd","application/vnd.smaf":"mmf","application/vnd.smart.teacher":"teacher","application/vnd.software602.filler.form+xml":"fo","application/vnd.solent.sdkm+xml":"sdkm","application/vnd.spotfire.dxp":"dxp","application/vnd.spotfire.sfs":"sfs","application/vnd.stardivision.calc":"sdc","application/vnd.stardivision.draw":"sda","application/vnd.stardivision.impress":"sdd","application/vnd.stardivision.math":"smf","application/vnd.stardivision.writer":"sdw","application/vnd.stardivision.writer-global":"sgl","application/vnd.stepmania.package":"smzip","application/vnd.stepmania.stepchart":"sm","application/vnd.sun.wadl+xml":"wadl","application/vnd.sun.xml.calc":"sxc","application/vnd.sun.xml.calc.template":"stc","application/vnd.sun.xml.draw":"sxd","application/vnd.sun.xml.draw.template":"std","application/vnd.sun.xml.impress":"sxi","application/vnd.sun.xml.impress.template":"sti","application/vnd.sun.xml.math":"sxm","application/vnd.sun.xml.writer":"sxw","application/vnd.sun.xml.writer.global":"sxg","application/vnd.sun.xml.writer.template":"stw","application/vnd.sus-calendar":"sus","application/vnd.svd":"svd","application/vnd.symbian.install":"sis","application/vnd.syncml+xml":"xsm","application/vnd.syncml.dm+wbxml":"bdm","application/vnd.syncml.dm+xml":"xdm","application/vnd.syncml.dmddf+xml":"ddf","application/vnd.tao.intent-module-archive":"tao","application/vnd.tcpdump.pcap":"pcap","application/vnd.tmobile-livetv":"tmo","application/vnd.trid.tpt":"tpt","application/vnd.triscape.mxs":"mxs","application/vnd.trueapp":"tra","application/vnd.ufdl":"ufd","application/vnd.uiq.theme":"utz","application/vnd.umajin":"umj","application/vnd.unity":"unityweb","application/vnd.uoml+xml":"uoml","application/vnd.vcx":"vcx","application/vnd.visio":"vsd","application/vnd.visionary":"vis","application/vnd.vsf":"vsf","application/vnd.wap.wbxml":"wbxml","application/vnd.wap.wmlc":"wmlc","application/vnd.wap.wmlscriptc":"wmlsc","application/vnd.webturbo":"wtb","application/vnd.wolfram.player":"nbp","application/vnd.wordperfect":"wpd","application/vnd.wqd":"wqd","application/vnd.wt.stf":"stf","application/vnd.xara":"xar","application/vnd.xfdl":"xfdl","application/vnd.yamaha.hv-dic":"hvd","application/vnd.yamaha.hv-script":"hvs","application/vnd.yamaha.hv-voice":"hvp","application/vnd.yamaha.openscoreformat":"osf","application/vnd.yamaha.openscoreformat.osfpvg+xml":"osfpvg","application/vnd.yamaha.smaf-audio":"saf","application/vnd.yamaha.smaf-phrase":"spf","application/vnd.yellowriver-custom-menu":"cmp","application/vnd.zul":"zir","application/vnd.zzazz.deck+xml":"zaz","application/voicexml+xml":"vxml","application/wasm":"wasm","application/watcherinfo+xml":"wif","application/widget":"wgt","application/winhlp":"hlp","application/wsdl+xml":"wsdl","application/wspolicy+xml":"wspolicy","application/x-7z-compressed":"7z","application/x-abiword":"abw","application/x-ace-compressed":"ace","application/x-apple-diskimage":"dmg","application/x-arj":"arj","application/x-authorware-bin":"aab","application/x-authorware-map":"aam","application/x-authorware-seg":"aas","application/x-bcpio":"bcpio","application/x-bdoc":"bdoc","application/x-bittorrent":"torrent","application/x-blorb":"blb","application/x-bzip":"bz","application/x-bzip2":"bz2","application/x-cbr":"cbr","application/x-cdlink":"vcd","application/x-cfs-compressed":"cfs","application/x-chat":"chat","application/x-chess-pgn":"pgn","application/x-chrome-extension":"crx","application/x-cocoa":"cco","application/x-conference":"nsc","application/x-cpio":"cpio","application/x-csh":"csh","application/x-debian-package":"deb","application/x-dgc-compressed":"dgc","application/x-director":"dir","application/x-doom":"wad","application/x-dtbncx+xml":"ncx","application/x-dtbook+xml":"dtb","application/x-dtbresource+xml":"res","application/x-dvi":"dvi","application/x-envoy":"evy","application/x-eva":"eva","application/x-font-bdf":"bdf","application/x-font-ghostscript":"gsf","application/x-font-linux-psf":"psf","application/x-font-pcf":"pcf","application/x-font-snf":"snf","application/x-font-type1":"pfa","application/x-freearc":"arc","application/x-futuresplash":"spl","application/x-gca-compressed":"gca","application/x-glulx":"ulx","application/x-gnumeric":"gnumeric","application/x-gramps-xml":"gramps","application/x-gtar":"gtar","application/x-hdf":"hdf","application/x-httpd-php":"php","application/x-install-instructions":"install","application/x-iso9660-image":"iso","application/x-iwork-keynote-sffkey":"key","application/x-iwork-numbers-sffnumbers":"numbers","application/x-iwork-pages-sffpages":"pages","application/x-java-archive-diff":"jardiff","application/x-java-jnlp-file":"jnlp","application/x-keepass2":"kdbx","application/x-latex":"latex","application/x-lua-bytecode":"luac","application/x-lzh-compressed":"lzh","application/x-makeself":"run","application/x-mie":"mie","application/x-mobipocket-ebook":"prc","application/x-ms-application":"application","application/x-ms-shortcut":"lnk","application/x-ms-wmd":"wmd","application/x-ms-wmz":"wmz","application/x-ms-xbap":"xbap","application/x-msaccess":"mdb","application/x-msbinder":"obd","application/x-mscardfile":"crd","application/x-msclip":"clp","application/x-msdos-program":"exe","application/x-msdownload":"exe","application/x-msmediaview":"mvb","application/x-msmetafile":"wmf","application/x-msmoney":"mny","application/x-mspublisher":"pub","application/x-msschedule":"scd","application/x-msterminal":"trm","application/x-mswrite":"wri","application/x-netcdf":"nc","application/x-ns-proxy-autoconfig":"pac","application/x-nzb":"nzb","application/x-perl":"pl","application/x-pilot":"prc","application/x-pkcs12":"p12","application/x-pkcs7-certificates":"p7b","application/x-pkcs7-certreqresp":"p7r","application/x-rar-compressed":"rar","application/x-redhat-package-manager":"rpm","application/x-research-info-systems":"ris","application/x-sea":"sea","application/x-sh":"sh","application/x-shar":"shar","application/x-shockwave-flash":"swf","application/x-silverlight-app":"xap","application/x-sql":"sql","application/x-stuffit":"sit","application/x-stuffitx":"sitx","application/x-subrip":"srt","application/x-sv4cpio":"sv4cpio","application/x-sv4crc":"sv4crc","application/x-t3vm-image":"t3","application/x-tads":"gam","application/x-tar":"tar","application/x-tcl":"tcl","application/x-tex":"tex","application/x-tex-tfm":"tfm","application/x-texinfo":"texinfo","application/x-tgif":"obj","application/x-ustar":"ustar","application/x-virtualbox-hdd":"hdd","application/x-virtualbox-ova":"ova","application/x-virtualbox-ovf":"ovf","application/x-virtualbox-vbox":"vbox","application/x-virtualbox-vbox-extpack":"vbox-extpack","application/x-virtualbox-vdi":"vdi","application/x-virtualbox-vhd":"vhd","application/x-virtualbox-vmdk":"vmdk","application/x-wais-source":"src","application/x-web-app-manifest+json":"webapp","application/x-x509-ca-cert":"der","application/x-xfig":"fig","application/x-xliff+xml":"xlf","application/x-xpinstall":"xpi","application/x-xz":"xz","application/x-zmachine":"z1","application/xaml+xml":"xaml","application/xcap-att+xml":"xav","application/xcap-caps+xml":"xca","application/xcap-diff+xml":"xdf","application/xcap-el+xml":"xel","application/xcap-ns+xml":"xns","application/xenc+xml":"xenc","application/xhtml+xml":"xhtml","application/xliff+xml":"xlf","application/xml":"xml","application/xml-dtd":"dtd","application/xop+xml":"xop","application/xproc+xml":"xpl","application/xslt+xml":"xsl","application/xspf+xml":"xspf","application/xv+xml":"mxml","application/yang":"yang","application/yin+xml":"yin","application/zip":"zip","audio/3gpp":"3gpp","audio/adpcm":"adp","audio/amr":"amr","audio/basic":"au","audio/midi":"mid","audio/mobile-xmf":"mxmf","audio/mp3":"mp3","audio/mp4":"m4a","audio/mpeg":"mpga","audio/ogg":"oga","audio/s3m":"s3m","audio/silk":"sil","audio/vnd.dece.audio":"uva","audio/vnd.digital-winds":"eol","audio/vnd.dra":"dra","audio/vnd.dts":"dts","audio/vnd.dts.hd":"dtshd","audio/vnd.lucent.voice":"lvp","audio/vnd.ms-playready.media.pya":"pya","audio/vnd.nuera.ecelp4800":"ecelp4800","audio/vnd.nuera.ecelp7470":"ecelp7470","audio/vnd.nuera.ecelp9600":"ecelp9600","audio/vnd.rip":"rip","audio/wav":"wav","audio/wave":"wav","audio/webm":"weba","audio/x-aac":"aac","audio/x-aiff":"aif","audio/x-caf":"caf","audio/x-flac":"flac","audio/x-m4a":"m4a","audio/x-matroska":"mka","audio/x-mpegurl":"m3u","audio/x-ms-wax":"wax","audio/x-ms-wma":"wma","audio/x-pn-realaudio":"ram","audio/x-pn-realaudio-plugin":"rmp","audio/x-realaudio":"ra","audio/x-wav":"wav","audio/xm":"xm","chemical/x-cdx":"cdx","chemical/x-cif":"cif","chemical/x-cmdf":"cmdf","chemical/x-cml":"cml","chemical/x-csml":"csml","chemical/x-xyz":"xyz","font/collection":"ttc","font/otf":"otf","font/ttf":"ttf","font/woff":"woff","font/woff2":"woff2","image/aces":"exr","image/apng":"apng","image/avci":"avci","image/avcs":"avcs","image/avif":"avif","image/bmp":"bmp","image/cgm":"cgm","image/dicom-rle":"drle","image/emf":"emf","image/fits":"fits","image/g3fax":"g3","image/gif":"gif","image/heic":"heic","image/heic-sequence":"heics","image/heif":"heif","image/heif-sequence":"heifs","image/hej2k":"hej2","image/hsj2":"hsj2","image/ief":"ief","image/jls":"jls","image/jp2":"jp2","image/jpeg":"jpeg","image/jph":"jph","image/jphc":"jhc","image/jpm":"jpm","image/jpx":"jpx","image/jxr":"jxr","image/jxra":"jxra","image/jxrs":"jxrs","image/jxs":"jxs","image/jxsc":"jxsc","image/jxsi":"jxsi","image/jxss":"jxss","image/ktx":"ktx","image/ktx2":"ktx2","image/png":"png","image/prs.btif":"btif","image/prs.pti":"pti","image/sgi":"sgi","image/svg+xml":"svg","image/t38":"t38","image/tiff":"tif","image/tiff-fx":"tfx","image/vnd.adobe.photoshop":"psd","image/vnd.airzip.accelerator.azv":"azv","image/vnd.dece.graphic":"uvi","image/vnd.djvu":"djvu","image/vnd.dvb.subtitle":"sub","image/vnd.dwg":"dwg","image/vnd.dxf":"dxf","image/vnd.fastbidsheet":"fbs","image/vnd.fpx":"fpx","image/vnd.fst":"fst","image/vnd.fujixerox.edmics-mmr":"mmr","image/vnd.fujixerox.edmics-rlc":"rlc","image/vnd.microsoft.icon":"ico","image/vnd.ms-dds":"dds","image/vnd.ms-modi":"mdi","image/vnd.ms-photo":"wdp","image/vnd.net-fpx":"npx","image/vnd.pco.b16":"b16","image/vnd.tencent.tap":"tap","image/vnd.valve.source.texture":"vtf","image/vnd.wap.wbmp":"wbmp","image/vnd.xiff":"xif","image/vnd.zbrush.pcx":"pcx","image/webp":"webp","image/wmf":"wmf","image/x-3ds":"3ds","image/x-cmu-raster":"ras","image/x-cmx":"cmx","image/x-freehand":"fh","image/x-icon":"ico","image/x-jng":"jng","image/x-mrsid-image":"sid","image/x-ms-bmp":"bmp","image/x-pcx":"pcx","image/x-pict":"pic","image/x-portable-anymap":"pnm","image/x-portable-bitmap":"pbm","image/x-portable-graymap":"pgm","image/x-portable-pixmap":"ppm","image/x-rgb":"rgb","image/x-tga":"tga","image/x-xbitmap":"xbm","image/x-xpixmap":"xpm","image/x-xwindowdump":"xwd","message/disposition-notification":"disposition-notification","message/global":"u8msg","message/global-delivery-status":"u8dsn","message/global-disposition-notification":"u8mdn","message/global-headers":"u8hdr","message/rfc822":"eml","message/vnd.wfa.wsc":"wsc","model/3mf":"3mf","model/gltf+json":"gltf","model/gltf-binary":"glb","model/iges":"igs","model/mesh":"msh","model/mtl":"mtl","model/obj":"obj","model/step+xml":"stpx","model/step+zip":"stpz","model/step-xml+zip":"stpxz","model/stl":"stl","model/vnd.collada+xml":"dae","model/vnd.dwf":"dwf","model/vnd.gdl":"gdl","model/vnd.gtw":"gtw","model/vnd.mts":"mts","model/vnd.opengex":"ogex","model/vnd.parasolid.transmit.binary":"x_b","model/vnd.parasolid.transmit.text":"x_t","model/vnd.sap.vds":"vds","model/vnd.usdz+zip":"usdz","model/vnd.valve.source.compiled-map":"bsp","model/vnd.vtu":"vtu","model/vrml":"wrl","model/x3d+binary":"x3db","model/x3d+fastinfoset":"x3db","model/x3d+vrml":"x3dv","model/x3d+xml":"x3d","model/x3d-vrml":"x3dv","text/cache-manifest":"appcache","text/calendar":"ics","text/coffeescript":"coffee","text/css":"css","text/csv":"csv","text/html":"html","text/jade":"jade","text/jsx":"jsx","text/less":"less","text/markdown":"markdown","text/mathml":"mml","text/mdx":"mdx","text/n3":"n3","text/plain":"txt","text/prs.lines.tag":"dsc","text/richtext":"rtx","text/rtf":"rtf","text/sgml":"sgml","text/shex":"shex","text/slim":"slim","text/spdx":"spdx","text/stylus":"stylus","text/tab-separated-values":"tsv","text/troff":"t","text/turtle":"ttl","text/uri-list":"uri","text/vcard":"vcard","text/vnd.curl":"curl","text/vnd.curl.dcurl":"dcurl","text/vnd.curl.mcurl":"mcurl","text/vnd.curl.scurl":"scurl","text/vnd.dvb.subtitle":"sub","text/vnd.familysearch.gedcom":"ged","text/vnd.fly":"fly","text/vnd.fmi.flexstor":"flx","text/vnd.graphviz":"gv","text/vnd.in3d.3dml":"3dml","text/vnd.in3d.spot":"spot","text/vnd.sun.j2me.app-descriptor":"jad","text/vnd.wap.wml":"wml","text/vnd.wap.wmlscript":"wmls","text/vtt":"vtt","text/x-asm":"s","text/x-c":"c","text/x-component":"htc","text/x-fortran":"f","text/x-handlebars-template":"hbs","text/x-java-source":"java","text/x-lua":"lua","text/x-markdown":"mkd","text/x-nfo":"nfo","text/x-opml":"opml","text/x-org":"org","text/x-pascal":"p","text/x-processing":"pde","text/x-sass":"sass","text/x-scss":"scss","text/x-setext":"etx","text/x-sfv":"sfv","text/x-suse-ymp":"ymp","text/x-uuencode":"uu","text/x-vcalendar":"vcs","text/x-vcard":"vcf","text/xml":"xml","text/yaml":"yaml","video/3gpp":"3gp","video/3gpp2":"3g2","video/h261":"h261","video/h263":"h263","video/h264":"h264","video/iso.segment":"m4s","video/jpeg":"jpgv","video/jpm":"jpm","video/mj2":"mj2","video/mp2t":"ts","video/mp4":"mp4","video/mpeg":"mpeg","video/ogg":"ogv","video/quicktime":"qt","video/vnd.dece.hd":"uvh","video/vnd.dece.mobile":"uvm","video/vnd.dece.pd":"uvp","video/vnd.dece.sd":"uvs","video/vnd.dece.video":"uvv","video/vnd.dvb.file":"dvb","video/vnd.fvt":"fvt","video/vnd.mpegurl":"mxu","video/vnd.ms-playready.media.pyv":"pyv","video/vnd.uvvu.mp4":"uvu","video/vnd.vivo":"viv","video/webm":"webm","video/x-f4v":"f4v","video/x-fli":"fli","video/x-flv":"flv","video/x-m4v":"m4v","video/x-matroska":"mkv","video/x-mng":"mng","video/x-ms-asf":"asf","video/x-ms-vob":"vob","video/x-ms-wm":"wm","video/x-ms-wmv":"wmv","video/x-ms-wmx":"wmx","video/x-ms-wvx":"wvx","video/x-msvideo":"avi","video/x-sgi-movie":"movie","video/x-smv":"smv","x-conference/x-cooltalk":"ice"},g0=/^\s*([^;\s]*)(?:;|\s|$)/;function kr(n){if(!n||typeof n!="string")return"";let e=g0.exec(n);return e&&h0[e[1].toLowerCase()]||""}var Dn=n=>ze(n).replace(/[\[\]\#\^]/g,""),xi=(n,e)=>K.readdirSync(n).filter(i=>{let r=i.split(".").slice(0,-1).join("."),o=Cn(e),a=r.match(new RegExp(`${o}\\.\\d+`));return r===e||a}).length,vu=(n,e)=>{let t=B.useUniqueUnknownFileNames?"unknown_filename"+(Math.random().toString(16)+"0000000").slice(2,10):"unknown_filename",i=x0(e),r=t;if(e["resource-attributes"]&&e["resource-attributes"]["file-name"]){let s=e["resource-attributes"]["file-name"].substr(0,50);r=oe(s).basename}r=r.replace(/[/\\?%*:|"<>\[\]\+]/g,"-"),B.sanitizeResourceNameSpaces&&(r=r.replace(/ /g,B.replacementChar));let o=xi(n,r);return{fileName:`${o>0?`${r}.${o}`:r}.${i}`,extension:i,index:o}},Ua=n=>Dn(n.title?`${n.title.toString()}`:"Untitled"),Eu=(n,e,t="md")=>`${ja(n,e)}.${t}`,y0=n=>{if(!(n["resource-attributes"]&&n["resource-attributes"]["file-name"]))return"";let e=n["resource-attributes"]["file-name"].split(".");return e.length>1?e[e.length-1]:void 0},b0=n=>{let e=n.mime;return e&&kr(e)||""},x0=n=>y0(n)||b0(n)||"dat",w0=(n,e)=>(0,wu.moment)(n.created).format("YYYYMMDDHHmm"),ja=(n,e)=>{let t;if(B.isZettelkastenNeeded||B.useZettelIdAsFilename){let i=w0(e,n),r=xi(n,i),o=" ";t=r!==0?`${i}.${r}`:i,B.useZettelIdAsFilename||(t+=Ua(e)!=="Untitled"?`${o}${Ua(e)}`:"")}else{let i=Ua(e),r=xi(n,i);t=r===0?i:`${i}.${r}`}return t};var _u=n=>n==="Table of Contents";var v0=n=>n.replace(/\[|\]/g,""),E0=n=>n.replace(/\\/g,""),Au={filter:Ye("A"),replacement:(n,e)=>{let t=de(e);if(!t.href)return"";let i=Nr(B).turndown(v0(e.innerHTML));i=E0(i);let r="",o=i.match(/^(#{1,6} )(.*)/);o&&(r=o[1],i=o[2]);let a=t.href.value,s=t.type?t.type.value:void 0,l=B.urlEncodeFileNamesAndLinks?encodeURI(a):a;if(s==="file")return`![[${l}]]`;if(a.match(/^(https?:|www\.|file:|ftp:|mailto:)/))return r+A0(i,a);if(a.startsWith("evernote://")){let c=Dn(i),p=ke.getInstance(),m=_n(6);return _u(p.getCurrentNoteName())?p.addItemToTOCMap({url:a,title:c,uniqueEnd:m}):p.addItemToMap({url:a,title:c,uniqueEnd:m}),r+`[[${a}]]`}return r+`[[${l}${i===l?"":`|${i}`}]]`}},_0={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},ku=/&(?:amp|lt|gt|quot|#39);/g,T0=RegExp(ku.source);function Tu(n){return n&&T0.test(n)?n.replace(ku,e=>_0[e]):n}var A0=(n,e)=>!n||Tu(n)===Tu(e)?B.generateNakedUrls?e:`<${e}>`:`[${n}](${e})`;var ve={bold:"**",italic:"_",highlight:"==",strikethrough:"~~"};var k0="-evernote-highlight:true;",N0="--en-highlight",S0="bold",R0="italic",Nu={filter:Ye("SPAN"),replacement:(n,e)=>{let t=de(e);if(t.style&&n.trim()!==""){let i=t.style.value;if(n!=="<YARLE_NEWLINE_PLACEHOLDER>"){let r=i.includes(S0),o=i.includes(R0);if(r&&!o)return`${ve.bold}${n}${ve.bold}`;if(!r&&o)return`${ve.italic}${n}${ve.italic}`;if(r&&o)return`${ve.italic}${ve.bold}${n}${ve.bold}${ve.italic}`}return i.includes(k0)||i.includes(N0)?`${ve.highlight}${n}${ve.highlight}`:n}return n}};var Su={filter:["del","s","strike"],replacement:n=>`${ve.strikethrough}${n}${ve.strikethrough}`};var Ru={filter:Ye("EN-TODO"),replacement:(n,e)=>{var r,o;let t=de(e);return`${((o=(r=e.parentElement)==null?void 0:r.nodeName)==null?void 0:o.toUpperCase())==="LI"?"":"- "}${t.checked&&t.checked.value==="true"?"[x]":"[ ]"} ${n}`}};var Ou={filter:Ye("BR"),replacement:(n,e)=>"<YARLE_NEWLINE_PLACEHOLDER>"};var O0=n=>{let e=de(n);return e.style&&e.style.value.indexOf("--en-task-group:true")>=0},C0=n=>de(n).style.value.split("--en-id:")[1].split(";")[0],Cu={filter:Ye("DIV"),replacement:(n,e)=>O0(e)?`<YARLE-EN-V10-TASK>${C0(e)}</YARLE-EN-V10-TASK>`:B.monospaceIsCodeBlock?bu(n,e):gu(n,e)};var Du={filter:["i"],replacement:n=>n.trim()!==""?`${ve.italic}${n}${ve.italic}`:n};var Sr="- [ ]",Rr="- [x]";var Iu="	",Fu={filter:"li",replacement:(n,e,t)=>{let i=m=>{let g=de(m);return g.style&&g.style.value.indexOf("--en-checked:true;")>=0},r=m=>{let g=de(m);return g.style&&g.style.value.indexOf("--en-checked:false;")>=0},o=n.match(/^\n*/)[0].length||0,a=Iu.repeat(o),s=n.replace(/^\n+/,"").replace(/\n+$/,`
`).replace(/\n/gm,`
${Iu}`),l=o>0?a:i(e)?`${Rr} `:r(e)?`${Sr} `:"* ",c=e.parentNode;if(c.nodeName==="OL"){let m=c.getAttribute("start"),g=Array.prototype.indexOf.call(c.children,e);l=`${m?Number(m)+g:g+1}. `}let p;return p=l+s+(e.nextSibling&&!/\n$/.test(s)?`
`:""),p}};var Nr=n=>{let e=new window.TurndownService({br:"",...n.turndownOptions,blankReplacement:(t,i)=>i.isBlock?`

`:"",keepReplacement:(t,i)=>i.isBlock?`
${i.outerHTML}
`:i.outerHTML,defaultReplacement:(t,i)=>i.isBlock?`
${t}
`:t});return e.use(Lu.gfm),e.addRule("span",Nu),e.addRule("strikethrough",Su),e.addRule("evernote task items",Ru),e.addRule("wikistyle links",Au),e.addRule("images",xu),e.addRule("list",Fu),e.addRule("italic",Du),n.keepMDCharactersOfENNotes&&(e.escape=t=>t),e.addRule("divBlock",Cu),n.keepOriginalAmountOfNewlines&&e.addRule("newline",Ou),e};var Pu=n=>{n.replaceWith(...Array.from(n.children))},Mu=n=>{let e=n.parentElement;e.replaceWith(...Array.from(e.childNodes)),e.append(...Array.from(n.childNodes)),n.appendChild(e)},D0=n=>(Array.from(n.querySelectorAll("span>en-todo")).forEach(Mu),Array.from(n.querySelectorAll("a>en-todo")).forEach(Mu),n),I0=n=>{let e=n.replace(/<li>/g,"<li><div>");return e=e.replace(/<\/li>/g,"</div></li>"),e=e.replace(/<li><div>(\s)*<div>/g,"<li><div>"),e=e.replace(/<\/div>(\s)*<\/div><\/li>/g,"</div></li>"),e},F0=n=>{var o;let e=Array.from(n.getElementsByTagName("ul")),t=Array.from(n.getElementsByTagName("ol")),i=e.concat(t);i.forEach(a=>{a.parentElement.tagName==="LI"&&a.parentElement.replaceWith(a),a.previousElementSibling&&a.previousElementSibling.tagName==="LI"&&a.previousElementSibling.appendChild(a)});for(let a of i){let s=a.parentElement;(s==null?void 0:s.tagName)==="DIV"&&((o=s==null?void 0:s.parentElement)==null?void 0:o.tagName)==="UL"&&Pu(s),((s==null?void 0:s.tagName)==="UL"||(s==null?void 0:s.tagName)==="OL")&&(s==null?void 0:s.childNodes.length)===1&&Pu(s)}let r=Array.from(n.getElementsByTagName("li"));for(let a of r){let s=a.firstElementChild;if(s&&s.tagName==="DIV"){let l=Array.from(s.childNodes);s.replaceWith(...l)}}return n},$u=(n,{htmlContent:e})=>{let t=e.replace(/<!DOCTYPE en-note [^>]*>/,"<!DOCTYPE html>").replace(/(<a [^>]*)\/>/,"$1></a>").replace(/<div[^\/\<]*\/>/g,""),i=new DOMParser().parseFromString(I0(t),"text/html").getElementsByTagName("en-note").item(0),r=Nr(n).turndown(D0(F0(i))),o=new RegExp("<YARLE_NEWLINE_PLACEHOLDER>","g");return r=r.replace(o,""),r&&r!=="undefined"?{content:r}:{content:""}};var Of=Jr(require("crypto"));var bt=require("obsidian");var Bu=(n,e)=>({createdAt:L0(n),updatedAt:P0(n),sourceUrl:M0(n),location:$0(n),reminderTime:B0(n),reminderOrder:U0(n),reminderDoneTime:j0(n),notebookName:e});var L0=n=>!B.skipCreationTime&&n.created?(0,bt.moment)(n.created).format(B.dateFormat):"",P0=n=>!B.skipUpdateTime&&n.updated?(0,bt.moment)(n.updated).format(B.dateFormat):"",M0=n=>!B.skipSourceUrl&&n["note-attributes"]?n["note-attributes"]["source-url"]:"",$0=n=>!B.skipLocation&&n["note-attributes"]&&n["note-attributes"].longitude?`${n["note-attributes"].latitude},${n["note-attributes"].longitude}`:"",B0=n=>!B.skipReminderTime&&n["note-attributes"]&&n["note-attributes"]["reminder-time"]?(0,bt.moment)(n["note-attributes"]["reminder-time"]).format(B.dateFormat):"",U0=n=>!B.skipReminderOrder&&n["note-attributes"]&&n["note-attributes"]["reminder-order"]?n["note-attributes"]["reminder-order"]:"",j0=n=>!B.skipReminderDoneTime&&n["note-attributes"]&&n["note-attributes"]["reminder-done-time"]?(0,bt.moment)(n["note-attributes"]["reminder-done-time"]).format(B.dateFormat):"",Uu=n=>({tags:H0(n)}),H0=n=>{if(!B.skipTags&&n.tag){let e=Array.isArray(n.tag)?n.tag:[n.tag],t=B.nestedTags;return e.map(r=>{let o=r.toString().replace(/^#/,"");t&&(o=o.replace(new RegExp(Cn(t.separatorInEN),"g"),t.replaceSeparatorWith));let a=t&&t.replaceSpaceWith||"-";return o=o.replace(/ /g,a),`${B.useHashTags?"#":""}${o}`}).join(" ")}return""},Ha;try{Ha=window.require("btime")}catch(n){}var Or=(n,e)=>{let t=(0,bt.moment)(e.created).valueOf();t>0&&Ha&&Ha.btime(n,t);let r=(0,bt.moment)(e.updated).valueOf()/1e3;try{K.utimesSync(n,r,r)}catch(o){}},ju=n=>n["resource-attributes"]&&n["resource-attributes"].timestamp?(0,bt.moment)(n["resource-attributes"].timestamp):(0,bt.moment)();var Hu=(n,e,t)=>{let i=n.lastIndexOf(e);return i<0?n:n.substring(0,i)+t+n.substring(i+e.length)};var Ne={mdPath:"",resourcePath:""},Dr=249,qu=(n,e)=>ja(n,e).replace(/\s/g,"_"),Wu=(n,e)=>{if(n.length<=11)throw Error("FATAL: note folder directory path exceeds the OS limitation. Please pick a destination closer to the root folder.");return`${Wa()}${q.sep}${n}`.length<Dr?n:`${n.slice(0,Dr-11)}_${e}.md`},q0=(n,e,t)=>{let r=ke.getInstance().getNoteIdNameMapByNoteTitle(Dn(n.title))[0]||{uniqueEnd:_n(6)};if(e.length<=11)throw Error("FATAL: note folder directory path exceeds the OS limitation. Please pick a destination closer to the root folder.");return`${t.slice(0,Dr-11)}_${r.uniqueEnd}.md`},W0=(n,e,t)=>{let i=Eu(n,e,t),r=`${n}${q.sep}${Dn(i)}`;return r.length<Dr?r:q0(e,i,r)},Yu=n=>W0(Ne.mdPath,n,"md");var Y0=n=>{K.existsSync(n)&&(K.rmSync?K.rmSync(n,{recursive:!0,force:!0}):K.rmdirSync(n,{recursive:!0})),K.mkdirSync(n)},zu=n=>{let e=`${q.sep}${B.resourcesDir}`;return B.haveGlobalResources?`..${e}`:B.haveEnexLevelResources?`.${e}`:`.${e}${q.sep}${qu(Ne.mdPath,n)}.resources`},qa=n=>B.haveGlobalResources?q.resolve(Ne.resourcePath,"..","..",B.resourcesDir):B.haveEnexLevelResources?Ne.resourcePath:`${Ne.resourcePath}${q.sep}${qu(Ne.mdPath,n)}.resources`,Cr=new Map,Zu=n=>{let e=qa(n);Cr.has(e)||Cr.set(e,0);let t=Cr.get(e)||0;(B.haveEnexLevelResources||B.haveGlobalResources)&&t>=1||(Y0(e),Cr.set(e,t+1))},Gu=n=>{let e=n.split("@@@"),t=e.pop();return t||(t=n),{notebookName:t,notebookFolderNames:e}},Vu=n=>{if(!(n instanceof Be))throw new Error("Evernote import currently only works on desktop");let{notebookName:e}=Gu(n.basename);return{fullpath:Hu(n.fullpath,n.basename,e||n.basename),basename:e}},Ku=(n,e)=>{let{notebookFolderNames:t}=Gu(n.basename);return K.mkdirSync(q.join(e.outputDir,...t),{recursive:!0}),[e.outputDir,...t].join(e.pathSeparator)},Xu=(n,e)=>{let t=n.basename;Qu(t,e)},Ju=(n,e)=>{let t=n.basename;Qu(t,e)},Qu=(n,e)=>{let t=q.isAbsolute(e.outputDir)?e.outputDir:`${process.cwd()}${q.sep}${e.outputDir}`;Ne.mdPath=`${t}${q.sep}`,Ne.resourcePath=`${t}${q.sep}${e.resourcesDir}`,e.skipEnexFileNameFromOutputPath||(Ne.mdPath=`${Ne.mdPath}${n}`,Ne.resourcePath=`${t}${q.sep}${n}${q.sep}${e.resourcesDir}`),K.mkdirSync(Ne.mdPath,{recursive:!0}),!e.haveEnexLevelResources&&!e.haveGlobalResources&&K.mkdirSync(Ne.resourcePath,{recursive:!0}),console.log(`path ${Ne.mdPath} created`)},Wa=()=>Ne.mdPath;var ef=n=>!!n.resource,tf=n=>n["note-attributes"]&&(n["note-attributes"]["source-application"]==="webclipper.evernote"||n["note-attributes"].source==="web.clip7");var Ya={};Oe(Ya,{CONTENT_PLACEHOLDER:()=>z0,END_BLOCK:()=>G0,START_BLOCK:()=>Z0});var z0="{content}",Z0="{content-block}",G0="{end-content-block}";var $t=({template:n,check:e,startBlockPlaceholder:t,endBlockPlaceholder:i,valuePlaceholder:r,value:o})=>{if(o&&e())return n.replace(new RegExp(`${t}`,"g"),"").replace(new RegExp(`${i}`,"g"),"").replace(new RegExp(`${r}`,"g"),o);let a=`${t}([\\d\\D])(?:.|(\r
|\r|
))*?(?=${i})${i}`;return n.replace(new RegExp(a,"g"),"")};var Bt=(n,e,t,i)=>({template:n,check:e,startBlockPlaceholder:t.START_BLOCK,endBlockPlaceholder:t.END_BLOCK,valuePlaceholder:t.CONTENT_PLACEHOLDER,value:i});var nf=(n,e,t)=>{let i=Bt(e,t,Ya,n.content);return $t(i)};var wi={};Oe(wi,{CONTENT_PLACEHOLDER:()=>V0,END_BLOCK:()=>X0,START_BLOCK:()=>K0});var V0="{tags}",K0="{tags-block}",X0="{end-tags-block}";var rf=(n,e,t)=>{let i=Bt(e,t,wi,n.tags);return $t(i)};var za={};Oe(za,{CONTENT_PLACEHOLDER:()=>J0,END_BLOCK:()=>ey,START_BLOCK:()=>Q0});var J0="{title}",Q0="{title-block}",ey="{end-title-block}";var of=(n,e,t)=>{let i=Bt(e,t,za,n.title);return $t(i)};var pn={};Oe(pn,{CONTENT_PLACEHOLDER:()=>ty,END_BLOCK:()=>iy,START_BLOCK:()=>ny});var ty="{created-at}",ny="{created-at-block}",iy="{end-created-at-block}";var _e=(n,e,t)=>n.replace(new RegExp(`${e.CONTENT_PLACEHOLDER}`,"g"),t||"").replace(new RegExp(`${e.START_BLOCK}`,"g"),"").replace(new RegExp(`${e.END_BLOCK}`,"g"),"");var af=(n,e)=>_e(e,pn,n.createdAt);var un={};Oe(un,{CONTENT_PLACEHOLDER:()=>ry,END_BLOCK:()=>ay,START_BLOCK:()=>oy});var ry="{updated-at}",oy="{updated-at-block}",ay="{end-updated-at-block}";var sf=(n,e)=>_e(e,un,n.updatedAt);var fn={};Oe(fn,{CONTENT_PLACEHOLDER:()=>sy,END_BLOCK:()=>cy,START_BLOCK:()=>ly});var sy="{source-url}",ly="{source-url-block}",cy="{end-source-url-block}";var lf=(n,e)=>_e(e,fn,n.sourceUrl);var dn={};Oe(dn,{CONTENT_PLACEHOLDER:()=>py,END_BLOCK:()=>fy,START_BLOCK:()=>uy});var py="{location}",uy="{location-block}",fy="{end-location-block}";var cf=(n,e)=>_e(e,dn,n.location);var mn={};Oe(mn,{CONTENT_PLACEHOLDER:()=>dy,END_BLOCK:()=>hy,START_BLOCK:()=>my});var dy="{notebook}",my="{notebook-block}",hy="{end-notebook-block}";var pf=(n,e)=>_e(e,mn,n.notebookName);var In={};Oe(In,{CONTENT_PLACEHOLDER:()=>gy,END_BLOCK:()=>by,START_BLOCK:()=>yy});var gy="{reminder-time}",yy="{reminder-time-block}",by="{end-reminder-time-block}";var uf=(n,e)=>_e(e,In,n.reminderTime);var Fn={};Oe(Fn,{CONTENT_PLACEHOLDER:()=>xy,END_BLOCK:()=>vy,START_BLOCK:()=>wy});var xy="{reminder-order}",wy="{reminder-order-block}",vy="{end-reminder-order-block}";var ff=(n,e)=>_e(e,Fn,n.reminderOrder);var Ln={};Oe(Ln,{CONTENT_PLACEHOLDER:()=>Ey,END_BLOCK:()=>Ty,START_BLOCK:()=>_y});var Ey="{reminder-done-time}",_y="{reminder-done-time-block}",Ty="{end-reminder-done-time-block}";var df=(n,e)=>_e(e,Ln,n.reminderDoneTime);var vi={};Oe(vi,{CONTENT_PLACEHOLDER:()=>Ay,END_BLOCK:()=>Ny,START_BLOCK:()=>ky});var Ay="{tags-yaml-list}",ky="{tags-yaml-list-block}",Ny="{end-tags-yaml-list-block}";var mf=(n,e,t)=>{let i;n.tags&&(i=`
`+n.tags.split(" ").map(o=>`  - ${o.replace(/^#/,"")}`).join(`
`));let r=Bt(e,t,vi,i);return $t(r)};var Za="{metadata-block}",Ga="{end-metadata-block}";var hf=`\r?
?`;var me=(n,e)=>n.replace(new RegExp(`${e.START_BLOCK}(?<=${e.START_BLOCK})(.*)(?=${e.END_BLOCK})${e.END_BLOCK}${hf}`,"g"),"");var gf=n=>me(n,pn);var yf=n=>me(n,un);var bf=n=>me(n,fn);var xf=n=>me(n,mn);var wf=n=>me(n,dn);var Ir={};Oe(Ir,{CONTENT_PLACEHOLDER:()=>Ry,END_BLOCK:()=>Cy,START_BLOCK:()=>Oy});var Ry="{link-to-original}",Oy="{link-to-original-block}",Cy="{end-link-to-original-block}";var vf=n=>me(n,Ir);var Ef=n=>me(n,In);var _f=n=>me(n,Ln);var Tf=n=>me(n,Fn);var Af=(n,e)=>{let t=e.currentTemplate;return t=of(n,t,()=>n.title),t=rf(n,t,()=>!e.skipTags),t=mf(n,t,()=>!e.skipTags),t=nf(n,t,()=>n.content),t=vf(t),t=!e.skipCreationTime&&n.createdAt?af(n,t):gf(t),t=!e.skipUpdateTime&&n.updatedAt?sf(n,t):yf(t),t=!e.skipSourceUrl&&n.sourceUrl?lf(n,t):bf(t),t=!e.skipLocation&&n.location?cf(n,t):wf(t),t=e.isNotebookNameNeeded&&n.notebookName?pf(n,t):xf(t),t=!e.skipReminderTime&&n.reminderTime?uf(n,t):Ef(t),t=!e.skipReminderOrder&&n.reminderOrder?ff(n,t):Tf(t),t=!e.skipReminderDoneTime&&n.reminderDoneTime?df(n,t):_f(t),t=t.replace(Za,"").replace(Ga,""),t};var kf=(n,e,t)=>{try{K.writeFileSync(n,e),Or(n,t)}catch(i){throw console.error("Cannot write file ",i),i}};var Nf=(n,e)=>{let t=Yu(e);ke.getInstance().setCurrentNotePath(t),kf(t,n,e),console.log(`Note saved to ${t}`)};var Cf=n=>{let e=new RegExp(`\\${q.sep}`,"g"),t=zu(n).replace(e,B.pathSeparator||"/");return{absoluteResourceWorkDir:qa(n),relativeResourceWorkDir:t}},Df=n=>{let e={},t=n.content,{absoluteResourceWorkDir:i,relativeResourceWorkDir:r}=Cf(n);if(console.log(`relative resource work dir: ${r}`),console.log(`absolute resource work dir: ${i}`),Zu(n),Array.isArray(n.resource))for(let o of n.resource)e={...e,...Rf(i,o)};else e={...e,...Rf(i,n.resource)};for(let o of Object.keys(e))t=Dy(t,e,o,r);return t},Dy=(n,e,t,i)=>{let r=`${i}${B.pathSeparator}${e[t].fileName.replace(/ /g," ")}`;console.log(`mediaReference src ${r} added`);let o,a=`<en-media ([^>]*)hash="${t}".([^>]*)>`,s=new RegExp(a,"g"),l=n.match(s),c=l&&l.length>0&&l[0].split("type=");if(c&&c.length>1&&c[1].startsWith('"image')){let p=l[0].match(/width="(\w+)"/),m=p?` width="${p[1]}"`:"",g=l[0].match(/height="(\w+)"/),b=g?` height="${g[1]}"`:"";o=n.replace(s,`<img src="${r}"${m}${b} alt="${e[t].fileName}">`)}else o=n.replace(s,`<a href="${r}" type="file">${e[t].fileName}</a>`);return o},Rf=(n,e)=>{let t={},i=e.data.$text,r=ju(e),a=vu(n,e).fileName,s=`${n}${q.sep}${a}`;console.log(e),console.log(i);let l=Buffer.from(i,"base64");K.writeFileSync(s,l);let c=r.valueOf()/1e3;try{K.utimesSync(s,c,c)}catch(p){}if(e.recognition&&a){let p=e.recognition.match(/[a-f0-9]{32}/);console.log(`resource ${a} added with hash ${p}`),t[p]={fileName:a,alreadyUsed:!1}}else{let p=Of.default.createHash("md5");p.update(l);let m=p.digest("hex");t[m]={fileName:a,alreadyUsed:!1}}return t},If=(n,e)=>{if(e.indexOf('src="data:')<0)return e;let{absoluteResourceWorkDir:t,relativeResourceWorkDir:i}=Cf(n);return K.mkdirSync(t,{recursive:!0}),e.replace(/src="data:([^;,]*)(;base64)?,([^"]*)"/g,(r,o,a,s)=>{let l=Iy(o,a===";base64",s,t,n);return`src="${`${i}${B.pathSeparator}${l}`}"`})},Iy=(n,e,t,i,r)=>{let o="embedded",a=Fy(n)||".dat",s=xi(i,o),l=s<1?`${o}.${a}`:`${o}.${s}.${a}`,c=`${i}${q.sep}${l}`;return e||(t=decodeURIComponent(t)),K.writeFileSync(c,t,e?"base64":void 0),Or(c,r),console.log(`data url resource ${l} added`),l},Fy=n=>n.split("/").pop().split("+")[0];var Ff=(n,e)=>{let t=new Date;ke.getInstance().setCurrentNoteName(n.title),Array.isArray(n.content)&&(n.content=n.content.join(""));let r={title:n.title,content:n.content,htmlContent:n.content,originalContent:n.content};console.log(`Converting for note "${r.title}" started at ${t}...`);try{ef(n)&&(r.htmlContent=Df(n)),r.htmlContent=If(n,r.htmlContent),r={...r,...$u(B,r)},r={...r,...Bu(n,e)},r={...r,...Uu(n)};let o=Af(r,B);Nf(o,n)}catch(o){throw console.error(`Failed to convert note: ${r.title}`,o),o}finally{let o=new Date,a=(o.getTime()-t.getTime())/1e3;console.log(`Conversion for note "${r.title}" finished at ${o}. Took ${a} seconds`)}};var Mf=require("obsidian");var Lf=n=>n.taskstatus==="open"?Sr:Rr;var Ly="\u{1F53C}",Py="\u{1F53D}",My="\u{1F4C5}",$y="\u23F3",$f=(n,e)=>{let t=Lf(n),i=n.title?` ${n.title}`:"",r=B.obsidianTaskTag!==""?` ${B.obsidianTaskTag}`:"",o=n.duedate&&!isNaN(n.duedate.getTime())?` ${My} ${Pf(n.duedate)}`:"",a=n.reminderdate?` ${$y} ${Pf(n.reminderdate)}`:"",s=n.taskflag?` ${Ly}`:` ${Py}`;return`${t}${r}${i}${o}${a}${s}`},Pf=n=>(0,Mf.moment)(n).format("YYYY-MM-DD").toString();var Ka=(n,e,t)=>{let i=K.readdirSync(n);return e=e||[],i.forEach(r=>{K.statSync(`${n}${q.sep}${r}`).isDirectory()?e=Ka(`${n}${q.sep}${r}`,e,t):(t&&q.extname(r)==`.${t}`||!t)&&e.push(q.join(n,"/",r))}),e};var Bf=(n,e)=>{let i=ke.getInstance().getAllNoteIdNameMap(),r=Object.entries(i);if(r.length===0)return;console.log("About to update links...");let o=[];for(let a of e)Ka(a,o,"");for(let a of e){console.log(`Notebook: ${a}`);let l=K.readdirSync(a).filter(c=>q.extname(c).toLowerCase()===".md");console.log(`${l.length} files to check for links`);for(let c of l){let p=q.join(a,c),m=K.readFileSync(p,"utf8"),g=m;for(let[b,E]of r){let f=E.uniqueEnd,d=E.title;o.find(O=>O.includes(f))&&(d=Wu(d,f));let h=E.notebookName,v=n.urlEncodeFileNamesAndLinks?encodeURI(d):d,_=v;h&&!a.endsWith(h)&&(_=`${h}/${v}`);let A=new RegExp(Cn(b),"g");g=g.replace(A,_)}m!==g&&(console.log(`File written: ${p}`),K.writeFileSync(p,g))}}console.log("Link update complete.")};var Uf=n=>hn(pn,n);var jf=n=>hn(dn,n),Hf=n=>hn(mn,n);var qf=n=>hn(fn,n),Wf=n=>hn(wi,n)||hn(vi,n);var Yf=n=>hn(un,n),hn=(n,e)=>e.includes(n.START_BLOCK)&&e.includes(n.CONTENT_PLACEHOLDER)&&e.includes(n.END_BLOCK);var zf=`---
`,By=`{source-url-block}source: {source-url}{end-source-url-block}
`,Uy=`{tags-yaml-list-block}
tags: {tags-yaml-list}

{end-tags-yaml-list-block}`,jy=`{content-block}{content}{end-content-block}
`,Zf=zf+Uy+By+zf+jy;var i1=nd.Platform.isDesktopApp?td():null,Pr={enexSources:[],currentTemplate:"",outputDir:"./mdNotes",isMetadataNeeded:!1,isNotebookNameNeeded:!1,isZettelkastenNeeded:!1,useZettelIdAsFilename:!1,plainTextNotesOnly:!1,skipWebClips:!1,useHashTags:!0,nestedTags:{separatorInEN:"_",replaceSeparatorWith:"/",replaceSpaceWith:"-"},obsidianTaskTag:"",urlEncodeFileNamesAndLinks:!1,sanitizeResourceNameSpaces:!1,replacementChar:"_",pathSeparator:"/",resourcesDir:"_resources",turndownOptions:{headingStyle:"atx"}},r1="@@@",B={...Pr};function o1(n){return n==null?n:JSON.parse(JSON.stringify(n))}function id(n,...e){for(let t of e)for(let i of Object.keys(t)){let r=t[i],o=n[i];!Array.isArray(r)&&typeof r=="object"&&!Array.isArray(o)&&typeof o=="object"?n[i]=id({},o,r):n[i]=o1(r)}return n}var a1=n=>{B=id({},Pr,n);let e=B.templateFile?K.readFileSync(B.templateFile,"utf-8"):Zf;e=B.currentTemplate?B.currentTemplate:e,B.skipCreationTime=!Uf(e),B.skipLocation=!jf(e),B.skipSourceUrl=!qf(e),B.skipTags=!Wf(e),B.skipUpdateTime=!Yf(e),B.isNotebookNameNeeded=Hf(e),B.currentTemplate=e,console.log(`Current config is: ${JSON.stringify(B,null,4)}`),console.log(`Path separator:${q.sep}`)},s1=async(n,e,t)=>{if(!(e instanceof Be))throw new Error("Evernote import currently only works on desktop");let i=ke.getInstance();t.status("Processing "+e.name),console.log(`Getting stream from ${e}`);let r=e.createReadStream(),o={},a=i.getCurrentNotebookName();return new Promise((s,l)=>{let c=g=>(t.reportFailed(i.getCurrentNotebookFullpath(),g),l(g)),p=i1(r),m=null;p.on("tag:note-attributes",g=>{m=g}),p.on("tag:note",g=>{if(t.isCancelled()){r.close();return}if(n.skipWebClips&&tf(g))t.reportSkipped(g.title);else{t.status("Importing note "+g.title),m&&(g["note-attributes"]=m);try{Ff(g,a),t.reportNoteSuccess(a+"/"+g.title)}catch(E){return t.reportFailed(g.title||e,E),s()}}m=null;let b=i.getCurrentNotePath();if(b)for(let E of Object.keys(o)){let f=`<YARLE-EN-V10-TASK>${E}</YARLE-EN-V10-TASK>`,d=K.readFileSync(b,"utf8"),h=new Map([...o[E]].sort()),v=d.replace(f,[...h.values()].join(`
`));K.writeFileSync(b,v)}}),p.on("tag:task",g=>{let b=Qp(g);o[b.taskgroupnotelevelid]||(o[b.taskgroupnotelevelid]=new Map),o[b.taskgroupnotelevelid].set(b.sortweight,$f(b,a))}),p.on("end",s),p.on("error",c),r.on("error",c)})};async function rd(n,e){a1(n);let t=[],i=n.outputDir;for(let r of n.enexSources){if(e.isCancelled())return;let o,a=ke.getInstance();r.basename.includes(r1)?(n.outputDir=Ku(r,n),o=Vu(r),Ju(o,n),a.setCurrentNotebookName(o.basename),a.setCurrentNotebookFullpath(o.fullpath)):(Xu(r,n),a.setCurrentNotebookName(r.basename),a.setCurrentNotebookFullpath(r.fullpath)),await s1(n,r,e),t.push(Wa()),n.outputDir=i}e.isCancelled()||await Bf(n,t)}var Mr=class extends ue{init(){this.addFileChooserSetting("Evernote",["enex"],!0),this.addOutputLocationSetting("Evernote")}async import(e){let{files:t}=this;if(t.length===0){new _i.Notice("Please pick at least one file to import.");return}let i=await this.getOutputFolder();if(!i){new _i.Notice("Please select a location to export to.");return}let{app:r}=this,o=r.vault.adapter;if(!(o instanceof _i.FileSystemAdapter))return;let a={...Pr,enexSources:t,outputDir:q.join(o.getBasePath(),i.path)};await rd(a,e)}};var Se=require("obsidian");var $r=class extends ue{init(){this.addFileChooserSetting("HTML",["htm","html"],!0),this.addAttachmentSizeLimit(0),this.addMinimumImageSize(65),this.addOutputLocationSetting("HTML import")}addAttachmentSizeLimit(e){this.attachmentSizeLimit=e*10**6,new Se.Setting(this.modal.contentEl).setName("Attachment size limit (MB)").setDesc("Set 0 to disable.").addText(t=>t.then(({inputEl:i})=>{i.type="number",i.step="0.1"}).setValue(e.toString()).onChange(i=>{let r=["+","-"].includes(i)?0:Number(i);if(Number.isNaN(r)||r<0){t.setValue((this.attachmentSizeLimit/10**6).toString());return}this.attachmentSizeLimit=r*10**6}))}addMinimumImageSize(e){this.minimumImageSize=e,new Se.Setting(this.modal.contentEl).setName("Minimum image size (px)").setDesc("Set 0 to disable.").addText(t=>t.then(({inputEl:i})=>i.type="number").setValue(e.toString()).onChange(i=>{let r=["+","-"].includes(i)?0:Number(i);if(!Number.isInteger(r)||r<0){t.setValue(this.minimumImageSize.toString());return}this.minimumImageSize=r}))}async import(e){let{files:t}=this;if(t.length===0){new Se.Notice("Please pick at least one file to import.");return}let i=await this.getOutputFolder();if(!i){new Se.Notice("Please select a location to export to.");return}let r=new Map;e.reportProgress(0,t.length);for(let l=0;l<t.length;l++){if(e.isCancelled())return;let c=t[l],p=await this.processFile(e,i,c);p&&r.set(c instanceof Be?ni.pathToFileURL(c.filepath).href:c.name,{file:c,tFile:p}),e.reportProgress(l+1,t.length)}let{metadataCache:o}=this.app,a,s=new Promise(l=>{a=l});o.onCleanCache(async()=>{for(let[l,{file:c,tFile:p}]of r){if(e.isCancelled())break;try{let m=await this.app.vault.cachedRead(p),g=o.computeMetadataAsync?await o.computeMetadataAsync(Lo(m)):o.getFileCache(p);if(!g)continue;let b=[];if(g.links)for(let{link:E,position:f,displayText:d}of g.links){let{path:h,subpath:v}=(0,Se.parseLinktext)(E),_;if(ni){let O=new URL(encodeURI(h),l);O.hash="",O.search="",_=decodeURIComponent(O.href)}else _=oe(h.replace(/#/gu,"%23")).name;let A=r.get(_);if(A){let O=this.app.fileManager.generateMarkdownLink(A.tFile,p.path,v,d);b.push({from:f.start.offset,to:f.end.offset,text:O})}}b.sort((E,f)=>f.from-E.from);for(let E of b)m=m.substring(0,E.from)+E.text+m.substring(E.to);await this.vault.modify(p,m)}catch(m){e.reportFailed(c.fullpath,m)}}a()}),await s}async processFile(e,t,i){e.status("Processing "+i.name);try{let r=await i.readText(),o=dt(r);l1(o);let a=i instanceof Be?ni.pathToFileURL(i.filepath):void 0,s=new Map,l=new Map;for(let m of o.findAll("img, audio, video")){if(e.isCancelled())return;let g=m.getAttribute("src");if(g)try{let b=new URL(g.startsWith("//")?`https:${g}`:g,a),E=b.href,f=s.get(E);s.has(E)||(e.status("Downloading attachment for "+i.name),f=await this.downloadAttachment(t,m,b),s.set(E,f),f?(l.set(f.path,f),e.reportAttachmentSuccess(f.name)):e.reportSkipped(g)),f&&(m.setAttribute("src",f.path.replace(/ /g,"%20")),m instanceof HTMLImageElement||m.replaceWith(createEl("img",{attr:{src:f.path.replace(/ /g,"%20"),alt:m.getAttr("alt")}})))}catch(b){e.reportFailed(g,b)}}let c=(0,Se.htmlToMarkdown)(o),p=await this.saveAsMarkdownFile(t,i.basename,c);if(!Object.isEmpty(s)){let{metadataCache:m}=this.app,g;m.computeMetadataAsync?g=await m.computeMetadataAsync(Lo(c)):g=await new Promise(E=>{let f=m.getFileCache(p);if(f)return E(f);let d=m.on("changed",(h,v,_)=>{h===p&&(m.offref(d),E(_))})});let b=[];if(g.embeds){for(let{link:E,position:f}of g.embeds)if(l.has(E)){let d=this.app.fileManager.generateMarkdownLink(l.get(E),p.path);b.push({from:f.start.offset,to:f.end.offset,text:d})}}b.sort((E,f)=>f.from-E.from);for(let E of b)c=c.substring(0,E.from)+E.text+c.substring(E.to);await this.vault.modify(p,c)}return e.reportNoteSuccess(i.fullpath),p}catch(r){e.reportFailed(i.fullpath,r)}return null}async downloadAttachment(e,t,i){let r="",o="",a;switch(i.protocol){case"file:":let c=ni.fileURLToPath(i.href);({basename:r,extension:o}=oe(c)),a=Ki(await $e.readFile(c));break;case"https:":case"http:":let p=await p1(i),m=c1(i);r=m.basename,a=p.data,o=kr(p.mime)||m.extension;break;default:throw new Error(i.href)}if(!this.filterAttachmentSize(a)||t instanceof HTMLImageElement&&!await this.filterImageSize(a))return null;if(!o)if(t instanceof HTMLImageElement)o="png";else if(t instanceof HTMLAudioElement)o="mp3";else if(t instanceof HTMLVideoElement)o="mp4";else return null;let s=await this.createFolders((0,Se.normalizePath)(e.path+"/Attachments")),l=await this.vault.getAvailablePath(s.getParentPrefix()+r,o);return await this.vault.createBinary(l,a)}filterAttachmentSize(e){let{byteLength:t}=e;return!this.attachmentSizeLimit||t<=this.attachmentSizeLimit}async filterImageSize(e){if(!this.minimumImageSize)return!0;let t;try{t=await u1(e)}catch(o){return!0}let{height:i,width:r}=t;return r>=this.minimumImageSize&&i>=this.minimumImageSize}};function od(n,e){let t=n.getAttribute(e);t!==null&&n.setAttribute(e,t.replace(/ /gu,"%20"))}function l1(n){n.findAll("a").forEach(e=>od(e,"href")),n.findAll("audio, img, video").forEach(e=>od(e,"src"))}function c1(n){return oe((0,Se.normalizePath)(decodeURIComponent(n.pathname)))}async function p1(n){var t,i;try{let r=await fetch(n,{mode:"cors",referrerPolicy:"no-referrer"});if(r.ok)return{data:await r.arrayBuffer(),mime:(t=r.headers.get("Content-Type"))!=null?t:""}}catch(r){}let e=await(0,Se.requestUrl)(n.href);return{data:e.arrayBuffer,mime:(i=e.headers["Content-Type"])!=null?i:""}}async function u1(n){let e=new Image,t=URL.createObjectURL(new Blob([n]));try{return await new Promise((i,r)=>{e.addEventListener("error",({error:o})=>r(o),{once:!0,passive:!0}),e.addEventListener("load",()=>i({height:e.naturalHeight,width:e.naturalWidth}),{once:!0,passive:!0}),e.src=t})}finally{URL.revokeObjectURL(t)}}var Mn=require("obsidian");var f1=/(#[^ ^#]*)/g,d1=/[\\:*?<>\"|!@#$%^&()+=\`\'~;,.]/g;function Qa(n){let e=n.replace(d1,"");return e=e.split(" ").join("-"),isNaN(e[0])||(e="_"+e),e}function es(n){return n.replace(f1,e=>"#"+Qa(e))}function ad(n){return n.charAt(0).toUpperCase()+n.slice(1).toLowerCase()}var m1=["zip"],h1=["json"],g1=["html","txt"],Br=class extends ue{constructor(){super(...arguments);this.importArchived=!1;this.importTrashed=!1}init(){this.addFileChooserSetting("Notes & attachments",[...m1,...h1,...$n],!0),this.importArchivedSetting=new Mn.Setting(this.modal.contentEl).setName("Import archived notes").setDesc("If imported, files archived in Google Keep will be tagged as archived.").addToggle(t=>{t.setValue(this.importArchived),t.onChange(async i=>{this.importArchived=i})}),this.importTrashedSetting=new Mn.Setting(this.modal.contentEl).setName("Import deleted notes").setDesc("If imported, files deleted in Google Keep will be tagged as deleted. Deleted notes will only exist in your Google export if deleted recently.").addToggle(t=>{t.setValue(this.importTrashed),t.onChange(async i=>{this.importTrashed=i})}),this.addOutputLocationSetting("Google Keep")}async import(t){let{files:i}=this;if(i.length===0){new Mn.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new Mn.Notice("Please select a location to import your files to.");return}let o=`${r.path}/Assets`;for(let a of i){if(t.isCancelled())return;await this.handleFile(a,r,o,t)}}async handleFile(t,i,r,o){let{fullpath:a,name:s,extension:l}=t;o.status("Processing "+s);try{l==="zip"?await this.readZipEntries(t,i,r,o):l==="json"?await this.importKeepNote(t,i,o):$n.contains(l)?(o.status("Importing attachment "+s),await this.copyFile(t,r),o.reportAttachmentSuccess(a)):!(t instanceof gi)&&!g1.contains(l)&&o.reportSkipped(a)}catch(c){o.reportFailed(a,c)}}async readZipEntries(t,i,r,o){await yt(t,async(a,s)=>{for(let l of s){if(o.isCancelled())return;await this.handleFile(l,i,r,o)}})}async importKeepNote(t,i,r){let{fullpath:o,basename:a}=t;r.status("Importing note "+a);let s=await t.readText(),l=JSON.parse(s);if(!l||!l.userEditedTimestampUsec||!l.createdTimestampUsec){r.reportFailed(o,"Invalid Google Keep JSON");return}if(l.isArchived&&!this.importArchived){r.reportSkipped(o,"Archived note");return}if(l.isTrashed&&!this.importTrashed){r.reportSkipped(o,"Deleted note");return}await this.convertKeepJson(l,i,a),r.reportNoteSuccess(o)}async copyFile(t,i){let r=await this.createFolders(i),o=await t.read();await this.vault.createBinary(`${r.path}/${t.name}`,o)}async convertKeepJson(t,i,r){let o=[],a={};if(t.title){let c=t.title.split(`
`).filter(p=>p!==r);c.length>0&&(a.aliases=c)}let s=[];if(t.color&&t.color!=="DEFAULT"){let c=t.color.toLowerCase();c=ad(c),s.push(`Keep/Color/${c}`)}if(t.isPinned&&s.push("Keep/Pinned"),t.attachments&&s.push("Keep/Attachment"),t.isArchived&&s.push("Keep/Archived"),t.isTrashed&&s.push("Keep/Deleted"),t.labels)for(let c of t.labels)s.push(`Keep/Label/${c.name}`);if(s.length>0&&(a.tags=s.map(c=>Qa(c))),o.push(Xi(a)),t.textContent&&(o.push(`
`),o.push(es(t.textContent))),t.listContent){let c=[];for(let p of t.listContent){if(!p.text)continue;let m=`- [${p.isChecked?"X":" "}] ${p.text}`;c.push(es(m))}o.push(`

`),o.push(c.join(`
`))}if(t.attachments){o.push(`

`);for(let c of t.attachments)o.push(`![[${c.filePath}]]`)}let l=await this.saveAsMarkdownFile(i,r,o.join(""));await this.vault.append(l,"",{ctime:t.createdTimestampUsec/1e3,mtime:t.userEditedTimestampUsec/1e3})}};var wt=require("obsidian");var ts=require("obsidian");function sd({info:n,vault:e,targetFolderPath:t,parentsInSubfolders:i}){let r=e.getAllLoadedFiles(),o=new Set,a=new Set(r.map(s=>s.name));i&&b1(n),y1({info:n,pathDuplicateChecks:o,titleDuplicateChecks:a}),x1({info:n,loadedFiles:r,titleDuplicateChecks:a,targetFolderPath:t})}function y1({info:n,pathDuplicateChecks:e,titleDuplicateChecks:t}){for(let i of Object.values(n.idsToFileInfo)){let r=n.getPathForFile(i);if(e.has(`${r}${i.title}`)){let o=2;for(i.title=i.title+" "+o;e.has(`${r}${i.title}`);)o++,i.title=`${i.title.replace(/ \d+$/,"")} ${o}`}t.has(i.title+".md")&&(i.fullLinkPathNeeded=!0),e.add(`${r}${i.title}`),t.add(i.title+".md")}}function b1(n){let e=new Set(Object.values(n.idsToFileInfo).map(t=>t.parentIds).concat(Object.values(n.pathsToAttachmentInfo).map(t=>t.parentIds)).map(t=>t.length>0?t[t.length-1]:""));for(let t of Object.keys(n.idsToFileInfo))e.has(t)&&n.idsToFileInfo[t].parentIds.push(t)}function x1({info:n,loadedFiles:e,titleDuplicateChecks:t,targetFolderPath:i}){var l;let r=new Set(e.filter(c=>!c.path.endsWith(".md")).map(c=>c.path)),o=n.attachmentPath,a=/^\.\//.test(o),s=(l=o.match(/\.\/(.*)/))==null?void 0:l[1];for(let c of Object.values(n.pathsToAttachmentInfo)){t.has(c.nameWithExtension)&&(c.fullLinkPathNeeded=!0);let p="";if(a?p=(0,ts.normalizePath)(`${i}${n.getPathForFile(c)}${s!=null?s:""}`):p=(0,ts.normalizePath)(o+"/"),p.endsWith("/")||(p+="/"),r.has(p+c.nameWithExtension)){let m=2,{basename:g,extension:b}=oe(c.path);for(;r.has(`${p}${g} ${m}.${b}`);)m++;c.nameWithExtension=`${g} ${m}.${b}`}c.targetParentFolder=p,r.add(p+c.nameWithExtension),t.add(c.nameWithExtension)}}var Bn=require("obsidian");var ld=n=>n.replace(/-/g,"").replace(/[ -]?[a-z0-9]{32}(\.|$)/,"$1"),xt=n=>{var e;return(e=n.replace(/-/g,"").match(/([a-z0-9]{32})(\?|\.|$)/))==null?void 0:e[1]},ns=n=>{let{parent:e}=oe(n);return e.split("/").map(t=>xt(t)).filter(t=>t)};function is(n){return n.hour()===0&&n.minute()===0?n.format("YYYY-MM-DD"):n.format("YYYY-MM-DDTHH:mm")}function cd(n){return n.replace(/^(\.\.\/)+/,"")}function pd(n){let e=/#\d*?(?:[-_/a-z]|[^\x00-\x7F])/gi;if(!e.test(n))return n;let t=n.split(`
`);for(let i=0;i<t.length;i++){let r=t[i].match(e);if(!r)continue;let o=t[i];for(let a of r)new RegExp(`\\[\\[[^\\]]*${a}(?:.*[^\\]])?\\]\\]|\\[[^\\]]*${a}[^\\]]*\\]\\([^\\)]*\\)|\\[[^\\]]*\\]\\([^\\)]*${a}[^\\)]*\\)|\\\\${a}|\`[^\`]*${a}[^\`]*\``).test(o)||(o=o.replace(a,"\\"+a));t[i]=o}return n=t.join(`
`),n}function rs(n){n.replaceWith(...Array.from(n.childNodes))}async function wd(n,e){var p;let t=await e.readText(),i=dt(t),r=i.find("div[class=page-body]");if(r===null)throw new Error("page body was not found");let o=fd(n,r);xd(n,o,!0);let a={},s=i.find("table[class=properties] > tbody");if(s){let m=fd(n,s);xd(n,m,!1),U1(s);for(let g of Array.from(s.rows)){let b=w1(g);b&&(b.title=="Tags"&&(b.title="tags",typeof b.content=="string"?b.content=b.content.replace(/ /g,"-"):b.content instanceof Array&&(b.content=b.content.map(E=>E.replace(/ /g,"-")))),a[b.title]=b.content)}}O1(r,["strong","em","mark","del"]),N1(r),E1(r),P1(r),A1(r),L1(r),_d(r),yd(r,"div.indented"),yd(r,"details"),M1(r),bd(r,"ul"),bd(r,"ol"),$1(r),B1(r),F1(r),S1(r);let l=(0,Bn.htmlToMarkdown)(r.innerHTML);n.singleLineBreaks&&(l=l.replace(/\n\n(?!>)/g,`
`)),l=pd(l),l=v1(l);let c=(p=i.find("p[class*=page-description]"))==null?void 0:p.textContent;return c&&(l=c+`

`+l),Xi(a)+l}var ud={checkbox:["checkbox"],date:["created_time","last_edited_time","date"],list:["file","multi_select","relation"],number:["number","auto_increment_id"],text:["email","person","phone_number","text","url","status","select","formula","rollup","last_edited_by","created_by"]};function w1(n){var a,s,l,c,p,m;let e=(a=n.className.match(/property-row-(.*)/))==null?void 0:a[1];if(!e)throw new Error("property type not found for: "+n);let t=(0,Bn.htmlToMarkdown)((s=n.cells[0].textContent)!=null?s:""),i=n.cells[1],r=Object.keys(ud).find(g=>ud[g].includes(e));if(!r)throw new Error("type not found for: "+i);let o="";switch(r){case"checkbox":o=i.innerHTML.includes("checkbox-on");break;case"number":if(o=Number(i.textContent),isNaN(o))return;break;case"date":_d(i);let g=i.getElementsByTagName("time");if(g.length===0)o="";else if(g.length===1)o=is((0,Bn.moment)((l=g.item(0))==null?void 0:l.textContent));else{let f=[];for(let d=0;d<g.length;d++)f.push(is((0,Bn.moment)((c=g.item(d))==null?void 0:c.textContent)));o=f.join(" - ")}if(o.length===0)return;break;case"list":let b=i.children,E=[];for(let f=0;f<b.length;f++){let d=(p=b.item(f))==null?void 0:p.textContent;d&&E.push(d)}if(o=E,o.length===0)return;break;case"text":if(o=(m=i.textContent)!=null?m:"",o.length===0)return;break}return{title:t,content:o}}function fd(n,e){var i,r;let t=[];for(let o of e.findAll("a")){let a=cd(decodeURI((i=o.getAttribute("href"))!=null?i:"")),s=xt(a),l=Object.keys(n.pathsToAttachmentInfo).find(c=>c.includes(a));s&&a.endsWith(".html")?t.push({type:"relation",a:o,id:s}):l?t.push({type:"attachment",a:o,path:l}):s&&a.startsWith("#")&&((r=o.parentElement)!=null&&r.classList.contains("table_of_contents-item"))&&t.push({type:"toc-item",a:o,id:s})}return t}function v1(n){let e=/\[\[[^\]]*(\\\\)\|[^\]]*\]\]/,t=n.match(new RegExp(e,"g"));return t==null||t.forEach(i=>{n=n.replace(i,i.replace(/\\\\\|/g,"\\|"))}),n}function E1(n){R1(n,"style"),vd(n,"span.notion-text-equation-token");let e=n.ownerDocument,t=n.findAll("figure.equation");for(let r of t){let o=r.find("annotation");if(!o)continue;let a=e.createElement("div");a.className="annotation",a.appendText(`$$${dd(o.textContent)}$$`),r.replaceWith(a)}let i=n.findAll("span.notion-text-equation-token");for(let r of i){let o=r.find("annotation");o&&r.replaceWith(`$${dd(o.textContent,!0)}$`)}}function dd(n,e=!1){var i;let t=new RegExp(/^(?:\s|\\\\|\\\s)*(.*?)[\s\\]*$/,"s");return(i=n==null?void 0:n.replace(t,"$1").replace(/[\r\n]+/g,e?" ":`
`))!=null?i:""}function _1(n){var t;let e=(t=n.match(/^[^\.\?\!\n]*[\.\?\!]?/))==null?void 0:t[0];return e!=null?e:""}function T1(n){var e;return!!/callout|bookmark/.test((e=n.getAttribute("class"))!=null?e:"")}function A1(n){var t;let e=n.ownerDocument;for(let i of n.findAll("figure.callout")){let r=(t=i.lastElementChild)==null?void 0:t.childNodes;if(!r)continue;let o=e.createElement("blockquote");o.append(...Array.from(r)),k1(o),i.replaceWith(o)}}function k1(n){var o,a,s;let e=n.firstChild,t=(o=e==null?void 0:e.nodeName)!=null?o:"",i=n.ownerDocument.createElement("p"),r="";t=="#text"?r=(a=e==null?void 0:e.textContent)!=null?a:"":t=="P"?r=e.innerHTML:["EM","STRONG","DEL","MARK"].includes(t)?r=e.outerHTML:n.prepend(i),r=r.replace(/<br>/g,"&lt;br&gt;"),i.innerHTML=`[!important] ${r}`,(s=n.firstChild)==null||s.replaceWith(i)}function N1(n){var e,t,i;for(let r of n.findAll("a.bookmark.source")){let o=r.getAttribute("href"),a=(e=r.find("div.bookmark-title"))==null?void 0:e.textContent,s=_1((i=(t=r.find("div.bookmark-description"))==null?void 0:t.textContent)!=null?i:""),l=`> [!info] ${a}
> ${s}
> [${o}](${o})
`;r.nextElementSibling&&T1(r.nextElementSibling)&&(l+=`
`),r.replaceWith(l)}}function S1(n){var e,t,i;for(let r of n.findAll("span[class=user]"))r.innerText=(e=r.textContent)!=null?e:"";for(let r of n.findAll("td div[class*=checkbox]")){let o=createSpan();o.setText(r.hasClass("checkbox-on")?"X":""),r.replaceWith(o)}for(let r of n.findAll("table span[class*=selected-value]"))((t=r.parentElement)==null?void 0:t.lastElementChild)!==r&&r.setText(r.textContent+", ");for(let r of n.findAll("a[href]"))if(!/^(https?:\/\/|www\.)/.test(r.href)){let o=createSpan();o.setText((i=r.textContent)!=null?i:""),r.replaceWith(o)}}function R1(n,e){for(let t of n.findAll(e))t.remove()}function O1(n,e){for(let t of e)C1(n,t);for(let t of e)D1(n,t);for(let t of e)vd(n,t);for(let t of e)I1(n,t)}function C1(n,e){for(let t of n.findAll(e)){if(!t.parentElement||t.parentElement.tagName===e.toUpperCase())continue;let i=t.find(e);for(;i;)rs(i),i=t.find(e)}}function D1(n,e){let t=n.findAll(e);if(!t)return;let i=new RegExp(`</${e}>( *)<${e}>`,"g");for(let r of t){if(!r||!r.parentElement)continue;let o=r.parentElement,a=o==null?void 0:o.innerHTML;o.innerHTML=a==null?void 0:a.replace(i,"$1")}}function vd(n,e){let t=n.findAll(e);if(t)for(let i of t){let r=i.previousSibling;(r==null?void 0:r.nodeName)=="BR"&&(r==null||r.remove())}}function I1(n,e){let t=n.innerHTML,i=t.match(new RegExp(`<${e}>.*?</${e}>`,"sg"));if(i){for(let r of i.filter(o=>o.includes("<br>")))t=t.replace(r,r.split("<br>").join(`</${e}><br><${e}>`));n.innerHTML=t}}function md(n){var e;return Number((e=n==null?void 0:n.classList[1].slice(-1))!=null?e:-1)}function Ed(n,e){let t=md(n),i=md(e);if(t>i&&e.childElementCount==1){let r=createEl("ul");r.append(n),e.append(r)}else if(t>i&&e.childElementCount==2){let r=e.lastElementChild;r==null||r.append(n)}else if(t==i){let r=e.parentElement;r==null||r.append(n)}else t<i&&(e=e.parentElement.parentElement,Ed(n,e))}function hd(n){var t;let e=createEl("li");return e.className=n.className,e.append((t=n.firstElementChild)!=null?t:""),e}function F1(n){let e=n.find(".table_of_contents"),t=e==null?void 0:e.children;if(!e||t.length==0)return;let i=createEl("ul"),r=hd(t[0]);i.append(r);let o=r;for(let a=1;a<t.length;a++)r=hd(t[a]),Ed(r,o),o=r;e.replaceWith(i)}function L1(n){n.innerHTML=n.innerHTML.replace(/(?:\n|<br ?\/>)/g,"<br>");for(let e of n.findAll("code"))for(let t of e.findAll("br"))t.replaceWith(`
`)}function P1(n){var e;for(let t of n.findAll("link"))t.innerText=(e=t.textContent)!=null?e:""}function _d(n){var e,t;for(let i of n.findAll("time"))i.textContent=(t=(e=i.textContent)==null?void 0:e.replace(/@/g,""))!=null?t:""}var gd={"1.875em":"h1","1.5em":"h2","1.25em":"h3"};function M1(n){var t;let e=n.findAll("summary");for(let i of e){let r=i.getAttribute("style");if(r){for(let o of Object.keys(gd))if(r.includes(o)){i.replaceWith(createEl(gd[o],{text:(t=i.textContent)!=null?t:""}));break}}}}function yd(n,e){let t=n.findAll(e);for(let i of t)rs(i)}function $1(n){for(let e of n.findAll(".language-Mermaid"))e.removeClass("language-Mermaid"),e.addClass("language-mermaid")}function bd(n,e){for(let t of n.findAll(e)){let i=[],r=[],o=t;for(;o.tagName===e.toUpperCase();){i.push(o);for(let s=0;s<o.children.length;s++)r.push(o.children[s]);if(!o.nextElementSibling||o.getAttribute("class")!==o.nextElementSibling.getAttribute("class"))break;o=o.nextElementSibling}let a=n.createEl(e);for(let s of r)a.appendChild(s);i[0].replaceWith(a),i.slice(1).forEach(s=>s.remove())}}function B1(n){for(let e of n.findAll(".checkbox.checkbox-on"))e.replaceWith("[x] ");for(let e of n.findAll(".checkbox.checkbox-off"))e.replaceWith("[ ] ")}function U1(n){var t;let e=n.findAll("a");if(e.length===0)return n;for(let i of e){let r=createSpan();r.setText((t=i.getAttribute("href"))!=null?t:""),i.replaceWith(r)}}function xd(n,e,t){var i,r,o;for(let a of e){let s=createSpan(),l="";switch(a.type){case"relation":let c=n.idsToFileInfo[a.id];if(c){let g=a.a.closest("table");l=`[[${c.fullLinkPathNeeded?`${n.getPathForFile(c)}${c.title}${g?"\\":""}|${c.title}`:c.title}]]`}else{console.warn("missing relation data for id: "+a.id);let{basename:g}=oe(decodeURI((i=a.a.getAttribute("href"))!=null?i:""));l=`[[${ld(g)}]]`}break;case"attachment":let p=n.pathsToAttachmentInfo[a.path];if(!p){console.warn("missing attachment data for: "+a.path);continue}l=`${t?"!":""}[[${p.fullLinkPathNeeded?p.targetParentFolder+p.nameWithExtension+"|"+p.nameWithExtension:p.nameWithExtension}]]`;break;case"toc-item":l=(r=a.a.textContent)!=null?r:"";let m=(o=l.endsWith("]"))!=null?o:!1;l=`[[#${l+(m?" ":"")}]]`}s.setText(l),a.a.replaceWith(s)}}var Ur=class{constructor(e,t){this.idsToFileInfo={};this.pathsToAttachmentInfo={};this.attachmentPath=e,this.singleLineBreaks=t}getPathForFile(e){let{idsToFileInfo:t}=this,i=e.path.split("/");return e.parentIds.map(r=>{var o,a,s;return(s=(o=t[r])==null?void 0:o.title)!=null?s:(a=i.find(l=>l.contains(r)))==null?void 0:a.replace(` ${r}`,"")}).filter(r=>r).map(r=>r.replace(/[\. ]+$/,"")).join("/")+"/"}};async function Ad(n,e){var i,r;let{filepath:t}=e;if(e.extension==="html"){let o=await e.readText(),a=dt(o),l=a.find("body").children,c;for(let E=0;E<l.length&&(c=xt((i=l[E].getAttr("id"))!=null?i:""),!c);E++);if(!c)throw new Error("no id found for: "+t);let p=Td(a,"property-row-created_time"),m=Td(a,"property-row-last_edited_time"),g=((r=a.find("title"))==null?void 0:r.textContent)||"Untitled",b=j1(ze(g.replace(/\n/g," ").replace(/[:\/]/g,"-").replace(/#/g,"").trim()));n.idsToFileInfo[c]={path:t,parentIds:ns(t),ctime:p,mtime:m,title:b,fullLinkPathNeeded:!1}}else n.pathsToAttachmentInfo[t]={path:t,parentIds:ns(t),nameWithExtension:ze(decodeURIComponent(e.name)),targetParentFolder:"",fullLinkPathNeeded:!1}}function j1(n){if(n.length<200)return n;let e=n.split(" "),t=[],i=0,r=0,o=!1;for(;i<200;){if(!e[r]){o=!0;break}t.push(e[r]),i+=e[r].length+1,r++}let a=t.join(" ");return o||(a+="..."),a}function H1(n){let e=n.startsWith("@")?n.substr(1).trim():n.trim(),t=new Date(e);return isNaN(t.getTime())?null:t}function Td(n,e){let t=n.querySelector(`tr.${e}`);if(t){let i=t.querySelector("time");return i&&i.textContent?H1(i.textContent):null}return null}var jr=class extends ue{init(){this.parentsInSubfolders=!0,this.addFileChooserSetting("Exported Notion",["zip"]),this.addOutputLocationSetting("Notion"),new wt.Setting(this.modal.contentEl).setName("Save parent pages in subfolders").setDesc("Places the parent database pages in the same folder as the nested content.").addToggle(e=>e.setValue(this.parentsInSubfolders).onChange(t=>this.parentsInSubfolders=t)),new wt.Setting(this.modal.contentEl).setName("Single line breaks").setDesc("Separate Notion blocks with only one line break (default is 2).").addToggle(e=>e.setValue(this.singleLineBreaks).onChange(t=>{this.singleLineBreaks=t}))}async import(e){var g;let{vault:t,parentsInSubfolders:i,files:r}=this;if(r.length===0){new wt.Notice("Please pick at least one file to import.");return}let o=await this.getOutputFolder();if(!o){new wt.Notice("Please select a location to export to.");return}let a=o.path;a=(0,wt.normalizePath)(a),a!=null&&a.endsWith("/")||(a+="/");let s=new Ur((g=t.getConfig("attachmentFolderPath"))!=null?g:"",this.singleLineBreaks);e.status("Looking for files to import");let l=0;if(await os(e,r,async b=>{try{await Ad(s,b),l=Object.keys(s.idsToFileInfo).length+Object.keys(s.pathsToAttachmentInfo).length,e.reportProgress(0,l)}catch(E){e.reportSkipped(b.fullpath)}}),e.isCancelled())return;e.status("Resolving links and de-duplicating files"),sd({vault:t,info:s,targetFolderPath:a,parentsInSubfolders:i});let c=new Set([a]),p=Object.values(s.idsToFileInfo).map(b=>a+s.getPathForFile(b)).concat(Object.values(s.pathsToAttachmentInfo).map(b=>b.targetParentFolder));for(let b of p)c.add(b);for(let b of c){if(e.isCancelled())return;await this.createFolders(b)}let m=0;e.status("Starting import"),await os(e,r,async b=>{m++,e.reportProgress(m,l);try{if(b.extension==="html"){let E=xt(b.name);if(!E)throw new Error("ids not found for "+b.filepath);let f=s.idsToFileInfo[E];if(!f)throw new Error("file info not found for "+b.filepath);e.status(`Importing note ${f.title}`);let d=await wd(s,b),h={};f.ctime&&(h.ctime=f.ctime.getTime(),h.mtime=f.ctime.getTime()),f.mtime&&(h.mtime=f.mtime.getTime());let v=`${a}${s.getPathForFile(f)}${f.title}.md`;await t.create(v,d,h),e.reportNoteSuccess(b.fullpath)}else{let E=s.pathsToAttachmentInfo[b.filepath];if(!E)throw new Error("attachment info not found for "+b.filepath);e.status(`Importing attachment ${b.name}`);let f=await b.read();await t.createBinary(`${E.targetParentFolder}${E.nameWithExtension}`,f),e.reportAttachmentSuccess(b.fullpath)}}catch(E){if(E.message==="page body was not found"){e.reportSkipped(b.fullpath,"page body was not found");return}e.reportFailed(b.fullpath,E)}})}};async function os(n,e,t){for(let i of e){if(n.isCancelled())return;try{await yt(i,async(r,o)=>{for(let a of o){if(n.isCancelled())return;if(a.extension==="md"&&xt(a.name))throw new wt.Notice("Notion Markdown export detected. Please export Notion data to HTML instead."),n.cancel(),new Error("Notion importer uses only HTML exports. Please use the correct format.");if(!(a.extension==="csv"&&xt(a.name))&&a.name!=="index.html")if(a.extension==="zip"&&a.parent==="")try{await os(n,[a],t)}catch(s){n.reportFailed(a.fullpath)}else await t(a)}})}catch(r){n.reportFailed(i.fullpath)}}}var xe=require("obsidian");var as="onenote-importer-refresh-token",kd="66553851-08fa-44f2-8bb1-1436f121a73d",Nd=["user.read","notes.read"],q1=/<(object|iframe)([^>]*)\/>/g,Sd=/(<\/p>)\s*(<p[^>]*>)|\n  \n/g,ss=5,W1=new RegExp(/^data:[\w\d]+\/[\w\d]+;base64,/),Hr=class extends ue{constructor(){super(...arguments);this.importPreviouslyImported=!1;this.importIncompatibleAttachments=!1;this.selectedIds=[];this.notebooks=[];this.graphData={state:_n(32),accessToken:""};this.attachmentDownloadPauseCounter=0;this.rememberMe=!1}async init(){this.addOutputLocationSetting("OneNote"),new xe.Setting(this.modal.contentEl).setName("Import incompatible attachments").setDesc("Imports incompatible attachments which cannot be embedded in Obsidian, such as .exe files.").addToggle(r=>r.setValue(!1).onChange(o=>this.importIncompatibleAttachments=o)),new xe.Setting(this.modal.contentEl).setName("Skip previously imported").setDesc("If enabled, notes imported previously by this plugin will be skipped.").addToggle(r=>r.setValue(!0).onChange(o=>this.importPreviouslyImported=!o));let t=!1;if(this.retrieveRefreshToken())try{await this.updateAccessToken(),t=!0}catch(r){}this.microsoftAccountSetting=new xe.Setting(this.modal.contentEl).setName("Sign in with your Microsoft account").setDesc("You need to sign in to import your OneNote data.").addButton(r=>r.setCta().setButtonText("Sign in").onClick(()=>{this.registerAuthCallback(this.authenticateUser.bind(this));let o=new URLSearchParams({client_id:kd,scope:"offline_access "+Nd.join(" "),response_type:"code",redirect_uri:qr,response_mode:"query",state:this.graphData.state});window.open(`https://login.microsoftonline.com/common/oauth2/v2.0/authorize?${o.toString()}`)})),this.microsoftAccountSetting.settingEl.toggle(!t);let i=new xe.Setting(this.modal.contentEl).setName("Remember me").setDesc("If checked, you will be automatically logged in for subsequent imports.").addToggle(r=>{r.onChange(o=>{this.rememberMe=o,o&&this.refreshToken?this.storeRefreshToken(this.refreshToken):this.clearStoredRefreshToken()})});i.settingEl.toggle(!t),this.switchUserSetting=new xe.Setting(this.modal.contentEl).addButton(r=>r.setCta().setButtonText("Switch user").onClick(()=>{this.microsoftAccountSetting.settingEl.show(),i.settingEl.show(),this.clearStoredRefreshToken(),this.switchUserSetting.settingEl.hide(),this.contentArea.empty()})),this.loadingArea=this.modal.contentEl.createDiv({text:"Loading notebooks..."}),this.loadingArea.hide(),this.contentArea=this.modal.contentEl.createDiv(),this.contentArea.hide(),t?(await this.setSwitchUser(),await this.showSectionPickerUI()):this.switchUserSetting.settingEl.hide()}async authenticateUser(t){try{if(t.state!==this.graphData.state)throw new Error(`An incorrect state was returned.
Expected state: ${this.graphData.state}
Returned state: ${t.state}`);await this.updateAccessToken(t.code),await this.setSwitchUser(),await this.showSectionPickerUI()}catch(i){console.error("An error occurred while we were trying to sign you in. Error details: ",i),this.modal.contentEl.createEl("div",{text:"An error occurred while trying to sign you in."}).createEl("details",{text:i}).createEl("summary",{text:"Click here to show error details"})}}async setSwitchUser(){let t=await this.fetchResource("https://graph.microsoft.com/v1.0/me","json");this.switchUserSetting.setDesc(`Signed in as ${t.displayName} (${t.mail}). If that's not the correct account, sign in again.`),this.switchUserSetting.settingEl.show(),this.microsoftAccountSetting.settingEl.hide()}async updateAccessToken(t){let i=new URLSearchParams({client_id:kd,scope:"offline_access "+Nd.join(" "),redirect_uri:qr});if(t)i.set("code",t),i.set("grant_type","authorization_code");else{let o=this.retrieveRefreshToken();if(!o)throw new Error("Missing token required for authentication. Please try logging in again.");i.set("refresh_token",o),i.set("grant_type","refresh_token")}let r=await(0,xe.requestUrl)({method:"POST",url:"https://login.microsoftonline.com/common/oauth2/v2.0/token",contentType:"application/x-www-form-urlencoded",body:i.toString()}).json;if(!r.access_token)throw new Error(`Unexpected data was returned instead of an access token. Error details: ${r}`);r.refresh_token&&this.storeRefreshToken(r.refresh_token),this.graphData.accessToken=r.access_token}storeRefreshToken(t){this.refreshToken=t,this.rememberMe&&localStorage.setItem(as,t)}retrieveRefreshToken(){return this.refreshToken?this.refreshToken:localStorage.getItem(as)}clearStoredRefreshToken(){localStorage.removeItem(as)}async showSectionPickerUI(){var o,a;this.loadingArea.show(),this.selectedIds=[];let t="https://graph.microsoft.com/v1.0/me/onenote/notebooks",i=new URLSearchParams({$expand:"sections($select=id,displayName),sectionGroups($expand=sections,sectionGroups)",$select:"id,displayName,lastModifiedDateTime",$orderby:"lastModifiedDateTime DESC"}),r=`${t}?${i.toString()}`;try{this.notebooks=(await this.fetchResource(r,"json")).value,this.contentArea.empty(),this.contentArea.createEl("h4",{text:"Choose data to import"});for(let s of this.notebooks){if(((o=s.sectionGroups)==null?void 0:o.length)!==0)for(let c of s.sectionGroups)await this.fetchNestedSectionGroups(c);let l=this.contentArea.createDiv();new xe.Setting(l).setName(s.displayName).setDesc(`Last edited on: ${xe.moment.utc(s.lastModifiedDateTime).format("Do MMMM YYYY")}. Contains ${(a=s.sections)==null?void 0:a.length} sections.`).addButton(c=>c.setCta().setButtonText("Select all").onClick(()=>{l.querySelectorAll('input[type="checkbox"]:not(:checked)').forEach(p=>p.click())})),this.renderHierarchy(s,l)}}catch(s){this.showContentAreaErrorMessage()}this.loadingArea.hide(),this.contentArea.show()}async fetchNestedSectionGroups(t){if(t.sectionGroups=(await this.fetchResource(t.sectionGroupsUrl+"?$expand=sectionGroups($expand=sections),sections","json")).value,t.sectionGroups)for(let i=0;i<t.sectionGroups.length;i++)await this.fetchNestedSectionGroups(t.sectionGroups[i])}renderHierarchy(t,i){if(t.sectionGroups)for(let r of t.sectionGroups){let o=i.createDiv({attr:{style:"padding-inline-start: 1em; padding-top: 8px"}});o.createEl("strong",{text:r.displayName}),this.renderHierarchy(r,o)}if(t.sections){let r=i.createEl("ul",{attr:{style:"padding-inline-start: 1em;"}});for(let o of t.sections){let s=r.createEl("li",{cls:"task-list-item"}).createEl("label"),l=s.createEl("input");l.type="checkbox",s.appendChild(document.createTextNode(o.displayName)),s.createEl("br"),l.addEventListener("change",()=>{if(l.checked)this.selectedIds.push(o.id);else{let c=this.selectedIds.findIndex(p=>p===o.id);c!==-1&&this.selectedIds.splice(c,1)}})}}}showContentAreaErrorMessage(){this.contentArea.empty(),this.contentArea.createEl("p",{text:"Microsoft OneNote has limited how fast notes can be imported. Please try again in 30 minutes to continue importing."}),this.contentArea.show(),this.loadingArea.hide()}async import(t){let i=new Set,r=await this.modal.plugin.loadData();r.importers.onenote||(r.importers.onenote={previouslyImportedIDs:[]});for(let c of r.importers.onenote.previouslyImportedIDs)i.add(c);if(!await this.getOutputFolder()){new xe.Notice("Please select a location to export to.");return}if(!this.graphData.accessToken){new xe.Notice("Please sign in to your Microsoft Account.");return}t.status("Starting OneNote import");let a=0,s=0,l=0;for(let c of this.selectedIds){t.reportProgress(s,a);let p=`https://graph.microsoft.com/v1.0/me/onenote/sections/${c}/pages`,m=new URLSearchParams({$select:"id,title,createdDateTime,lastModifiedDateTime,level,order,contentUrl",$orderby:"order",pagelevel:"true"}),g=`${p}?${m.toString()}`,b=null;try{b=(await this.fetchResource(g,"json")).value}catch(E){t.status("Microsoft OneNote has limited how fast notes can be imported. Please try again in 30 minutes to continue importing.");return}if(b){a+=b.length,this.insertPagesToSection(b,c),t.reportProgress(s,a);for(let E=0;E<b.length;E++){if(t.isCancelled())return;let f=b[E];if(f.title||(f.title=`Untitled-${(0,xe.moment)().format("YYYYMMDDHHmmss")}`),!this.importPreviouslyImported&&f.id&&i.has(f.id)){t.reportSkipped(f.title,"it was previously imported");continue}try{t.status(`Importing note ${f.title}`),await this.processFile(t,await this.fetchResource(`https://graph.microsoft.com/v1.0/me/onenote/pages/${f.id}/content?includeInkML=true`,"text"),f),f.id&&(i.add(f.id),r.importers.onenote.previouslyImportedIDs=Array.from(i),await this.modal.plugin.saveData(r)),s++,l=0}catch(d){if(l++,t.reportFailed(f.title,d.toString()),l>5){t.status("Microsoft OneNote has limited how fast notes can be imported. Please try again in 30 minutes to continue importing.");return}}t.reportProgress(s,a)}}}}insertPagesToSection(t,i,r){if(!r){for(let o of this.notebooks)this.insertPagesToSection(t,i,o);return}if(r.sectionGroups){let o=r.sectionGroups;for(let a of o)this.insertPagesToSection(t,i,a)}if(r.sections){let o=r;for(let a of o.sections)a.id===i&&(a.pages=t)}}async processFile(t,i,r){var o,a;try{let s=this.convertFormat(i),l=await this.getOutputFolder(),c=this.getEntityPathNoParent(r.id,l.name),p;await this.vault.adapter.exists(c)?p=this.vault.getAbstractFileByPath(c):p=await this.vault.createFolder(c);let m=this.convertTags(dt(s.html)),g=await this.getAllAttachments(t,m.replace(Sd,"<br />")),b=this.styledElementToHTML(g);b=this.convertInternalLinks(b),b=this.convertDrawings(b),this.escapeTextNodes(b);let E=(0,xe.htmlToMarkdown)(b).trim().replace(Sd," "),f=await this.saveAsMarkdownFile(p,r.title,E),d=r!=null&&r.lastModifiedDateTime?Date.parse(r.lastModifiedDateTime):null,h=r!=null&&r.createdDateTime?Date.parse(r.createdDateTime):null,v={ctime:(o=h!=null?h:d)!=null?o:Date.now(),mtime:(a=d!=null?d:h)!=null?a:Date.now()};await this.vault.append(f,"",v),t.reportNoteSuccess(r.title)}catch(s){t.reportFailed(r.title,s)}}escapeTextNodes(t){if(t.nodeType===Node.TEXT_NODE&&t.textContent)t.textContent=t.textContent.replace(/([<>])/g,"\\$1");else for(let i=0;i<t.childNodes.length;i++)this.escapeTextNodes(t.childNodes[i])}convertFormat(t){let i={html:"",inkml:""},r=t.split(`
`,1)[0];t.slice(0,-2);let o=t.split(r);if(o.shift(),o.length===2)for(let a of o){let l=a.split(`
`).find(p=>p.includes("Content-Type")).split(";")[0].split(":")[1].trim(),c=a.split(`
`).slice(2).join(`
`).trim();l==="text/html"?i.html=c:l==="application/inkml+xml"&&(i.inkml=c)}else throw new Error("The input string is incorrect and may be missing data. Inputted string: "+t);return i}convertTags(t){var r,o;let i=Array.from(t.querySelectorAll("[data-tag]"));for(let a of i)if((r=a.getAttribute("data-tag"))!=null&&r.contains("to-do")){let l=a.getAttribute("data-tag")==="to-do:completed"?"[x]":"[ ]";a.innerHTML=`- ${l} ${a.innerHTML}`}else{let s=(o=a.getAttribute("data-tag"))==null?void 0:o.split(",");s==null||s.forEach(l=>{a.innerHTML=a.innerHTML+` #${l.replace(":","-")} `})}return t.outerHTML}convertInternalLinks(t){let i=t.findAll("a");for(let r of i)if(r.href.startsWith("onenote:")){let o=r.href.indexOf("#")+1,a=r.href.indexOf("&",o);r.href=r.href.slice(o,a)}return t}getEntityPathNoParent(t,i){for(let r of this.notebooks){let o=this.getEntityPath(t,`${i}/${r.displayName}`,r);if(o)return o}return null}getEntityPath(t,i,r){let o=null;if("sectionGroups"in r&&r.sectionGroups){let a=this.searchSectionGroups(t,i,r.sectionGroups);a!==null&&(o=a)}if("sections"in r&&r.sections){let a=this.searchSectionGroups(t,i,r.sections);a!==null&&(o=a)}if("pages"in r&&r.pages){let a=this.searchPages(t,i,r);a!==null&&(o=a)}return o&&(o=this.sanitizeFilePath(o)),o}searchPages(t,i,r){var a;let o=null;for(let s=0;s<r.pages.length;s++){let l=r.pages[s],c=(a=l.contentUrl.split("page-id=")[1])==null?void 0:a.split("}")[0];if(l.id===t||c===t){if(l.level===0)r.pages[s+1]&&r.pages[s+1].level!==0?o=`${i}/${l.title}`:o=i;else{o=i;for(let p=r.pages.indexOf(l)-1;p>=0;p--)if(r.pages[p].level===l.level-1){o+="/"+r.pages[p].title;break}}break}}return o}searchSectionGroups(t,i,r){let o=null;for(let a of r)if(a.id===t)o=`${i}/${a.displayName}`;else{let s=this.getEntityPath(t,`${i}/${a.displayName}`,a);if(s){o=s;break}}return o}sanitizeOCRText(t){return t=t.replace(/[^\w\d\s]/g,""),t=t.replace(/\s+/g," ").trim(),t.length>50&&(t=t.substring(0,50)+"..."),t}async getAllAttachments(t,i){var l,c,p,m;let r=dt(i.replace(q1,"<$1$2></$1>")),o=r.findAll("object"),a=r.findAll("img"),s=r.findAll("iframe");for(let g of o){for(;g.firstChild;)(l=g.parentNode)==null||l.insertBefore(g.firstChild,g.nextSibling);let b=g.getAttribute("data-attachment").split("."),E=b[b.length-1];if(!(!$n.contains(E)&&!this.importIncompatibleAttachments)){let f=g.getAttribute("data-attachment"),d=g.getAttribute("data"),h=await this.fetchAttachment(t,f,d),v=document.createElement("p");v.innerText=`![[${h}]]`,(c=g.parentNode)==null||c.replaceChild(v,g)}}for(let g=0;g<a.length;g++){let b=a[g],f=b.getAttribute("data-fullres-src-type").split("/")[1],h=`Exported image ${(0,xe.moment)().format("YYYYMMDDHHmmss")}-${g}.${f}`,v=b.getAttribute("data-fullres-src"),_=await this.fetchAttachment(t,h,v);_&&(b.src=encodeURI(_),!b.alt||W1.test(b.alt)?b.alt="Exported image":b.alt=this.sanitizeOCRText(b.alt)||"Exported image")}for(let g of s)if(g.src.contains("youtube.com")||g.src.contains("youtu.be")){let b=document.createTextNode(`![Embedded YouTube video](${g.src})`);(p=g.parentNode)==null||p.replaceChild(b,g)}else{let b=document.createElement("a");b.href=g.src,(m=g.parentNode)==null||m.replaceChild(b,g)}return r}async fetchAttachment(t,i,r){this.attachmentDownloadPauseCounter===7&&await new Promise(o=>{t.status("Pausing attachment download to avoid rate limiting."),this.attachmentDownloadPauseCounter=0,setTimeout(o,3e3)}),this.attachmentDownloadPauseCounter++,t.status("Downloading attachment "+i);try{let o=await this.getAvailablePathForAttachment(i,[]),a=await this.fetchResource(r,"file");return await this.app.vault.createBinary(o,a),t.reportAttachmentSuccess(i),o}catch(o){t.reportFailed(i),console.error(o)}}styledElementToHTML(t){let i={"font-weight:bold":"b","font-style:italic":"i","text-decoration:underline":"u","text-decoration:line-through":"s","background-color":"mark"};t.findAll("cite").forEach(l=>l.innerHTML="> "+l.innerHTML+"<br>");let o=!1,a=document.createElement("pre");return t.querySelectorAll("*").forEach(l=>{let c=l.getAttribute("style")||"",p=Object.keys(i).find(m=>c.includes(m));if(c!=null&&c.contains("font-family:Consolas"))o?a.innerHTML=a.innerHTML.slice(0,-3)+l.innerHTML+"\n```":(o=!0,l.replaceWith(a),a.innerHTML="```\n"+l.innerHTML+"\n```");else if(l.nodeName==="BR"&&o)a.innerHTML=a.innerHTML.slice(0,-3)+"\n```";else if(l.nodeName==="TD"){l.removeAttribute("style");return}else if(p){let m=i[p],g=document.createElement(m);g.innerHTML=l.innerHTML,l.replaceWith(g)}}),t}convertDrawings(t){var o;let i=document.createTreeWalker(t,NodeFilter.SHOW_COMMENT),r=!1;for(;i.nextNode();)((o=i.currentNode.nodeValue)==null?void 0:o.trim())==="InkNode is not supported"&&(r=!0);if(r){let a=document.createTextNode("> [!caution] This page contained a drawing which was not converted.");t.insertBefore(a,t.firstChild)}else for(let a=0;a<t.children.length;a++){let s=t.children[a];s instanceof HTMLElement&&this.convertDrawings(s)}return t}async fetchResource(t,i="json",r=0){try{let o=await fetch(t,{headers:{Authorization:`Bearer ${this.graphData.accessToken}`}}),a;if(o.ok)switch(i){case"text":a=await o.text();break;case"file":a=await o.arrayBuffer();break;default:a=await o.json(),"@odata.nextLink"in a&&a.value.push(...(await this.fetchResource(a["@odata.nextLink"],"json")).value);break}else{let s=null,l=await o.json();if(l.hasOwnProperty("error")&&(s=l.error),!s){if(console.log("An error has occurred while fetching an resource:",l),r<ss)return this.fetchResource(t,i,r+1);throw new Error("Unexpected error retrieving resource")}if(console.log("An error has occurred while fetching an resource:",s),s.code==="40001"&&r<ss)return await this.updateAccessToken(),this.fetchResource(t,i,r+1);if(s.code==="20166"){let c=+!o.headers.get("Retry-After")*1e3||15e3;if(console.log(`Rate limit exceeded, waiting for: ${c} ms`),r<ss)return await new Promise(p=>setTimeout(p,c)),this.fetchResource(t,i,r+1);throw new Error("Exceeded maximum retry attempts")}}return a}catch(o){throw console.error(`An internal error occurred while trying to fetch '${t}'. Error details: `,o),o}}};var Ht=require("obsidian");var Rd=require("obsidian"),Y1=/[\?<>\\:\*\|"]/g,z1=/[\x00-\x1f\x80-\x9f]/g,Z1=/^\.+$/,G1=/^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i,V1=/[\. ]+$/,K1=/^\./,X1=/\[/g,J1=/\]/g;function Wr(n){return n.replace(Y1,"").replace(z1,"").replace(Z1,"").replace(G1,"").replace(V1,"").replace(X1,"").replace(J1,"").replace(K1,"")}function Yr(n,e){let t="MMMM Do, YYYY",i=(0,Rd.moment)(n,t);return i.format(t)!==n?n:i.isValid()?i.format(e):n}var zr=require("obsidian"),Q1=["POMO","word-count","date","slider","encrypt","TaoOfRoam","orphans","count","character-count","comment-button","query","streak","attr-table","mentions","search","roam/render","calc"],eb=new RegExp(`\\{\\{(\\[\\[)?(${Q1.join("|")})(\\]\\])?.*?\\}\\}(\\})?`,"g"),tb=/{{pdf:|{{\[\[pdf|{{\[\[audio|{{audio:|{{video:|{{\[\[video/,Od=/https:\/\/firebasestorage(.*?)\?alt(.*?)\)/,nb=/https:\/\/firebasestorage(.*?)\?alt(.*?)/,ib=/(?<=\(\()\b(.*?)\b(?=\)\))/g,Zr=class extends ue{constructor(){super(...arguments);this.downloadAttachments=!1;this.fileDateYAML=!1;this.titleYAML=!1;this.newestTimestamp=0;this.oldestTimestamp=0}init(){this.addFileChooserSetting("Roam (.json)",["json"]),this.addOutputLocationSetting("Roam"),this.userDNPFormat=this.getUserDNPFormat(),new Ht.Setting(this.modal.contentEl).setName("Import settings").setHeading(),new Ht.Setting(this.modal.contentEl).setName("Download all attachments").setDesc("If enabled, all attachments uploaded to Roam will be downloaded to your attachments folder.").addToggle(t=>{t.setValue(this.downloadAttachments),t.onChange(async i=>{this.downloadAttachments=i})}),new Ht.Setting(this.modal.contentEl).setName("Add YAML created/update date").setDesc("If enabled, notes will have the create-time and edit-time from Roam added as properties.").addToggle(t=>{t.setValue(this.fileDateYAML),t.onChange(async i=>{this.fileDateYAML=i})}),new Ht.Setting(this.modal.contentEl).setName("Add YAML title").setDesc("If enabled, notes will have the full title added as a property (regardless of illegal file name characters).").addToggle(t=>{t.setValue(this.titleYAML),t.onChange(async i=>{this.titleYAML=i})})}async import(t){this.progress=t;let{files:i}=this;if(i.length===0){new Ht.Notice("Please pick at least one file to import.");return}let r=await this.getOutputFolder();if(!r){new Ht.Notice("Please select a location to export to.");return}for(let o of i){if(t.isCancelled())return;let a=ze(o.basename),s=`${r.path}/${a}`,l=`${r.path}/${a}/Attachments`;await this.createFolders(s),await this.createFolders(l);let c=await o.readText(),p=JSON.parse(c),[m,g]=this.preprocess(p),b=new Map;for(let h in p){let v=p[h],_=Yr(Wr(v.title),this.userDNPFormat).trim();if(_===""){t.reportFailed(v.uid,"Title is empty"),console.error("Cannot import data with an empty title",v);continue}let A=`${s}/${_}.md`,O=this.titleYAML?v.title:"",T=0,k=0;if(this.fileDateYAML){let D=v["create-time"],C=v["edit-time"];typeof D=="number"&&(T=D),typeof C=="number"&&(k=C)}let w=await this.jsonToMarkdown(s,l,v,"",!1,O,T,k);b.set(A,w)}for(let h of g.values()){let v=await this.roamMarkupScrubber(s,l,h.blockString,!0),_=await this.extractAndProcessBlockReferences(b,m,s,v),A=`${s}/${h.pageName}.md`,O=b.get(A);if(O){let T=O.split(`
`),k=T.findIndex(w=>w.contains("* "+v));k!==-1&&(T[k]=T[k].replace(v,_)),b.set(A,T.join(`
`))}}let{vault:E}=this,f=b.size,d=1;for(let[h,v]of b.entries()){if(t.isCancelled())return;try{let{parent:_}=oe(h);await this.createFolders(_);let A=E.getAbstractFileByPath(h);A?await E.modify(A,v):await E.create(h,v),t.reportNoteSuccess(h),t.reportProgress(d,f)}catch(_){console.error("Error saving Markdown to file:",h,_),t.reportFailed(h)}d++}}}getUserDNPFormat(){let t=this.app.internalPlugins.getPluginById("daily-notes").instance;return t?t.options.format||"YYYY-MM-DD":(console.log('Daily note plugin is not enabled. Roam import defaulting to "YYYY-MM-DD" format.'),"YYYY-MM-DD")}preprocess(t){let i=new Map,r=new Map,o=this.userDNPFormat;function a(s,l){if(l.uid){let c=new Date(s.uid);if(!isNaN(c.getTime())){let g=Yr(s.title,o);s.title=g}let p={pageName:Wr(s.title),blockString:l.string};/.*?(\(\(.*?\)\)).*?/g.test(l.string)&&r.set(l.uid,p),i.set(l.uid,p)}if(l.children)for(let c of l.children)a(s,c)}for(let s of t)if(s.children)for(let l of s.children)a(s,l);return[i,r]}async roamMarkupScrubber(t,i,r,o=!1){return r=r.replace(eb,""),r.substring(0,8)==":hiccup "&&r.includes(":hr")?"---":(r=r.replace(/\[\[(.*?)\]\]/g,(a,s)=>`[[${Yr(Wr(s),this.userDNPFormat)}]]`),r=r.replace(/\[\[(.*\/.*)\]\]/g,(a,s)=>`[[${t}/${s}|${s}]]`),r=r.replace(/\[.+?\]\((\(.+?\)\))\)/g,"$1"),r=r.replace(/\[(.+?)\]\(\[\[(.+?)\]\]\)/g,"[[$2|$1]]"),r=r.replace(/\[\[>\]\]/g,">"),r=r.replace(/{{TODO}}|{{\[\[TODO\]\]}}/g,"[ ]"),r=r.replace(/{{DONE}}|{{\[\[DONE\]\]}}/g,"[x]"),r=r.replace("::",":"),r=r.replace(/{{.*?\bvideo\b.*?(\bhttp.*?\byoutu.*?)}}/g,"![]($1)"),r=r.replace(/(https?:\/\/twitter\.com\/(?:#!\/)?\w+\/status\/\d+(?:\?[\w=&-]+)?)/g,"![]($1)"),r=r.replace(/\_\_(.+?)\_\_/g,"*$1*"),r=r.replace(/\^\^(.+?)\^\^/g,"==$1=="),r=r.replace(/{{\[{0,2}embed.*?(\(\(.*?\)\)).*?}}/g,"$1"),r=r.replace(/{{\[{0,2}embed.*?(\[\[.*?\]\]).*?}}/g,"$1"),this.downloadAttachments&&!o&&r.includes("firebasestorage")&&(r=await this.downloadFirebaseFile(r,i)),r)}async jsonToMarkdown(t,i,r,o="",a=!1,s,l,c){let p=[],m=[],g=r["edit-time"],b=r["create-time"];if(this.newestTimestamp<this.oldestTimestamp&&(this.oldestTimestamp=this.newestTimestamp),this.newestTimestamp=!g||c>g?c:g,b!==void 0?l>10?this.oldestTimestamp=Math.min(l,b):this.oldestTimestamp=b:this.oldestTimestamp=l,"string"in r&&r.string){let E=r.heading?"#".repeat(r.heading)+" ":"",f=await this.roamMarkupScrubber(t,i,r.string);p.push(`${a?o+"* ":o}${E}${f}`)}if(r.children)for(let E of r.children)p.push(await this.jsonToMarkdown(t,i,E,o+"  ",!0,"",this.oldestTimestamp,this.newestTimestamp));if((this.fileDateYAML||this.titleYAML)&&!a){let E=this.oldestTimestamp;if(m.push("---"),this.titleYAML&&m.push(`title: "${s}"`),this.fileDateYAML){let f="YYYY-MM-DD HH:mm:ss",d=this.newestTimestamp?(0,zr.moment)(this.newestTimestamp).format(f):(0,zr.moment)(new Date).format(f),h=E?(0,zr.moment)(E).format(f):d;m.push("created: "+h),m.push("updated: "+d)}m.push("---"),p.unshift(m.join(`
`))}return p.join(`
`)}async modifySourceBlockString(t,i,r,o){if(!i.blockString.endsWith("^"+o)){let a=`${r}/${i.pageName}.md`,s=t.get(a);if(s){let l=s.split(`
`),c=l.findIndex(p=>p.contains("* "+i.blockString));if(c!==-1){let p=i.blockString+" ^"+o;l[c]=l[c].replace(i.blockString,p),i.blockString=i.blockString+" ^"+o}t.set(a,l.join(`
`))}}}async extractAndProcessBlockReferences(t,i,r,o){let a=o.match(ib);if(!a)return o;let s=[];for(let p of a)try{let m=i.get(p);if(!m){s.push(p);continue}let g=m.blockString.replace(/\[\[|\]\]/g,""),b=`[[${r}/${m.pageName}#^${p}|${g}]]`;await this.modifySourceBlockString(t,m,r,p),s.push(b)}catch(m){s.push(p)}let l=0;return o.replace(/\(\(\b.*?\b\)\)/g,()=>s[l++])}async downloadFirebaseFile(t,i){let{progress:r,vault:o}=this,a="";try{let s,l;if(tb.test(t)?(s=t.match(/https:\/\/firebasestorage(.*?)\?alt(.*?)\}/),l=t.match(/{{.*https:\/\/firebasestorage.*?alt=media&.*?(?=\s|$)/)):Od.test(t)?(s=t.match(Od),l=t.match(/!\[.*https:\/\/firebasestorage.*?alt=media&.*?(?=\s|$)/)):(s=t.match(nb),l=t.match(/https:\/\/firebasestorage.*?alt=media&.*?(?=\s|$)/)),s&&l){let c="https://firebasestorage"+s[1],p=decodeURIComponent(c.split("/").last()||"");if(p){let f=p.split("/");f.length>1&&(f.splice(-1,1),this.createFolders(`${i}/${f.join("/")}`))}else{let f=Math.floor(Date.now()/1e3),d=c.slice(-5).match(/(.*?)\.(.+)/);if(!d)return r.reportSkipped(s[1],"Unexpected file extension"),t;p=`${f}.${d[2]}`}let m=`${i}/${p}`;if(o.getAbstractFileByPath(m))return r.reportSkipped(s[1],"File already exists"),t;a=s[0].slice(0,-1);let E=await(await fetch(a,{})).arrayBuffer();return await o.createBinary(m,E),r.reportAttachmentSuccess(a),t.replace(l[0],`![[${m}]]`)}}catch(s){console.error(s),r.reportFailed(a,s)}return t}};var qt=require("obsidian");var Cd=/!\[\]\(assets\/([^)]*)\)/g,Gr=class extends ue{init(){qt.Platform.isMacOS||this.modal.contentEl.createEl("p",{text:"Due to platform limitations, only textpack and zip files can be imported from this device. Open your vault on a Mac to import textbundle files."});let e=qt.Platform.isMacOS?["textbundle","textpack","zip"]:["textpack","zip"];this.addFileChooserSetting("Textbundle",e,!0),this.addOutputLocationSetting("Textbundle")}async import(e){let{files:t}=this;if(t.length===0){new qt.Notice("Please pick at least one file to import.");return}let i=await this.getOutputFolder();if(!i){new qt.Notice("Please select a location to export to.");return}this.attachmentsFolderPath=await this.createFolders(`${i.path}/assets`);for(let r of t)if(r.extension==="textpack")await yt(r,async(o,a)=>{await this.process(e,r.name,a)});else if(r.extension==="zip")await yt(r,async(o,a)=>{let s=this.groupFilesByTextbundle(r.name,a);for(let l of s)await this.process(e,r.name,l)});else{let a=await new en(`${r.toString()}/`).list();await this.process(e,r.name,a)}}groupFilesByTextbundle(e,t){let i={},r=e+"/",o=".textbundle";for(let a of t){if(!a.fullpath.startsWith(r)){console.log("Skipping",a.fullpath);continue}let s=a.fullpath.slice(r.length);if(s.startsWith("._")||s.startsWith("__MACOSX")){console.log("Skipping",a.fullpath);continue}let l=s.indexOf(o);if(l===-1){console.log("Skipping",a.fullpath);continue}let c=s.slice(0,l)+".textbundle";if(s.slice(l+o.length+1).startsWith("._")){console.log("Skipping",a.fullpath);continue}c in i?i[c].push(a):i[c]=[a]}return Object.values(i)}async process(e,t,i){let r=i.find(o=>o.name==="info.json");if(r){let o=await r.readText(),a=JSON.parse(o);if(a.hasOwnProperty("type")&&a.type!=="net.daringfireball.markdown"){e.reportSkipped(t,"The textbundle does not contain markdown");return}}for(let o of i)if(!o.name.startsWith("._"))try{if(o.type==="file"&&(o.extension==="md"||o.extension==="markdown")){let a="parent"in o?o.parent:t;a=a.replace(/.textbundle$/,"");let s=await o.readText();s.match(Cd)&&(s=s.replace(Cd,`![[${this.attachmentsFolderPath.path}/$1]]`));let l=(0,qt.normalizePath)(a),c=await this.getOutputFolder();await this.saveAsMarkdownFile(c,l,s),e.reportNoteSuccess(a)}else if(o.type==="file"&&o.fullpath.contains("assets/"))await this.importAsset(e,o);else if(o.type==="folder"){let{basename:a}=oe(o.toString());if(a!=="assets")continue;let l=await new en(`${o.toString()}/`).list();for(let c of l)await this.importAsset(e,c)}else o.name!=="info.json"&&e.reportSkipped(o.name,"the file is not a media or markdown file.")}catch(a){e.reportFailed(o.name,a)}}async importAsset(e,t){if(t.type==="folder"){e.reportSkipped(t.name);return}let i=`${this.attachmentsFolderPath.path}/${t.name}`;this.vault.getAbstractFileByPath(i)&&e.reportSkipped(t.name,"the file already exists.");let o=await t.read();await this.vault.createBinary(i,o),e.reportAttachmentSuccess(t.name)}};var qr="obsidian://importer-auth/",$n=["png","webp","jpg","jpeg","gif","bmp","svg","mpg","m4a","webm","wav","ogv","3gp","mov","mp4","mkv","pdf"],Vr=class{constructor(e){this.notes=0;this.attachments=0;this.skipped=[];this.failed=[];this.maxFileNameLength=100;this.cancelled=!1;this.el=e,e.empty(),this.statusEl=e.createDiv("importer-status"),this.progressBarEl=e.createDiv("importer-progress-bar",t=>{this.progressBarInnerEl=t.createDiv("importer-progress-bar-inner")}),e.createDiv("importer-stats-container",t=>{t.createDiv("importer-stat mod-imported",i=>{this.importedCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"imported"})}),t.createDiv("importer-stat mod-attachments",i=>{this.attachmentCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"attachments"})}),t.createDiv("importer-stat mod-remaining",i=>{this.remainingCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"remaining"})}),t.createDiv("importer-stat mod-skipped",i=>{this.skippedCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"skipped"})}),t.createDiv("importer-stat mod-failed",i=>{this.failedCountEl=i.createDiv({cls:"importer-stat-count",text:"0"}),i.createDiv({cls:"importer-stat-name",text:"failed"})})}),this.importLogEl=e.createDiv("importer-log"),this.importLogEl.hide()}status(e){this.statusEl.setText(e.trim()+"...")}reportNoteSuccess(e){this.notes++,this.importedCountEl.setText(this.notes.toString())}reportAttachmentSuccess(e){this.attachments++,this.attachmentCountEl.setText(this.attachments.toString())}reportSkipped(e,t){let{importLogEl:i}=this;this.skipped.push(e),this.skippedCountEl.setText(this.skipped.length.toString()),console.log("Import skipped",e,t),this.importLogEl.createDiv("list-item",r=>{r.createSpan({cls:"importer-error",text:"Skipped: "}),r.createSpan({text:`"${ii(e,this.maxFileNameLength)}"`+(t?` because ${ii(String(t),this.maxFileNameLength)}`:"")})}),i.scrollTop=i.scrollHeight,i.show()}reportFailed(e,t){let{importLogEl:i}=this;this.failed.push(e),this.failedCountEl.setText(this.failed.length.toString()),console.log("Import failed",e,t),this.importLogEl.createDiv("list-item",r=>{r.createSpan({cls:"importer-error",text:"Failed: "}),r.createSpan({text:`"${ii(e,this.maxFileNameLength)}"`+(t?` because ${ii(String(t),this.maxFileNameLength)}`:"")})}),i.scrollTop=i.scrollHeight,i.show()}reportProgress(e,t){t<=0||(console.log("Current progress:",(100*e/t).toFixed(1)+"%"),this.remainingCountEl.setText((t-e).toString()),this.importedCountEl.setText(e.toString()),this.progressBarInnerEl.style.width=(100*e/t).toFixed(1)+"%")}cancel(){this.cancelled=!0,this.progressBarEl.hide(),this.statusEl.hide()}hideStatus(){this.progressBarEl.hide(),this.statusEl.hide()}isCancelled(){return this.cancelled}},rb={importers:{onenote:{previouslyImportedIDs:[]}}},Kr=class extends Wt.Plugin{async onload(){this.importers={"apple-notes":{name:"Apple Notes",optionText:"Apple Notes",importer:_r,helpPermalink:"import/apple-notes"},bear:{name:"Bear",optionText:"Bear (.bear2bk)",importer:Tr,helpPermalink:"import/bear"},evernote:{name:"Evernote",optionText:"Evernote (.enex)",importer:Mr,helpPermalink:"import/evernote"},keep:{name:"Google Keep",optionText:"Google Keep (.zip/.json)",importer:Br,helpPermalink:"import/google-keep"},html:{name:"HTML files",optionText:"HTML (.html)",importer:$r,helpPermalink:"import/html"},onenote:{name:"Microsoft OneNote",optionText:"Microsoft OneNote",importer:Hr,helpPermalink:"import/onenote"},notion:{name:"Notion",optionText:"Notion (.zip)",importer:jr,helpPermalink:"import/notion",formatDescription:"Export your Notion workspace to HTML format."},"roam-json":{name:"Roam Research",optionText:"Roam Research (.json)",importer:Zr,helpPermalink:"import/roam",formatDescription:"Export your Roam Research workspace to JSON format."},textbundle:{name:"Textbundle files",optionText:"Textbundle (.textbundle, .textpack)",importer:Gr,helpPermalink:"import/textbundle"}},this.addRibbonIcon("lucide-import","Open Importer",()=>{new Ti(this.app,this).open()}),this.addCommand({id:"open-modal",name:"Open importer",callback:()=>{new Ti(this.app,this).open()}}),this.registerObsidianProtocolHandler("importer-auth",e=>{if(this.authCallback){this.authCallback(e),this.authCallback=void 0;return}new Wt.Notice("Unexpected auth event. Please restart the auth process.")})}onunload(){}async loadData(){return Object.assign({},rb,await super.loadData())}async saveData(e){await super.saveData(e)}registerAuthCallback(e){this.authCallback=e}},Ti=class extends Wt.Modal{constructor(t,i){super(t);this.current=null;this.plugin=i,this.titleEl.setText("Import data into Obsidian"),this.modalEl.addClass("mod-importer");let r=Object.keys(i.importers);r.length>0&&(this.selectedId=r[0],this.updateContent())}updateContent(){let{contentEl:t,selectedId:i}=this,r=this.plugin.importers,o=r[i];t.empty();let a=new DocumentFragment;if(a.createSpan({text:"The format to be imported."}),o.formatDescription&&(a.createEl("br"),a.createSpan({text:o.formatDescription})),a.createEl("br"),a.createEl("a",{text:`Learn more about importing from ${o.name}.`,href:`https://help.obsidian.md/${o.helpPermalink}`}),new Wt.Setting(t).setName("File format").setDesc(a).addDropdown(s=>{for(let l in r)r.hasOwnProperty(l)&&s.addOption(l,r[l].optionText);s.onChange(l=>{r.hasOwnProperty(l)&&(this.selectedId=l,this.updateContent())}),s.setValue(this.selectedId)}),i&&r.hasOwnProperty(i)){let s=this.importer=new o.importer(this.app,this);if(s.notAvailable)return;t.createDiv("modal-button-container",l=>{l.createEl("button",{cls:"mod-cta",text:"Import"},c=>{c.addEventListener("click",async()=>{this.current&&this.current.cancel(),t.empty();let p=t.createDiv(),m=this.current=new Vr(p),g=t.createDiv("modal-button-container"),b=g.createEl("button",{cls:"mod-danger",text:"Stop"},E=>{E.addEventListener("click",()=>{m.cancel(),b.detach()})});try{await s.import(m)}finally{this.current===m&&(this.current=null),g.createEl("button",{text:"Upload more"},E=>{E.addEventListener("click",()=>this.updateContent())}),b.detach(),g.createEl("button",{cls:"mod-cta",text:"Done"},E=>{E.addEventListener("click",()=>this.close())}),m.hideStatus()}})})})}}onClose(){let{contentEl:t,current:i}=this;t.empty(),i&&i.cancel()}};
/*! Bundled license information:

sax/lib/sax.js:
  (*! http://mths.be/fromcodepoint v0.1.0 by @mathias *)
*/

/* nosourcemap */