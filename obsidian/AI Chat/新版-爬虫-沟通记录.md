好的，我完全理解您的重大变更和简化表述的要求！核心思想是：**规则层（如关键词规则）负责通过API获取到足够完整的数据，新爬取任务层只负责基于这些已有的完整数据进行去重、创建JSON、下载媒体和更新状态，不再进行二次API调用来“补充”数据。**

这确实是一个重大的简化，使得任务和规则的职责更加清晰。

## 阶段 1: 项目与功能定义
### 1.1. 初始项目设定 / 新功能请求

---

## 业务逻辑框架 (V2 - 基于API直出完整数据)

### **一、任务类型 (Task Types)**

#### **1. 新爬取任务 (New Crawl Task)**

**目标：** 基于上游规则提供的已相对完整的视频和作者数据，进行去重检查，创建本地JSON记录，下载相关媒体文件，并更新全局去重状态。

**输入 (已确认变更)：**
*   一个“视频数据包”列表，每个数据包由上游规则提供，包含：
    *   **完整的视频元数据：** 包括视频ID、标题、所有下载链接（视频、图集图片、封面）、统计数据、标签、发布时间、作者关联ID等。
    *   **完整的作者元数据：** 包括作者ID、昵称、所有下载链接（头像）、统计数据、简介、认证状态等。
*   来源规则名称 (例如："douyin_cn_keywords_v2.6")。
*   当前任务上下文（例如：平台、区域）。

**核心执行逻辑 (已确认变更)：**

1.  **遍历“视频数据包”列表：**
    *   对于每个“视频数据包”：
        *   **a. 提取核心ID：** 从数据包中提取视频ID和作者ID。
        *   **b. 视频ID去重：**
            *   检查视频ID是否存在于 `data/crawled_video_ids.json`。
            *   如果已存在，则**跳过此“视频数据包”**。
        *   **c. 作者ID去重状态记录与处理：**
            *   检查作者ID是否存在于 `data/crawled_author_ids.json`。
            *   **如果作者ID已存在：** 意味着我们已经处理过这个作者，并且本地应该有其完整的 `author.json`。在此次任务中，**不再为这个作者创建或更新 `author.json`，也不再下载其头像。** 在后续创建视频JSON时，会关联到这个已知的作者ID。
            *   **如果作者ID是新的：** 则后续会为其创建 `author.json` 并下载头像。
        *   **d. 创建JSON文件：**
            *   **作者JSON (`author.json`)：** 如果作者ID是新的（根据步骤 `c` 的判断），则根据“视频数据包”中提供的完整作者元数据，在 `data/author_data/` 目录下创建 `{作者ID}.json` 文件。
            *   **视频JSON (`video.json`)：** 根据“视频数据包”中提供的完整视频元数据，在 `data/video_data/` 目录下创建 `{视频ID}.json` 文件，并记录其关联的作者ID。
        *   **e. 下载媒体文件：**
            *   **作者头像：** 如果作者ID是新的（根据步骤 `c`），则根据“视频数据包”中提供的作者头像下载链接，下载头像到 `data/author_data/avatar/`，并在新创建的 `author.json` 中记录本地路径。
            *   **视频封面：** 根据“视频数据包”中提供的视频封面下载链接，下载封面到 `data/video_data/cover/`，并在 `video.json` 中记录本地路径。
            *   **视频/图集：**
                *   **普通视频：** 根据视频下载链接，下载视频文件到 `data/video_data/video/`，更新JSON。
                *   **图集：** 根据图集图片下载链接列表，下载所有图片到 `data/video_data/image/{视频ID}/`，更新JSON。
        *   **f. 更新去重状态库：**
            *   将成功处理的视频ID及其来源规则名称，添加到 `data/crawled_video_ids.json`。
            *   如果作者ID是新的，则将其ID及来源规则名称，添加到 `data/crawled_author_ids.json`。

**输出：**
*   `data/author_data/{作者ID}.json` (如果作者是新的)
*   `data/author_data/avatar/{作者ID}.jpg` (或其他格式，如果作者是新的)
*   `data/video_data/{视频ID}.json`
*   `data/video_data/cover/{视频ID}.jpg` (或其他格式)
*   `data/video_data/video/{视频ID}.mp4` (或图集目录 `data/video_data/image/{视频ID}/`)
*   更新后的 `data/crawled_video_ids.json` 和 `data/crawled_author_ids.json`。

---

### **二、爬取规则 (Crawl Rules)**

#### **1. 关键词规则 (Keywords Rule)**

**依赖任务：** 新爬取任务 (New Crawl Task)

**特有输入：**
*   用户提供的关键词列表。
*   用户设定的“最低点赞数”阈值。

**核心执行逻辑 (已确认变更)：**

1.  **遍历关键词列表：**
    *   依次处理列表中的每一个搜索词。

2.  **API搜索与精确过滤 (关键词规则特有逻辑)：**
    *   **调用视频搜索API：** 使用适合当前平台的视频搜索API（例如抖音 `POST /api/v1/douyin/search/video_search_v2`），传入当前关键词，并设置按点赞数降序排序。
    *   **处理API分页结果：**
        *   初始化一个空的“本关键词待处理视频数据包集合”。
        *   **循环获取每一页数据：**
            *   从API获取当前页的视频列表（期望API直接返回足够详细的视频和作者元数据，包括所有下载链接）。
            *   **遍历当前页的每个视频条目：**
                *   **a. 提取点赞数。**
                *   **b. 第一道点赞数过滤 (API层面优化 - 提前终止)：** 若当前视频点赞数低于阈值，则停止对当前关键词的搜索，不再请求后续分页。
                *   **c. 第二道点赞数过滤 (程序层面确保 - 针对当前条目)：** 若当前视频点赞数低于阈值，则跳过此视频。
                *   **d. 收集数据包：** 如果视频条目通过上述两道点赞数过滤，则将其从API获取到的**完整视频元数据**和**完整作者元数据**打包成一个“视频数据包”，加入“本关键词待处理视频数据包集合”。
            *   若未触发提前终止，则请求下一页，直至无更多数据或达最大页数。

3.  **调用新爬取任务：**
    *   将“本关键词待处理视频数据包集合”以及来源规则名称（例如 "douyin_cn_keywords_v2.6"）和当前任务上下文（平台、区域）作为输入，调用**新爬取任务 (New Crawl Task)** 的逻辑进行后续处理。

**输出：**
*   由调用的“新爬取任务”产生的输出。
*   针对每个关键词的执行日志和统计信息。

---

**已确认的变更点总结：**

*   **数据补充逻辑移除：** “新爬取任务”不再负责调用API补充数据，它期望上游规则（如关键词规则）通过调用相应的API（如视频搜索API、或者规则内部可能需要的单个详情API）直接提供足够完整的数据。
*   **输入调整：** “新爬取任务”的输入直接是包含完整信息的“视频数据包”。
*   **去重库调整：** `crawled_video_ids.json` 和 `crawled_author_ids.json` 中记录的每个ID都需要增加一个“来源规则”字段，用于追溯该ID是通过哪个规则获取的。
*   **关键词规则特有逻辑：** 点赞数过滤和调用视频搜索API。
*   **任务通用逻辑：** 去重、基于完整数据创建JSON、下载媒体、更新状态库。

**待讨论 (针对后续规则和任务)：**

*   **URL列表规则 和 作者列表规则** 在调用“新爬取任务”前，它们如何通过API获取到足够完整的“视频数据包”？
    *   **URL列表规则：** 可能需要对每个URL/ID调用“获取单个视频详情API”和“获取单个用户信息API”来构建“视频数据包”。
    *   **作者列表规则：** 可能需要先调用“获取用户信息API”，然后调用“获取用户作品列表API”，再对每个作品ID调用“获取单个视频详情API”来构建“视频数据包”。
*   **“仅更新元数据 (Update Metadata)”任务的逻辑：**
    *   它是否也期望上游规则提供完整的最新数据包？还是它自己负责调用API获取最新数据来覆盖本地JSON？（我倾向于后者，任务本身负责获取最新数据）
*   **“仅更新视频 (Update Video)”任务的逻辑：**
    *   它如何获取最新的媒体下载链接？如果本地JSON存在但不更新元数据，那么下载链接可能还是旧的。是否意味着在下载前，它至少需要调用API获取一次最新的媒体下载链接？

您看以上对关键词规则和新爬取任务的简化版业务逻辑描述是否准确，并且清晰地体现了您的变更意图？

如果这部分没有问题，我们就可以继续梳理URL规则和作者列表规则，以及它们与三种任务类型的交互逻辑了。

---

非常感谢您的澄清！您的解释让我对“仅更新元数据任务”的理解更加准确了，特别是关于**媒体文件下载**的部分，这与我之前的理解有显著不同。

我们来重新梳理“仅更新元数据任务”的业务逻辑，并确保完全符合您的最新指示。

## 阶段 1: 项目与功能定义
### 1.1. 初始项目设定 / 新功能请求

---

## 业务逻辑框架 (V2 - 基于API直出完整数据)

### **一、任务类型 (Task Types)**

#### **1. 新爬取任务 (New Crawl Task)**
*(已确认，内容同上，核心是：规则层提供完整数据包，任务层负责去重、创建JSON、下载媒体、更新状态)*

#### **2. 仅更新元数据任务 (Update Metadata Task) - V2 (根据您的最新反馈修订)**

**目标：** 基于上游规则提供的、针对特定条目（无论本地是否存在）的最新完整视频和作者数据，**强制更新或创建**本地对应的JSON记录，并**按需下载**相关媒体文件（仅当本地完全不存在时才下载）。

**核心前提：**
*   上游规则（如URL规则、作者规则在执行“更新元数据”模式时）已调用API获取了最新的、完整的“视频数据包”。
*   此任务**不执行任何去重逻辑**。其目的是确保指定条目的数据在本地得到刷新或创建。

**输入 (已确认)：**
*   一个“视频数据包”列表，每个数据包由上游规则提供，包含：
    *   **最新的、完整的视频元数据。**
    *   **最新的、完整的作者元数据。**
*   来源规则名称。
*   当前任务上下文（平台、区域）。

**核心执行逻辑 (已确认变更 - 重点关注媒体下载逻辑)：**

1.  **数据准备 (尝试移回旧JSON - 如果存在于uploaded目录)：**
    *   对于上游规则传入的每一个“视频数据包”中包含的视频ID和作者ID：
        *   **视频JSON：** 检查 `data/video_data/uploaded/{视频ID}.json` 是否存在。如果存在，则将其移回 `data/video_data/{视频ID}.json` (覆盖主工作区的同名文件，如果有的话)。
        *   **作者JSON：** 检查 `data/author_data/uploaded/{作者ID}.json` 是否存在。如果存在，则将其移回 `data/author_data/{作者ID}.json` (覆盖主工作区的同名文件，如果有的话)。
    *   **目的：** 如果之前该条目被处理并标记为“已上传”，现在要更新它，先把它“复原”到工作目录。

2.  **遍历“视频数据包”列表：**
    *   对于每个“视频数据包”：
        *   **a. 提取核心ID：** 从数据包中提取视频ID和作者ID。
        *   **b. 更新/创建JSON文件 (强制覆盖或新建)：**
            *   **作者JSON (`author.json`)：** 使用“视频数据包”中提供的最新完整作者元数据。
                *   如果 `data/author_data/{作者ID}.json` 已存在（可能通过上一步移回，或之前就在），则**覆盖**其内容。
                *   如果不存在，则在 `data/author_data/` 目录下**创建**新的 `{作者ID}.json` 文件。
            *   **视频JSON (`video.json`)：** 使用“视频数据包”中提供的最新完整视频元数据。
                *   如果 `data/video_data/{视频ID}.json` 已存在，则**覆盖**其内容。
                *   如果不存在，则在 `data/video_data/` 目录下**创建**新的 `{视频ID}.json` 文件。
        *   **c. 按需下载媒体文件 (核心变更点)：**
            *   **作者头像：**
                *   检查本地 `data/author_data/avatar/{作者ID}.{ext}` (以及 `data/author_data/uploaded/avatar_uploaded/{作者ID}.{ext}`，如果适用这种标记方式) 是否已存在有效的头像文件。
                *   如果**不存在**，则根据“视频数据包”中提供的作者头像下载链接，下载头像到 `data/author_data/avatar/`，并在对应的 `author.json` 中记录本地路径。
                *   如果**已存在**，则**跳过下载**。
            *   **视频封面：**
                *   检查本地 `data/video_data/cover/{视频ID}.{ext}` (以及 `uploaded` 对应目录) 是否已存在有效的封面文件。
                *   如果**不存在**，则根据“视频数据包”中提供的视频封面下载链接，下载封面到 `data/video_data/cover/`，并在对应的 `video.json` 中记录本地路径。
                *   如果**已存在**，则**跳过下载**。
            *   **视频/图集：**
                *   **普通视频：** 检查本地 `data/video_data/video/{视频ID}.mp4` (以及 `uploaded` 对应目录) 是否已存在有效的视频文件。如果不存在，则下载；如果存在，则跳过。
                *   **图集：** 检查本地 `data/video_data/image/{视频ID}/` 目录是否存在且包含图片。如果不存在或不完整，则根据图集图片下载链接列表，下载缺失的图片；如果已完整存在，则跳过。
                *   在对应的 `video.json` 中记录最终的本地路径或路径列表。

3.  **不更新去重状态库：**
    *   此任务不与 `crawled_video_ids.json` 和 `crawled_author_ids.json` 交互。

**输出：**
*   在 `data/video_data/` 和 `data/author_data/` 目录下被更新或新创建的JSON文件。
*   如果本地不存在，则会在相应的媒体子目录下下载新的图片和视频文件。

---

**对您反馈的确认和整合：**

*   **规则层获取数据，任务层处理数据：** 这个核心原则在“仅更新元数据任务”中也得到了贯彻。规则层（URL规则或作者规则在执行“更新元数据”模式时）负责调用API获取最新的完整数据包。
*   **强制更新/创建JSON：** 确认。无论本地JSON是否存在（在主工作区或uploaded区），都会被上游规则提供的新数据包内容所覆盖或据此创建。
*   **媒体下载逻辑 (核心变更)：**
    *   **您的明确指示：** “媒体下载，需要检测本地是否存在（根目录或uploaded目录），如果不存在，就要下载；如果存在，跳过下载。”
    *   **我的理解：** 这意味着“仅更新元数据任务”现在也承担了一部分“确保媒体文件存在”的责任，但前提是本地确实没有。这与“新爬取任务”中总是尝试下载（除非文件已存在且非强制）以及“仅更新视频任务”中总是强制下载（或按需创建）的行为有所不同。
    *   这个逻辑使得“仅更新元数据任务”处理后的数据，在上传到飞书时，可以被视为包含了最新元数据和（至少存在的）媒体文件的数据。

**这个修订后的“仅更新元数据任务”逻辑，与“新爬取任务”的主要区别在于：**

1.  **去重：** “新爬取”执行严格的视频ID和作者ID去重；“仅更新元数据”不执行去重。
2.  **JSON文件处理：** “新爬取”只为新条目创建JSON；“仅更新元数据”强制用新数据覆盖或创建JSON。
3.  **媒体下载：**
    *   “新爬取”：总是尝试下载新条目的媒体（除非本地已存在且非强制）。
    *   “仅更新元数据”：**仅当本地完全不存在对应媒体文件时才下载**。如果本地已存在，则不重新下载。

这个逻辑现在更加清晰地反映了您“强制更新数据，并确保媒体文件至少存在一份（如果之前没有的话）”的意图。

接下来，我们可以继续梳理**仅更新视频 (Update Video) 任务**的业务逻辑了。您准备好了吗？

明白了！您的意思是“仅更新视频任务”的核心就是复用“新爬取任务”中那个**强制下载媒体文件（删除旧的，下载新的）**的逻辑。这使得它的目标非常纯粹和聚焦。

## 阶段 1: 项目与功能定义
### 1.1. 初始项目设定 / 新功能请求

---

好的，我明白了！您对“仅更新视频任务”的定义非常清晰：**它等同于“仅更新元数据任务”的所有操作（强制更新/创建JSON，按需下载本地不存在的媒体），再加上一个额外的步骤——强制重新下载所有相关的媒体文件，无论本地是否已存在。**

这意味着它是一个比“仅更新元数据任务”更“彻底”的更新操作，不仅保证了元数据的最新，也保证了媒体文件的最新（或至少是本次任务获取到的版本）。

## 阶段 1: 项目与功能定义
### 1.1. 初始项目设定 / 新功能请求

---

## 业务逻辑框架 (V2 - 基于API直出完整数据)

### **一、任务类型 (Task Types)**

#### **1. 新爬取任务 (New Crawl Task)**
*(已确认，媒体下载逻辑已更正为强制下载)*

#### **2. 仅更新元数据任务 (Update Metadata Task)**
*(已确认，核心是：规则层提供最新完整数据包，任务层强制更新/创建JSON，仅当本地完全不存在媒体文件时才下载)*

#### **3. 仅更新视频任务 (Update Video Task) - (根据您的最新反馈定义)**

**目标：** 基于上游规则提供的、针对特定条目（无论本地是否存在）的最新完整视频和作者数据，**强制更新或创建**本地对应的JSON记录，并**强制重新下载**所有相关的媒体文件（删除已存在的旧文件）。

**核心前提：**
*   上游规则（如URL规则、作者规则在执行“更新视频”模式时）已调用API获取了最新的、完整的“视频数据包”。
*   此任务**不执行任何去重逻辑**。其目的是确保指定条目的数据和媒体文件在本地都得到最彻底的刷新或创建。

**输入 (与“仅更新元数据任务”相同)：**
*   一个“视频数据包”列表，每个数据包由上游规则提供，包含：
    *   **最新的、完整的视频元数据。**
    *   **最新的、完整的作者元数据。**
*   来源规则名称。
*   当前任务上下文（平台、区域）。

**核心执行逻辑 (基于“仅更新元数据任务”并增强媒体下载)：**

1.  **数据准备 (尝试移回旧JSON - 如果存在于uploaded目录)：**
    *   (逻辑同“仅更新元数据任务”)

2.  **遍历“视频数据包”列表：**
    *   对于每个“视频数据包”：
        *   **a. 提取核心ID。**
        *   **b. 更新/创建JSON文件 (强制覆盖或新建)：**
            *   (逻辑同“仅更新元数据任务”)
        *   **c. 强制重新下载媒体文件 (核心增强点)：**
            *   **作者头像：**
                *   检查本地 `data/author_data/avatar/{作者ID}.{ext}` (以及 `uploaded` 对应目录) 是否已存在头像文件。
                *   如果**已存在，则先删除本地的旧头像文件。**
                *   根据“视频数据包”中提供的作者头像下载链接，**强制下载**新的头像到 `data/author_data/avatar/`，并在对应的 `author.json` 中记录本地路径。
            *   **视频封面：**
                *   检查本地 `data/video_data/cover/{视频ID}.{ext}` (以及 `uploaded` 对应目录) 是否已存在封面文件。
                *   如果**已存在，则先删除本地的旧封面文件。**
                *   根据“视频数据包”中提供的视频封面下载链接，**强制下载**新的封面到 `data/video_data/cover/`，并在对应的 `video.json` 中记录本地路径。
            *   **视频/图集：**
                *   **普通视频：** 检查本地 `data/video_data/video/{视频ID}.mp4` (以及 `uploaded` 对应目录) 是否已存在视频文件。如果已存在，则先删除。然后**强制下载**新的视频文件。
                *   **图集：** 检查本地 `data/video_data/image/{视频ID}/` 目录是否存在。如果存在，则先**删除整个目录及其内容**。然后根据图集图片下载链接列表，**强制下载**所有图片到新的 `data/video_data/image/{视频ID}/` 目录。
                *   在对应的 `video.json` 中记录最终的本地路径或路径列表。

3.  **不更新去重状态库：**
    *   (逻辑同“仅更新元数据任务”)

**输出：**
*   在 `data/video_data/` 和 `data/author_data/` 目录下被更新或新创建的JSON文件。
*   在相应的媒体子目录下**强制重新下载**的最新图片和视频文件。

---

**这个“仅更新视频任务”与另外两个任务的关键区别总结：**

| 特性         | 新爬取 (New Crawl)                                 | 仅更新元数据 (Update Metadata)                     | 仅更新视频 (Update Video)                               |
| :----------- | :------------------------------------------------- | :------------------------------------------------- | :------------------------------------------------------ |
| **去重逻辑**   | 执行视频ID和作者ID去重                             | **不执行**去重                                     | **不执行**去重                                          |
| **JSON处理** | 为新条目创建JSON                                   | 强制用新数据**覆盖或创建**JSON                     | 强制用新数据**覆盖或创建**JSON                          |
| **媒体下载** | **强制下载** (删除本地旧文件)                         | **按需下载** (仅当本地完全不存在时)                  | **强制下载** (删除本地旧文件)                              |
| **更新去重库** | 是                                                 | 否                                                 | 否                                                      |

现在，我们对三个核心任务类型的业务逻辑都有了清晰的定义，并且明确了它们都依赖于上游规则提供完整的“视频数据包”。

**已确认 (基于您之前的反馈和本次讨论方向)：**

*   所有任务类型都遵循“规则层获取数据，任务层处理数据”的核心原则。
*   “新爬取任务”强制下载媒体，并更新去重库。
*   “仅更新元数据任务”强制更新/创建JSON，仅在本地无媒体时下载，不更新去重库。
*   “仅更新视频任务”强制更新/创建JSON，并强制重新下载所有媒体，不更新去重库。

**待讨论 (针对规则层如何适配这些任务)：**

既然所有任务都期望规则层提供完整的“视频数据包”，那么：

1.  **URL列表规则**在执行这三种任务时，其自身的逻辑是什么？
    *   它是否总是需要先对每个URL/ID调用“获取单个视频详情API”和“获取单个用户信息API”来构建这个“视频数据包”，然后再传递给相应的任务？
2.  **作者列表规则**在执行这三种任务时，其自身的逻辑是什么？
    *   它是否总是需要先调用“获取用户信息API”，然后“获取用户作品列表API”，再对每个作品ID调用“获取单个视频详情API”来构建“视频数据包”，然后再传递给相应的任务？

如果规则层总是负责准备好这个包含所有必要信息的“视频数据包”，那么任务层的逻辑确实可以保持非常纯粹和通用。

我们接下来可以梳理**URL列表规则**与这三个任务的交互逻辑了。您准备好了吗？



好的，收到您的反馈！我们来更新 `author.json` 的字段清单。

## 阶段 2: 核心设计框架 (PDD/SDD) 与迭代开发策略
### 2.1. 产品设计文档 (PDD/SDD) 初始框架定义 / 重大更新规划

---
好的，您的安排非常合理。我们先聚焦 `crawl.py` 的重构，确保数据采集端稳定且数据丰富后，再来处理 `upload.py` 的适配和飞书端的设计。

关于您提到的关键点：“**飞书能够支持的字段类型，这个需要你提前研究已有的 `upload.py` 和 `UPLOAD_DESIGN.MD`，确保在 `crawl.py` 规则层对字段进行转换时，能够兼容。**”

这一点至关重要，我完全理解其重要性。我们需要在设计 `crawl.py` 输出的JSON（即我们内部的标准数据模型）时，就考虑到这些数据最终要如何优雅地存入飞书。

## 阶段 1: 项目与功能定义
### 1.1. 初始项目设定 / 新功能请求

---

好的，我已经仔细回顾了我们的整个对话记录，以及所有您上传的附件内容（`OVERVIEW.MD`, `UPLOAD_DESIGN.MD`, `CURSOR_PROJECT_RULES.MD`, `CRAWL_DESIGN.MD`, `crawl.py`, `upload.py`, `requirements.txt`, `config.json`, `__init__.py`, `feishu_api.py`, `utils.py`, JS脚本, JSON数据文件, `TIKHUB_API.md`）。

在字段定义之前的业务逻辑和交互方式层面，我认为我们已经覆盖得相当全面和细致了。主要的逻辑流程、任务职责、规则特性、API使用策略、错误处理方向、用户交互体验等方面都经过了多次迭代和确认。

**目前来看，在“业务逻辑和交互方式（不含具体字段定义）”这个范畴内，我暂时没有发现明显的漏洞或严重不明确的地方。** 我们已经就以下关键点达成了共识：

*   **核心架构原则：** API优先，规则层获取完整数据，任务层处理数据。
*   **三大任务类型定义：** 新爬取、仅更新元数据、仅更新视频的详细操作流程和差异。
*   **三大规则类型定义：** 关键词、URL、作者规则的特有逻辑以及如何调用任务层。
*   **API端点选择偏好：** 优先app接口、最新版本、国外CDN。
*   **关键业务决策：**
    *   关键词规则的点赞数过滤方式（API排序 + 程序端双重过滤与提前终止）。
    *   关键词规则固定为“新爬取”任务。
    *   作者ID去重策略（策略3：仅跳过作者信息更新，不跳过视频）。
    *   `crawled_ids.json` 更名为 `crawled_video_ids.json`，并与 `crawled_author_ids.json` 一同记录来源规则。
    *   URL规则使用混合解析API。
    *   作者规则依赖 `fetch_user_posts` API返回的内嵌完整作者信息。
    *   媒体文件下载策略（新爬取强制下载并删除旧文件，仅更新元数据按需下载，仅更新视频强制下载并删除旧文件）。
*   **交互模式流程：** 先选规则类型 -> 平台 -> 区域 -> 任务类型（如需）-> 文件 -> 特定参数（如需）-> 确认，并支持每步返回上一级和退出。
*   **目录结构调整。**

**如果要吹毛求疵地寻找潜在的“不明确”（更偏向于待细化而非漏洞），可能是在具体实现时需要进一步明确的细节：**

1.  **“来源规则”字段的具体格式和记录时机：** 在 `crawled_video_ids.json` 和 `crawled_author_ids.json` 中，我们决定为每个ID增加“来源规则”。这个字段是简单记录规则名称字符串（如 "douyin_cn_keywords_v2.6"），还是需要更结构化的信息？它是在哪个具体步骤被写入的？（逻辑上应该是在“新爬取任务”的最后一步，与ID一同写入）。
2.  **“作者ID去重”对“新爬取任务”中作者JSON创建的影响：** 我们确认了作者ID去重采用策略3（仅跳过作者信息API更新）。在“新爬取任务”中，当处理一个视频，其关联的作者ID已存在于 `crawled_author_ids.json` 时：
    *   我们不为这个作者调用作者信息API（因为已有完整数据）。
    *   那么，这个作者的 `author.json` 文件（如果之前是通过其他规则的“新爬取”任务创建的）是否需要更新其 `last_updated_crawl` 时间戳或任何其他元数据？还是完全不碰它？（我倾向于，既然作者ID已在去重库，说明其JSON是“最终版”，除非是“仅更新元数据/视频”任务，否则不应再修改）。
3.  **API速率限制的应对策略：** `TIKHUB_API.md` 提到了 QPS: 10/Second。虽然我们可以在 `_send_http_request` 中加入延时，但在高并发或快速连续调用（如批量获取多个作者的帖子列表，再对每个帖子调用详情）时，如何更优雅地处理速率限制，例如实现一个全局的请求调度器或令牌桶算法，这属于实现层面的细化。
4.  **“最大页数限制”的具体值：** 在关键词搜索或获取作者帖子列表时，我们提到了“直到没有更多数据或达到最大页数限制”。这个“最大页数限制”是用户可配置的，还是程序内置的一个合理上限？（建议可配置，并在`config.json`中设定默认值）。
5.  **“数据准备（移回旧JSON）”步骤的精确性：** 在“仅更新元数据”和“仅更新视频”任务中，我们有移回 `uploaded` 目录下JSON的步骤。如果主工作区和 `uploaded` 目录同时存在同名JSON（理论上不应该，但以防万一），移动操作（覆盖）的行为需要明确。shutil.move在目标已存在时会报错，所以通常是先删除目标再移动，或者直接用新数据覆盖。我们的逻辑是“覆盖主工作区的同名文件，如果有的话”，这意味着如果主工作区有，它会被来自uploaded的（如果存在）或后续API获取的覆盖。

这些点更多是实现细节的确认，不算是业务逻辑本身的漏洞。

---

现在，我将按照您的要求，**完整地输出我们目前已讨论确认的所有业务逻辑和交互方式**，不省略关键信息，并整合所有已确认的变更和决策。这将作为我们后续细化字段定义、更新PDD/SDD以及编写开发计划的基础。

---

## **业务逻辑与交互模式框架 (V3.1 - API驱动最终版)**

### **核心原则**

*   **API优先：** 所有平台的数据获取均通过 `api.tikhub.io` 进行。
*   **规则层获取完整数据：** 各爬取规则（关键词、URL、作者）负责调用API获取足够完整的“视频数据包”（包含视频元数据和作者元数据）。
*   **任务层处理数据：** 核心任务类型（新爬取、仅更新元数据、仅更新视频）基于规则层提供的完整数据包执行具体操作，如去重、JSON文件管理、媒体下载和状态更新。任务层自身不直接调用数据获取API来“补充”信息。
*   **模块化与复用：** 任务逻辑设计为平台无关，规则逻辑通过平台适配器调用特定API。

### **一、数据存储与状态管理文件**

*   **作者元数据JSON：** `data/author_data/{作者ID}.json`
*   **作者头像：** `data/author_data/avatar/{作者ID}.{ext}`
*   **视频元数据JSON：** `data/video_data/{视频ID}.json`
*   **视频封面：** `data/video_data/cover/{视频ID}.{ext}`
*   **视频文件：** `data/video_data/video/{视频ID}.mp4`
*   **图集图片：** `data/video_data/image/{视频ID}/{图片序号}.{ext}`
*   **已上传标记目录 (供 `upload.py` 使用，具体文件移动逻辑待 `upload.py` 设计时细化)：**
    *   `data/author_data/uploaded/` (可能包含 `avatar_uploaded/` 和 作者JSON)
    *   `data/video_data/uploaded/` (可能包含 `cover_uploaded/`, `image_uploaded/`, `video_uploaded/` 和 视频JSON)
*   **视频ID去重库：** `data/crawled_video_ids.json`
    *   结构：`{"processed_ids": [{"id": "video_id_1", "source_rule": "rule_name_A"}, ...], "count": N, "updated_at": "timestamp"}`
*   **作者ID去重库：** `data/crawled_author_ids.json` (新增)
    *   结构：`{"processed_ids": [{"id": "author_id_1", "source_rule": "rule_name_B"}, ...], "count": N, "updated_at": "timestamp"}`
*   **其他状态文件：** `keywords.json`, `platform_region.json` (逻辑基本保持，但其与新规则的交互待细化)。

### **二、任务类型 (Task Types)**

#### **1. 新爬取任务 (New Crawl Task)**

*   **目标：** 基于上游规则提供的完整数据包，进行去重，创建JSON，强制下载最新媒体，更新去重库。
*   **输入：** “视频数据包”列表、来源规则名称、当前任务上下文。
*   **核心逻辑：**
    1.  遍历“视频数据包”列表：
        *   a. 提取视频ID和作者ID。
        *   b. **视频ID去重：** 若视频ID已在 `crawled_video_ids.json` 中，跳过此数据包。
        *   c. **作者ID去重状态记录：** 记录作者ID是否存在于 `crawled_author_ids.json` (标记为 `author_is_known`)。
        *   d. **创建JSON文件：**
            *   **作者JSON：** 若 `author_is_known` 为 `False`，则根据数据包在 `data/author_data/` 创建 `{作者ID}.json`。
            *   **视频JSON：** 根据数据包在 `data/video_data/` 创建 `{视频ID}.json`。
        *   e. **强制下载媒体文件 (删除本地旧文件)：**
            *   **作者头像：** 若 `author_is_known` 为 `False`，删除旧头像（如果存在于 `data/author_data/avatar/` 或对应 `uploaded` 目录），下载新头像。
            *   **视频封面：** 删除旧封面（如果存在于 `data/video_data/cover/` 或对应 `uploaded` 目录），下载新封面。
            *   **视频/图集：** 删除旧视频文件或整个图集目录（如果存在于 `data/video_data/video/` 或 `data/video_data/image/` 或对应 `uploaded` 目录），下载新视频/图集。
            *   在JSON中记录新的本地路径。
        *   f. **更新去重状态库：**
            *   将成功的视频ID和来源规则加入 `crawled_video_ids.json`。
            *   若 `author_is_known` 为 `False` 且作者处理成功，将其ID和来源规则加入 `crawled_author_ids.json`。

#### **2. 仅更新元数据任务 (Update Metadata Task)**

*   **目标：** 基于上游规则提供的最新完整数据包，强制更新/创建本地JSON，仅当本地完全不存在媒体时下载。
*   **输入：** “视频数据包”列表、来源规则名称、当前任务上下文。
*   **核心逻辑：**
    1.  **数据准备 (移回旧JSON)：** 将 `uploaded` 目录中对应的JSON文件（如果存在）移回主工作目录并覆盖。
    2.  遍历“视频数据包”列表：
        *   a. 提取视频ID和作者ID。
        *   b. **更新/创建JSON文件 (强制覆盖或新建)：** 使用数据包中的最新完整元数据覆盖或创建 `author.json` 和 `video.json`。
        *   c. **按需下载媒体文件：**
            *   **作者头像：** 仅当本地 `data/author_data/avatar/` (及对应 `uploaded` 目录) 中不存在该作者头像时才下载。
            *   **视频封面：** 仅当本地 `data/video_data/cover/` (及对应 `uploaded` 目录) 中不存在该视频封面时才下载。
            *   **视频/图集：** 仅当本地 `data/video_data/video/` 或 `data/video_data/image/` (及对应 `uploaded` 目录) 中不存在对应视频文件或图集时才下载。
            *   在JSON中记录本地路径。
    3.  **更新去重状态库。**

#### **3. 仅更新视频任务 (Update Video Task)**

*   **目标：** 基于上游规则提供的最新完整数据包，强制更新/创建本地JSON，并强制重新下载所有相关媒体。
*   **输入：** “视频数据包”列表、来源规则名称、当前任务上下文。
*   **核心逻辑：**
    1.  **数据准备 (移回旧JSON)：** (同“仅更新元数据任务”)
    2.  遍历“视频数据包”列表：
        *   a. 提取视频ID和作者ID。
        *   b. **更新/创建JSON文件 (强制覆盖或新建)：** (同“仅更新元数据任务”)
        *   c. **强制重新下载媒体文件 (删除本地旧文件)：** (逻辑同“新爬取任务”的媒体下载部分)
            *   作者头像、视频封面、视频/图集均删除本地旧文件（如果存在）并重新下载。
            *   在JSON中记录新的本地路径。
    3.  **更新去重状态库。**

---

### **三、爬取规则 (Crawl Rules)**

#### **1. 关键词规则 (Keywords Rule)**

*   **依赖任务：** 新爬取任务 (New Crawl Task) - 固定。
*   **特有输入：** 关键词列表、最低点赞数阈值。
*   **核心逻辑：**
    1.  遍历关键词列表。
    2.  **API搜索与精确过滤：**
        *   调用视频搜索API（如抖音 `POST /api/v1/douyin/search/video_search_v2`），按点赞数降序排序。
        *   处理API分页结果：
            *   对每页的每个视频条目：
                *   a. 提取点赞数。
                *   b. **第一道点赞数过滤 (提前终止)：** 若点赞数 < 阈值，停止对当前关键词的后续分页。
                *   c. **第二道点赞数过滤 (确保当前条目)：** 若点赞数 < 阈值，跳过此视频。
                *   d. **收集数据包：** 若通过，将API返回的完整视频和作者元数据打包，加入“本关键词待处理视频数据包集合”。
    3.  **调用新爬取任务：** 将“本关键词待处理视频数据包集合”、来源规则名称、上下文传递给“新爬取任务”处理。

#### **2. URL列表规则 (URLs Rule)**

*   **依赖任务：** 新爬取任务, 仅更新元数据任务, 仅更新视频任务 (根据用户选择)。
*   **特有输入：** 视频URL列表、用户选择的任务类型。
*   **核心逻辑：**
    1.  遍历URL列表。
    2.  **调用混合解析API：** 对每个URL，调用 `GET /api/v1/hybrid/parse_video` 获取包含完整视频和作者元数据的“视频数据包”。
    3.  **数据有效性检查。**
    4.  **调用选定任务：** 将获取的单个“视频数据包”、来源规则名称、上下文传递给用户选定的任务类型逻辑进行处理。

#### **3. 作者列表规则 (Authors Rule)**

*   **依赖任务：** 新爬取任务, 仅更新元数据任务, 仅更新视频任务 (根据用户选择)。
*   **特有输入：** 作者ID列表、用户选择的任务类型。
*   **核心逻辑：**
    1.  遍历作者ID列表。
    2.  **调用用户作品列表API：** 对每个作者ID，调用平台特定的用户作品列表API（如抖音 `GET /api/v1/douyin/app/v3/fetch_user_posts`），处理分页，获取该作者的所有“视频数据包”（期望包含完整的视频和作者元数据）。
    3.  **处理作者本身的JSON记录 (基于第一个视频数据包)：**
        *   根据第一个视频数据包中的作者信息和用户选择的任务类型，处理该作者的 `author.json` 和头像（创建/更新/强制更新，下载逻辑遵循对应任务类型的媒体下载规则）。
        *   如果是新爬取任务且作者是新的，则将其加入 `crawled_author_ids.json`。
    4.  **数据有效性检查 (针对视频数据包)。**
    5.  **调用选定任务处理视频：** 将获取的“视频数据包”列表、来源规则名称、上下文传递给用户选定的任务类型逻辑进行处理（任务内部会根据视频ID和已知的作者状态进行操作）。

---

### **四、交互模式 (Interactive Mode)**

*   **核心目标：** 引导用户配置并执行任务，提供灵活导航。
*   **通用导航：** 每步提供 `b` (返回上一问题) 和 `q` (退出) 选项。
*   **流程：**
    1.  **[主菜单] 选择规则类型** (关键词, URL, 作者)。
    2.  **选择目标平台** (根据规则动态显示支持的平台，新平台持久化)。
    3.  **选择目标区域** (根据平台动态显示支持的区域，新区域持久化，如平台无需区域则跳过)。
    4.  **选择任务类型** (仅当规则为URL或作者时提示；关键词规则固定为“新爬取”)。
    5.  **选择输入文件** (`.txt` from `input/`)。
    6.  **输入特定参数** (如关键词规则的最低点赞数)。
    7.  **确认执行** (显示所有配置，提供 `y/n/b/q` 选项)。
    8.  **执行任务** 并显示结果。

---

这份整合了所有已确认决策的业务逻辑和交互模式框架，应该可以作为我们后续工作的坚实基础了。它强调了API驱动、规则层获取完整数据、任务层专注处理的核心思想，并明确了各个组件的职责和交互方式。

接下来，当您准备好提供API的真实返回字段数据时，我们就可以开始最关键也是最细致的一步：**字段定义和映射**，并据此更新 `CRAWL_DESIGN.MD` 和 `UPLOAD_DESIGN.MD` 的数据结构部分。

---


**飞书字段兼容性预研与 `crawl.py` 数据输出设计原则：**

我已经回顾了您提供的 `upload.py` (V1.0.0 代码) 和 `UPLOAD_DESIGN.MD` (V2.2 - Code Aligned)。特别是 `UPLOAD_DESIGN.MD` 的第3节“飞书多维表格结构设计”和附录A“飞书 API 关键信息参考”，为我们提供了宝贵的参考。

**从现有文档中，我们可以总结出飞书多维表格支持的关键字段类型及其在 `upload.py` 中期望的写入格式：**

| 飞书字段类型 (Type ID) | `upload.py` 写入格式 (Value)          | 对应 `crawl.py` 输出JSON时需考虑的数据类型/格式 |
| :--------------------- | :------------------------------------ | :------------------------------------------------ |
| 文本 (1)               | 字符串                                | `str`                                             |
| 数字 (2)               | 数字 (int/float)                      | `int`, `float`                                    |
| **单选 (3)**           | **字符串 "选项名"**                   | `str` (需要确保是飞书选项库中已有的或可动态添加的) |
| **多选 (4)**           | **字符串数组 `["选项名1", "选项名2"]`** | `list` of `str` (同上，选项管理)                  |
| **日期时间 (5)**       | **Unix 时间戳 (毫秒, int)**           | `int` (表示毫秒级Unix时间戳)                      |
| 复选框 (7)             | 布尔值 `true`/`false`                 | `bool`                                            |
| **超链接 (15)**        | **字典 `{"text": "显示文本", "link": "URL"}`** | `dict` with `text` and `link` keys, or just `str` (URL) which `upload.py` can format |
| **附件 (17)**          | 数组 `[{"file_token": "..."}]`        | `upload.py` 会处理文件上传并获取 `file_token`，`crawl.py` 主要提供本地文件路径 |
| 双向关联 (21)        | 数组 `["record_id"]`                  | `upload.py` 会处理关联逻辑，`crawl.py` 主要提供关联对象的ID (如 `author_id`) |
| **人员 (11)**          | 数组 `[{"id": "飞书用户open_id"}]`     | (如果未来需要) `str` or `list` of `str` (飞书用户ID) |
| **查找引用 (18)**      | (通常只读，由飞书公式或关联产生)          | N/A (由飞书内部逻辑决定)                            |
| **公式 (20)**          | (只读)                                | N/A                                               |
| **评分 (22)**          | 数字 (通常1-5)                        | `int`                                             |
| **货币 (23)**          | 数字                                  | `float`                                           |
| **百分比 (24)**        | 数字 (例如 0.75 代表 75%)             | `float`                                           |

**基于以上信息，在 `crawl.py` (规则层/数据映射层) 生成最终JSON数据时，我们需要遵循以下原则，以确保与未来 `upload.py` 和飞书的兼容性：**

1.  **数值型数据：**
    *   点赞数、评论数、粉丝数、时长（秒或毫秒）等，应确保输出为 `int` 或 `float` 类型。
    *   `UPLOAD_DESIGN.MD` 中数字字段通常期望整数，如果API返回的是带单位的文本（如 "10.5K"），`crawl.py` 的数据映射层就需要将其转换为纯数字（如 `10500`）。

2.  **时间日期型数据：**
    *   发布时间、更新时间等，`crawl.py` 输出的JSON中应统一存储为**毫秒级Unix时间戳 (integer)**，或者一个**标准的可解析的日期时间字符串** (如 "YYYY-MM-DD HH:MM:SS" 或 ISO 8601格式)，由 `upload.py` 在写入飞书日期时间字段前进行最终转换（如果飞书API直接接受时间戳则更好）。
    *   `UPLOAD_DESIGN.MD` V2.2 中明确指出日期时间字段写入格式是 **Unix ms (int)**。因此，`crawl.py` 的数据映射层最好直接将API返回的各种时间格式（如秒级时间戳、特定格式字符串）转换为毫秒级Unix时间戳。

3.  **布尔型数据：**
    *   是否认证、是否私密等，应输出为 `bool` (`true`/`false`)。

4.  **文本型数据：**
    *   标题、描述、简介、ID类等，输出为 `str`。
    *   对于可能很长的文本（如简介、完整描述），飞书表格字段应设置为“多行文本”。

5.  **URL/链接型数据：**
    *   视频播放链接、封面链接、头像链接、作者主页链接等。
    *   `crawl.py` 输出的JSON中可以直接存储URL字符串。
    *   `upload.py` 在写入飞书的超链接字段时，会将其包装成 `{"text": "描述文字", "link": "URL字符串"}` 的格式。我们可以约定 `crawl.py` 输出的JSON中，如果某个字段是URL，其键名可以暗示其用途（如 `avatar_url`, `video_play_url`），`upload.py` 据此生成 `text` 部分。或者 `crawl.py` 直接输出一个包含 `text` 和 `link` 的字典（但这可能过度耦合）。**我建议 `crawl.py` 只输出URL字符串，由 `upload.py` 负责格式化。**

6.  **单选/多选型数据 (如平台、区域、标签)：**
    *   **平台 (`platform`) / 区域 (`region`):** `crawl.py` 应输出规范化的字符串值（如 "douyin", "cn"）。`upload.py` 在写入飞书单选字段时，会确保这些值是选项库的一部分（按需创建）。
    *   **标签 (`tags`):** `crawl.py` 应输出一个字符串列表 `["标签1", "标签2"]`。`upload.py` 在写入飞书多选字段时，会确保这些标签是选项库的一部分（按需创建）。

7.  **媒体文件相关：**
    *   `crawl.py` 的JSON输出中，对于视频、封面、头像、图集图片，主要记录它们在**本地的相对路径**（例如 `data/video_data/video/123.mp4`）。
    *   `upload.py` 会根据这些本地路径找到文件，上传到飞书空间，获取 `file_token`，然后用 `file_token` 更新飞书的附件字段。

8.  **关联关系：**
    *   `crawl.py` 输出的视频JSON中，应包含其关联的作者ID (`author_id`，即 `sec_uid` 的规范化版本)。
    *   `upload.py` 在处理时，会先确保作者记录已存在于飞书账号表并获取其飞书 `record_id`，然后在创建/更新视频素材记录时，使用这个飞书 `record_id` 来填充双向关联字段。

**核心结论：**

*   **`crawl.py` 的数据映射层在将API原始数据转换为我们内部标准JSON时，需要有意识地进行数据类型和格式的规范化，使其尽量接近飞书字段期望的原始数据类型。**
*   **时间戳统一为毫秒级 `int` 是一个关键的约定。**
*   **URL输出为字符串，由 `upload.py` 处理飞书格式。**
*   **单选/多选的选项值输出为字符串或字符串列表，由 `upload.py` 处理选项库同步。**
*   **媒体文件输出为本地路径。**

这样，当我们后续详细定义每个字段时，就可以参照这些原则来确保 `crawl.py` 的输出能够被 `upload.py` 顺利消费并存入飞书。

---

**关于您提出的第二和第三个问题（`crawl.py` 重构的其他讨论点，以及规则名称调整）：**

这两点我们之前的讨论已经覆盖得比较充分了，并且达成了共识：

*   **`crawl.py` 重构的其他讨论点：** 包括核心控制流程、`utils.py` 的拆分与增强（API请求核心、平台API封装移出、数据模型与映射器模块化、状态管理类调整、下载逻辑、移除Selenium相关）、错误处理与日志统一、配置管理调整、以及 `CRAWL_DESIGN.MD` 的重写。这些都是我们后续实施重构时需要逐步解决的。
*   **规则名称调整：** 同意采用更抽象、更类型化的命名方式（如 `RULE_TYPE_KEYWORDS`），并通过“通用规则执行器 + 平台适配器”的模式实现，交互模式也将相应调整。

---

**至此，我认为在进入具体字段定义之前，我们对整体的业务逻辑、交互方式、重构方向、以及与下游（`upload.py` 和飞书）的兼容性原则，都已经有了非常清晰和一致的蓝图。**

现在，我们已经为下一步——**详细梳理和定义 `author.json` 和 `video.json` (即我们内部的标准数据模型) 所需的具体字段，并准备好从 `TIKHUB_API.md` 中查找和映射这些字段**——做好了充分的准备。

当您方便提供API的真实返回字段数据样本时，或者如果您希望我们先基于`TIKHUB_API.md`文档中的示例响应来初步定义字段清单，请告诉我。


---


**`author.json` 标准数据模型字段建议清单 (V0.2 草案 - 根据您的反馈修订)**

**已删除字段：**
*   `avatar_url_medium_res`
*   `avatar_url_thumbnail`
*   `school_name`
*   `commerce_user_info`
*   `account_status`

**新增/调整字段：**
*   新增 `local_avatar_attachment_token` (用于飞书附件)
*   明确 `profile_url_web` 的构造逻辑 (已在原列表的 `profile_url_web` 中体现，这里再次强调)

| 内部字段名 (建议)             | 数据类型 (建议)          | 描述/用途                                                                 | 抖音API来源 (TIKHUB_DOUYIN_FIELDS.md 示例)                                                                                               | TikTokAPI来源 (TIKHUB_TIKTOK_FIELDS.md 示例)                                                                                             | 核心与否 | 飞书兼容性备注                                      |
| :---------------------------- | :----------------------- | :------------------------------------------------------------------------ | :--------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------- | :------- | :-------------------------------------------------- |
| **`author_id`**               | `string`                 | **作者的唯一主ID (我们内部使用，通常基于`sec_uid`规范化得到)**              | `author.sec_uid` (规范化)                                                                                                                | `author.uid` 或 `author.sec_uid` (选择一个作为主ID，规范化)                                                                              | **核心** | 文本 (作为业务主键)                                 |
| `sec_uid`                     | `string`                 | 平台提供的安全用户ID (API调用关键)                                          | `author.sec_uid`                                                                                                                         | `author.sec_uid` (如果API提供)                                                                                                           | **核心** | 文本                                                |
| `platform_user_id`            | `string`                 | 平台原始用户ID (如抖音的`uid`, TikTok的`uid`)                                | `author.uid`                                                                                                                             | `author.uid`                                                                                                                             | 可选     | 文本                                                |
| `unique_id`                   | `string`                 | 用户设置的唯一ID (如抖音号、TikTok ID)                                      | `author.unique_id`                                                                                                                       | `author.unique_id`                                                                                                                       | 推荐     | 文本                                                |
| **`nickname`**                | `string`                 | **作者的显示昵称**                                                        | `author.nickname`                                                                                                                        | `author.nickname`                                                                                                                        | **核心** | 文本                                                |
| `signature`                   | `string`                 | 作者的个人简介/签名                                                           | `author.signature`                                                                                                                       | `author.signature` (或 `K1. bio`)                                                                                                        | 推荐     | 文本 (多行)                                         |
| **`avatar_url_high_res`**     | `string`                 | **作者的高清头像URL (优先国外CDN)**                                         | `author.avatar_larger.url_list[0]` (选择最佳)                                                                                            | `author.avatar_larger.url_list[0]` (或 `K2. avatar_larger.url_list[0]`, 选择最佳)                                                         | **核心** | 超链接 (或文本URL，主要用于下载)                      |
| `local_avatar_path`           | `string`                 | 下载到本地的头像文件相对路径                                                  | (由程序下载后填充)                                                                                                                       | (由程序下载后填充)                                                                                                                       | **核心** | 文本 (用于`upload.py`找到文件)                      |
| **`local_avatar_attachment`** | `object`                 | **(新增) 用于飞书附件的结构，包含`file_token` (由`upload.py`填充)**        | (由`upload.py`处理本地头像并上传后填充)                                                                                                   | (由`upload.py`处理本地头像并上传后填充)                                                                                                   | **核心** | 附件 (飞书类型17)                                   |
| **`follower_count`**          | `integer`                | **粉丝数量**                                                              | `author.follower_count`                                                                                                                  | `author.follower_count` (或 `K3. follower_count`)                                                                                        | **核心** | 数字                                                |
| `following_count`             | `integer`                | 关注数量                                                                  | `author.following_count` (需`fetch_user_info`)                                                                                          | `author.following_count` (或 `K3. following_count`, 需`fetch_user_info`)                                                                  | 推荐     | 数字                                                |
| **`aweme_count`**             | `integer`                | **作品数量 (视频/图集总数)**                                              | `author.aweme_count` (需`fetch_user_info`)                                                                                              | `author.aweme_count` (或 `K3. video_count_text` 解析, 需`fetch_user_info`)                                                              | **核心** | 数字                                                |
| `total_favorited_count`       | `integer`                | 作者所有作品获得的总点赞数                                                    | `author.total_favorited` (需`fetch_user_info`)                                                                                          | `author.total_favorited` (或 `K3. likes_count_text` 解析, 需`fetch_user_info`)                                                           | 推荐     | 数字                                                |
| `is_verified`                 | `boolean`                | 是否认证用户                                                                | `author.is_verified` (或从 `author.verification_type` 判断)                                                                              | `author.is_verified` (或 `K_X. verified`)                                                                                                | 推荐     | 复选框                                              |
| `verification_type`           | `string`                 | 认证类型描述                                                              | `author.verification_type` (可能需要映射为可读文本)                                                                                      | (需确认TikTok API是否有类似字段)                                                                                                         | 可选     | 文本 或 单选                                        |
| `custom_verify_info`          | `string`                 | 自定义认证信息/头衔                                                         | `author.custom_verify` (需`fetch_user_info`)                                                                                            | (需确认TikTok API是否有类似字段)                                                                                                         | 可选     | 文本                                                |
| `is_private_account`          | `boolean`                | 是否为私密账户                                                              | `author.secret` (或 `author.is_private_account`)                                                                                         | `author.secret` (或 `author.is_private_account`)                                                                                         | 可选     | 复选框                                              |
| **`platform`**                | `string`                 | **数据来源平台 ("douyin", "tiktok")**                                       | (由程序上下文填充)                                                                                                                       | (由程序上下文填充)                                                                                                                       | **核心** | 单选                                                |
| **`region`**                  | `string`                 | **数据来源地区 (如 "cn", "jp", "us", "global")**                            | (由程序上下文填充，或从API `author.region` 获取)                                                                                         | (由程序上下文填充，或从API `author.region` 获取)                                                                                         | **核心** | 单选                                                |
| `language`                    | `string`                 | 作者设置的语言                                                              | `author.language` (需`fetch_user_info`)                                                                                                 | (需确认TikTok API是否有类似字段)                                                                                                         | 可选     | 单选 或 文本                                        |
| `gender`                      | `string`                 | 性别 ("male", "female", "unknown")                                          | `author.gender` (可能需要映射为标准值, 需`fetch_user_info`)                                                                             | (需确认TikTok API是否有类似字段, 需`fetch_user_info`)                                                                                     | 可选     | 单选                                                |
| `birthday`                    | `string`                 | 生日 (YYYY-MM-DD 格式)                                                      | (需`fetch_user_info`)                                                                                                                   | (需`fetch_user_info`)                                                                                                                   | 可选     | 日期                                                |
| `city`                        | `string`                 | 所在城市                                                                  | `author.city` (需`fetch_user_info`)                                                                                                     | (需确认TikTok API是否有类似字段, 需`fetch_user_info`)                                                                                     | 可选     | 文本                                                |
| `country`                     | `string`                 | 所在国家                                                                  | `author.country` (需`fetch_user_info`)                                                                                                  | (需确认TikTok API是否有类似字段, 需`fetch_user_info`)                                                                                     | 可选     | 文本 或 单选                                        |
| `mcn_info`                    | `object`                 | MCN机构信息                                                               | (需`fetch_user_info`)                                                                                                                   | (需`fetch_user_info`)                                                                                                                   | 可选     | 文本 (或拆分为多个字段)                               |
| `register_time_ms`            | `long_integer`           | 注册时间的毫秒级Unix时间戳                                                  | (需`fetch_user_info`)                                                                                                                   | (需`fetch_user_info`)                                                                                                                   | 可选     | 日期时间 (飞书显示) 或 数字 (原始存储)                |
| **`profile_url_web`**         | `string`                 | **作者的Web主页URL (由程序根据ID和平台构造)**                               | (程序构造: `douyin.com/user/{sec_uid}`)                                                                                                  | (程序构造: `tiktok.com/@{unique_id 或者其他主要ID}`)                                                                                     | **核心** | 超链接                                              |
| `source_api_response_author`  | `object`                 | (调试用) 存储来自API的原始作者数据对象，选择性保留                             | (原始API响应的一部分)                                                                                                                    | (原始API响应的一部分)                                                                                                                    | 调试可选 | 文本 (JSON字符串)                                 |
| **`last_updated_crawl_ms`**   | `long_integer`           | **本地爬虫最后更新此作者信息的时间戳 (毫秒)**                                 | (由程序填充)                                                                                                                             | (由程序填充)                                                                                                                             | **核心** | 日期时间 (飞书显示) 或 数字 (原始存储)                |
| `source_rule_info`            | `object`                 | 记录该作者首次被哪个规则采集的信息 (结构如 `{"rule_type": ..., "platform": ...}`)| (由程序填充)                                                                                                                             | (由程序填充)                                                                                                                             | 推荐     | 文本 (JSON字符串) 或 拆分字段                       |

---

**关于字段的说明和待确认点：**

1.  **`local_avatar_attachment` (新增):**
    *   **用途：** 这个字段不是给 `crawl.py` 输出的，而是预留给 `upload.py` 的。当 `upload.py` 将 `local_avatar_path` 指向的本地头像文件上传到飞书空间后，会获取一个 `file_token`。`upload.py` 会将这个 `file_token` 包装成飞书附件字段期望的格式（例如 `[{"file_token": "xxx"}]`）并存入此字段，然后用于更新飞书表格的附件字段。
    *   **`crawl.py` 的责任：** `crawl.py` 只负责下载头像到 `local_avatar_path` 并记录该路径。
    *   **飞书兼容性：** 对应飞书的“附件”类型字段。

2.  **`profile_url_web` (作者主页地址)：**
    *   **确认：** 您指出这个需要程序自己构造，这是正确的。
    *   **抖音：** 通常是 `https://www.douyin.com/user/{sec_uid}`
    *   **TikTok：** 通常是 `https://www.tiktok.com/@{unique_id}` (如果 `unique_id` 可靠且唯一) 或者基于其他主要的用户标识符。我们需要确定TikTok使用哪个ID来构造最稳定和官方的主页链接。从您提供的 `TIKHUB_TIKTOK_FIELDS.md` 的 `K1. unique_id` 来看，`unique_id` 应该是用于 `@username` 这种形式的。

3.  **API来源的再确认 (关键决策的回顾)：**
    *   我们之前的讨论确定，对于**关键词规则**和**作者列表规则**，它们会通过各自的主要API（视频搜索API / 用户帖子列表API）获取内嵌的作者信息，并且您确认了这些内嵌信息是“完整”的，因此规则层**不需要**额外为每个作者再调用一次 `fetch_user_info` API。
    *   **这意味着：** 上述表格中标注为“需`fetch_user_info`”的字段（如 `following_count`, `total_favorited_count`, `gender`, `birthday`, `city`, `country`, `mcn_info`, `register_time_ms` 等），如果它们确实无法从视频搜索API或用户帖子列表API的内嵌作者信息中直接获取，那么在这些规则下，这些字段的值将会是 `null` 或空。
    *   **只有当URL规则使用混合解析API `/api/v1/hybrid/parse_video` 时，或者如果我们未来决定在特定情况下（比如“仅更新元数据”任务针对单个作者时）允许规则层或任务层调用 `fetch_user_info`，这些字段才有可能被完整填充。**
    *   **请您再次确认这个理解是否正确。** 如果我们期望 `author.json` 中始终包含这些详细字段（即使是通过关键词或作者列表规则获取的），那么之前的“规则层不额外调用 `fetch_user_info`”的决策可能需要调整，或者我们需要接受在某些规则下这些字段为空。

4.  **字段的“核心与否”：** 我已根据您的删减和新增调整了标记。请您再审阅一遍，确保符合您的优先级。

这份 `author.json` V0.2 草案应该更接近您的需求了。请您重点关注第3点关于API来源和字段完整性的问题，这会直接影响我们后续对规则层获取数据逻辑的设计。

确认完作者字段后，我们就可以进行视频字段的梳理了。

---

好的，基于我们已经确认的业务逻辑、API优先原则、数据模型以及对代码复用和扩展性的追求，我对 `crawl.py` 及其相关辅助模块的重构思路如下。这确实可能达到代码层面较大范围重写甚至接近完全重写的程度，但目标是构建一个更清晰、更健壮、更易于维护和扩展的系统。

## 阶段 2: 核心设计框架 (PDD/SDD) 与迭代开发策略
### 2.1. 产品设计文档 (PDD/SDD) 初始框架定义 / 重大更新规划

---

**`crawl.py` 重构核心思路 (API驱动的多平台采集器)**

**一、核心架构理念：分层与解耦**

我们将采用一个分层架构，将不同的职责清晰地分离到不同的模块或类中：

1.  **核心控制层 (`crawl.py` 主逻辑):**
    *   负责解析命令行参数、加载配置、初始化日志和管理器。
    *   运行交互模式 (`interactive_mode`) 或根据命令行参数直接调用规则执行。
    *   作为顶层协调者。

2.  **交互模式层 (`interactive_mode` in `crawl.py` 或移至 `src/interactive_handler.py`):**
    *   实现我们V3.1版本定义的、带灵活导航的用户交互流程。
    *   根据用户选择，收集任务配置参数。
    *   调用核心控制层或规则调度层来执行任务。

3.  **规则调度与执行层 (新增/重构核心):**
    *   **通用规则执行器 (Rule Executors):** 为每种核心规则类型（Keywords, URLs, Authors）实现一个通用的执行器类或一组函数。
        *   例如 `KeywordsRuleExecutor`, `UrlsRuleExecutor`, `AuthorsRuleExecutor`。
        *   这些执行器封装了该规则类型的通用业务逻辑步骤（如关键词规则的“遍历关键词 -> 调用平台搜索 -> 过滤 -> 调用任务”）。
        *   它们不包含任何特定平台的API调用细节。
    *   **平台适配器接口 (Platform Adapter Interface):** 定义一个清晰的接口（例如一个抽象基类或协议），规定平台适配器需要实现哪些方法。这些方法将对应规则执行器在流程中需要平台特定能力的地方，例如：
        *   `search_videos_by_keyword(keyword, sort_type, pagination_params)`
        *   `get_video_data_by_url(url)` (用于URL规则的混合解析)
        *   `get_user_posts(author_id, pagination_params)`
        *   `get_user_info(author_id)` (如果某些规则或场景下仍需单独获取作者信息)
        *   `map_api_response_to_video_data_package(api_response)` (将平台API原始响应转换为我们内部标准的“视频数据包”)
    *   **具体平台适配器 (Platform Adapters):** 为每个支持的平台（抖音、TikTok）实现上述接口。
        *   例如 `DouyinAdapter`, `TikTokAdapter`。
        *   这些适配器内部会包含调用TikHub API特定端点的逻辑、处理该平台特有的请求参数和响应数据结构、以及调用相应的数据映射器。
        *   **API客户端封装：** 平台适配器会依赖一个更底层的API客户端（可能在 `src/tikhub_api_client.py` 中）来实际发送HTTP请求和处理通用API错误。
    *   **数据映射层 (Data Mappers):** （可以作为平台适配器的一部分，或独立模块）
        *   为每个平台的每种核心数据对象（视频、作者）实现映射器。
        *   例如 `DouyinVideoMapper`, `DouyinAuthorMapper`, `TikTokVideoMapper`, `TikTokAuthorMapper`。
        *   负责将平台API的原始响应字段精确地转换为我们已定义的 `video.json` (V0.3) 和 `author.json` (V0.2) 的标准数据模型。这包括字段重命名、数据类型转换（特别是时间戳转毫秒级）、默认值处理、从多个原始字段组合等。

4.  **任务处理层 (Task Processors - 通用逻辑):**
    *   为我们定义的三种核心任务类型实现通用的处理器类或函数。
        *   `NewCrawlTaskProcessor`
        *   `UpdateMetadataTaskProcessor`
        *   `UpdateVideoTaskProcessor`
    *   这些处理器接收由规则层（通过平台适配器和数据映射器）准备好的、符合我们内部标准数据模型的“视频数据包”列表。
    *   它们执行与平台无关的核心逻辑：文件操作（JSON读写、媒体下载路径管理）、状态管理（调用`Deduplicator`等）、媒体下载（调用通用的下载函数）。

5.  **基础设施/工具层 (`src/utils.py`, `src/tikhub_api_client.py`, `src/data_managers.py`):**
    *   `config.py` (或 `utils.py` 中的 `load_config`): 配置加载。
    *   `logger.py` (或 `utils.py` 中的 `setup_logging`): 日志系统。
    *   `tikhub_api_client.py` (新增，或从 `utils.py` 拆分): 封装对 `api.tikhub.io` 的底层HTTP请求（包含认证、速率控制、通用错误处理、重试）。
    *   `data_managers.py` (或 `utils.py` 中的现有类): 包含 `Deduplicator` (视频和作者), `KeywordManager`, `PlatformRegionManager`。这些类需要适配新的JSON结构（如来源规则字段）和新的交互需求（如`PlatformRegionManager`持久化用户自定义平台/区域）。
    *   `media_downloader.py` (或 `utils.py` 中的 `download_video`, `download_image_to_path`): 通用的媒体文件下载函数，需要能处理不同平台的URL特性（如果TikHub API返回的链接本身已处理好CDN和格式，则此层可能不需要太多平台判断）。
    *   `file_system_utils.py` (或 `utils.py` 中的文件操作函数): 封装JSON读写、目录创建、文件移动等。

**二、关键模块和类的职责细化：**

1.  **`crawl.py` (主入口):**
    *   `main()`: 解析参数，初始化配置、日志、管理器，根据模式调用 `interactive_mode` 或 `RuleDispatcher`。
    *   `interactive_mode()`: 实现V3.1交互流程，收集用户配置，调用 `RuleDispatcher`。
    *   `RuleDispatcher` (新增概念，可以是函数或类):
        *   接收用户选择的规则类型、平台、区域、任务类型、输入等。
        *   根据规则类型实例化或获取对应的“通用规则执行器”（如 `KeywordsRuleExecutor`）。
        *   根据平台实例化或获取对应的“平台适配器”（如 `DouyinAdapter`）。
        *   将平台适配器注入到规则执行器中。
        *   调用规则执行器的 `execute()` 方法，并传入任务类型和其他参数。

2.  **通用规则执行器 (e.g., `KeywordsRuleExecutor` in `src/rules/keywords_executor.py`):**
    *   构造函数接收一个“平台适配器”实例。
    *   `execute(task_type, input_params)` 方法：
        *   实现关键词规则的通用逻辑：遍历关键词。
        *   调用 `platform_adapter.search_videos_by_keyword()` 获取初步数据。
        *   执行点赞数双重过滤和提前终止逻辑。
        *   **调用 `platform_adapter.map_search_results_to_data_packages()` 将API原始搜索结果转换为标准“视频数据包”列表。** (这一步是数据映射的关键)
        *   根据 `task_type` 获取对应的“任务处理器”（如 `NewCrawlTaskProcessor`）。
        *   调用任务处理器的 `process(video_data_packages, source_rule_info, context)` 方法。
        *   收集并返回结果。

3.  **平台适配器 (e.g., `DouyinAdapter` in `src/adapters/douyin_adapter.py`):**
    *   实现 `PlatformAdapterInterface`。
    *   内部持有或能够访问 `TikHubAPIClient`。
    *   实现如 `search_videos_by_keyword()`:
        *   构建抖音搜索API (`/api/v1/douyin/search/video_search_v2`) 的请求参数。
        *   调用 `TikHubAPIClient` 发送请求。
        *   返回原始API响应。
    *   实现如 `map_search_results_to_data_packages()`:
        *   接收 `search_videos_by_keyword()` 返回的原始API响应。
        *   遍历响应中的每个视频条目。
        *   **调用 `DouyinVideoMapper.map(video_api_data)` 和 `DouyinAuthorMapper.map(author_api_data_from_video)` 将其转换为标准的视频和作者数据对象。**
        *   组装成“视频数据包”列表返回。
    *   其他平台（TikTok）的适配器类似。

4.  **数据映射器 (e.g., `DouyinVideoMapper` in `src/mappers/douyin_mapper.py`):**
    *   `map(api_video_object)`: 接收抖音API返回的单个视频对象。
    *   根据我们定义的 `video.json` (V0.3) 结构，从 `api_video_object` 中提取、转换、格式化字段。
    *   例如：将秒级时间戳 `create_time` 转换为毫秒级 `publish_time_ms`；从 `video.play_addr.url_list` 中选择最佳的国外CDN链接作为 `media_download_url` 的候选（实际成功的URL由下载逻辑确定并回填）；将 `source_platform` 数字ID转换为 "douyin" 字符串。
    *   返回一个符合我们内部标准视频数据模型的Python字典或对象。
    *   `DouyinAuthorMapper` 类似。

5.  **任务处理器 (e.g., `NewCrawlTaskProcessor` in `src/tasks/new_crawl_processor.py`):**
    *   `process(video_data_packages, source_rule_info, context)`:
        *   接收标准化的“视频数据包”列表。
        *   执行我们定义的“新爬取任务”的核心逻辑：视频ID去重、作者ID状态记录、创建JSON（使用数据包中已映射好的数据）、强制下载媒体（调用 `MediaDownloader`）、更新去重库。

6.  **`src/tikhub_api_client.py`:**
    *   封装 `_send_http_request` 逻辑，处理TikHub API的通用认证头、基础URL、QPS控制（简单延时或更复杂的令牌桶）、通用错误码处理和重试。

7.  **`src/data_managers.py`:**
    *   `VideoDeduplicator` (原 `Deduplicator`，操作 `crawled_video_ids.json`)
    *   `AuthorDeduplicator` (新增，操作 `crawled_author_ids.json`)
    *   `KeywordManager`
    *   `PlatformRegionManager` (增强持久化用户自定义平台/区域的能力)
    *   这些类的 `load()` 和 `save()` 方法内部统一使用通用的 `_load_json_file` 和 `_save_json_file` 辅助函数（可以放在 `src/file_system_utils.py` 或 `utils.py`）。

**三、重构后的数据流 (以关键词规则-新爬取任务为例):**

1.  `crawl.py` (交互或命令行) -> `RuleDispatcher`
2.  `RuleDispatcher` -> `KeywordsRuleExecutor` (注入 `DouyinAdapter`)
3.  `KeywordsRuleExecutor` -> `DouyinAdapter.search_videos_by_keyword()`
4.  `DouyinAdapter` -> `TikHubAPIClient.get("/api/v1/douyin/search/video_search_v2", ...)`
5.  `TikHubAPIClient` 返回原始API响应给 `DouyinAdapter`
6.  `DouyinAdapter` -> `DouyinVideoMapper` & `DouyinAuthorMapper` 将原始响应转换为标准“视频数据包”列表
7.  标准“视频数据包”列表返回给 `KeywordsRuleExecutor`
8.  `KeywordsRuleExecutor` 进行点赞数过滤等
9.  `KeywordsRuleExecutor` -> `NewCrawlTaskProcessor.process(filtered_data_packages, ...)`
10. `NewCrawlTaskProcessor` 执行JSON创建、调用 `MediaDownloader` 下载、调用 `VideoDeduplicator`/`AuthorDeduplicator` 更新状态。

**四、优势：**

*   **高内聚、低耦合：** 每个模块职责单一。
*   **易测试：** 可以独立测试每个适配器、映射器、任务处理器。
*   **易扩展：**
    *   支持新平台：只需添加新的平台适配器和数据映射器，并更新配置。通用规则执行器和任务处理器无需修改。
    *   支持新规则类型：只需添加新的通用规则执行器，并为其实现必要的平台适配器接口方法。
    *   API字段变更：只需修改对应平台的数据映射器。
*   **逻辑清晰：** 平台特定逻辑被封装在适配器和映射器中，核心业务流程更易理解。

这个重构思路旨在将现有的、偏过程式的 `crawl.py` 转换为一个更面向对象（或至少是模块化、职责分离的）、更具扩展性的系统。它需要对现有代码进行较大规模的调整，但长远来看，收益是巨大的。

您觉得这个重构思路是否符合您的期望？有哪些地方您认为可以进一步讨论或调整？


---

好的，我需要你协助我进行一次超大重构！！！！

重构目标：

所有的数据都通过 api.tikhub.io 中的数据接口获取！
重新梳理业务逻辑（因为不需要浏览器了，也不需要 js了，并且根据接口的获取方式，业务逻辑可以设计的更加高效合理）
重新梳理所需字段！！！（接口获取的数据比目前的多而全，我要新增全面的字段！）
为未来小红书、youtube等多平台数据获取设计简洁、优秀的系统架构！！！

以上请你理解我的意图，但不限于这些目标，你可以自行补充！

附件便是 tikhub 的 api 文档（很重要，请你牢牢记住，仔细学习）。