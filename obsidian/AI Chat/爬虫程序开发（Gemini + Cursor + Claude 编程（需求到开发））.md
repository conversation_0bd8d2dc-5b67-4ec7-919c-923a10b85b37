
1、整个背景是，我需要创造多个工具，帮我搭建 AI 爆款视频批量生成的工作流，蓝图如下：

1）在 tiktok 日区根据关键词爬爆款视频  
2）将视频上传至飞书，包括视频相关数据（包括账号数据，有可能这个也是可以分开一个程序的，先上传账号数据，再上传视频数据，视频数据可以绑定账号数据）  
3）逐个将视频上传到视频理解大模型，根据我的提示词，提取视频内容的关键信息  
4）将视频内容关键信息 上传到飞书  
5）在飞书上用 AI 工作流（这步不需要程序开发），二次创作：脚本、视觉主图 mj 提示词，分镜运镜提示词等会  
6）将 mj 提示词批量传给 mj 生图（google 插件）  
7）【人工 】选图  
8）选好的图上传飞书  
9）批量传给图生视频+提示词，生成分镜  
10）【人工】选分镜  
11）剪辑
 差不多是这样，你先理解，我资料还没给你

---

请先熟悉我们的项目背景和爬虫设计，参考： @OVERVIEW.md @CRAWL_DESIGN.md 我们的开发将严格按照 @CRAWL_PLAN.md 中的步骤进行。

现在，请开始执行 @CRAWL_PLAN.md 中的【步骤 27: 最终代码审查与优化】。 完成后请告诉我“步骤 27 已完成，请测试验证”并暂停，等待我的确认。  

---
背景：我是一名产品设计师。我想开发自己使用的软件工具。之前我不会写项目蓝图、产品设计文档、开发计划，尤其是面对 cursor ai 编程。当我只输出了一份产品设计文档（融合了项目蓝图、产品开发功能细节）给 cursor 时，cursor 给我设计的一套复杂的架构，并且在项目越实施到越后面，程序的验证和修改变的异常困难，一个程序涉及到好几个程序文件，一处bug 要在多处修改。

所以，我和你进行了深度的交流（在别的对话中），我们的共识是，以轻便、简单的程序结构（尤其是目录）结构设计 ，以 3 份文档与 AI 沟通：项目蓝图（用来描述项目的中远期规划）、A 产品设计文档（聚焦某个产品的程序设计思路）、A 产品开发计划（可逐步进行、逐步验证的用于 AI 自动化编程的交互式开发计划）。

附件中的：OVERVIEW.MD CRAWL_DESIGN.MD CRAWL_PLAN.MD 就是在这个设定下，由我来口述需求、加上提供我认为有重要的资料和关键信心，你来完成所有文档的编写工作。（UPLOAD_DESIGN.MD UPLOAD_PLAN.MD 也在项目蓝图之中，也是由你编写，但尚未进行开发，后面会讲到。）

首先，要肯定的是，这种交互模式取得了非常好的效果！ crawl.py 程序已经完全按照 crawl_plan.md 实现全部开发计划和功能，并且我自己、我指挥AI已经全部验证！

说明 我与你指定详细的 3份计划，再拿到 cursor 去指挥它干活，这种方式是可行的！

下面我们需要继续：

1、请帮我仔细阅读所有程序文件，以程序文件实现的功能、机制为准，反向 更新 CRAWL_DESIGN.MD CRAWL_PLAN.MD 文件，确保设计、计划、实际程序的体现都是统一的。（因为我在与AI编程按 开发计划进行的过程中，随着验证，我可能提出一些新的需求，或者修订了一些需求，未更新到 设计文档或开发计划里，也未同步给你，导致设计文档、开发计划不是最新版）

2、根据所有程序实现的功能，帮我编写 README.MD 文件

3、我们继续讨论 UPLOAD_DESIGN.MD 和 UPLOAD_PLAN 的编写，我会再给你一些资料。

---
你好，请切换到 @python-best-practices.mdc 身份。  
  
下面，根据 @TikTok视频数据采集与飞书导入设计文档.md 这份文档，审核@tkautorun 这个项目下除 doc 以外，所有的功能是否都已经按照文档实现。  
  
重点关注：  
  
一、爬虫规则对照我现在的说明：运行主程序后，由我输入关键词，爬取数量，爬取要求（最低点赞数），回车，然后程序会打开 tiktok.com/search?=关键词 ，加载视频列表，在视频列表中按顺序翻找符合爬取标准（点赞数）的视频，点击第一个符合标准的视频，进入视频详情页，爬取点赞数、评论数、收藏数、标题、标题关键词（这就是我的搜索关键词来源）、视频分享链接、根据视频分享链接下载视频；用新标签页点击作者名字，进入账号主页，爬取账号 ID、账号名、粉丝数、账号 url，爬完后关闭标签页，回到视频主页，后退，回到搜索列表。继续执行第二个符合规则的视频。直至爬取数量达标。然后，将除了视频以外的数据，同步到飞书多维表格。  
  
这个爬虫规则，在我另外一个项目中已经完全实现，请你对照 @tiktok_simple.py 这个程序和我口述的爬虫逻辑，去看  
  
1、项目文档是否详细说明了爬虫规则，如果没有，请修改项目文档  
2、爬虫规则的功能实现上，是否与 @tiktok_simple.py 一致？并不是整个程序架构，而是具体的功能实现，因为那代表了我要的爬虫规则。  
  
二、支持批量关键词搜索，请对照文档与我口述的逻辑：支持单一关键词和批量关键词两种模式 批量关键词也是指一个一个关键词搜索，而不是同时很多个。即排队搜索，即时去重，爬完了一个关键词的数据，再爬第二个，直至爬完所有选择的关键词。  
  
查看功能上是否实现？  
  
整体审查之后，给我一份修改清单。

---

# 飞书应用凭证

FEISHU_APP_ID=cli_a77df84ce77bd00c

FEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1w

  

# 飞书多维表格 - 账号表

FEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHe

FEISHU_ACCOUNT_TABLE_ID=tblvg8I0foe4g4ib

  

# 飞书多维表格 - 素材表

FEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHe

FEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOl

# 飞书多维表格 - 素材表附件字段名 (请确认或修改为你使用的名称)

FEISHU_VIDEO_ATTACHMENT_FIELD_NAME=视频文件



[LOG_LEVEL=INFO](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[BROWSER_HEADLESS=false](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[USE_CHROME=true](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[TIKTOK_BASE_URL=https://www.tiktok.com](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[TIKTOK_SEARCH_URL=https://www.tiktok.com/search?q=](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[TIKTOK_SCROLL_WAIT=2](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[DOWNLOAD_PATH=./downloads](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[DEDUPE_STORE_PATH=./data/deduplication.json](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[KEYWORD_STORE_PATH=./data/keywords.json](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[FEISHU_APP_ID=cli_a77df84ce77bd00c](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[FEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1w](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[FEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHe](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[FEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOl](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[FEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHe](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[FEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOl](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)

[FEISHU_VIDEO_ATTACHMENT_FIELD_NAME=视频源](https://www.tiktok.comtiktok_search_url=https//www.tiktok.com/search?q=TIKTOK_SCROLL_WAIT=2DOWNLOAD_PATH=./downloadsDEDUPE_STORE_PATH=./data/deduplication.jsonKEYWORD_STORE_PATH=./data/keywords.jsonFEISHU_APP_ID=cli_a77df84ce77bd00cFEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1wFEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_ACCOUNT_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHeFEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOlFEISHU_VIDEO_ATTACHMENT_FIELD_NAME=%26%2335270%3B%26%2339057%3B%26%2328304%3B)


---

我想要像素级复制 tiktok 爆款视频，除了音频，画面、视觉、视频、分镜都用 AI 文生图、图生视频搞定，剪辑环节把分镜一组合、音频用原装的就搞定了。现在我需要你：  
  
1、帮我写一份给视频理解大模型（gemini 2.5）的系统提示词，让它来把我这个流程里必须要用到的信息都提取出来

# 飞书应用凭证

FEISHU_APP_ID=cli_a77df84ce77bd00c

FEISHU_APP_SECRET=aCtytKq9D9BcDvpMefJ97fSdlkGZZP1w

  

# 飞书多维表格 - 账号表

FEISHU_ACCOUNT_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHe

FEISHU_ACCOUNT_TABLE_ID=tblvg8I0foe4g4ib

  

# 飞书多维表格 - 素材表

FEISHU_VIDEO_BASE_TOKEN=CNtHbpe38a6r1LsEV3kcfUTynHe

FEISHU_VIDEO_TABLE_ID=tblkZpPQ7CfFUUOl

# 飞书多维表格 - 素材表附件字段名 (使用默认名称，请确认或修改)

FEISHU_VIDEO_ATTACHMENT_FIELD_NAME=视频文件

  

# (可选) 其他配置

LOG_LEVEL=INFO

LOG_FILE=src/logs/tiktok_crawler.log

SELENIUM_TIMEOUT=60

HEADLESS_MODE=False

# USER_DATA_DIR=

DOWNLOAD_PATH=src/downloads/videos

DEDUPE_STORE_PATH=src/data/processed_ids.json

MAX_SCROLLS=20


---


补充前面提到的，你可以改 开发文档的名字  
  
待确认的关键点  
  
1、工作流程： 使用我给到的爬取规则，运行程序后，由我输入关键词，爬取数量，爬取要求（最低点赞数），回车，然后程序会打开 tiktok.com/search?=关键词 ，加载视频列表，在视频列表中按顺序翻找符合爬取标准（点赞数）的视频，点击第一个符合标准的视频，进入视频详情页，爬取点赞数、评论数、收藏数、标题、标题关键词（这就是我的搜索关键词来源）、视频分享链接、根据视频分享链接下载视频；用新标签页点击作者名字，进入账号主页，爬取账号 ID、账号名、粉丝数、账号 url，爬完后关闭标签页，回到视频主页，后退，回到搜索列表。继续执行第二个符合规则的视频。直至爬取数量达标。然后，将除了视频以外的数据，同步到飞书多维表格。以上流程都在这个程序里：@tiktok_simple.py 。

支持单一关键词和批量关键词两种模式 批量关键词也是指一个一个关键词搜索，而不是同时很多个。即排队搜索，即时去重，爬完了一个关键词的数据，再爬第二个，直至爬完所有选择的关键词。  
  
由于这个程序写的太大，不方便直接改，并且 飞书多维表格的上传 需要测试，我又新建了一个测试飞书上传的程序，也跑通。你可以 @video_upload_example.py 这里看到视频上传的流程。测试是成功的，但并没有合并进入主程序，而且测试流程不是实际流程，实际应该是根据爬取的视频条目直接上传视频。  
  
另外，原程序没有去重功能，没有关键词管理功能 。这个要新开发。  
  
差不多讲完了，其它你仔细阅读 @archive 自己学习。  
  
2、飞书如何接收视频，@video_upload_example.py 看这个。  
  
我把旧项目的其它程序文件都放进来了，你可以参考学习下。  
  
@auto_sync_field_types.py @check_feishu_field_types.py @check_feishu_fields.py @configure_field.py @feishu_upload_test.py @requirements.txt @simple_feishu_test.py @test_feishu_sync.py @test_feishu.py @update_feishu_field_types.py   
  
注意，工作流程不可照搬照抄，我们要根据使用效率、用户体验迭代优化流程。

---

---

背景：我正在学习、尝试在 tiktok 上发布视频。我没有任何短视频策划、制作的经验。要入门，我给我自己找的路径是先运用各种 AI大模型，帮助我像素级的复刻别人的爆款视频，即：保留音频（配音、口播）不变，把画面换成新的，先发出去。有流量之后，我再考虑自己做原创内容。这样至少我掌握了 AI 做爆款短视频的能力。  
  
我做视频的流程：  
  
1、把视频发给视频理解大模型，让它出详细的视频分析；  
2、将视频分析中关于画面描述的部分，先发给推理大模型（如 deepseek） ，让它转化为文生图提示词，再把文生图提示词发给文生图大模型（如 midjourney）出图；  
3、将视频分析中关于动态描述的部分，以及文生图大模型出的图，一并发给图生视频大模型（如 海螺、即梦），出分镜视频；  
4、根据原视频场景，重复2-3步骤，直至做出全部分镜视频，再把视频分析中关于画面文字动画、场景中的视觉特效、转场特效的剪辑类分析，以及所有的分镜视频，放到剪辑软件中剪辑成片。  
  
任务：你的任务是做出一份专业的能让视频理解大模型输出详细视频分析的系统提示词。这份提示词必须要能够让视频理解大模型很好的输出【我做视频的流程】所述、所需要的一切信息。同时视频本身的基础信息也要提取，比如：完整的口播内容（基于音频提取），视频中出现的文字（剔除字幕、音频内容）等等。  
  
帮助我像素级的复刻别人的爆款视频  
  
要求：深刻理解我的目的，自由发挥，写出一份专业的提示词，要带有角色定义、角色能力等等系统定义。  
  
注意：对视频理解的角度应该遵循爆款视频策划的角度，即排除音频，你考虑的是爆款画面 ——吸引读者眼球。尤其是前3秒，排除音频的吸引因素，视觉的作用就是让人们停留下来，把视频看完。什么能吸引眼球：新奇、特别、精致、符合当地人们心智中的某个图腾、甚至诡异……，解析视频画面可以从这个视角去理解。  
  
注意2：画面描述和动态描述，均不需要考虑画面文字和音频内容。你应该在理解时就将视频信息归纳成 3类：画面视觉类；动态效果类；剪辑效果类。除了画面视觉作为动态效果的必要条件外，避免剪辑效果类的信息影响到画面、动态的理解。  
  
注意3：场景的区分遵从规则：有无明显的分镜。因为很多视频是单一场景，只是加了特效而已。  
  
注意4：文生图部分要求描述的要细节、细节再细节，便于推理模型能够从文字的解读就能准确的理解画面。  
  
  
这是对您提供的第六个视频（苔藓地藏）的详细分析报告。  
  
  
这是对您提供的第五个视频（岛田秀平讲金运穴位）的详细分析报告。

这是对您提供的第四个视频（金龙）的详细分析报告。


---

-   
      
      
      
      
    非常好，根据最后一份 prompt，我需要修改输出规范：
    
    1、以 markdown table输出  
    2、输出两个表：
    
    【视频基本信息】  
    字段：视频时长；口播脚本（中文）；口播脚本（英文）；口播脚本（日语）；深层含义解读；视频内容总结
    
    【分镜细节信息】  
    字段：时间段；运镜；视觉；首帧主体；首帧背景；尾帧主体；尾帧背景；中间帧主体；中间帧背景  
      
      
      
    
    - 非常好，根据最后一份 prompt，我需要修改输出规范：
        
        1、以 markdown table输出  
        2、输出两个表：
        
        【视频基本信息】  
        字段：视频时长；口播脚本（中文）；口播脚本（英文）；口播脚本（日语）；画面文字提取；深层含义解读；视频内容总结
        
        【画面与运镜描述】  
        字段：时间段；分镜描述；运镜；视觉；首帧主体；首帧背景；尾帧主体；尾帧背景；中间帧主体；中间帧背景  
          
          
          
          
        你好，请帮我优化一份专业的 prompt：
        
        我需要 deepseek r1 等推理大模型扮演一名短视频拍摄剪辑老司机，并且精通与文生图、图生视频大模型的沟通语言，能够不依靠抽奖就能让文生图、图生视频大模型输出想要的图和镜头。
        
        1、你会得到一个信息包，包含了一部短视频的所有分镜和画面信息，你需要按逻辑理解：镜头下方编号对应画面下方编号，编号顺序正序代表视频的头和尾，即：画面【1】（）+ 镜头【1】+ 画面【2】+镜头【2】 ……你无需输出编排正确的镜头、画面内容，你只需按此逻辑理解整部短视频再讲什么
        
        2、总任务：策划一个 ai 短视频的各个分镜画面和运镜、拍摄要求 —— 指导文生图、图生视频；


---

好的，我们来准备与 Cursor AI 的沟通文本。核心策略就是利用我们精心制定的 `@UPLOAD_PLAN.md@v1.0`，让 AI 严格按照步骤执行，每一步完成后都暂停等待您的验证。

**沟通文本模板**

以下是您可以直接复制并粘贴到 Cursor 对话框中的文本。

**1. 初始指令 (开始开发 `upload.py`)**

```text
你好，我们需要开始开发一个新的 Python 模块 `upload.py`，用于将之前 `crawl.py` 爬取的数据上传到飞书多维表格。

**项目背景与设计:**
请先仔细熟悉我们的项目设计文档 `@UPLOAD_DESIGN.md@v1.8` (特别是第 3 节飞书表格结构设计 和 附录 A)。这份文档定义了 `upload.py` 的目标、功能、数据结构、核心逻辑和关键 API 交互。

**开发计划与执行:**
我们将严格按照开发计划 `@UPLOAD_PLAN.md@v1.0` 中的步骤顺序进行开发。请你**一次只执行一个步骤**。

**核心文件:**
主要涉及的文件包括 `upload.py` (主程序), `feishu_api.py` (封装 API 调用), `utils.py` (可能需要扩展), `config.json` (读取配置), `requirements.txt` (添加依赖)。

**现在，请开始执行 `@UPLOAD_PLAN.md@v1.0` 中的【步骤 1: 更新项目结构与依赖】。**

完成后请告诉我“**步骤 1 已完成，请测试验证**”并暂停，等待我的确认。
```

**2. 后续步骤指令 (模板 - 用于步骤 2 到 24)**

**在您验证完上一步骤后**，发送类似以下的指令来继续下一步：

```text
好的，步骤 [上一步骤编号] 已验证通过。

现在，请开始执行 `@UPLOAD_PLAN.md@v1.0` 中的【步骤 [当前步骤编号]】。

完成后请告诉我“**步骤 [当前步骤编号] 已完成，请测试验证**”并暂停，等待我的确认。
```

**示例 (执行步骤 2):**

```text
好的，步骤 1 已验证通过。

现在，请开始执行 `@UPLOAD_PLAN.md@v1.0` 中的【步骤 2】。

完成后请告诉我“**步骤 2 已完成，请测试验证**”并暂停，等待我的确认。
```

**示例 (执行步骤 15):**

```text
好的，步骤 14 已验证通过。

现在，请开始执行 `@UPLOAD_PLAN.md@v1.0` 中的【步骤 15】。

完成后请告诉我“**步骤 15 已完成，请测试验证**”并暂停，等待我的确认。
```

---

**使用说明与注意事项:**

1.  **提供文件**: 在开始对话时，确保 Cursor AI 能够访问项目中的相关文件，特别是 `@UPLOAD_DESIGN.md@v1.8` 和 `@UPLOAD_PLAN.md@v1.0`，以及需要修改的 `config.json`, `requirements.txt`, `utils.py` 等。
2.  **严格按计划**: 坚持让 AI 一次只执行一个步骤。如果 AI 试图一次完成多个步骤，请明确指示它停止并只完成当前要求的步骤。
3.  **验证每一步**: 在 AI 回复“步骤 X 已完成，请测试验证”后，您需要：
    *   检查 AI 生成或修改的代码是否符合 `@UPLOAD_PLAN.md@v1.0` 中该步骤的要求。
    *   (如果可行) 运行代码片段或整个脚本进行初步测试。例如，步骤 3 完成后，可以尝试调用获取 token 的代码。
    *   确认无误后，再发送执行下一步的指令。
4.  **处理错误或偏差**: 如果 AI 的实现不符合要求或有错误：
    *   明确指出问题所在，并引用 `@UPLOAD_DESIGN.md@v1.8` 或 `@UPLOAD_PLAN.md@v1.0` 中的相关说明。
    *   要求 AI 修正当前步骤的代码，例如：“步骤 X 的实现不符合要求，`[具体问题]`。请根据 `@UPLOAD_PLAN.md@v1.0` 步骤 X 的描述 [或 `@UPLOAD_DESIGN.md@v1.8` 的某部分] 重新修改代码。”
    *   修正后再次验证。
5.  **保持耐心**: 与 AI 协作开发需要耐心和清晰的指令。严格遵循计划和验证流程是成功的关键。

您现在就可以使用上面的“初始指令”开始与 Cursor 的交互了。祝您开发顺利！

---

您提出了一个非常好的、值得深入思考的问题！您是对的，我之前的回答可能过于简化了。**是否需要提及 `crawl.py` 取决于我们希望 Cursor AI 在多大程度上理解模块间的依赖关系，以及这种理解对编码 `upload.py` 是否有实质性帮助。**

让我们重新评估一下：

*   **不提及 `crawl.py` 的理由 (我之前的逻辑)**: `upload.py` 的核心任务是读取特定目录 (`./output/`, `./downloads/`) 下特定格式的文件 (JSON, MP4)，然后执行上传逻辑。理论上，只要 `@UPLOAD_DESIGN.md` 清晰定义了这些输入文件的位置和预期结构，Cursor 不需要知道是哪个 *具体* 的脚本 (`crawl.py`) 生成了它们，也能编写出 `upload.py` 的读取和处理逻辑。关注点在于 `upload.py` 自身的输入、处理和输出。

*   **提及 `crawl.py` 的理由 (您的担忧，也是合理的)**:
    1.  **更完整的上下文**: 明确告知 `upload.py` 处理的是 `crawl.py` 的输出，能让 AI 对数据流有更完整的理解（数据从哪里来 -> 要到哪里去）。
    2.  **潜在的关联**: 虽然代码上直接依赖可能没有，但理解数据来源有助于 AI 更好地理解 JSON 文件中某些字段的含义或来源（例如，`metadata.extracted_at` 就是 `crawl.py` 提取的时间）。
    3.  **风险规避**: 万一 `@UPLOAD_DESIGN.md` 对输入数据的描述不够详尽（例如，JSON 结构有细微之处未在 Design 中明确），AI 如果知道数据源是 `crawl.py`，理论上它可以通过分析 `crawl.py`（如果能访问到）来推断更准确的数据结构。但这增加了 AI 的任务复杂度，也依赖于 `crawl.py` 代码的可读性。

**权衡与结论:**

我认为您的担忧是有道理的。虽然从纯粹的功能实现角度，不提 `crawl.py` *可能* 也行得通（前提是 Design 对输入描述足够完美），但**提及 `crawl.py` 作为数据来源，可以提供更健壮的上下文，有助于 AI 更准确地理解数据，并减少因设计文档对输入描述不充分而产生的潜在误解。** 这点额外的上下文信息带来的好处可能大于其引入的微小复杂度。

**因此，我同意您的看法，在初始指令中简要提及 `crawl.py` 作为数据来源是更优的选择。**

**修正后的 Cursor 初始指令模板 (V1.1):**

```text
你好，我们现在开始开发 `upload.py` 模块。这个模块的主要任务是将 `crawl.py` 模块生成在 `./output/` 目录下的 JSON 元数据文件和 `./downloads/` 目录下的视频文件，上传并同步到飞书多维表格中。

**模块设计与计划:**
请参考设计文档 `@UPLOAD_DESIGN.md@v1.8` (它详细定义了输入文件、飞书目标结构、处理逻辑等) 和开发计划 `@UPLOAD_PLAN.md@v1.0`。我们将严格按照开发计划中的步骤顺序进行。请你**一次只执行一个步骤**。

**项目结构与文件:**
我们正在 `tiktok_project` 目录下工作。本次开发主要涉及创建/修改 `upload.py`, `feishu_api.py` (请创建此新文件), `config.json`, 和 `requirements.txt` 文件。请确保代码中的相对路径 (如 `./output/`, `./downloads/`, `./output/failed/`) 和模块导入 (如 `from utils import ...`, `from feishu_api import ...`) 正确。

**现在，请开始执行 `@UPLOAD_PLAN.md@v1.0` 中的【步骤 1: 更新项目结构与依赖】。**

完成后请告诉我“**步骤 1 已完成，请测试验证**”并暂停，等待我的确认。
```

**修改说明:**

*   在第一段增加了 "这个模块的主要任务是将 `crawl.py` 模块生成在 `./output/` 目录下的 JSON 元数据文件和 `./downloads/` 目录下的视频文件，上传并同步到飞书多维表格中。" 这句话简明扼要地说明了 `upload.py` 与 `crawl.py` 的关系以及处理的数据来源。
*   在设计文档引用后括号中也补充说明了其内容概要。

感谢您提出这个关键问题，让我们的沟通指令更加完善！这个 V1.1 版本的初始指令应该既聚焦任务，又提供了必要的上下文。


你好，我们现在开始对项目 **@tiktok_project** 进行功能迭代。   
  
本次迭代的目标是实现**第二个新功能**：  
  
1. **增加链接模式 (URL Mode)**：允许用户通过命令行参数 (`--url-file`, `--urls`) 或交互式选择 `.txt` 文件的方式，提供一批视频 URL 进行定向爬取。   
2. **引入手动爬虫规则选择机制**: 用户在每次运行时，可以从当前可用的规则列表（初始包括 "tiktok_keyword_v1" 和 "tiktok_url_v1"）中手动选择一个规则来执行爬取。平台/区域标签仅用于数据标记。   
3. **实现 TikTok URL 规则**: 需要适配或新增代码/JS脚本来处理直接通过 URL 访问 TikTok 视频页面的数据提取。 

  
模块设计与计划: 本次迭代主要涉及对 `@utils.py` 和 `@crawl.py` 两个代码文件的修改。请参考以下**已经更新并最终确定的**设计文档和开发计划，我们将严格按照开发计划中的步骤顺序进行：  
`@OVERVIEW.md` _(V1.5)_  
`@CRAWL_DESIGN.md` (V1.9)   
`@CRAWL_PLAN.md` _(V1.9)_  
  
*注意**: 本次迭代**不涉及**修改 `@upload.py` 或 `@feishu_api.py`。   
  
我们将严格按照 `@CRAWL_PLAN.md` (V1.9) 的步骤顺序，从第三阶段开始执行。请你**一次只执行一个步骤**。  
  
**项目结构与文件:** 我们仍在 **@tiktok_project** 目录下工作。本次开发主要涉及修改 `@utils.py` 和 `@crawl.py` 文件。请确保代码中的相对路径 (如 `./output/`, `./downloads/`, `./data/`, `./url_lists/`) 和模块导入正确。   
  
---   
  
请确认你已理解本次迭代的目标（链接模式和规则选择）和所依据的文档版本。确认后，请等待我的第一个具体开发步骤指令（将从 `@CRAWL_PLAN.md` V1.9 的第三阶段开始）。