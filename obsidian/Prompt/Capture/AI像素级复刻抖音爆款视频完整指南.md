您说得对，我的回答确实存在问题。Midjourney和Stable Diffusion对中文理解有限，而且它们的提示词格式也不同。我会重新提供更准确、实用的指南。

# AI像素级复刻抖音爆款视频完整指南

## 一、元提示词（适用于ChatGPT/Claude等AI助手）

```
/role "视频复刻专家"
您是一位专业的视频复刻专家，精通使用AI工具复制抖音爆款视频的每一个细节。
您的核心技能包括：视频结构分析、关键帧提取、精准AI提示词工程和跨平台AI工具操作。
无论遇到多复杂的视频，您都能将其分解为可精确复制的元素。
您的回答将直接提供可复制使用的英文提示词，专为Midjourney和Stable Diffusion等AI工具优化。
```

## 二、视频分析框架（用英文输出结果）

```
请分析这个抖音爆款视频并提取以下关键信息：

1. 视频长度：___秒
2. 关键场景数量：___个
3. 主要拍摄对象：_______
4. 主要场景：_______
5. 主色调：_______
6. 视频风格：_______
7. 转场效果：_______
8. 关键动作描述：_______

基于分析，提供4-5个关键帧时间点：
- 关键帧1（开场）：___秒
- 关键帧2（主体展示）：___秒
- 关键帧3（情感/使用展示）：___秒
- 关键帧4（结束引导）：___秒
```

## 三、Midjourney关键帧生成提示词（英文提示词）

### 关键帧1（开场）Midjourney提示词

```
A [age] [gender] standing in [location], wearing [detailed clothing description], [pose description], [facial expression], showing [product/subject]. Natural [lighting condition], [background description]. Professional photography, 8K resolution, commercial quality, vibrant colors, TikTok/Douyin style video first frame, ultra-detailed, photorealistic, trending on social media --ar 9:16 --v 6.0 --style raw --q 2 --s 750
```

### 关键帧2（主体展示）Midjourney提示词

```
Close-up of [product/subject] being showcased by [brief person description if applicable], [product presentation angle], [detailed product features], [lighting highlighting product], [simplified background]. Product photography style, 8K resolution, commercial quality, professional lighting, extremely detailed textures, photorealistic, TikTok/Douyin product showcase aesthetic --ar 9:16 --v 6.0 --style raw --q 2 --s 750
```

### 关键帧3（使用场景）Midjourney提示词

```
[Person description] using/interacting with [product/subject] in [environment], [detailed action description], [facial expression], [surrounding elements details], [lighting conditions]. Lifestyle photography, 8K resolution, natural lighting, authentic moment, TikTok/Douyin lifestyle content style, photorealistic, highly detailed --ar 9:16 --v 6.0 --style raw --q 2 --s 750
```

### 关键帧4（结束引导）Midjourney提示词

```
[Product/subject] prominently displayed for commercial finale, [product angle], clear space for text overlay, [background description], [lighting effects], [brand elements if applicable]. Commercial photography, 8K resolution, professional product lighting, perfect details, vibrant colors, TikTok/Douyin commercial ending style, photorealistic --ar 9:16 --v 6.0 --style raw --q 2 --s 750
```

### Midjourney通用负面提示词

```
--no low quality, blur, distortion, disfigured, bad anatomy, deformed, poorly drawn face, mutation, mutated, extra limb, ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, malformed hands, blurry, ((((mutation)))), ((((mutated)))), out of focus, long neck, long body, (((distorted))), ((blurry)), ((bad art)), ((deformed)), ((extra limbs)), ((close up)), ((b&w)), weird colors, blurry, bad anatomy
```

## 四、Stable Diffusion关键帧生成提示词（英文提示词）

### 关键帧1（开场）Stable Diffusion提示词

```
(masterpiece:1.2), (best quality:1.2), (photorealistic:1.2), (8k, RAW photo:1.2), (soft lighting:1.2), A [age] [gender] standing in [location], wearing [detailed clothing description], [pose description], [facial expression], showing [product/subject]. Natural [lighting condition], [background description]. Professional photography, 8K resolution, commercial quality, vibrant colors, TikTok/Douyin style video first frame, ultra-detailed.
```

### 负面提示词

```
(worst quality:2), (low quality:2), (normal quality:2), lowres, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name, (ugly:1.3), (duplicate:1.3), (morbid:1.3), (mutilated:1.3), (tranny:1.3), mutated hands, (poorly drawn hands:1.3), blurry, (bad anatomy:1.3), (bad proportions:1.3), extra limbs, (disfigured:1.3), (more than 2 nipples:1.3), (missing arms:1.3), (extra legs:1.3), (fused fingers:1.3), (too many fingers:1.3), (unclear eyes:1.3), lowers, bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry
```

### 关键帧2（主体展示）Stable Diffusion提示词

```
(masterpiece:1.2), (best quality:1.2), (photorealistic:1.2), (8k, RAW photo:1.2), (product photography:1.2), Close-up of [product/subject] being showcased by [brief person description if applicable], [product presentation angle], [detailed product features], [lighting highlighting product], [simplified background]. Product photography style, commercial quality, professional lighting, extremely detailed textures, TikTok/Douyin product showcase aesthetic.
```

### 关键帧3（使用场景）Stable Diffusion提示词

```
(masterpiece:1.2), (best quality:1.2), (photorealistic:1.2), (8k, RAW photo:1.2), (lifestyle photography:1.2), [Person description] using/interacting with [product/subject] in [environment], [detailed action description], [facial expression], [surrounding elements details], [lighting conditions]. Natural lighting, authentic moment, TikTok/Douyin lifestyle content style, highly detailed.
```

### 关键帧4（结束引导）Stable Diffusion提示词

```
(masterpiece:1.2), (best quality:1.2), (photorealistic:1.2), (8k, RAW photo:1.2), (commercial photography:1.2), [Product/subject] prominently displayed for commercial finale, [product angle], clear space for text overlay, [background description], [lighting effects], [brand elements if applicable]. Professional product lighting, perfect details, vibrant colors, TikTok/Douyin commercial ending style.
```

## 五、图生视频转换提示词（主流AI视频生成工具）

### 5.1 Runway ML Gen-2 提示词（英文）

```
Using these keyframes, create a TikTok-style vertical video, 20 seconds long.

Scene sequence:
1. Start with frame 1 (0-5s): Subject looking at camera with slight natural movement, gentle camera zoom from medium shot to medium close-up.
2. Transition to frame 2 (5-10s): Product showcase with hands naturally rotating the product to show details, facial expressions changing from neutral to delighted.
3. Transition to frame 3 (10-15s): Usage scenario with natural interaction between subject and product, environment has subtle background movement.
4. End with frame 4 (15-20s): Clean product display with space for text, slight product rotation or shimmer effect.

Technical requirements:
- Ensure completely natural human movements and expressions
- Smooth, professional transitions between scenes
- Consistent lighting and color grading throughout
- Stable camera work with deliberate, smooth movements
- 60fps for smooth motion
- High-quality rendering with sharp details
```

### 5.2 Pika Labs 提示词（英文）

```
Create a 20-second TikTok/Douyin-style video based on these keyframes. Vertical format (9:16).

Motion directions:
- 0-5s: Starting with the first frame, subject makes subtle natural movements while maintaining eye contact with camera. Gentle camera push-in.
- 5-10s: Transition to product showcase with smooth hand movements highlighting product features from multiple angles.
- 10-15s: Natural usage scene with realistic environment interaction, subject using the product with authentic expressions.
- 15-20s: Clean finale with product prominently displayed, slight rotation/movement to maintain visual interest.

Key requirements:
- Hyper-realistic human movements and expressions
- Cinema-quality smooth transitions
- Consistent professional lighting
- Stable camera with intentional movements
- High detail retention throughout
```

### 5.3 Gen-1/Lumiere/Sora 风格提示词（英文）

```
Using these keyframes as reference, create a seamless 20-second TikTok/Douyin style video with the following specifications:

Sequence:
- First scene (0-5s): Begin with frame 1, subject should make natural micro-movements while maintaining eye contact, with subtle background activity. Camera slowly pushes in.
- Second scene (5-10s): Transition to frame 2 showing detailed product showcase with hands naturally demonstrating the product. Include realistic hand movements and natural facial expressions.
- Third scene (10-15s): Transition to frame 3 showing authentic usage scenario with natural environmental interaction. Include subtle atmospheric elements (steam from coffee, light shifting, etc).
- Final scene (15-20s): Transition to frame 4 with clean product display, maintaining visual interest with subtle product movement or lighting effects.

Technical requirements:
- Ultra-realistic human movement physics
- Professional cinematography with stable framing
- Consistent professional lighting throughout
- High-fidelity detail preservation
- Cinema-grade color consistency
- Seamless transitions between scenes
```

## 六、完整示例（产品展示类视频）

### 6.1 产品视频示例 - Midjourney关键帧提示词

```
# 关键帧1（开场）
A 25-year-old Asian female standing in a bright modern cafe, wearing white linen button-up shirt and high-waisted blue jeans, standing confidently with right hand raised showing a colorful phone case, smiling directly at camera. Natural sunlight coming from large windows on left, softly blurred cafe interior with wooden furniture and green plants in background. Professional photography, 8K resolution, commercial quality, vibrant warm colors, TikTok/Douyin style video first frame, ultra-detailed, photorealistic, trending on social media --ar 9:16 --v 6.0 --style raw --q 2 --s 750

# 关键帧2（产品展示）
Close-up of colorful gradient phone case being showcased by female hands with natural manicure, case held at 45-degree angle showing reflective surface and pattern details, professional product lighting from upper right creating highlight points on product surface, softly blurred warm cafe background. Product photography style, 8K resolution, commercial quality, professional lighting, extremely detailed textures, photorealistic, TikTok/Douyin product showcase aesthetic --ar 9:16 --v 6.0 --style raw --q 2 --s 750

# 关键帧3（使用场景）
Young Asian woman sitting at window seat in bright cafe, using smartphone with colorful case, slightly looking down at screen with satisfied smile, one hand holding phone while other hand rests near coffee cup, table has latte with latte art, open notebook and gold pen. Soft natural light through large windows creating warm atmosphere, subtle cafe environment with other patrons blurred in background. Lifestyle photography, 8K resolution, natural lighting, authentic moment, TikTok/Douyin lifestyle content style, photorealistic, highly detailed --ar 9:16 --v 6.0 --style raw --q 2 --s 750

# 关键帧4（结束引导）
Colorful gradient phone case prominently displayed at center with brand logo adjacent, product at 45-degree angle showing all design elements, clear space in upper third for product name text, clear space in lower third for price and purchase button, simple gradient background transitioning from warm orange to soft pink. Commercial photography, 8K resolution, professional product lighting, perfect material details with visible texture and reflections, vibrant colors, TikTok/Douyin commercial ending style, photorealistic --ar 9:16 --v 6.0 --style raw --q 2 --s 750
```

### 6.2 产品视频示例 - Runway ML Gen-2 提示词

```
Using these 4 keyframes, create a professional TikTok-style product video for a colorful phone case, 20 seconds long, in vertical format.

Scene progression:
1. Opening (0-5s): Start with the young woman in cafe standing position, showing subtle natural movement including slight head tilt and blinking. Camera slowly pushes in from medium shot to medium close-up. Her expression gradually shifts from neutral smile to interested/curious.

2. Product showcase (5-10s): Smoothly transition to close-up of hands holding and showcasing the phone case. Hands should naturally rotate the case approximately 180 degrees to show all sides and features. Fingers should move naturally and realistically interact with the product surface. Expression changes to delighted/impressed.

3. Usage scene (10-15s): Transition to cafe table scene with woman using the phone with case. Include natural actions: looking at phone, taking a small sip of coffee, typing on phone. Add subtle environment details like steam rising from coffee, very slight movement of background elements. Lighting should have subtle natural variation.

4. Commercial ending (15-20s): Smooth transition to final product showcase with clean background. Product should have very slight rotation/movement to maintain visual interest while leaving text areas clean for overlay. Add subtle lighting effects that highlight the product's texture and colors.

Technical requirements:
- Human movements must be 100% natural with accurate physics
- All transitions must be professionally smooth with no jump cuts
- Facial expressions must be natural and subtle
- Image quality must remain consistently sharp and clear
- Lighting and color grading must be professionally consistent
- Camera movement must be stable and intentional
```

## 七、视频优化检查列表

```
最终视频质量检查清单：

1. 人物动作：
   □ 所有动作遵循自然物理规律
   □ 无异常抖动或机械感
   □ 手指动作精确自然
   □ 面部表情变化流畅自然

2. 视觉质量：
   □ 整体画面保持高清晰度
   □ 无模糊、像素化或压缩痕迹
   □ 材质和纹理细节表现精准
   □ 光影效果专业自然

3. 视频流畅度：
   □ 场景转换平滑无跳跃感
   □ 动作速度曲线自然
   □ 全程帧率稳定
   □ 无卡顿或停滞现象

4. 抖音平台适配：
   □ 垂直视频比例正确(9:16)
   □ 视觉风格符合当下流行趋势
   □ 节奏感适合短注意力平台
   □ 预留文字空间位置合适
```

希望这个更新后的指南能更好地满足您的需求。所有提示词都使用了英文，优化了格式以适应Midjourney和Stable Diffusion的参数要求，并分别提供了不同平台的专用提示词。