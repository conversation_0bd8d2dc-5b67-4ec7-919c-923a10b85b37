# MCP 清单
## Augment
```json
{
  "mcpServers": {
    "Sequential thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}
{
  "mcpServers": {
    "Playwright": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}
{
  "mcpServers": {
    "/Users/<USER>/.local/bin/uvx": {
      "command": "/Users/<USER>/.local/bin/uvx",
      "args": [
        "mcp-feedback-enhanced@latest"
      ]
    }
  }
}
{
  "mcpServers": {
    "Context 7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp@latest"
      ]
    }
  }
}
{
  "mcpServers": {
    "Exa Search": {
      "command": "npx",
      "args": [
        "-y",
        "exa-mcp-server"
      ],
      "env": {
        "EXA_API_KEY": "b226d29a-dc05-4977-bdda-5bc55d7328d4"
      }
    }
  }
}
{
  "mcpServers": {
    "Desktop Commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander@latest"
      ]
    }
  }
}
{
  "mcpServers": {
    "Task Manager": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@kazuph/mcp-taskmanager",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c"
      ]
    }
  }
}
{
  "mcpServers": {
    "API 文档": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--project=6521113"
      ],
      "env": {
        "APIFOX_ACCESS_TOKEN": "APS-BB8S7m0uYOYxv1rItTgOJnMVYne9ucPE"
      }
    }
  }
}
{
  "mcpServers": {
    "TikHub.io API Docs": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=4705614"
      ]
    }
  }
}
{
  "mcpServers": {
    "飞书 API - API 文档": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=532425"
      ]
    }
  }
}
{
  "mcpServers": {
    "GitHub": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@smithery-ai/github",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c",
        "--profile",
        "outrageous-prawn-X1Ojbe"
      ]
    }
  }
}
{
  "mcpServers": {
    "Filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Downloads/project-cursor-github"
      ]
    }
  }
}
```

## Optional

```json
{
  "mcpServers": {
    "Brave Search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAXComZb3HInv5jJ7htVVZ6aZczcli"
      }
    }
  }
}
{
  "mcpServers": {
    "Puppeteer": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-puppeteer"
      ]
    }
  }
}

```

## 其他


### firecrawl

```
{
  "mcpServers": {
    "mcp-server-firecrawl": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@mendableai/mcp-server-firecrawl",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c",
        "--profile",
        "outrageous-prawn-X1Ojbe"
      ]
    }
  }
}
```

### context7

```
{
  "mcpServers": {
    "context7-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@upstash/context7-mcp",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c"
      ]
    }
  }
}
```

### exa

```
{
  "mcpServers": {
    "exa": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "exa",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c"
      ]
    }
  }
}
```

### Desktop-commander

```
{
  "mcpServers": {
    "desktop-commander": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@wonderwhy-er/desktop-commander",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c"
      ]
    }
  }
}
```

### Sequential thinking

```
{
  "mcpServers": {
    "server-sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@smithery-ai/server-sequential-thinking",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c"
      ]
    }
  }
}
```

### fetch

```
{
  "mcpServers": {
    "fetch": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-fetch"
      ]
    }
  }
}
```

### memory

```
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    }
  }
}
```