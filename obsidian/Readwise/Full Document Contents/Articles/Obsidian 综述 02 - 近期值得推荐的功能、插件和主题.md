# Obsidian 综述 02 - 近期值得推荐的功能、插件和主题

![rw-book-cover](https://cdnfile.sspai.com/2023/03/22/da1ece7f405602b822609359f91e7f79.jpg)

## Metadata
- Author: [[闲者时间_王掌柜]]
- Full Title: Obsidian 综述 02 - 近期值得推荐的功能、插件和主题
- Category: #articles
- Summary: 欢迎来到Obsidian综述系列！在这里，我们将分享近期Obsidian更新的功能、插件和主题等相关内容。如果您是一位Obsidian用户，或者对Obsidian的功能和迭代感兴趣，那么这个系列将会对 ...
- URL: https://sspai.com/post/78977

## Full Document
欢迎来到 Obsidian 综述系列！在这里，我们将分享近期 Obsidian 更新的功能、插件和主题等相关内容。如果您是一位 Obsidian 用户，或者对 Obsidian 的功能和迭代感兴趣，那么这个系列将会对您有帮助，以随时掌握最新的技巧。

#### v1.1.16 版本

自 Obsidian 升级到 v1.0 后，它经历了大量的迭代和更新，使用体验得到了相当多的优化。一方面，Obsidian 的「标签页」功能已基本完善，使用起来更加顺手；另一方面，最大的变化是增加了「白板」Canvas。

##### Canvas

Canvas 是 Obsidian 提供的视觉笔记工具。它最大的作用就是采用视觉元素，在一张无限大的画布上，布置笔记和其他资源，并用线条将他们链接，将相关笔记连接在一起，从而更好的理解他们之间的关系，如图：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/21ce0cbf0844f8d4bf67fe89a841d815.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=e3a5c46cb8fd1683a021b5a1042a3464&referer=https://sspai.com)要开始使用 Canvas，您首先需要创建一个文件来保存您的画布。您可以使用以下方法之一创建新画布：

* 在左侧的功能区中选择“新建白板”（四个矩形图标），以在活动文件的同一文件夹中创建画布。
* 打开命令面板。选择“白板：新建白板”，以在活动文件的同一文件夹中创建画布。
* 在文件资源管理器中右键单击要在其中创建画布的文件夹。选择“new Canvas”。

Obsidian 会将每个画布的配置存储在具有 .canvas 扩展名的文件中，该文件采用自定义 JSON 格式。

您可以将文件从 Obsidian 或其他应用程序拖入画布中。例如，Markdown 文件、图像、音频、PDF 或甚至无法识别的文件类型。

经过一段时间的迭代，Canvas 白板功能已经趋于完善，而且似乎 Obsidian 也在推荐使用该功能。总之，Canvas 可以帮助我们更好地整理笔记或理顺思路，是一种新颖的组织笔记方法，强烈推荐大家尝试使用。

#### 插件

##### Heading Level Indent

Obsidian 默认只缩进第一级标题，现在 [Heading Level Indent](https://github.com/svonjoi/obsidian-heading-level-indent?ref=obsidian-iceberg) 插件可以缩进多个级别（目前只在「阅读视图」中可以展示层级）。对于一些喜欢在笔记中使用多个标题级别的用户来说，这是一个很实用的功能。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/c5e4c5090e17d84d4c3e9515625bbe21.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=208aec474c610e3178051759094b1717&referer=https://sspai.com)##### Open files with commands

插件 [Open files with commands](https://github.com/LostPaul/ob-open-files-with-commands?ref=obsidian-iceberg) 支持通过「命令行」（CMD+P）打开一个文件，在某些场景下会比较高效。例如我通过配置好的命令，可以快速打开 HomePage 的 两个文件：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/a53200ac4156220f36ea21f2bffe9b31.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=e65bc22b21010974f86a1636c95256b8&referer=https://sspai.com)打开后效果如图：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/d258a229de01d75c6b8edbb69c47557b.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=23c5a02010bb6918050e1fafc271393f&referer=https://sspai.com)##### Open In New Tab

[Open In New Tab](https://github.com/patleeman/obsidian-open-in-new-tab) 插件可以在文件资源管理器中，打开所有文件到一个新的标签页中，避免了频繁切换标签页的麻烦。如果文件已经存在于现有的选项卡中，则该插件将激活该选项卡，而不是打开新的选项卡。

##### Obsidian Audio Notes

[Obsidian Audio Notes](https://github.com/jjmaldonis/obsidian-audio-notes) 插件可以集成 Ingrated Deepgram AI 进行音频转文字，使您可以轻松地将音频笔记转换为文本。这个插件可以让您更方便地记录您的想法和思路，同时也可以帮助您更好地组织和整理您的笔记。

##### **Note Refactor**

[**Note Refactor**](https://github.com/lynchjames/note-refactor-obsidian) 插件可以帮助您在做「项目研究」时更加高效地重构笔记。只需写一段话，选择后即可创建一个新笔记，很方便。此插件可以让您更好地管理和组织您的笔记，同时也可以提高您的写作效率。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/20baa46789eb6228d458cc06be12139b.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=4381290aa8dc319c9dd6c997a42d646a&referer=https://sspai.com)##### **obsidian-paste-image-rename**

[**obsidian-paste-image-rename**](https://github.com/reorx/obsidian-paste-image-rename) 是一个支持博客写作的插件。它可以在粘贴图像或其他附件时，自动重命名文件并将它们添加到 Obsidian 库中。使用此插件，您可以更轻松地管理您的笔记，并更有效地整理和追踪相关的信息。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/01cbe1c81d52e6fdb16e0e9626c6fe91.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=b7b2f2a53c8ee908ad27b4ea646c4729&referer=https://sspai.com)##### Outliner

[Outliner](https://github.com/vslinko/obsidian-outliner) 插件可以帮助您更好地使用大纲，使 Obsidian 更类似于 WorkFlowy 和 RoamResearch。该插件提供了一种简单而有条理的方法来组织和管理您的笔记，从而提高您的写作效率和准确性。

##### Influx

[Influx](https://github.com/jensmtg/influx) 反向链接插件可以在笔记的页脚中聚合一个简洁的反向链接剪报流，使您可以更方便地跟踪笔记之间的链接。使用此插件，您可以更好地了解您的笔记之间的关系，并更好地组织和管理您的笔记。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/40abedb1be7900b23b05ec585de81a6d.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=be0fd4c30b7137b3d53e158da93aa427&referer=https://sspai.com)##### Short Internal Links to Headings

[Short Internal Links to Headings](https://github.com/scottwillmoore/obsidian-short-internal-links-to-headings) 插件可以在使用双向链接时，只显示引入的「标题」，从而使您的笔记更加简洁。使用此插件，您可以更好地抓住您的读者的注意力，并更好地组织和管理您的笔记。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/e2da54d3da8e1fa1d877fa65f52b7485.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=5f84842c21f41e256751d4bb8c0a33eb&referer=https://sspai.com)#### 主题

##### Minimal 新增 Rose Pine 主题

Minimal 是 Obsidian 上最欢迎的主题之一，个人非常欣赏其简约和整洁的风格，在《[Obsidian 综述 - 新 UI 新征程（v0.16 发布之际）](https://xzsj.vip/2022/obsidian-roundup-01)》中有详细使用介绍。在 Minimal 升级到 v6.3.2 后，新增了 [Rose Pine](https://rosepinetheme.com/) 配色方案，非常适合在这个春日享用，让你的 Obsidian 充满樱花的味道。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/315b0a00c310b5eaf9f339ffa1c917c2.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=d2012ced58907db6f7a228d17b87075a&referer=https://sspai.com)##### **obsidianotion**

仿 Notion 主题 \*\*[obsidianotion](https://github.com/diegoeis/obsidianotion)\*\*，整体比较干净素雅。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/368111b5c3682a8b7cfca1d20dea09fb.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=b9c3969ba8e0728449cf8d3830f783b5&referer=https://sspai.com)##### **obsidian-notation-2**

仿 Notion 主题 [**obsidian-notation-2**](https://github.com/Bluemoondragon07/obsidian-notation-2?ref=obsidian-iceberg) ，效果如图：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/efd500352061f6adb3c3f3b21621e412.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=48a0720eb557a188fc41ab1b04257f12&referer=https://sspai.com)##### Polka

[Polka](https://github.com/callumhackett/obsidian_polka_theme) 提供了一个带「斑点」的主题样式，看起来不错。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/77a6edd020d192d132feb82bb16bc5ca.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=5e09798bca10a37196cfc1c0641b586a&referer=https://sspai.com)##### iA Writer

如果你喜欢 [iA Writer](https://ia.net/writer) 这个软件，[这个主题](https://github.com/mrowa44/obsidian-ia-writer?ref=obsidian-iceberg)将给你带来熟悉的感觉。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/4ddf5b42d84da78d3c4fc1309718be57.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=425cb9036d182b1e7fb1eef54a33e304&referer=https://sspai.com)#### 实例库

[manage web articles and videos in Obsidian](https://thebuccaneersbounty.wordpress.com/2023/02/22/how-i-manage-web-articles-and-videos-in-obsidian-a-comprehensive-guide/) 是一个「英文实例库」，介绍了如何在 Obsidian 中管理网络文件。该插件提供了一种简单而有条理的方法来存储和组织您的网络资源。使用此插件，您可以更有效地管理您的笔记，并更好地识别和追踪相关的信息。

#### 总结

Obsidian 综述系列文章的主旨就是介绍一些个人认为比较好的 Feature 更新以及插件，所有的内容我都在使用了一段时间后才跟大家分享，所以内容上有些滞后同时也是非常的个人向。今天的文章算是奠定了「综述」系列的基调，如果大家对文章内容和更新有什么需求，可以留言交流，我会根据实际情况进行调整。

另外值得一提的是本篇文章使用 Notion AI 作为润色，简单跟大家聊聊我是如何生成本篇文章的：

首先，最近一直比较忙，但我没忘了「综述」系列，所以遇到不错内容，我会随手记录到「备忘录」中，作为文章内容的基础，如图：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/a2966bec4c64440da2fd4f88516b1a4e.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=8afc88301a46f243a92b98e70f4759cf&referer=https://sspai.com)当内容积攒到一定程度，我先大概在 Obsidian 中进行编辑，将内容补充完整，并配上相应的图片，如图：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/adae73352ed4c68c40f15c5f38219f9b.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=fdbac5bae07da2755e3c3c3cba4c6a8a&referer=https://sspai.com)最后再讲内容粘贴到 Notion 中，使用 Notion AI 的「总结」和「润色」功能，将内容进行修改和调试：

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2023/03/22/743b341a692281632050b251c1e57cd7.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=ae24678417d099ca1dab75ccde58f58a&referer=https://sspai.com)经过以上多轮调教后，整体文章就在 Notion 中生成了，整体用下来有几点感受：

* Notion AI 可以说是围绕「写作」提供了多种功能，分别包括创建文章、润色文章、总结文章、续写内容等，可以说在帮我们完成写作这件事上不再是个玩具，而是一名基本合格的「写作助手」
* 当然他也有缺点，就是对于一些较新的知识储备不够，会生成一些「不明所以」的内容，这就需要我们不断的调教和使用，通过「提问的艺术」得到想要的答案。

个人比较看好 Open AI + 写作这个领域。在一定程度上，它提高了生产力。将原本需要 2-4 小时的写作，缩短到 1-2 小时之内搞定（当然是在素材准备齐全的情况下）。这样的成果已经很令人满意，相信未来会有更大的提高。我们也拭目以待。

本篇是《Obsidian 综述》系列第二篇，后续会持续更新。同时个人还一直在维护《玩转 Obsidian》系列文章，对此系列感兴趣可以在以下渠道找到相关文章：

* [少数派专栏 - 知识管理之术](https://sspai.com/my/column/263/post)
* [闲者时间博客](https://sspai.com/link?target=https%3A%2F%2Fxzsj.vip%2F)
* [闲者时间 Medium](https://sspai.com/link?target=https%3A%2F%2Fxzsj.icu%2F)

Obsidian 综述系列目前包括文章：

* 《[Obsidian 综述 - 新 UI 新征程（v0.16 发布之际）](https://sspai.com/post/75781)》
* 《Obsidian 综述 02 - 近期值得推荐的功能、插件和主题》

玩转 Obsidian 系列目前包括文章：

* 《[玩转 Obsidian 01：用 Obsidian 打造「知识循环」利器](https://sspai.com/post/62414)》
* 《[玩转 Obsidian 02：基础设置篇](https://sspai.com/post/63481)》
* 《[玩转 Obsidian 03：如何记录「间歇式日记」](https://sspai.com/post/63674)》
* 《[玩转 Obsidian 04：为什么推荐使用 Obsidian 做知识管理](https://sspai.com/post/67339)》
* 《[玩转 Obsidian 05：如何进行阅读及摘要](https://sspai.com/post/68492)》
* 《[玩转 Obsidian 06：如何用渐进式总结笔记，把知识交给未来的自己](https://sspai.com/post/69025)》
* 《[玩转 Obsidian 07 ：自动化「间歇式日记」](https://sspai.com/post/69982)》

可以在 [Twitter](https://sspai.com/link?target=https%3A%2F%2Ftwitter.com%2Fxianzheshijian)、[Telegram](https://sspai.com/link?target=https%3A%2F%2Ft.me%2Fxztime) 、[instagram](https://sspai.com/link?target=https%3A%2F%2Finstagram.com%2Fshopkeeper.wang) 等渠道关注我，获取更多有意思的讯息。
