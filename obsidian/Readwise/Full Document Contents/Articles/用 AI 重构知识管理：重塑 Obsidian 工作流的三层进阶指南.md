# 用 AI 重构知识管理：重塑 Obsidian 工作流的三层进阶指南

![rw-book-cover](https://cdnfile.sspai.com/2025/04/05/2db4fe0e4e493c861e23efc92d79d990.png)

## Metadata
- Author: [[西郊次生林]]
- Full Title: 用 AI 重构知识管理：重塑 Obsidian 工作流的三层进阶指南
- Category: #articles
- Summary: AI 应该如何改变知识管理？网上针对这个话题的讨论不多。在这篇文章里我将介绍一下我的思考以及我的 Obsidian AI 工作流方法。
- URL: https://sspai.com/post/98106

## Full Document
**Matrix 首页推荐**

[Matrix](https://sspai.com/matrix) 是少数派的写作社区，我们主张分享真实的产品体验，有实用价值的经验与思考。我们会不定期挑选 Matrix 最优质的文章，展示来自用户的最真实的体验和观点。   

文章代表作者个人观点，少数派仅对标题和排版略作修改。

> 本文采用古法手作，从立意到观点到文本都没有任何 AI 参与，请放心阅读。
> 
> 

这是一个 AI 颠覆一切的时代，AI 正在以前所未有的速度重塑人们的生活方式。吴恩达曾在演讲中说，AI 会像 100 年前的电力一样，为我们的生产方式带来根本性的变革。这一预言似乎已经在知识管理这个小领域中实现了：在区区两三年时间里，各种 AI 笔记和知识库产品如雨后春笋一般成长起来，比如 Google 的 NotebookLM、腾讯的 ima 以及开源的 Cherry Studio 等等。它们之中每一个都或多或少被冠以过「革命性」、「颠覆性」这样的形容词，似乎 AI 的突进已经不容阻挡。

各家 AI 知识库产品的体验报告我暂时还没动笔，不过细细观察下来的话其实不难发现，这些产品的卖点基本上可以归结如下：

1. **AI 总结、智能写作、生成脑图**（大语言模型最基本的功能）；
2. **AI 搜索**（由已经成熟的向量搜索技术支持）；
3. **知识库问答**（由 RAG 技术支持的功能）。

在我看来，这些产品对知识管理的改造尚还有提升空间，没能很好地考虑现阶段知识管理存在的痛点以及大语言模型的长处。早在 GPT 3.5 的时代，我就怀着 100% 的期望尝试了当时的知识库问答产品。我将我所有的笔记都存入知识库供其阅读，一开始我为一个能流利运用独家知识的 AI 兴奋不已，但是在新鲜感消失之后，其就陷入了无尽的闲置中。后来我承认，我其实不需要一个 AI 替我转述我自己写的内容。笔记知识库问答对我来说是一个纯粹的「伪需求」。

AI 到底如何真正改善知识管理？网上针对这个话题的讨论不多。在这篇文章里我将介绍一下我的思考以及我的 Obsidian AI 工作流方法。如果有不同想法或对你有所启发，欢迎在评论区交流。

#### AI 能解决什么样的痛点？

打工人最深重的恐惧就是被 AI 取代，这其实刚好说明了现阶段大语言模型的本质：**模仿了一部分人类智能，能够完成一部分过去需要人力完成的任务**。这侧面反映了 **AI 能解决的问题也就是过去需要消耗人力才能完成的任务**。基于这个认识去寻找需求，才能找到真正的痛点。

回过来看之前那个案例，我们不难发现其荒谬之处：向 AI 询问自己笔记中的内容，相当于是雇了一个人来替自己看笔记，然后把其中的内容二手转述给自己。可是我作为内容的作者，我真的需要一个人来替我看自己写的笔记吗？

**作为对比，文献库问答才是真正有价值的应用**。大型文献库拥有海量的文献，这些文献可能来自不同语言，大概率不适合我阅读。但是有时候我又需要快速从其中找到我需要的一小部分信息，这时候我可能会想雇一个助理根据我的问题替我去文献库中搜索文献，整理成报告后给我答案。正因如此，现在的知识库问答产品抓住了「助理」这一定位，不局限于解析用户自己写的笔记，而是花大力气去支持解析包含网页、图像、PDF、Docx 在内的各种各样的文档，力图去成为那样一个「文献库助理」。

基于这样的思路，我们可以重新思考如何将 AI 结合进知识管理工作流中。按照对 AI 能力要求的不同，AI 的应用也可以分若干等级。接下我会先介绍一下 Obsidian 中的 AI 应用，然后从低等级开始，逐层展示我如何在知识管理中利用 AI。

#### Obsidian AI 入门

我一向使用 Obsidian 来完成知识管理以及写作之类的任务。Obsidian 本体并没有提供 AI 接口，但是有许多插件都实现了 AI 功能。我们只需要安装 Copilot 插件就可以使用丰富的 AI 功能了：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/3291c1dea094cbbcc0df459413762a79.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=5149e611293dc7d7b51c2fa30a17eae0&referer=https://sspai.com)Copilot 支持的大模型服务很多，但是本身不提供任何大模型服务，都需要有自己的 API key 才能使用。

网上有很多教程教你如何部署自己的本地大模型，**但是如果没有保密需求的话，我还是推荐你直接购买别人的服务**。拜 Deepseek 所赐，现在的 API 价格已经低到白菜价。这是在白天（北京时间 08:30-00:30）调用 deepseek 官方服务的价格：

| 种类 | Deepseek-chat | Deepseek-reasoner |
| --- | --- | --- |
| 百万tokens输入（缓存命中） | 0.5元 | 1元 |
| 百万tokens输入（缓存未命中） | 2元 | 4元 |
| 百万tokens输出 | 8元 | 16元 |

考虑到一个 token 大约相当于 1.x 个汉字，而且仅需个位数人民币就可以买到数以百万计的 token。可以说购买 token 用来处理笔记基本上造不成什么消耗。购买之后生成一个 API key，填入对应的地方点击 verify 即可使用。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/08/b3bc9594978cbbd0387cc66ed7e244c6.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=8d6d9475a6460611ed8c5f5c341bf1e7&referer=https://sspai.com)回到主页，点击左侧的图标，右侧就会出现一个你熟悉的 AI 聊天框，此时你就可以使用 Copilot 丰富的功能了。 `

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/61c601c3853b9274599361ba51b9fbee.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=5c7d6f399144691332cf63ac9af024d1&referer=https://sspai.com)为了方便，我将 `Alt+Q` 这个快捷键绑定到 `CopilotL Apply custom prompt` 这个命令上，方便后续调取自定义提示词。

![](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/2a17c7c51b0970936d57cfa7459d49a1.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=3f9a8fc533aa1ad83dae2192da25d350&referer=https://sspai.com)做好了这一切之后，我们就可以正式开始探索知识管理中的 AI 应用了。

#### 基础 AI 应用（语言级）

##### Copilot 自带功能

大语言模型作为自然语言处理学科的产物，处理语言是其看家本领。基本的语言处理功能主要是这些：

1. 语言润色（语法、表达）；
2. 翻译；
3. 扩写、缩写

润色和翻译是最基础而且也最实用的大模型功能，Copilot 已经直接提供。选中一段话之后，右键在菜单中选择「fix grammar and spelling」或者「Translate to Chinese」即可调用这两个功能：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/5aa0d23405044e68632b987ff7875de3.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=bbd20477d698b7e463da65492e17b615&referer=https://sspai.com)![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/5195a46d3ee894143992c89464f56d50.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=20e0d59a0c3664ec03408afff68d885c&referer=https://sspai.com)这些基本功能大多是围绕语言优化方面的，很容易理解以及使用。其中 Emojify 是一个比较有意思的功能，其可以用 emoji 装点干巴巴的文本，使其看起来更加「活人」或者抽象。笔记软件非常喜欢用 emoji 装点主页，GPT 在某个版本之后也很喜欢在回答中掺杂大量 emoji。

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/7a7e01c4e91ef3f4bc4b54685adf3cd6.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=8427f5181200ab30362e637d0db36873&referer=https://sspai.com)##### 参考文献引用格式转换

语言级的应用看起来鸡毛蒜皮，但是实际上可以解决很多过去很让人烦恼的小问题。比如我写了个用 Copilot 转换参考文献引用格式的提示词，从此再也不用为转换引用格式而发愁。将下述模板保存到 `copilot-custom-prompts` 文件夹下，用命令调用：

```
请将以下表示参考文献的文本转换为GB/T 7714 格式。要求：
1. 
严格遵循目标格式的规范（如作者名缩写、标题大小写、标点符号等）；
2. 
保留所有原始信息（作者、标题、期刊、年份等）；
3. 
若字段缺失或格式模糊，请标注‘[需补充]’并保留原始内容；
4. 
按目标格式的文献类型（如期刊论文、书籍、会议论文）分类处理；
5. 
直接输出，不要有任何多余文字。
示例输入（BibTeX）：
@article{key,  
  author = {Author, A. and Coauthor, B.},  
  title = {An Example Title},  
  journal = {Journal Name},  
  year = {2023},  
  volume = {10},  
  pages = {100--120}  
}
示例输出（GB/T 7714）：
AUTHOR A, COAUTHOR B. An example title[J]. Journal Name, 2023, 10: 100-120.  
待转换的文本内容：  
{}
```
![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/28225ab201d35443334e6651cc6b86d8.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=ed99e7a6f89396f454e20ec2a213b462&referer=https://sspai.com)不过需要注意的是，我这里的参考文献格式仅仅用于笔记内，对格式要求并不严格，因此我并没有写一个很长的提示词来精确控制生成效果。如果需要生成精准的引用格式，你需要在提示词中添加更多规则来指导 AI。

##### 格式优化

沉浸式写作笔记时我们往往会顾不得观感，将高浓度知识写成没有格式的大段文字，不利于之后阅读笔记。在过去，我们往往需要事后浏览全文再修改笔记，费时费力。现在这件事可以交给 AI 来完成。使用如下提示词：

```
请将以下文本内容转换为结构化的有序列表（1. 2. 3. ...），要求：  
1. **逻辑分层**：按主题或步骤拆解，保持因果关系或时间顺序；  
2. **简洁明确**：每点用 1-2 句概括，避免冗长；  
3. **关键信息优先**：保留核心事实、结论或行动项，可以用**加粗**强调关键信息  
4. **若内容复杂**：可嵌套子列表。  
待处理文本：  
{}
**示例输出格式：**  
1. 第一主题/步骤  
 1. 子要点（可选）
2. 第二主题/步骤  
3. 第三主题/步骤  
```
选中一段文本，再按 `Ctrl+P` 打开命令面板，选择 `Copilot: Apply Custom Prompt` 之后选择对应的提示词即可（或使用定义的快捷键）：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/012efaf34b6049c386f86972b36b9a71.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=3c756d4d11db73895cb5c94772af6545&referer=https://sspai.com)可以发现，经过分点、划重点重新组织之后，文本的可读性大大提高了。如果你喜欢乘着兴致不拘小节地写完整个笔记，但是又为笔记可读性差为后续阅读带来困难所烦恼，那你可能会需要这一小功能来替你整理笔记内容。

#### 中级 AI 应用（语义级）

语义级的应用不仅要求能够正确使用语言，还要正确理解文章的意思。比较常见的语义级应用主要是 AI 总结（summarize）。AI 总结是一个应用很广泛的功能，一般总是用来生成标题、摘要以及 tldr。

比如这是一个生成 tldr 的提示词：

```
请根据以下笔记内容，分析并生成一段说明文本，解释「为什么会有这篇笔记」。要求：
1. **写作目的**：总结这篇笔记的核心意图，比如是为了记录知识、解决问题、整理灵感还是其他用途。
2. **使用场景**：说明在什么情况下可以参考或使用这篇笔记（例如学习、写作、决策等）。
3. **语言风格**：简洁清晰，避免冗余，不需要生成格式和所需内容之外的任何文本，直接关联笔记内容。
**笔记标题**：[[笔记标题]]  
**笔记内容**：  
{activeNote}
**示例输出结构**：
> [!tldr]
> 这篇笔记的目的是……（如：系统梳理XX领域的核心概念/记录XX问题的解决方案）。  
> 它可能在以下场景中发挥作用：……（如：当需要快速回顾XX知识时/当遇到XX类问题需参考步骤时）。
```
但是对于笔记用户来说，AI 总结还可以解决一个知识管理长久以来的难题，即书写元数据。

##### 元数据生成

元数据，即用来记录笔记各种属性信息的一组数据。在 Obsidian 中，元数据是一组用 YAML 格式书写的、放在笔记开头的属性数据：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/60a1792918f3ecf91f5c667f601a8738.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=b1344925b9ab5dcd2ef419658d01ecdd&referer=https://sspai.com)元数据是知识管理的重要抓手之一，Dataview 检索以及标签检索都需要依赖元数据。但是填写元数据有时候是一件很费时费力费脑的事情。一个经典的场景是：你思如泉涌，想马上记一个笔记；当你兴冲冲打开一个新文件，你却得先耐着性子填一个元数据表。

简单的元数据表很容易填，复杂的就很费时费力。这是我的某个元数据模板：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/6e15a1a8b3ae547bbb98c8535c28c6c4.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=796b0a15cfc7138ad05bc052c2c6ff99&referer=https://sspai.com)在这个元数据模板中，光是标签就至少有三个（Functions、Projects、Techniques），其他字段也不太适合用 templater 生成。经常出现的情况是，我填写元数据的时间超过了写笔记本身；当我终于写好了元数据字段，我的灵感早已消失不见了。

填写元数据会给人造成压力，久而久之就会让记录这一习惯难以为继。因此，近些年「无压笔记」的概念越来越流行。许多笔记软件都倡导「打开软件，然后开始记录」，尽可能扫清横亘在写笔记之前的障碍，最好让用户在记录之前连创建文件的工作都不需要做，从而保证用户能把记录的习惯保持下去。

相较 flomo 这类面向一般用户的软件，Obsidian 仗着自己的用户普遍爱折腾、不怕麻烦，一直以来都对「无压笔记」这一概念不甚热心。但是由于 Obsidian 的元数据是纯文本形式，这就给了我们操作空间去自己实现无压笔记。我们可以：

1. 先 `Ctrl+N` 新建文件，然后直接开始写作；
2. **写作完成后，要求 AI 依据文章内容生成所有元数据**。

就这样，我们一定程度上也就实现了 Obsidian 无压记录，大大减轻了记录笔记的负担。比如这是我写好的笔记：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/0a08269384f5e319159535bb830b649c.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=47d028fe85b417f590209435c1189656&referer=https://sspai.com)直接应用提示词，生成元数据：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/9e3cd3b105bb32b303ef2218369464c4.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=1a078c4be21b0fb38ef9527ac81b2e3e&referer=https://sspai.com)复制元数据内容直接粘贴在笔记顶部：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/cd964b39fba51e7afb81324c649ff99e.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=56d40f21b60df3c7d5796fe953ed5c86&referer=https://sspai.com)
> DeepSeek API 并没有自动获取日期的功能，因此日期仍然需要手动修改。
> 
> 

生成元数据是 AI 总结在知识管理上的一种应用。在生成元数据过程中，AI 需要总结文章内容，思考文章适合什么样的标签、文章属于的文件种类、文章是否结构完整以及文件名可能有什么别名。若你的元数据中还有 title、description 这样的字段，则 AI 还需要根据文章内容总结合适的标题以及一句话描述。这一切都需要 AI 拥有理解文章内容并且抓住重点的能力，因此被归类于中级（语义级）应用。这是本文案例使用的提示词：

```
# 元数据生成助手
## 任务描述
请根据提供的笔记内容，按照以下模板规范生成YAML格式的元数据。
## 元数据模板
---
tags: 
doc_type: 
aliases: 
finished:
creation: 
---
## 生成规则
1. **tags** (标签)：
 1. 按照 IAPR标签法打标签：
  1. Area和Resource标记内容属于的领域，每个笔记必须至少有一个；Area包含专业领域，比如人工智能、数学、软件工程、算法、英语、科研等；Resource包含其他兴趣领域；同一个领域不能同时属于Area和Resource；
  2. Area和Resource用三级标签表示，第一级为Area或者Resource，第二级为大类，第三级别为具体细分领域，比如 `Area/数学/优化理论`，`esource/写作/技巧`；
  3. Area和Resource标签可以有多个，你可以按照笔记内容总结并且生成，最后按照重要性排序。
2. **doc_type** (文档类型)：
    - 从以下类型中选择最匹配的：
     - 知识卡片：具体围绕一个概念进行介绍的笔记，像维基百科页面一样的笔记；比如笔记名称为「Kubernetes」的笔记就是一个知识卡片；
     - 文章：涉及多个概念的讨论，或者教程，或者一个章节的课程笔记；
     - 计划书；
     - 记录：一些整理过的以及未整理的文字，一般是记录了某些备忘信息、日志或者未经整理的实践记录；
3. **aliases** (别名)：
    - 提供1-3个替代标题
    - 包含可能的缩写或英文翻译
4. **finished** (完成状态)：
    - 默认设为
false
    - 如果文章已经完整内容可设为
true
5. **creation** (创建时间)：
    - 自动生成当前日期，格式：YYYY-MM-DD
## 输出要求
1. 只输出最终的YAML格式元数据
2. 不要包含任何解释性文字
3. 确保YAML格式正确
## 示例输入
笔记标题：[机器学习基础](app://obsidian.md/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80) 笔记内容：介绍机器学习的基本概念、监督学习和无监督学习的区别，以及常见算法概述。
## 示例输出
---
tags:
    - Area/人工智能/机器学习
doc_type: 文章
aliases: 
    - ML基础
    - Machine Learning Basics
finished: 
true
creation: 2023-11-15
---
# 你的任务
请根据实际笔记内容生成元数据。笔记内容：
{activeNote}
元数据：
```
#### 高级 AI 应用（思想级）

思想级应用不仅要求 AI 能够理解语言的含义，还要能理解语义背后的抽象规律，即我们说的「思想」。思想级应用对 AI 能力的要求较高，自行部署的小型模型就大概率不够看，最好老老实实用 GPT-4o 或者满血 DeepSeek 模型。

##### 六经注我

一篇良好的议论文或者演讲稿往往都会用引经据典的方式来佐证自己的观点。但是对于文化程度有限的人来说，这件事有时候有些难做。这时候就不妨请出 AI 来帮忙，**让 AI 从经典中找出有利于自己的观点的证据或者案例，使得文章内容更加充实**。相比 AI 代写这样的滥觞，利用 AI 搜索证据并没有减少你创作的参与度，这其实是对读者和自己都负责任的做法。

使用提示词直接让 AI 根据自己掌握的知识就已经可以提供很完善专业的论据了：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/34f2dc5ec05e827cca3e9d0fd03350a8.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=bc1767a5e5ff4824f35379f00a2c4a73&referer=https://sspai.com)提示词：

```
请为以下文本提供支持性论据，要求论据类型明确、来源可靠，并标注具体出处。按优先级排序，优先提供最契合的论据。
可以包含的论据类型（可多选）：  
□ 典故/历史事件 □ 学术文献 □ 名人名言 □ 统计数据 □ 案例研究 □ 其他（请说明_____）  
输出格式要求：  
1. 每个论据单独标注类型（如【典故】、【文献】等）；  
2. 直接引用原文时用“引用标记”；  
3. 注明出处（如书籍/论文/演讲的标题、作者、年份）；  
4. 简要说明该论据如何支持目标文本（1-2 句话）。  
示例：  
【文献】  
引用：
"群体智慧在决策中常优于个体专家（Surowiecki, 2004）"
  
出处：《群体的智慧》，James Surowiecki，2004 年  
支持逻辑：该研究直接验证了目标文本中关于集体决策优势的论点。  
目标文本：  
「 {} 」
论据：
```
##### 苏格拉底式讨论

和搜索引擎一样，AI 也可以看作一个信息来源。和传统的信息来源不同的是，**AI 是交互式的，可以在讨论中为你带来真知灼见**。尽管 AI 并没有自己的实体，无法从实践中获得独一无二的见解，但是讨论本身就已经具有引导思考走向深入的效用，可以有效地帮助我们澄清以及提炼自己的想法。

讨论流程主要包含以下四步：

1. 向对方表达你的想法；
2. 对方重述你的想法，对你的想法发表评论或者引出一个新的概念；在这个过程中，原来的观点可能会被扩展或者受到挑战；
3. 你根据对方的回应，扩充或者修正自己的想法；
4. 重复 1~3 步，不断优化自己的想法。

这样的讨论方法和苏格拉底倡导的很相似，因此我暂且将其称之为「苏格拉底式讨论」。

苏格拉底倡导的讨论方法一向以可以给人带来启发而著称。不过考虑到现实中我们往往很难找到具有相似知识背景、永远有时间而且足够耐心的讨论伙伴，而人类又恰好是很容易因为信仰观点不同而产生冲突的物种。因此参与一场这样的讨论对大多数人来说可望而不可及的。巧的是，大语言模型显然刚好解决了上述痛点：

1. **知识面极宽**：了解几乎所有领域的知识，不存在鸡同鸭讲的情况；
2. **极有耐心**：没有情绪，任何时候都能保持温和与礼貌；
3. **冷静客观**：由于数据是从整个互联网上采样得来的，如果不做主动要求，大模型的观点基本上可以看作当下社会人们观点的最大公约数，不存在偏见。

接下来我将以「AI 写作」这一争议话题为例，演示一下这套讨论流程。这里再次提醒：

> 虽然这是一篇介绍 AI 应用的文章，但是本文使用古法手作，从立意到观点到文本都没有任何 AI 参与。
> 
> 此外，由于本人对保持英语能力有一定执念，会逼迫自己用英语和 AI 交流，因此本文从过去的聊天记录中挑选得到的案例也用英语书写。造成不便还望见谅。
> 
> 

我不是一个很喜欢讨论技术伦理的人，对于「AI 写作」，一开始我并没有太多想法，因此我首先向 AI 征询建议：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/ed4a8b45f2f73f5d8392c73e9ef4667d.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=28434a94394d8f33129c65b902762324&referer=https://sspai.com)AI 列出了 AI 写作的积极消极两方面内容，最后抛出了自己的观点：AI 属于工具，只是使用者智慧的延伸，AI 创作的内容版权完全可以归于使用者。

我认为这个思路很有意思，而且确实和现在国内的法律实践相吻合。于是我开始追问观点的源头：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/4662a1710cda5514d8230c8ea0b1870e.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=8b87ac8906f11f9ea288e61d7056201d&referer=https://sspai.com)AI 提到，这种观点其实来源于「工具理性（instrumental rationality）」，即技术其实是人类身体的延伸，其代表人物是黑格尔和康德。以 OpenAI 为代表的许多公司都持有类似的观点，但是也有一些思想流派有不同的见解。

将 AI 视作是打字机、语法检查器这样的写作工具在我看来似乎也没有问题。毕竟现阶段的 AI 的输出内容完全取决于用户输入什么以及随机数种子，说是用户的创作也能讲得通。但是问题在于 AI 拥有如此高的能力不完全是 AI 公司的功劳，训练用的海量数据都是互联网上的其他人创作的，但是这些人却没有从中获益；此外，完全由 AI 创作的内容质量往往不过关，创作者享受了收益却没有对读者负责。因此 AI 写作才引起了如此大的争议。于是我将这些思考返回给了 AI：

![image.png](https://imgproxy.readwise.io/?url=https%3A//cdnfile.sspai.com/2025/04/05/article/ac5aca9cafacf4f91fe5a4ae46390554.png%3FimageView2/2/w/1120/q/40/interlace/1/ignore-error/1/format/webp&hash=7f49b2cb36dd391e3d0dcb127d283bc0&referer=https://sspai.com)AI 更加清晰地重述了我的观点，然后列了一张表比较 AI 公司、用户和公众三方彼此的权利义务。从这里可以发现，这三方里面 AI 公司获得了利润，用户享受了服务，而且可能通过服务获利，只有作为 AI 的基础的公众没有对应的收益。主要矛盾有两点：

1. AI 写作普及之后，许多人用 AI 生成内容批量运营自媒体，使得低质量 AI 生成内容充斥互联网，影响了公众的互联网使用体验；
2. 与此同时，公众创作的内容被无偿地供 AI 公司用于训练模型，但是模型却被闭源垄断用于获利。

分析清楚这些之后，很容易就可以得到一些建议解决方案：

1. 对于 AI 写作用户：仍然可以获取收益，但是要求其保障内容质量，保证内容对读者负责，而非禁用 AI；
2. 对于 AI 公司：使用公开数据训练的模型必须进行一定的开源（开源权重、开源训练方法等）或是分享其收益；避免有人损害开源以谋取私利。

到这里，观点似乎就开始有一些价值了。如果沿着这两个方向深入下去调研，应该可以写成一篇深度还不错的文章。我们就这样从无到有地利用和 AI 的讨论获得了新的见解。

#### 结语

大语言模型作为第一个能让大家看见「智能」的产品，其庞大应用潜力可能远还没有被挖掘。我认为大模型真正的应用生态位应该是过去需要由助理代行的工作，基于这个思路，可以按照需要的智能等级将 AI 应用分为三级：语言级、语义级、思想级。

我在本文中针对这三个级别都列举并且实践了一些 AI 应用。不过我相信这只是大语言模型应用的冰山一角。本文案例更多应该作为抛砖引玉之用，希望在评论区看到你的见解。

我是@西郊次生林，一个研究自然语言处理的研究生，希望得到你的关注。

> 关注 [少数派小红书](https://www.xiaohongshu.com/user/profile/63f5d65d000000001001d8d4)，感受精彩数字生活 🍃

> 实用、好用的 [正版软件](https://sspai.com/mall)，少数派为你呈现 🚀
