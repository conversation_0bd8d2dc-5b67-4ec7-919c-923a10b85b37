# crawl.py

import logging
import argparse
import os
import sys
import subprocess
import time
from datetime import datetime

# --- 核心服务导入 ---
from src.core.api_client import APIClient
from src.core.data_manager import Deduplicator
from src.core.config import load_config
from src.core.utils import (
    select_from_list,
    select_file_from_input,
    UserQuitInterrupt
)
from src.core.constants import RuleType, PlatformType, TaskType

# --- 平台适配器导入 ---
from src.platforms.douyin_adapter import DouyinAdapter
from src.platforms.tiktok_adapter import TikTokAdapter

# --- 规则执行器导入 ---
from src.rules.keywords_executor import KeywordsExecutor
from src.rules.urls_executor import UrlsExecutor
from src.rules.authors_executor import AuthorsExecutor

# --- 任务处理器导入 ---
from src.tasks.new_crawl_processor import NewCrawlProcessor
from src.tasks.update_meta_processor import UpdateMetaProcessor
from src.tasks.update_video_processor import UpdateVideoProcessor


def setup_logging(config):
    """设置双通道日志系统：文件记录DEBUG级别，控制台只显示MONITOR级别及以上。"""
    # 从 utils 导入 ContextFilter
    from src.core.utils import ContextFilter

    log_settings = config.crawl_settings
    log_level_str = log_settings.log_level
    log_dir = os.path.join(log_settings.data_path, 'logs/')

    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)
    log_filename = f"crawl_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    log_filepath = os.path.join(log_dir, log_filename)

    # 获取根日志记录器并设置最低级别为DEBUG
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 清除任何可能已存在的处理器
    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    # --- 文件处理器 (DEBUG级别) ---
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    # 添加上下文过滤器到文件处理器
    file_handler.addFilter(ContextFilter())
    root_logger.addHandler(file_handler)

    # --- 控制台处理器 (MONITOR级别) ---
    console_formatter = logging.Formatter('[%(asctime)s] 任务状态/进度: %(message)s', datefmt='%H:%M:%S')
    console_handler = logging.StreamHandler()
    console_handler.setLevel(25) # 直接使用 MONITOR_LEVEL_NUM 的值
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    logging.info(f"日志系统已初始化。DEBUG日志将记录到: {log_filepath}")
    logging.getLogger().monitor("日志系统启动。")



def interactive_mode(config, services):
    """交互式模式主流程，支持返回上一步和退出。"""
    print("--- 欢迎使用V3.0数据采集器 (交互模式) ---")

    state = "SELECT_RULE"
    context = {}

    while state != "EXIT":
        try:
            if state == "SELECT_RULE":
                rule_map = {
                    RuleType.KEYWORDS.value: KeywordsExecutor,
                    RuleType.URLS.value: UrlsExecutor,
                    RuleType.AUTHORS.value: AuthorsExecutor
                }
                _, selected_rule_name = select_from_list(list(rule_map.keys()), "请选择采集规则:", show_back=False)
                if selected_rule_name == "back":
                    # 在第一步，返回就是退出
                    state = "EXIT"
                    continue
                context['rule_class'] = rule_map[selected_rule_name]
                state = "SELECT_PLATFORM"

            elif state == "SELECT_PLATFORM":
                if context['rule_class'] is UrlsExecutor:
                    # URL规则使用混合解析API，默认使用DouyinAdapter（它支持TikTok和抖音URL）
                    context['platform_adapter'] = DouyinAdapter(services["api_client"])
                    state = "SELECT_TASK_TYPE"
                    continue

                platform_map = {
                    PlatformType.DOUYIN.value: DouyinAdapter,
                    PlatformType.TIKTOK.value: TikTokAdapter
                }
                _, selected_platform_name = select_from_list(list(platform_map.keys()), "请选择目标平台:")
                if selected_platform_name == "back":
                    state = "SELECT_RULE"
                    continue
                context['platform_adapter'] = platform_map[selected_platform_name](services["api_client"])
                state = "SELECT_TASK_TYPE"

            elif state == "SELECT_TASK_TYPE":
                if context['rule_class'] is KeywordsExecutor:
                    context['task_processor'] = services["new_crawl_processor"]
                    state = "SELECT_INPUT_FILE"
                    continue

                task_map = {
                    TaskType.NEW_CRAWL.value: services["new_crawl_processor"],
                    TaskType.UPDATE_META.value: services["update_meta_processor"],
                    TaskType.UPDATE_VIDEO.value: services["update_video_processor"]
                }
                _, selected_task_name = select_from_list(list(task_map.keys()), "请选择任务类型:")
                if selected_task_name == "back":
                    state = "SELECT_PLATFORM"
                    continue
                context['task_processor'] = task_map[selected_task_name]
                state = "SELECT_INPUT_FILE"

            elif state == "SELECT_INPUT_FILE":
                input_file = select_file_from_input(config)
                if input_file is None: # select_file_from_input 内部处理了 'q'
                    state = "SELECT_TASK_TYPE"
                    continue
                context['input_file'] = input_file
                state = "GET_PARAMS"

            elif state == "GET_PARAMS":
                kwargs = {}
                if context['rule_class'] is KeywordsExecutor:
                    min_likes_str = input("请输入最低点赞数 (例如 1000, 'b'返回, 'q'退出): ").strip().lower()
                    if min_likes_str == 'q':
                        raise UserQuitInterrupt("用户选择退出。")
                    if min_likes_str == 'b':
                        state = "SELECT_INPUT_FILE"
                        continue
                    try:
                        kwargs['min_likes'] = int(min_likes_str)
                    except ValueError:
                        print("请输入有效的数字。")
                        continue

                context['kwargs'] = kwargs
                state = "CONFIRM_EXECUTION"

            elif state == "CONFIRM_EXECUTION":
                print("\n--- 请确认任务配置 ---")
                print(f"规则: {context['rule_class'].__name__}")
                print(f"平台: {context['platform_adapter'].__class__.__name__}")
                print(f"任务: {context['task_processor'].__class__.__name__}")
                print(f"文件: {os.path.basename(context['input_file'])}")
                if context.get('kwargs'):
                    print(f"参数: {context['kwargs']}")

                choices = ["执行任务", "执行任务并立即上传"]
                _, confirm_choice = select_from_list(choices, "确认执行?")
                if confirm_choice == "back": # User selected 'b'
                    state = "GET_PARAMS"
                    continue

                state = "EXECUTE"

            elif state == "EXECUTE":
                rule_executor = context['rule_class'](context['platform_adapter'], context['task_processor'], config)
                with open(context['input_file'], 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f if line.strip()]

                kwargs = {
                    'start_time': time.time(),
                    'input_file_path': context['input_file']
                }
                kwargs.update(context.get('kwargs', {}))

                if context['rule_class'] is KeywordsExecutor:
                    result = rule_executor.execute(keyword_list=lines, **kwargs)
                elif context['rule_class'] is AuthorsExecutor:
                    result = rule_executor.execute(author_id_list=lines, **kwargs)
                else:
                    result = rule_executor.execute(url_list=lines, **kwargs)

                # 调用摘要和通知函数
                _log_and_notify_summary(result, config)

                # 新增联动逻辑
                if confirm_choice == "执行任务并立即上传":
                    logging.monitor("爬取任务完成，将在3秒后自动启动上传程序...")
                    time.sleep(3)
                    try:
                        # 确保使用与当前crawl.py相同的Python解释器来运行upload.py
                        python_executable = sys.executable
                        subprocess.run([python_executable, "upload.py"], check=True)
                    except FileNotFoundError:
                        logging.error("错误: 'upload.py' 未找到。请确保它与crawl.py在同一目录下。")
                    except subprocess.CalledProcessError as e:
                        logging.error(f"上传程序upload.py执行失败，返回码: {e.returncode}")

                print("\n任务执行完毕。")
                state = "EXIT"

        except UserQuitInterrupt:
            print("\n操作已取消，程序退出。")
            state = "EXIT"
        except Exception as e:
            logging.error(f"交互模式出现意外错误: {e}", exc_info=True)
            print(f"\n发生错误，请检查日志。程序退出。")
            state = "EXIT"

def _log_and_notify_summary(result: dict, config):
    """格式化、记录并发送标准化的任务摘要。"""
    if not result:
        logging.warning("任务执行未返回结果，无法生成摘要。")
        return

    # --- 1. 任务目标 ---
    rule_type = result.get('rule_type', '未知')
    task_type_map = {
        "NewCrawlProcessor": "新爬取",
        "UpdateMetaProcessor": "仅更新元数据",
        "UpdateVideoProcessor": "仅更新视频"
    }
    task_type_name = task_type_map.get(result.get('task_processor_name', ''), '未知')
    input_file = os.path.basename(result.get('input_file', '未知'))

    summary_lines = [
        "🎯 任务目标",
        f"   - 规则类型: {rule_type}",
        f"   - 任务类型: {task_type_name}",
        f"   - 来源文件: {input_file}",
        "",
        "📊 任务结果",
    ]

    # --- 2. 任务结果 ---
    # 2.1 项目级总览
    summary_lines.append(f"   - 总计处理: 成功 {result.get('processed_items', 0)} 个, 失败 {result.get('failed_items', 0)} 个 (共 {result.get('total_items', 0)} 个)")

    # 2.2 数据级统计
    data_stats = []
    if (v_processed := result.get('videos_processed', 0)) > 0: data_stats.append(f"处理 {v_processed}")
    if (v_skipped := result.get('videos_skipped', 0)) > 0: data_stats.append(f"跳过 {v_skipped}")
    if (v_failed := result.get('videos_failed', 0)) > 0: data_stats.append(f"失败 {v_failed}")
    if data_stats:
        summary_lines.append(f"   - 数据处理: {', '.join(data_stats)} 个视频")

    # 2.3 文件产出统计
    task_processor_name = result.get('task_processor_name', '')
    if "NewCrawl" in task_processor_name:
        # 新爬取任务显示新增数据文件
        file_stats = []
        if (nv := result.get('new_videos', 0)) > 0: file_stats.append(f"视频 {nv} 个")
        if (na := result.get('new_authors', 0)) > 0: file_stats.append(f"作者 {na} 个")
        if file_stats:
            summary_lines.append(f"   - 新增数据文件: {', '.join(file_stats)}")
    else:
        # 更新任务显示更新数据文件
        file_stats = []
        if (uv := result.get('updated_videos', 0)) > 0: file_stats.append(f"视频 {uv} 个")
        if (ua := result.get('updated_authors', 0)) > 0: file_stats.append(f"作者 {ua} 个")
        if file_stats:
            summary_lines.append(f"   - 更新数据文件: {', '.join(file_stats)}")

    # 2.4 媒体下载统计
    media_summary = []
    media_stats = result.get('media_stats', {})
    for media_type, stats in media_stats.items():
        if stats.get('downloaded', 0) > 0:
            type_map = {"avatar": "头像", "cover": "封面", "video": "视频", "image_set": "图集图片"}
            media_summary.append(f"{stats['downloaded']} {type_map.get(media_type, media_type)}")
    if media_summary:
        summary_lines.append(f"   - 媒体下载: {', '.join(media_summary)}")

    # 2.5 总耗时
    summary_lines.append(f"   - 总耗时: {result.get('elapsed_seconds', 0):.1f}s")

    # 控制台显示版本（带分割线）
    console_lines = ["----------------------------------------"] + summary_lines + ["----------------------------------------"]
    console_text = "\n".join(console_lines)
    print(console_text)  # 直接输出，避免日志前缀

    # 飞书通知版本（无分割线，无markdown格式，无缩进空格）
    feishu_lines = []
    for line in summary_lines:
        # 移除markdown格式的 "- " 和所有前导空格
        if line.startswith("   - "):
            feishu_lines.append(line[5:])  # 移除 "   - "
        elif line.startswith("   "):
            feishu_lines.append(line[3:])  # 移除前导空格
        else:
            feishu_lines.append(line)

    feishu_text = "\n".join(feishu_lines)

    # --- 3. 发送飞书通知 ---
    webhook_url = getattr(config.crawl_settings, 'FEISHU_BOT_WEBHOOK_URL_CRAWL', None)
    if not webhook_url:
        # 尝试从原始配置字典中获取upload_settings
        try:
            # 重新读取配置文件以获取upload_settings
            import json
            with open('config.json', 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            webhook_url = config_data.get('upload_settings', {}).get('FEISHU_BOT_WEBHOOK_URL')
        except Exception:
            webhook_url = None

    if webhook_url and webhook_url.startswith('https://'):
        try:
            status_icon = "✅" if result.get('failed_items', 0) == 0 else "⚠️"
            title = f"{status_icon} [{rule_type}] {task_type_name} 任务完成"

            import requests
            payload = {
                "msg_type": "post",
                "content": {
                    "post": {
                        "zh_cn": {
                            "title": title,
                            "content": [[{"tag": "text", "text": feishu_text}]]
                        }
                    }
                }
            }
            requests.post(webhook_url, json=payload, timeout=10)
            logging.info("成功发送飞书通知。")
        except Exception as e:
            logging.error(f"发送飞书通知失败: {e}")
    else:
        logging.info("未配置飞书Webhook URL，跳过发送通知。")

def command_line_mode(args, config, services):
    """命令行模式主流程"""
    logging.info(f"--- 启动命令行模式 ---")
    logging.info(f"参数: {args}")

    # 1. 映射规则
    rule_map = {"keywords": KeywordsExecutor, "urls": UrlsExecutor, "authors": AuthorsExecutor}
    selected_rule_class = rule_map.get(args.rule)
    if not selected_rule_class:
        logging.error(f"无效的规则类型: {args.rule}")
        return

    # 2. 映射平台
    platform_adapter = None
    if selected_rule_class is not UrlsExecutor:
        platform_map = {"douyin": DouyinAdapter, "tiktok": TikTokAdapter}
        selected_platform_class = platform_map.get(args.platform)
        if not selected_platform_class:
            logging.error(f"无效的平台类型: {args.platform}")
            return
        platform_adapter = selected_platform_class(services["api_client"])
    else:
        platform_adapter = DouyinAdapter(services["api_client"])

    # 3. 映射任务类型
    task_processor = None
    if selected_rule_class is KeywordsExecutor:
        task_processor = services["new_crawl_processor"]
    else:
        task_map = {"new_crawl": services["new_crawl_processor"],
                    "update_meta": services["update_meta_processor"],
                    "update_video": services["update_video_processor"]}
        task_processor = task_map.get(args.task_type)
        if not task_processor:
            logging.error(f"无效的任务类型: {args.task_type}")
            return

    # 4. 处理输入文件
    input_file = os.path.join(config.crawl_settings.input_path, args.input_file)
    if not os.path.exists(input_file):
        logging.error(f"输入文件不存在: {input_file}")
        return

    with open(input_file, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f if line.strip()]

    # 5. 准备并执行
    rule_executor = selected_rule_class(platform_adapter, task_processor, config)
    kwargs = {
        'start_time': time.time(),
        'input_file_path': input_file
    }
    if selected_rule_class is KeywordsExecutor:
        kwargs['min_likes'] = args.min_likes
        result = rule_executor.execute(keyword_list=lines, **kwargs)
    elif selected_rule_class is AuthorsExecutor:
        result = rule_executor.execute(author_id_list=lines, **kwargs)
    else: # UrlsExecutor
        result = rule_executor.execute(url_list=lines, **kwargs)

    # 调用摘要和通知函数
    _log_and_notify_summary(result, config)

def main():
    parser = argparse.ArgumentParser(description="API-Driven Data Collector V3.0")
    parser.add_argument('-i', '--interactive', action='store_true', help="启动交互模式")
    parser.add_argument('--rule', type=str, choices=['keywords', 'urls', 'authors'], help="规则类型")
    parser.add_argument('--platform', type=str, choices=['douyin', 'tiktok'], help="平台 (URL规则无需指定)")
    parser.add_argument('--task-type', type=str, choices=['new_crawl', 'update_meta', 'update_video'], default='new_crawl', help="任务类型 (关键词规则固定为new_crawl)")
    parser.add_argument('--input-file', type=str, help="位于input/目录下的输入文件名")
    parser.add_argument('--min-likes', type=int, default=1000, help="最低点赞数 (仅关键词规则)")

    args = parser.parse_args()

    config = load_config()
    setup_logging(config)

    try:
        api_client = APIClient(config)
        video_deduplicator = Deduplicator(os.path.join(config.crawl_settings.data_path, 'state', 'crawled_video_ids.json'))
        author_deduplicator = Deduplicator(os.path.join(config.crawl_settings.data_path, 'state', 'crawled_author_ids.json'))

        services = {
            "api_client": api_client,
            "new_crawl_processor": NewCrawlProcessor(video_deduplicator, author_deduplicator, config),
            "update_meta_processor": UpdateMetaProcessor(video_deduplicator, author_deduplicator, config),
            "update_video_processor": UpdateVideoProcessor(video_deduplicator, author_deduplicator, config)
        }
    except ValueError as e:
        logging.error(f"初始化服务失败: {e}")
        sys.exit(1)

    if args.interactive or not all([args.rule, args.input_file]):
        try:
            interactive_mode(config, services)
        except UserQuitInterrupt:
            # 这个异常已经在 interactive_mode 内部处理并打印消息了
            # 这里捕获它只是为了防止程序因未捕获的异常而崩溃
            logging.info("用户从交互模式正常退出。")
    else:
        command_line_mode(args, config, services)

if __name__ == "__main__":
    main()
