# 飞书多维表格上传工具 (`upload.py`) 设计文档 (V2.2 - Code Aligned)

**版本:** 2.2 (基于 V2.1 代码实现进行详细对齐，新增分片上传支持、基于列表的视频附件补传及本地元数据恢复功能)

**1. 项目概述**

*   **目标**: 开发一个独立的、健壮的工具 (`upload.py`)，负责将 `crawl.py` 的输出 (JSON 元数据和视频文件) 精确、结构化地同步到指定的飞书多维表格 Base 中的两张表：“账号表 (Account Table)”和“素材表 (Material Table)”，并**通过 DuplexLink 字段建立两者之间的双向关联**。确保同步过程不覆盖用户在飞书上手动添加的数据（如视频附件），并正确处理各种数据类型，包括**将视频标签作为可动态扩展的多选选项进行管理**，以及**将平台和区域作为可动态扩展的单选选项进行管理（写入时检查并按需更新选项）**。**新增支持大文件分片上传机制（针对超过20MB的视频）。新增基于用户提供的列表的视频附件补传功能，当本地视频缺失时记录待重新爬取的URL。新增基于用户提供的列表从飞书恢复本地缺失的元数据 JSON 文件及相关状态（如去重记录、关键词记录）的功能。** 处理成功后，**保留本地视频文件**以支持后续流程，并提供飞书通知告知处理结果，对持续失败的数据进行归档（常规上传模式下）。
*   **核心作用**: 作为数据管道，将爬虫数据注入飞书，并提供数据恢复和补全机制，确保数据类型的准确性、关联关系的完整性以及良好的可维护性，为后续视频理解、二次创作等流程提供高质量的数据中枢。参考项目蓝图 `@OVERVIEW.md` (V2.3)。
*   **基本流程**:
    *   **A) 常规上传模式 (默认，无特殊模式参数或仅指定单个 `--file`)**:
        *   解析命令行参数 (`parse_arguments`) -> 加载配置 (`load_config`) -> 设置日志 (`setup_logging`) -> 扫描待处理 JSON (`scan_output_directory`) -> 加载失败计数 (`load_failure_counts`) -> **实例化 `FeishuAPI` (处理认证，混合使用 SDK 和 `requests`)** -> **初始化检查 (`initialize_feishu_tables`): 确保表格存在，检查并按需创建/更新飞书字段 (含单选/多选选项库)** -> 循环处理 JSON 文件 (`process_json_file`) [ 处理账号信息 (`process_account_info`) -> 上传视频文件到飞书空间 (`upload_video_file` - **支持分片上传**) -> 处理素材信息 (`process_material_info`) -> 标记处理结果 (`mark_as_processed`) ] -> 保存失败计数 (`save_failure_counts`) -> **发送飞书通知 (`feishu_api.send_notification`)**。
    *   **B) 视频附件补传模式 (通过 `--reupload-from-file <FILE_PATH>` 触发)**:
        *   读取指定文件中的视频ID/URL列表。
        *   对每个视频：检查飞书记录，若本地视频存在则补传附件（支持分片），若本地视频缺失则记录其原始URL到待重新爬取列表（由 `--recrawl-output-file` 指定）。
        *   发送通知。
    *   **C) 本地元数据恢复模式 (通过 `--recover-local-data <FILE_PATH>` 触发)**:
        *   读取指定文件中的视频ID/URL列表。
        *   对每个视频：查询飞书记录，如果本地对应的元数据JSON文件（同时检查 `./output/` 和 `./output/uploaded/` 均没有）缺失，则根据飞书数据在 `./output/` 目录下生成该JSON文件，并更新本地的去重记录 (`data/crawled_ids.json`) 和关键词记录 (`data/keywords.json`)。
        *   发送通知。

**2. 文件与目录结构**

tiktok_project/ (或您的项目根目录，如 'cursor')
├── crawl.py         # 数据采集程序 (V2.0)
├── upload.py        # (**本设计文档对应的程序 - V2.1 代码实现，计划升级到V2.2**)
├── utils.py         # 共享工具函数与类 (Config, Logging, Deduplicator, Managers, etc.)
├── feishu_api.py    # (**封装飞书 API 调用逻辑 - V2.1 代码实现，计划升级支持分片上传**)
├── config.json      # 统一的配置文件 (包含 crawl_settings 和 upload_settings)
├── cleanup.py       # 清理工具
├── requirements.txt # 依赖列表 (包含 lark-oapi, requests)
├── venv/            # (建议) Python 虚拟环境目录
├── url_lists/       # (建议) 存放 URL 列表文件的建议目录
│   └── needs_recrawl_[timestamp].txt # (新增示例) 由附件补传功能生成，供 crawl.py 使用
├── src/             # 源代码模块目录
│   ├── destinations/
│   │   ├── __init__.py
│   │   ├── feishu_tables.py  # (**新增**) 飞书表格结构化定义
│   │   └── feishu_api.py     # (**新增**) 飞书API客户端
│   ├── mapping/
│   │   ├── __init__.py
│   │   ├── base_mapper.py    # (**新增**) 数据映射器抽象基类
│   │   ├── douyin_mapper.py  # (**新增**) 抖音数据映射器
│   │   └── tiktok_mapper.py  # (**新增**) TikTok数据映射器
│   ├── upload_tasks/
│   │   ├── __init__.py
│   │   ├── base_task.py      # (**新增**) 任务处理器抽象基类
│   │   └── sync_task.py      # (**新增**) 常规同步任务处理器
│   └── ...          # 其他模块
├── data/            # 持久化数据目录
│   ├── crawled_ids.json
│   ├── keywords.json
│   ├── platform_region.json
│   └── upload_failures.json
├── downloads/       # 视频文件下载目录 (文件会被保留)
├── output/          # 爬虫 JSON 输出及处理目录 (**--recover-local-data 模式会在此生成文件**)
│   ├── uploaded/    # 处理成功的 JSON 文件
│   └── failed/      # 处理失败达到阈值的 JSON 文件
└── README.md        # 项目说明 (V2.3 计划)

### 6. 飞书表格最终设计

飞书表格的结构定义遵循"代码即文档"的原则，其唯一真理来源是 `src/destinations/feishu_tables.py` 文件。该文件以程序化的方式，精确定义了"账号表"和"素材表"所需的所有字段、飞书类型和建议的显示名称。

`upload.py`在初始化时，会读取此文件中的定义，并自动检查飞书线上表格，确保所有字段都已存在且类型正确，如果不存在则会自动创建。

#### 6.1. 账号表 (Account Table)

定义于 `feishu_tables.py` 中的 `ACCOUNT_TABLE_FIELDS` 字典。

| 业务含义 (程序内部标识) | 飞书类型 (程序定义) | 飞书显示名称 (程序定义) |
| :--- | :--- | :--- |
| `sec_user_id` | `TEXT` | `作者唯一ID` |
| `username` | `TEXT` | `作者昵称` |
| `unique_id` | `TEXT` | `作者ID` |
| `author_link` | `URL` | `作者主页` |
| `bio` | `TEXT` | `作者简介` |
| `avatar` | `ATTACHMENT` | `作者头像` |
| `follower_count` | `NUMBER` | `粉丝数` |
| `following_count` | `NUMBER` | `关注数` |
| `video_count` | `NUMBER` | `视频数` |
| `author_likes_count`| `NUMBER` | `总获赞数` |
| `platform` | `SINGLE_SELECT` | `平台` |
| `region` | `SINGLE_SELECT` | `地区` |
| `related_videos` | `DUPLEX_LINK` | `关联素材` |
| `last_uploaded_uploader`| `DATETIME` | `同步更新时间` |

#### 6.2. 素材表 (Material Table)

定义于 `feishu_tables.py` 中的 `MATERIAL_TABLE_FIELDS` 字典。

| 业务含义 (程序内部标识) | 飞书类型 (程序定义) | 飞书显示名称 (程序定义) |
| :--- | :--- | :--- |
| `video_id` | `TEXT` | `视频ID` |
| `related_author` | `DUPLEX_LINK` | `关联作者` |
| `title` | `TEXT` | `视频标题` |
| `publish_time` | `DATETIME` | `发布时间` |
| `tags` | `MULTI_SELECT` | `视频标签` |
| `original_url` | `URL` | `原始链接` |
| `likes_count` | `NUMBER` | `点赞数` |
| `comment_count` | `NUMBER` | `评论数` |
| `share_count` | `NUMBER` | `分享数` |
| `collect_count` | `NUMBER` | `收藏数` |
| `duration` | `NUMBER` | `时长(秒)` |
| `dimensions` | `TEXT` | `分辨率` |
| `media_files` | `ATTACHMENT` | `视频/图集` |
| `platform` | `SINGLE_SELECT` | `平台` |
| `region` | `SINGLE_SELECT` | `地区` |
| `last_uploaded_uploader`| `DATETIME` | `同步更新时间` |

**3. 核心上传逻辑 (`upload.py`)**

*   **启动与扫描**: 解析命令行参数 (`parse_arguments`) -> 加载配置 (`load_config`) -> 设置日志 (`setup_logging`)。
*   **根据命令行参数决定执行模式**:
    *   **A) 常规上传模式 (默认行为，当无特定模式参数或仅提供单个 `--file` 时)**:
        *   扫描 `./output/` 目录 (或处理 `--file` 指定的单个文件) 获取待处理的 JSON 文件列表 (`scan_output_directory`)。
        *   加载失败计数 (`load_failure_counts`)。
        *   实例化 `FeishuAPI`。
        *   执行飞书表格初始化检查 (`initialize_feishu_tables`) (除非指定了 `--verify-only` 或 `--init-only` 等特殊操作模式)。
        *   循环处理每个 JSON 文件 (`process_json_file` 函数，处理 `--max-files` 参数):
            1.  读取 JSON 文件内容。记录处理开始，获取或重置该文件的失败计数 (根据 `--force` 参数)。
            2.  调用 `process_account_info()` 同步账号信息到飞书，获取 `account_record_id`。
            3.  如果账号信息处理失败，记录错误，调用 `mark_as_processed()` 将 JSON 文件标记为失败 (更新失败计数，可能移至 `failed/` 目录)，然后跳过此文件的后续处理。
            4.  调用 `upload_video_file()` 上传视频文件:
                *   检查是否设置了 `--skip-upload` 参数。
                *   检查本地视频文件路径和大小是否有效。
                *   **调用 `feishu_api.upload_media()`。此方法内部已包含逻辑：根据文件大小和配置的 `SEGMENT_UPLOAD_THRESHOLD_MB` 自动选择使用普通一次性上传 API (`/drive/v1/medias/upload_all`) 或分片上传系列 API (`/drive/v1/medias/upload_prepare`, `/drive/v1/medias/upload_part`, `/drive/v1/medias/upload_finish`)。**
                *   返回 `file_token` (成功) 或 `None` (失败)。
            5.  调用 `process_material_info()` 同步素材信息到飞书，将 `account_record_id` 和 `file_token` (如果存在) 关联到素材记录。
            6.  根据 `process_material_info()` 的返回结果 (`material_success`):
                *   **成功**: 调用 `mark_as_processed()` (将 JSON 文件移动到 `uploaded/` 目录，并清除该文件的失败计数)。更新飞书素材记录的 `upload_status` 字段为 "Success"。
                *   **失败**: 记录错误。更新飞书素材记录（如果记录已创建）的 `upload_status` 为 "Failed"，并将错误信息写入 `failure_reason` 字段。调用 `mark_as_processed()` (增加该文件的失败计数，如果达到 `MAX_UPLOAD_FAILURES` 阈值，则将其移动到 `failed/` 目录)。
        *   所有文件处理完毕后: 调用 `save_failure_counts()` 保存最新的失败计数状态。调用 `feishu_api.send_notification()` 发送处理摘要通知 (除非设置了 `--no-notify`)。
    *   **B) 视频附件补传模式 (通过 `--reupload-from-file <FILE_PATH>` 参数触发)**:
        *   实例化 `FeishuAPI`。
        *   执行飞书表格初始化检查 (`initialize_feishu_tables`)。
        *   定义并调用一个新的核心处理函数，例如 `_handle_reupload_from_file(args, feishu_api, config)`:
            1.  读取由 `args.reupload_from_file` 指定的文本文件，获取视频 ID 或视频 URL 列表 (每行一个)。
            2.  对列表中的每个条目：
                *   如果是 URL，使用 `utils.parse_tiktok_url()` 解析出 `video_id`。若解析失败，记录错误并跳过。
                *   根据 `video_id` 调用 `feishu_api.find_record()` 在素材表中查找对应的记录。
                *   若飞书素材记录存在：
                    *   构造本地视频文件路径，例如 `local_video_path = os.path.join(config['crawl_settings']['DOWNLOAD_PATH'], f"{video_id}.mp4")`。
                    *   检查 `local_video_path` 是否存在且文件有效 (大小 > 0)。
                    *   **若本地视频文件存在**: 调用 `upload_video_file()` (内部包含分片上传逻辑) 上传视频文件，获取 `file_token`。若成功获取 `file_token`，则调用 `feishu_api.update_record()` 更新对应飞书素材记录的 `video_file` 附件字段和 `upload_status`。
                    *   **若本地视频文件不存在**: 从飞书素材记录的字段中获取 `original_url`。如果获取到有效的原始URL，将其追加写入到由 `args.recrawl_output_file` 参数指定的文件中 (若未指定，则默认为 `./url_lists/needs_recrawl_[timestamp].txt`)。
                *   若飞书素材记录不存在，记录警告。
            3.  记录每个条目的处理结果（成功补传、记录待重爬、飞书记录未找到、解析失败等）。
        *   所有条目处理完毕后，输出处理摘要，并调用 `feishu_api.send_notification()` 发送通知。
    *   **C) 本地元数据恢复模式 (通过 `--recover-local-data <FILE_PATH>` 参数触发)**:
        *   实例化 `FeishuAPI`。
        *   执行飞书表格初始化检查 (`initialize_feishu_tables`)。
        *   实例化 `Deduplicator` (从 `utils.py`导入) 并加载其数据 (`data/crawled_ids.json`)。
        *   实例化 `KeywordManager` (从 `utils.py`导入) 并加载其数据 (`data/keywords.json`)。
        *   定义并调用一个新的核心处理函数，例如 `_handle_recover_local_data(args, feishu_api, config, deduplicator, keyword_manager)`:
            1.  读取由 `args.recover_local_data` 指定的文本文件，获取视频 ID 或视频 URL 列表。
            2.  对列表中的每个条目：
                *   解析出 `video_id`。
                *   调用 `feishu_api.find_record()` 查询飞书素材表，获取素材记录字段 `feishu_material_fields`。
                *   若飞书素材记录存在：
                    *   从 `feishu_material_fields.get('related_author')` 获取关联的账号 `record_id`，然后再次调用 `feishu_api.find_record()` (或特定ID查询方法) 查询账号表，获取账号记录字段 `feishu_account_fields`。
                    *   构造本地JSON文件的潜在路径: `output_json_path = os.path.join(output_dir, f"{video_id}.json")` 和 `uploaded_json_path = os.path.join(output_dir, "uploaded", f"{video_id}.json")` (`output_dir` 从config或args获取)。
                    *   **如果 `output_json_path` 和 `uploaded_json_path` 均不存在**:
                        *   调用内部辅助函数 `_generate_json_from_feishu_data(video_id, feishu_material_fields, feishu_account_fields, config)` (其逻辑应参考原 `restore_data.py` 中的 `generate_json_for_video`)，根据从飞书获取的素材和账号信息，在 **`./output/`** 目录下生成新的 `{video_id}.json` 文件。
                    *   **无论本地JSON是否需要重新生成，均执行以下状态同步**:
                        *   调用 `deduplicator.add(video_id)` 将该 `video_id` 添加到去重记录中（如果不存在）。
                        *   从飞书素材记录的 `tags` 字段（如果存在且是一个列表）提取标签，并调用 `keyword_manager.add_tags_as_keywords(tags_list, video_id)` 来更新关键词库。
                *   若飞书素材记录不存在，记录警告。
            3.  记录每个条目的处理结果。
        *   所有条目处理完毕后，调用 `deduplicator.save()` 和 `keyword_manager.save()` 持久化状态。输出处理摘要，并发送飞书通知。
    *   **D) 其他特殊操作模式 (`--verify-only`, `--init-only`)**: 按设计，仅执行连接验证或表格初始化，不进行文件处理。

**4. 关键交互与数据流 (V2.2 对齐)**
*   用户输入/配置: `config.json`, 命令行参数。
*   内部状态: `data/upload_failures.json`, `data/crawled_ids.json` (在恢复模式下会被更新), `data/keywords.json` (在恢复模式下会被更新), `FeishuAPI` 实例。
*   核心 API 交互 (`feishu_api.py`):
    *   Bitable (SDK): 获取/创建字段, 获取/创建/更新记录, 更新字段选项。
    *   **Drive (使用 `requests`)**:
        *   上传文件 (常规): `/drive/v1/medias/upload_all`。
        *   **上传文件 (分片 - 新增)**: `/drive/v1/medias/upload_prepare` (原为 `/drive/v1/upload_all/prepare`)，`/drive/v1/medias/upload_part` (原为 `/drive/v1/upload_all/part`)，`/drive/v1/medias/upload_finish` (原为 `/drive/v1/upload_all/finish`)。 **注意API路径已根据最新飞书文档截图调整为 `/drive/v1/medias/...` 系列。**
    *   Bot (`requests`): 发送 Webhook 通知。
*   文件系统交互:
    *   (常规模式) 读取 `./output/` JSON, `./downloads/` 视频; 写入日志; 移动 JSON 到 `uploaded/` 或 `failed/`。
    *   **(本地元数据恢复模式)** 读取指定列表文件; 在 `./output/` 生成 JSON (检查 `./output/uploaded/` 避免重复); 更新 `./data/` 下的状态文件。
    *   **(视频附件补传模式)** 读取指定列表文件; 读取 `./downloads/` 视频; 写入 `./url_lists/` (待重爬列表)。
*   核心数据产出: 更新飞书表格数据。**恢复模式下还会生成/更新本地 JSON 和状态文件。**

**5. 核心组件设计**

### 5.3.1. 飞书API客户端 (`feishu_api.py`)
*   **文件路径:** `src/destinations/feishu_api.py`
*   **核心职责:** 封装所有与飞书开放平台API的底层交互，为上层业务逻辑提供一个简洁、健壮且统一的调用接口。
*   **核心特性:**
    *   **自动认证与Token管理:** 内部自动处理`tenant_access_token`的获取、缓存和过期刷新。
    *   **统一错误处理:** 封装了自定义的`FeishuAPIError`异常，用于处理所有API返回的业务错误。
    *   **内置重试机制:** 使用`@retry_api_call`装饰器，为所有网络请求和部分可恢复的API错误（如频率限制）提供自动指数退避重试。
*   **主要方法 (Part 1 - 基础交互):**
    *   `__init__(self, config)`: 初始化客户端，读取App ID和Secret。
    *   `_get_tenant_access_token()`: 内部方法，管理Token生命周期。
    *   `_make_request(...)`: 内部核心请求方法，封装了请求头、错误处理等。
    *   `find_record_by_filter(...)`: 根据筛选条件查询单条记录。
    *   `create_record(...)`: 创建一条新记录。
    *   `update_record(...)`: 更新一条已有记录。
    *   `get_table_fields(...)`: 获取一个表格的所有字段定义。
    *   `create_field(...)`: 创建一个新字段。
    *   `update_field_options(...)`: 更新单选/多选字段的选项。

### 5.3.2. 数据映射器 (`src/mapping/`)
*   **文件路径:** `src/mapping/` 目录
*   **核心职责:** 将来自不同平台的原始JSON数据转换为统一的标准内部模型，实现数据标准化和平台解耦。
*   **设计模式:** 采用策略模式，每个平台都有自己的映射器实现，继承自抽象基类`BaseMapper`。
*   **核心组件:**
    *   **`base_mapper.py`**: 抽象基类，定义了所有映射器必须实现的接口：
        *   `to_standard_video(data: Dict) -> Optional[StandardVideo]`: 将原始视频数据转换为标准视频模型
        *   `to_standard_author(data: Dict) -> Optional[StandardAuthor]`: 将原始作者数据转换为标准作者模型
    *   **`douyin_mapper.py`**: 抖音数据映射器，处理抖音特有的数据结构：
        *   支持视频和图集两种媒体类型的转换
        *   处理抖音特有的字段命名（如`aweme_id`、`sec_uid`等）
        *   提取标签信息和统计数据
    *   **`tiktok_mapper.py`**: TikTok数据映射器，处理TikTok特有的数据结构：
        *   支持视频和图集两种媒体类型的转换
        *   处理TikTok特有的字段命名（如`secUid`、`uniqueId`等）
        *   提取标签信息和统计数据
*   **核心特性:**
    *   **类型安全**: 使用类型注解确保数据转换的正确性
    *   **容错处理**: 对于缺失或异常的数据字段，返回None而不是抛出异常
    *   **标准化输出**: 所有映射器都输出相同结构的StandardVideo和StandardAuthor模型
    *   **原始数据保留**: 在标准模型中保留原始JSON数据，便于调试和扩展

### 5.3.3. 任务处理器层 (`src/upload_tasks/`)
*   **文件路径:** `src/upload_tasks/` 目录
*   **核心职责:** 作为`upload.py`的"大脑"，编排和执行具体的数据同步任务。它负责实现核心业务流程，如两阶段同步、智能增量同步，并调度`mapping`层和`destinations`层来完成工作。
*   **设计模式:** 采用策略模式，定义一个`BaseTask`抽象基类，每种不同的上传模式（如常规同步、智能补传）都实现为一个具体的Task类。这使得主程序可以根据用户选择，轻松切换不同的任务处理器。
*   **核心组件:**
    *   **`base_task.py`**: 任务基类，定义了所有任务处理器共有的接口（如`run()`方法）和通用属性（如`config`, `feishu_api`等）
    *   **`sync_task.py`**: 常规同步任务处理器，实现默认的、从`data/`工作区到飞书的全量智能同步
*   **核心逻辑 (SyncTask):**
    1. **扫描与解析:** `_scan_and_parse_json()`: 扫描`data/video_data/`目录，解析所有待处理的JSON文件
    2. **数据映射:** `_map_to_standard_models()`: 调用相应平台的`mapper`，将原始数据批量转换为标准模型，并按ID去重
    3. **第一阶段 - 账号同步:** `_sync_accounts()`: 遍历所有标准作者对象。对每个对象：
        *   调用内部辅助方法 `_build_account_fields()` 将`StandardAuthor`模型转换为飞书API所需的字段字典。
        *   调用`feishu_api.find_record_by_filter()`在飞书中查找记录。
        *   如果记录存在，调用内部辅助方法 `_compare_account_data()` 进行智能比对，只在数据不一致时执行更新。
        *   如果记录不存在，则执行创建。
        *   将成功处理的`sec_user_id`和飞书`record_id`的映射关系存入内存缓存`author_record_id_cache`，供后续素材同步使用。
    4. **第二阶段 - 素材同步:** `_sync_materials()`: 遍历所有标准视频对象。对每个对象：
        *   从内存缓存中获取其作者的`record_id`。
        *   调用`feishu_api.find_record_by_filter()`在飞书中查找素材记录。
        *   调用内部辅助方法 `_handle_media_upload()` 处理文件上传逻辑，该方法会进行文件存在性检查和大小比对，返回需要上传的`file_token`列表。
        *   调用内部辅助方法 `_build_material_fields()` 将`StandardVideo`模型和`file_token`转换为飞书API字段。
        *   如果记录存在，调用 `_compare_material_data()` 进行智能比对，只在元数据或附件需要更新时执行`update_record`。
        *   如果记录不存在，则执行`create_record`。
*   **核心特性:**
    *   **两阶段同步**: 先同步账号信息，再同步素材信息，确保关联关系的正确性
    *   **智能去重**: 在映射阶段按ID去重，避免重复处理相同的作者或视频
    *   **模块化设计**: 清晰分离扫描、映射、同步等不同职责
    *   **可扩展架构**: 通过策略模式支持不同类型的同步任务

**6. 错误处理与数据完整性**
*   原子性**: 单 JSON 文件处理，关键步骤失败不移动到 `uploaded/` (常规模式)。各模式需详细记录处理日志。
*   **API 重试**: 由 `feishu_api.py` 中的 `@retry_api_call` 装饰器统一处理 (基于 `requests` 的调用除外，它们有自己的简单重试逻辑或由装饰器包裹，特别是分片上传的每个步骤)。
*   **字段创建失败**: 初始化阶段处理，失败则中止。
*   **特定错误处理**: `feishu_api.py` 中识别关键错误码 (如 `1254048` 选项不存在) 并抛出异常。`upload.py` 中的 `process_account_info` 和 `process_material_info` 捕获这些异常，并调用相应的选项处理函数 (`_handle_tags_options`, `_handle_singleselect_options`) 进行处理和重试。
*   **标签/单选选项更新失败**: 如果更新选项库本身失败，记录错误，当前记录的写入会失败。
*   **失败归档**: 基于 `MAX_UPLOAD_FAILURES` 配置和持久化的失败计数实现 (常规模式)。
*   **(新增)** **恢复/补传模式下的错误处理**: 对文件读取错误、API查询失败、记录不存在、本地文件不存在等情况进行明确处理和日志记录。

**7. 配置项 (`config.json` -> `upload_settings`)**
```json
"upload_settings": {
    "FEISHU_APP_ID": "cli_a77df84ce77bd00c",
    "FEISHU_APP_SECRET": "YOUR_APP_SECRET",
    "FEISHU_ACCOUNT_BASE_TOKEN": "YOUR_ACCOUNT_BASE_TOKEN",
    "FEISHU_ACCOUNT_TABLE_ID": "YOUR_ACCOUNT_TABLE_ID",
    "FEISHU_VIDEO_BASE_TOKEN": "YOUR_VIDEO_BASE_TOKEN", // 也作为分片上传的 parent_node
    "FEISHU_VIDEO_TABLE_ID": "YOUR_VIDEO_TABLE_ID",
    "FEISHU_BOT_WEBHOOK_URL": "YOUR_FEISHU_BOT_WEBHOOK_URL",
    "API_RETRY_COUNT": 2,
    "API_RETRY_DELAY": 2,
    "API_CALL_DELAY": 0.2,
    "LOG_LEVEL": "INFO",
    "MAX_UPLOAD_FAILURES": 3,
    "SEGMENT_UPLOAD_THRESHOLD_MB": 20, // 文件大小阈值(MB)，超过则使用分片上传
    "SEGMENT_UPLOAD_CHUNK_SIZE_MB": 8  // 分片上传的块大小(MB), 需符合飞书要求 (例如 4MB 的倍数)
}
```

**8. 性能与限制**
*   速率: API 调用间隔 (`API_CALL_DELAY`) 和重试延迟 (`API_RETRY_DELAY`) 是关键。标签/单选选项按需更新策略有助于缓解。
*   **文件大小**: 通过分片上传，现在应能支持大于 `SEGMENT_UPLOAD_THRESHOLD_MB` 设置的文件，具体上限取决于飞书平台和配置的分片大小 (`SEGMENT_UPLOAD_CHUNK_SIZE_MB`)。单个分片大小也受飞书限制（通常建议不超过20MB，最小4MB的倍数）。
*   本地存储**: `./downloads/` 目录下的视频文件会被**保留**，需要用户自行制定策略管理磁盘空间。
*   **恢复/补传模式性能**: `--recover-local-data` 和 `--reupload-from-file` 模式由于是基于用户提供的列表而非全量扫描飞书，其性能主要取决于列表大小和每次查询/更新飞书记录的API调用次数。如果列表过大，仍可能耗时较长。

**9. 命令行参数**
*   `upload.py` 支持丰富的命令行参数以控制行为：
*   **(新增)** `--reupload-from-file <FILE_PATH>`: 指定一个包含视频 ID 或视频 URL 的文本文件 (每行一个)。程序将尝试为这些视频补传附件到飞书。若视频的本地文件 (`./downloads/{video_id}.mp4`) 缺失，则将其原始 URL (从飞书获取) 记录到由 `--recrawl-output-file` 指定的文件中。此模式与其他主要处理模式互斥。
*   **(新增)** `--recrawl-output-file <FILE_PATH>`: (可选, 与 `--reupload-from-file` 配合使用) 指定当本地视频文件缺失时，将原始 URL 输出到的文件路径。默认为 `./url_lists/needs_recrawl_[timestamp].txt`。
*   **(新增)** `--recover-local-data <FILE_PATH>`: 指定一个包含视频 ID 或视频 URL 的文本文件 (每行一个)。程序将根据此列表从飞书恢复本地缺失的元数据 JSON 文件 (保存到 `./output/` 目录，并检查 `./output/uploaded/` 以避免重复生成)，同时更新相关的本地状态文件 (`data/crawled_ids.json`, `data/keywords.json`)。此模式与其他主要处理模式互斥。
*   其他参数 (`--config`, `--output-path`, `--file`, `--dry-run`, `--force`, `--skip-upload`, `--max-files`, `--verify-only`, `--init-only`, `--log-level`, `--no-notify`) 按原设计作用於其适用的模式。

**10. 技术选型与环境**
*   **飞书交互**: **混合使用 `lark-oapi` Python SDK (主要用于 Bitable 操作) 和 `requests` 库 (用于认证、文件上传[包括分片上传]、机器人通知)。**
*   **依赖**: `lark-oapi`, `requests` 需要添加到 `requirements.txt`。
*   **环境**: 强烈建议在项目目录下使用 Python 虚拟环境 (`venv`)。

---

**附录 A：飞书 API 关键信息参考 (V2.2 对齐)**

**A.1 字段类型 (关键类型)**

| 类型名称     | Type ID | 备注 / 写入格式 (Value)                       |
| :----------- | :------ | :-------------------------------------------- |
| 文本         | 1       | 字符串                                        |
| 数字         | 2       | 数字 (int/float)                              |
| **单选**     | **3**   | **字符串 `"选项名"`**                         |
| 多选         | 4       | 字符串数组 `["选项名1", "选项名2"]`           |
| **日期**     | **5**   | **Unix 时间戳 (毫秒, int)**                   |
| 复选框       | 7       | 布尔值 `true`/`false`                         |
| 超链接       | 15      | **字典 `{"text": "显示文本", "link": "实际URL"}`** |
| 附件         | 17      | 数组 `[{"file_token": "..."}]`               |
| 双向关联     | 21      | 数组 `["record_id"]`                          |

**A.2 附件上传与关联**

1.  **上传 API (常规, 用于小于阈值的文件)**: `POST /drive/v1/medias/upload_all` (**通过 `requests` 调用**，`parent_type` 为 `bitable_file`，`parent_node` 为 Base Token)。请求体包含 `file_name`, `parent_type`, `parent_node`, `size` 和 `file` (文件本身)。返回 `file_token`。
2.  **上传 API (分片, 用于大于等于阈值的文件)**:
    *   **a) 预上传 (Prepare Upload)**: `POST /drive/v1/medias/upload_prepare` (**通过 `requests` 调用**)。
        *   请求体 (JSON): `{"file_name": "...", "parent_type": "bitable_file", "parent_node": "YOUR_BASE_TOKEN", "size": FILE_SIZE_IN_BYTES}`。
        *   响应 (JSON): 包含 `upload_id` (上传事务ID) 和 `block_size` (或 `block_size_list`)。
    *   **b) 分片上传 (Upload Part)**: `POST /drive/v1/medias/upload_part` (**通过 `requests` 调用**)。
        *   请求体 ( multipart/form-data ):
            *   `upload_id`: 从预上传获取。
            *   `seq`: 分片序号 (从0开始)。
            *   `size`: 当前分片的大小 (字节)。
            *   `checksum`: (可选) 当前分片的 CRC32 校验和 (字符串形式)。
            *   `file`: 当前分片的文件内容 (二进制)。
        *   响应 (JSON): 通常为空或简单确认。
    *   **c) 完成上传 (Finish Upload)**: `POST /drive/v1/medias/upload_finish` (**通过 `requests` 调用**)。
        *   请求体 (JSON): `{"upload_id": "...", "block_num": TOTAL_NUMBER_OF_BLOCKS}`。
        *   响应 (JSON): 包含 `file_token`。
3.  **关联 API**: `PUT /bitable/v1/apps/:app_token/tables/:table_id/records/:record_id` (**通过 SDK 调用**)。请求体: `{ "fields": { "附件字段名": [{"file_token": "..."}] } }`。

**A.3 记录写入/更新**

*   **API**: `POST /bitable/v1/apps/:app_token/tables/:table_id/records` (创建) 或 `PUT /bitable/v1/apps/:app_token/tables/:table_id/records/:record_id` (更新) (**通过 SDK 调用**)。
*   **请求体**: `{ "fields": { "字段名1": value1, "字段名2": value2, ... } }`。Key 使用**字段名称**，Value 使用 **A.1 中确认的格式**。

**A.4 双向关联 (DuplexLink) 字段创建 (Type 21)**

*   **API**: `POST /bitable/v1/apps/:app_token/tables/:table_id/fields` (**通过 SDK 调用**，在一个表创建即可)。
*   **请求体关键部分 (在素材表创建 `related_author`)**:
    ```json
    {
      "field_name": "related_author",
      "type": 21,
      "property": {
        "table_id": "账号表的 Table ID", // 从 config 读取
        "multiple": false // 通常素材只关联一个账号
      }
    }
    ```
    飞书会自动在被关联的表（此处为账号表）创建对端的关联字段，通常命名为 `素材表名（本表）` 或 `related_素材表名`，例如 `related_videos`。

**A.5 获取/更新字段 (用于 SingleSelect/MultiSelect 选项)**

*   **获取字段列表**: `GET /bitable/v1/apps/:app_token/tables/:table_id/fields` (**通过 SDK 调用**)。响应中每项包含 `field_id`, `field_name`, `type`, `property` (其中 `property.options` 包含选项列表)。
*   **获取单个字段**: `GET /bitable/v1/apps/:app_token/tables/:table_id/fields/:field_id` (**通过 SDK 调用**)。
*   **更新字段 (用于修改选项)**: `PUT /bitable/v1/apps/:app_token/tables/:table_id/fields/:field_id` (**通过 SDK 调用**)。
    *   请求体: 必须包含 `field_name`, `type` (保持不变), 和完整的 `property` 对象。
    *   对于单选(3)或多选(4)字段，`property` 应为： `{"options": [{"name": "新选项1"}, {"name": "新选项2"}, ...]}`。**注意：这里需要提供完整的选项列表（包括旧的和新的），而不是增量添加。**