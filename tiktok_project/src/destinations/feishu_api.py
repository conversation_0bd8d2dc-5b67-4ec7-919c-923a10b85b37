# src/destinations/feishu_api.py
"""
飞书API客户端

封装与飞书开放平台API的交互，包括认证、表格操作、文件上传等。
"""
import os
import time
import logging
import requests
import functools
from typing import Optional, Dict, Any, List

# 飞书API错误代码常量
class FeishuErrorCode:
    TOKEN_INVALID = 99991663
    TOKEN_EXPIRED = 99991664
    REQUEST_FREQ_EXCEED = 99991405
    RECORD_NOT_EXIST = 1254063
    FIELD_NAME_DUPLICATED = 1254014
    OPTION_NOT_EXIST = 1254048

def retry_api_call(retries=2, delay=2, backoff=2):
    """飞书API调用重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            _retries, _delay = retries, delay
            while _retries > 0:
                try:
                    return func(self, *args, **kwargs)
                except (requests.exceptions.RequestException, FeishuAPIError) as e:
                    if isinstance(e, FeishuAPIError):
                        if e.code in [FeishuErrorCode.TOKEN_INVALID, FeishuErrorCode.TOKEN_EXPIRED]:
                            logging.warning("Token无效或过期，立即刷新并重试。")
                            self._get_tenant_access_token(force_refresh=True)
                            # 不消耗重试次数，立即重试
                            continue
                        if e.code not in [FeishuErrorCode.REQUEST_FREQ_EXCEED]:
                            raise # 对于非频率限制的业务错误，直接抛出
                        
                    _retries -= 1
                    if _retries == 0:
                        logging.error(f"API调用 {func.__name__} 达到最大重试次数后失败。")
                        raise
                        
                    logging.warning(f"API调用失败: {e}. 在 {_delay} 秒后重试... ({_retries} 次剩余)")
                    time.sleep(_delay)
                    _delay *= backoff
            return func(self, *args, **kwargs) # 最后一次尝试
        return wrapper
    return decorator

class FeishuAPIError(Exception):
    """自定义飞书API错误"""
    def __init__(self, code, msg):
        self.code = code
        self.msg = msg
        super().__init__(f"Feishu API Error: [Code {self.code}] {self.msg}")

class FeishuAPI:
    BASE_URL = "https://open.feishu.cn/open-apis"
    
    def __init__(self, config):
        upload_settings = config.get('upload_settings', {})
        self.app_id = upload_settings.get('FEISHU_APP_ID')
        self.app_secret = upload_settings.get('FEISHU_APP_SECRET')
        self.api_call_delay = upload_settings.get('API_CALL_DELAY', 0.2)
        
        if not self.app_id or not self.app_secret:
            raise ValueError("配置中必须包含FEISHU_APP_ID和FEISHU_APP_SECRET")
            
        self._token_cache = {"token": None, "expires_at": 0}
        self.session = requests.Session()
    
    def _get_tenant_access_token(self, force_refresh=False):
        current_time = time.time()
        if not force_refresh and self._token_cache["token"] and self._token_cache["expires_at"] > current_time + 60:
            return self._token_cache["token"]

        url = f"{self.BASE_URL}/auth/v3/tenant_access_token/internal"
        payload = {"app_id": self.app_id, "app_secret": self.app_secret}
        
        try:
            response = self.session.post(url, json=payload, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == 0:
                self._token_cache["token"] = data["tenant_access_token"]
                self._token_cache["expires_at"] = current_time + data["expire"]
                logging.info("成功获取新的Tenant Access Token。")
                return self._token_cache["token"]
            else:
                raise FeishuAPIError(data.get("code"), data.get("msg"))
        except requests.exceptions.RequestException as e:
            logging.error(f"获取Token时网络错误: {e}")
            raise

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        time.sleep(self.api_call_delay)
        token = self._get_tenant_access_token()
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json; charset=utf-8"}

        url = f"{self.BASE_URL}{endpoint}"

        try:
            response = self.session.request(method, url, headers=headers, timeout=20, **kwargs)
            response.raise_for_status()
            data = response.json()

            if data.get("code") != 0:
                raise FeishuAPIError(data.get("code"), data.get("msg"))

            return data.get("data", {})
        except requests.exceptions.RequestException as e:
            logging.error(f"API请求网络错误: {method.upper()} {url} - {e}")
            raise

    @retry_api_call()
    def find_record_by_filter(self, base_token: str, table_id: str, filter_query: str) -> Optional[Dict[str, Any]]:
        """根据筛选条件查询单条记录"""
        endpoint = f"/bitable/v1/apps/{base_token}/tables/{table_id}/records"
        params = {"filter": filter_query, "page_size": 1}
        data = self._make_request("GET", endpoint, params=params)

        if data and data.get("items"):
            return data["items"]
        return None

    @retry_api_call()
    def create_record(self, base_token: str, table_id: str, fields: Dict[str, Any]) -> Optional[str]:
        """创建一条新记录"""
        endpoint = f"/bitable/v1/apps/{base_token}/tables/{table_id}/records"
        payload = {"fields": fields}
        data = self._make_request("POST", endpoint, json=payload)

        return data.get("record", {}).get("record_id")

    @retry_api_call()
    def update_record(self, base_token: str, table_id: str, record_id: str, fields: Dict[str, Any]) -> bool:
        """更新一条已有记录"""
        endpoint = f"/bitable/v1/apps/{base_token}/tables/{table_id}/records/{record_id}"
        payload = {"fields": fields}
        self._make_request("PUT", endpoint, json=payload)
        return True

    @retry_api_call()
    def get_table_fields(self, base_token: str, table_id: str) -> Dict[str, Any]:
        """获取表格的所有字段"""
        endpoint = f"/bitable/v1/apps/{base_token}/tables/{table_id}/fields"
        params = {"page_size": 100}
        data = self._make_request("GET", endpoint, params=params)

        fields_map = {field["field_name"]: field for field in data.get("items", [])}
        return fields_map

    @retry_api_call()
    def create_field(self, base_token: str, table_id: str, field_name: str, field_type: int, property: Optional[Dict] = None) -> Optional[str]:
        """创建新字段"""
        endpoint = f"/bitable/v1/apps/{base_token}/tables/{table_id}/fields"
        payload = {"field_name": field_name, "type": field_type}
        if property:
            payload["property"] = property

        data = self._make_request("POST", endpoint, json=payload)
        return data.get("field", {}).get("field_id")

    @retry_api_call()
    def update_field_options(self, base_token: str, table_id: str, field_id: str, options: List[Dict]) -> bool:
        """更新单选或多选字段的选项"""
        endpoint = f"/bitable/v1/apps/{base_token}/tables/{table_id}/fields/{field_id}"
        # 更新字段需要提供字段名和类型
        # 先获取字段现有信息
        field_info = self._make_request("GET", f"/bitable/v1/apps/{base_token}/tables/{table_id}/fields/{field_id}")

        payload = {
            "field_name": field_info["field"]["field_name"],
            "type": field_info["field"]["type"],
            "property": {"options": options}
        }
        self._make_request("PUT", endpoint, json=payload)
        return True

    def _upload_prepare(self, file_name: str, file_size: int, parent_node: str) -> dict:
        """分片上传：准备阶段"""
        endpoint = "/drive/v1/medias/upload_prepare"
        payload = {
            "file_name": file_name,
            "parent_type": "bitable_file",
            "parent_node": parent_node,
            "size": file_size
        }
        return self._make_request("POST", endpoint, json=payload)

    def _upload_part(self, upload_id: str, seq: int, chunk: bytes):
        """分片上传：上传分片"""
        endpoint = f"/drive/v1/medias/upload_part"
        params = {
            "upload_id": upload_id,
            "seq": seq,
            "size": len(chunk)
        }
        # 上传分片时，需要使用 multipart/form-data
        files = {'file': chunk}
        # _make_request 需要稍作修改以支持 files
        token = self._get_tenant_access_token()
        headers = {"Authorization": f"Bearer {token}"}
        url = f"{self.BASE_URL}{endpoint}"
        response = self.session.post(url, headers=headers, params=params, files=files, timeout=60)
        response.raise_for_status()
        data = response.json()
        if data.get("code") != 0:
            raise FeishuAPIError(data.get("code"), data.get("msg"))

    def _upload_finish(self, upload_id: str, block_num: int) -> str:
        """分片上传：完成阶段"""
        endpoint = f"/drive/v1/medias/upload_finish"
        payload = {"upload_id": upload_id, "block_num": block_num}
        data = self._make_request("POST", endpoint, json=payload)
        return data.get("file_token")

    def upload_media(self, file_path: str, parent_node: str) -> Optional[str]:
        """
        统一媒体文件上传接口，自动处理分片。
        """
        if not os.path.exists(file_path):
            logging.error(f"上传失败：文件不存在 {file_path}")
            return None

        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)

        # 从配置中读取分片阈值，默认为20MB
        # 注意：这里需要从初始化时传入的config中读取，但当前架构中没有保存config
        # 暂时使用硬编码的默认值
        threshold_mb = 20  # 默认20MB
        chunk_size_mb = 8  # 默认8MB分片

        SEGMENT_THRESHOLD = threshold_mb * 1024 * 1024
        CHUNK_SIZE = chunk_size_mb * 1024 * 1024

        if file_size < SEGMENT_THRESHOLD:
            # --- 普通上传 ---
            logging.info(f"文件小于{threshold_mb}MB，使用普通上传...")
            endpoint = "/drive/v1/medias/upload_all"
            with open(file_path, 'rb') as f:
                files = {'file': (file_name, f)}
                form_data = {
                    'file_name': file_name,
                    'parent_type': 'bitable_file',
                    'parent_node': parent_node,
                    'size': str(file_size)
                }
                token = self._get_tenant_access_token()
                headers = {"Authorization": f"Bearer {token}"}
                url = f"{self.BASE_URL}{endpoint}"
                response = self.session.post(url, headers=headers, data=form_data, files=files, timeout=180)
                response.raise_for_status()
                data = response.json()
                if data.get("code") != 0:
                    raise FeishuAPIError(data.get("code"), data.get("msg"))
                return data.get("data", {}).get("file_token")
        else:
            # --- 分片上传 ---
            logging.info(f"文件大于{threshold_mb}MB，使用分片上传...")
            # 1. 准备上传
            prepare_data = self._upload_prepare(file_name, file_size, parent_node)
            upload_id = prepare_data['upload_id']

            # 2. 逐块上传
            block_num = 0
            with open(file_path, 'rb') as f:
                while True:
                    chunk = f.read(CHUNK_SIZE)
                    if not chunk:
                        break
                    self._upload_part(upload_id, block_num, chunk)
                    block_num += 1
                    logging.info(f"  -> 已上传分片 {block_num}...")

            # 3. 完成上传
            return self._upload_finish(upload_id, block_num)
