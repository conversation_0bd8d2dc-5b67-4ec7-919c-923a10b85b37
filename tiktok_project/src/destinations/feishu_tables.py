# src/destinations/feishu_tables.py
"""
飞书多维表格的结构化定义 (Schema as Code)

本文件作为与飞书表格结构交互的"唯一真理来源"。
所有字段的创建、更新和引用，都应基于此处的定义。
这确保了代码与飞书表格结构的一致性和可维护性。
"""

# 飞书字段类型常量
TEXT = 1
NUMBER = 2
SINGLE_SELECT = 3
MULTI_SELECT = 4
DATETIME = 5
CHECKBOX = 7
URL = 15
ATTACHMENT = 17
DUPLEX_LINK = 21

# --- 账号表 (Account Table) 定义 ---
ACCOUNT_TABLE_FIELDS = {
    # 业务主键
    "sec_user_id": {"type": TEXT, "name": "作者唯一ID"},
    
    # 基础信息
    "username": {"type": TEXT, "name": "作者昵称"},
    "unique_id": {"type": TEXT, "name": "作者ID"},
    "author_link": {"type": URL, "name": "作者主页"},
    "bio": {"type": TEXT, "name": "作者简介"},
    
    # 媒体文件
    "avatar": {"type": ATTACHMENT, "name": "作者头像"},
    
    # 统计数据
    "follower_count": {"type": NUMBER, "name": "粉丝数"},
    "following_count": {"type": NUMBER, "name": "关注数"},
    "video_count": {"type": NUMBER, "name": "视频数"},
    "author_likes_count": {"type": NUMBER, "name": "总获赞数"},
    
    # 分类与来源
    "platform": {"type": SINGLE_SELECT, "name": "平台"},
    "region": {"type": SINGLE_SELECT, "name": "地区"},
    
    # 关联关系 (由素材表反向创建)
    "related_videos": {"type": DUPLEX_LINK, "name": "关联素材"},
    
    # 程序元数据
    "last_uploaded_uploader": {"type": DATETIME, "name": "同步更新时间"},
}

# --- 素材表 (Material Table) 定义 ---
MATERIAL_TABLE_FIELDS = {
    # 业务主键
    "video_id": {"type": TEXT, "name": "视频ID"},
    
    # 核心关联
    "related_author": {"type": DUPLEX_LINK, "name": "关联作者"},
    
    # 基础信息
    "title": {"type": TEXT, "name": "视频标题"},
    "publish_time": {"type": DATETIME, "name": "发布时间"},
    "tags": {"type": MULTI_SELECT, "name": "视频标签"},
    "original_url": {"type": URL, "name": "原始链接"},
    
    # 统计数据
    "likes_count": {"type": NUMBER, "name": "点赞数"},
    "comment_count": {"type": NUMBER, "name": "评论数"},
    "share_count": {"type": NUMBER, "name": "分享数"},
    "collect_count": {"type": NUMBER, "name": "收藏数"},
    
    # 新增核心属性
    "duration": {"type": NUMBER, "name": "时长(秒)"},
    "dimensions": {"type": TEXT, "name": "分辨率"},
    
    # 媒体文件
    "media_files": {"type": ATTACHMENT, "name": "视频/图集"},
    
    # 分类与来源
    "platform": {"type": SINGLE_SELECT, "name": "平台"},
    "region": {"type": SINGLE_SELECT, "name": "地区"},
    
    # 程序元数据
    "last_uploaded_uploader": {"type": DATETIME, "name": "同步更新时间"},
}
