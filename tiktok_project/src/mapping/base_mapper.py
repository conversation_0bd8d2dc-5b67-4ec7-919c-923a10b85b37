# src/mapping/base_mapper.py
from abc import ABC, abstractmethod
from typing import Dict, Optional
from ..models import StandardVideo, StandardAuthor

class BaseMapper(ABC):
    """
    数据映射器的抽象基类。
    负责将特定平台的原始API数据转换为标准化的内部数据模型。
    """
    @staticmethod
    @abstractmethod
    def to_standard_video(data: Dict) -> Optional[StandardVideo]:
        """将原始视频数据转换为StandardVideo模型"""
        pass
    
    @staticmethod
    @abstractmethod
    def to_standard_author(data: Dict) -> Optional[StandardAuthor]:
        """将原始作者数据转换为StandardAuthor模型"""
        pass
