# src/mapping/douyin_mapper.py
from datetime import datetime
from typing import Dict, Optional, List
from .base_mapper import BaseMapper
from ..models import StandardVideo, StandardAuthor, StandardImage, PlatformType, MediaType

class DouyinMapper(BaseMapper):
    """抖音数据到标准模型的映射器"""
    
    @staticmethod
    def to_standard_video(data: Dict) -> Optional[StandardVideo]:
        if not data or 'aweme_id' not in data:
            return None

        author_info = data.get('author', {})
        stats = data.get('statistics', {})
        video_info = data.get('video', {})
        music_info = data.get('music', {})

        # 提取标签
        tags = [
            tag['hashtag_name']
            for tag in data.get('text_extra', [])
            if tag.get('type') == 1 and 'hashtag_name' in tag
        ]

        # 判断是否为图集
        is_image_set = data.get('aweme_type') == 68 and data.get('images')
        media_type = MediaType.IMAGE if is_image_set else MediaType.VIDEO
        
        images = None
        if is_image_set:
            images = [
                StandardImage(
                    url=img.get('url_list', [None]),
                    width=img.get('width'),
                    height=img.get('height')
                )
                for img in data.get('images', []) if img.get('url_list')
            ]

        try:
            video = StandardVideo(
                video_id=data['aweme_id'],
                platform=PlatformType.DOUYIN,
                title=data.get('desc', ''),
                description=data.get('desc', ''),
                author_id=author_info.get('sec_uid'),
                author_nickname=author_info.get('nickname'),
                author_unique_id=author_info.get('unique_id'),
                like_count=stats.get('digg_count', 0),
                comment_count=stats.get('comment_count', 0),
                share_count=stats.get('share_count', 0),
                collect_count=stats.get('collect_count', 0),
                create_time=datetime.fromtimestamp(data['create_time']),
                duration=video_info.get('duration', 0) / 1000, # 毫秒转秒
                width=video_info.get('width'),
                height=video_info.get('height'),
                cover_url=video_info.get('cover', {}).get('url_list', [None]),
                tags=tags,
                media_type=media_type,
                images=images,
                music_title=music_info.get('title'),
                music_author=music_info.get('author'),
                raw_data=data
            )
            return video
        except Exception:
            return None
    
    @staticmethod
    def to_standard_author(data: Dict) -> Optional[StandardAuthor]:
        if not data or 'sec_uid' not in data:
            return None
        
        try:
            author = StandardAuthor(
                author_id=data['sec_uid'],
                platform=PlatformType.DOUYIN,
                nickname=data.get('nickname'),
                unique_id=data.get('short_id') or data.get('unique_id'),
                signature=data.get('signature'),
                follower_count=data.get('follower_count', 0),
                following_count=data.get('following_count', 0),
                video_count=data.get('aweme_count', 0),
                like_count=data.get('total_favorited', 0),
                avatar_url=data.get('avatar_larger', {}).get('url_list', [None]),
                is_verified=bool(data.get('is_verified')),
                raw_data=data
            )
            return author
        except Exception:
            return None
