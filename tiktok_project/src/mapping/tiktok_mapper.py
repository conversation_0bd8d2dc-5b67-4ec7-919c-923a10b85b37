# src/mapping/tiktok_mapper.py
from datetime import datetime
from typing import Dict, Optional, List
from .base_mapper import BaseMapper
from ..models import StandardVideo, StandardAuthor, StandardImage, PlatformType, MediaType

class TikTokMapper(BaseMapper):
    """TikTok数据到标准模型的映射器"""
    
    @staticmethod
    def to_standard_video(data: Dict) -> Optional[StandardVideo]:
        if not data or 'id' not in data:
            return None

        author_info = data.get('author', {})
        stats = data.get('stats', {})
        video_info = data.get('video', {})
        music_info = data.get('music', {})

        tags = [
            tag['hashtagName']
            for tag in data.get('textExtra', [])
            if tag.get('type') == 1 and 'hashtagName' in tag
        ]
        
        is_image_set = data.get('imagePost') is not None
        media_type = MediaType.IMAGE if is_image_set else MediaType.VIDEO

        images = None
        if is_image_set:
            images = [
                StandardImage(
                    url=img.get('imageURL', {}).get('urlList', [None]),
                    width=img.get('imageURL', {}).get('width'),
                    height=img.get('imageURL', {}).get('height')
                )
                for img in data.get('imagePost', {}).get('images', []) if img.get('imageURL', {}).get('urlList')
            ]

        try:
            video = StandardVideo(
                video_id=data['id'],
                platform=PlatformType.TIKTOK,
                title=data.get('desc', ''),
                description=data.get('desc', ''),
                author_id=author_info.get('secUid'),
                author_nickname=author_info.get('nickname'),
                author_unique_id=author_info.get('uniqueId'),
                like_count=stats.get('diggCount', 0),
                comment_count=stats.get('commentCount', 0),
                share_count=stats.get('shareCount', 0),
                collect_count=stats.get('collectCount', 0),
                create_time=datetime.fromtimestamp(data['createTime']),
                duration=video_info.get('duration', 0),
                width=video_info.get('width'),
                height=video_info.get('height'),
                cover_url=video_info.get('cover', {}).get('urlList', [None]),
                tags=tags,
                media_type=media_type,
                images=images,
                music_title=music_info.get('title'),
                music_author=music_info.get('author'),
                raw_data=data
            )
            return video
        except Exception:
            return None
    
    @staticmethod
    def to_standard_author(data: Dict) -> Optional[StandardAuthor]:
        if not data or 'secUid' not in data:
            return None
        
        stats = data.get('stats', {})
        
        try:
            author = StandardAuthor(
                author_id=data['secUid'],
                platform=PlatformType.TIKTOK,
                nickname=data.get('nickname'),
                unique_id=data.get('uniqueId'),
                signature=data.get('signature'),
                follower_count=stats.get('followerCount', 0),
                following_count=stats.get('followingCount', 0),
                video_count=stats.get('videoCount', 0),
                like_count=stats.get('heartCount', 0),
                avatar_url=data.get('avatarLarger', {}).get('urlList', [None]),
                is_verified=data.get('verified', False),
                raw_data=data
            )
            return author
        except Exception:
            return None
