# src/upload_tasks/base_task.py
from abc import ABC, abstractmethod

class BaseTask(ABC):
    """
    所有上传任务处理器的抽象基类。
    """
    def __init__(self, config, feishu_api, mappers):
        self.config = config
        self.feishu_api = feishu_api
        self.mappers = mappers
        self.upload_settings = config.get('upload_settings', {})
        self.data_path = self.upload_settings.get('data_path', 'data/')
    
    @abstractmethod
    def run(self, **kwargs):
        """
        执行任务的入口方法。
        """
        pass
