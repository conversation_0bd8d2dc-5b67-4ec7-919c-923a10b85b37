# src/upload_tasks/reupload_task.py
import os
import json
import logging
import shutil
from .base_task import BaseTask
from ..core.utils import select_file_from_input

class ReuploadTask(BaseTask):
    """
    智能补传任务的处理器。
    负责根据输入文件列表，将已归档的数据移回工作区以待重新同步。
    """
    def __init__(self, config, feishu_api, mappers):
        super().__init__(config, feishu_api, mappers)
        self.uploaded_path = os.path.join(self.data_path, 'uploaded')

    def run(self, **kwargs):
        logging.info("--- 开始执行智能补传任务 ---")
        
        input_file = select_file_from_input(self.config)
        if not input_file:
            logging.warning("未选择任何文件，任务中止。")
            return

        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                video_ids = [line.strip() for line in f if line.strip()]
        except IOError as e:
            logging.error(f"读取文件失败: {input_file}, {e}")
            return
        
        logging.info(f"将为 {len(video_ids)} 个视频ID恢复数据...")
        for video_id in video_ids:
            self._recover_data_for_video(video_id)

        logging.info("--- 智能补传任务执行完毕 ---")
        logging.info("请再次运行常规同步任务来上传已恢复的数据。")

    def _recover_data_for_video(self, video_id: str):
        """将单个视频的所有相关数据从归档区移回工作区"""
        logging.info(f"正在恢复 video_id: {video_id} 的数据...")
        
        # 恢复视频JSON
        video_json_recovered = self._move_file(f"{video_id}.json", os.path.join('video_data'))
        
        if not video_json_recovered:
            logging.warning(f"  -> 未在归档区找到视频JSON: {video_id}.json，无法恢复作者信息和媒体文件。")
            return

        # 解析视频JSON以获取作者ID
        try:
            video_json_path = os.path.join(self.data_path, 'video_data', f"{video_id}.json")
            with open(video_json_path, 'r', encoding='utf-8') as f:
                video_data = json.load(f)
            author_id = video_data.get('author', {}).get('sec_uid')
        except Exception as e:
            logging.error(f"  -> 解析已恢复的视频JSON失败: {e}")
            author_id = None

        # 恢复作者数据
        if author_id:
            self._move_file(f"{author_id}.json", os.path.join('author_data'))
            self._move_file(f"{author_id}.jpg", os.path.join('author_data', 'avatar'))

        # 恢复视频媒体文件
        self._move_file(f"{video_id}.jpg", os.path.join('video_data', 'cover'))
        self._move_file(f"{video_id}.mp4", os.path.join('video_data', 'video'))
        self._move_dir(video_id, os.path.join('video_data', 'image'))

    def _move_file(self, filename: str, sub_path: str) -> bool:
        source = os.path.join(self.uploaded_path, sub_path, filename)
        dest = os.path.join(self.data_path, sub_path, filename)
        if os.path.exists(source):
            os.makedirs(os.path.dirname(dest), exist_ok=True)
            shutil.move(source, dest)
            logging.info(f"  -> 已恢复文件: {dest}")
            return True
        return False

    def _move_dir(self, dirname: str, sub_path: str):
        source = os.path.join(self.uploaded_path, sub_path, dirname)
        dest = os.path.join(self.data_path, sub_path, dirname)
        if os.path.isdir(source):
            shutil.move(source, dest)
            logging.info(f"  -> 已恢复目录: {dest}")
