# src/upload_tasks/sync_task.py
import os
import json
import logging
from datetime import datetime
from typing import Dict, <PERSON><PERSON>, Optional
from .base_task import BaseTask
from ..models import StandardVideo, StandardAuthor, PlatformType

class SyncTask(BaseTask):
    """
    常规同步任务的处理器。
    负责扫描工作区，执行两阶段同步（账号 -> 素材），并进行智能增量比对。
    """
    def __init__(self, config, feishu_api, mappers):
        super().__init__(config, feishu_api, mappers)
        self.account_table_id = self.upload_settings.get('FEISHU_ACCOUNT_TABLE_ID')
        self.material_table_id = self.upload_settings.get('FEISHU_MATERIAL_TABLE_ID')
        self.account_base_token = self.upload_settings.get('FEISHU_ACCOUNT_BASE_TOKEN')
        self.material_base_token = self.upload_settings.get('FEISHU_VIDEO_BASE_TOKEN')
        # 在 __init__ 方法的末尾添加
        self.author_record_id_cache = {}
    
    def run(self, **kwargs):
        """执行常规同步任务"""
        logging.info("--- 开始执行常规同步任务 ---")
        
        # 1. 扫描并解析所有待处理的JSON文件
        raw_data_packages = self._scan_and_parse_json()
        if not raw_data_packages:
            logging.info("没有找到待处理的数据，任务结束。")
            return
        
        # 2. 将原始数据转换为标准模型
        standard_authors, standard_videos = self._map_to_standard_models(raw_data_packages)
        
        # 3. 第一阶段：同步账号信息
        self._sync_accounts(standard_authors)
        
        # 4. 第二阶段：同步素材信息
        self._sync_materials(standard_videos)
        
        logging.info("--- 常规同步任务执行完毕 ---")
    
    def _scan_and_parse_json(self) -> list:
        """扫描 video_data 目录并解析所有JSON文件"""
        video_data_path = os.path.join(self.data_path, 'video_data')
        if not os.path.exists(video_data_path):
            logging.warning(f"视频数据目录不存在: {video_data_path}")
            return []
        
        packages = []
        for filename in os.listdir(video_data_path):
            if filename.endswith('.json'):
                file_path = os.path.join(video_data_path, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        packages.append(json.load(f))
                except (json.JSONDecodeError, IOError) as e:
                    logging.error(f"读取或解析JSON文件失败: {file_path}, 错误: {e}")
        logging.info(f"扫描到 {len(packages)} 个待处理的视频JSON文件。")
        return packages
    
    def _map_to_standard_models(self, raw_data_packages: list) -> Tuple[Dict, Dict]:
        """将原始数据包列表转换为去重后的标准作者和视频模型字典"""
        authors = {}
        videos = {}
        
        for raw_data in raw_data_packages:
            platform = raw_data.get('platform', 'tiktok') # 假设默认是tiktok
            mapper = self.mappers.get(platform)
            if not mapper:
                logging.warning(f"未找到平台 '{platform}' 的映射器，跳过此数据包。")
                continue
            
            # 映射作者
            author_info = raw_data.get('author', {})
            std_author = mapper.to_standard_author(author_info)
            if std_author and std_author.author_id not in authors:
                authors[std_author.author_id] = std_author
            
            # 映射视频
            std_video = mapper.to_standard_video(raw_data)
            if std_video:
                videos[std_video.video_id] = std_video
        
        logging.info(f"成功映射了 {len(authors)} 个独立作者和 {len(videos)} 个视频。")
        return authors, videos

    def _build_account_fields(self, author: StandardAuthor) -> dict:
        """将StandardAuthor对象转换为飞书API所需的字段字典"""
        fields = {
            "sec_user_id": author.author_id,
            "username": author.nickname,
            "unique_id": author.unique_id,
            "bio": author.signature,
            "follower_count": author.follower_count,
            "following_count": author.following_count,
            "video_count": author.video_count,
            "author_likes_count": author.like_count,
            "platform": author.platform.value,
            "last_uploaded_uploader": int(datetime.now().timestamp() * 1000)
        }
        # 构造作者主页链接
        if author.platform == PlatformType.DOUYIN:
            fields["author_link"] = {"text": "作者主页", "link": f"https://www.douyin.com/user/{author.author_id}"}
        elif author.platform == PlatformType.TIKTOK:
            fields["author_link"] = {"text": "作者主页", "link": f"https://www.tiktok.com/@{author.unique_id}"}

        # 移除值为None的字段，避免覆盖飞书已有数据
        return {k: v for k, v in fields.items() if v is not None}

    def _compare_account_data(self, local_fields: dict, feishu_fields: dict) -> bool:
        """
        比较本地构造的字段和飞书返回的字段，判断是否需要更新。
        返回True表示需要更新，False表示无需更新。
        """
        for key, local_value in local_fields.items():
            # 飞书字段名与我们的业务含义一致
            feishu_value = feishu_fields.get(key)

            # 特殊处理超链接和时间戳
            if isinstance(local_value, dict) and 'link' in local_value:
                if not isinstance(feishu_value, dict) or local_value['link'] != feishu_value.get('link'):
                    return True # 链接不一致
            elif key == 'last_uploaded_uploader':
                continue # 每次都更新时间戳，所以不比较
            elif local_value != feishu_value:
                return True # 其他值不一致
        return False

    def _sync_accounts(self, standard_authors: dict):
        """第一阶段：同步所有账号信息"""
        logging.info(f"--- [阶段1/2] 开始同步 {len(standard_authors)} 个账号信息 ---")

        for author_id, author_data in standard_authors.items():
            logging.info(f"正在处理作者: {author_data.nickname} ({author_id})")

            # 构造本地数据
            local_fields = self._build_account_fields(author_data)

            # 1. 在飞书中查找作者
            filter_query = f"CurrentValue.[sec_user_id]=\"{author_id}\""
            feishu_record = self.feishu_api.find_record_by_filter(
                self.account_base_token, self.account_table_id, filter_query
            )

            if not feishu_record:
                # 2. 创建新记录
                logging.info("  -> 飞书记录不存在，准备创建...")
                record_id = self.feishu_api.create_record(
                    self.account_base_token, self.account_table_id, local_fields
                )
                if record_id:
                    self.author_record_id_cache[author_id] = record_id
                    logging.info(f"  -> ✓ 创建成功，Record ID: {record_id}")
                else:
                    logging.error("  -> ✗ 创建失败。")
            else:
                # 3. 比较并更新已有记录
                record_id = feishu_record[0]['record_id']
                feishu_fields = feishu_record[0]['fields']
                self.author_record_id_cache[author_id] = record_id # 缓存ID

                if self._compare_account_data(local_fields, feishu_fields):
                    logging.info("  -> 飞书记录已存在，检测到数据不一致，准备更新...")
                    success = self.feishu_api.update_record(
                        self.account_base_token, self.account_table_id, record_id, local_fields
                    )
                    if success:
                        logging.info("  -> ✓ 更新成功。")
                    else:
                        logging.error("  -> ✗ 更新失败。")
                else:
                    logging.info("  -> 飞书记录已存在且数据一致，跳过更新。")

        logging.info(f"--- [阶段1/2] 账号信息同步完成 ---")

    def _build_material_fields(self, video: StandardVideo, author_record_id: str, file_tokens: list) -> dict:
        """将StandardVideo对象转换为飞书API所需的字段字典"""
        fields = {
            "video_id": video.video_id,
            "title": video.title,
            "related_author": [author_record_id] if author_record_id else None,
            "publish_time": int(video.create_time.timestamp() * 1000),
            "tags": video.tags,
            "original_url": video.raw_data.get('share_info', {}).get('share_url'),
            "likes_count": video.like_count,
            "comment_count": video.comment_count,
            "share_count": video.share_count,
            "collect_count": video.raw_data.get('statistics', {}).get('collect_count', 0),
            "duration": video.duration,
            "dimensions": f"{video.width}x{video.height}" if video.width and video.height else None,
            "platform": video.platform.value,
            "last_uploaded_uploader": int(datetime.now().timestamp() * 1000)
        }
        if file_tokens:
            fields["media_files"] = file_tokens

        return {k: v for k, v in fields.items() if v is not None}

    def _compare_material_data(self, local_fields: dict, feishu_fields: dict) -> bool:
        """比较素材数据，判断是否需要更新元数据。"""
        # 只比较部分关键的、可能会变化的元数据
        keys_to_compare = ["title", "tags", "likes_count", "comment_count", "share_count", "collect_count"]
        for key in keys_to_compare:
            local_value = local_fields.get(key)
            feishu_value = feishu_fields.get(key)
            if local_value != feishu_value:
                logging.info(f"  -> 数据不一致，字段: {key}, 本地: {local_value}, 飞书: {feishu_value}")
                return True
        return False

    def _handle_media_upload(self, video: StandardVideo, feishu_record: Optional[dict]) -> list:
        """处理媒体文件的上传逻辑，返回file_token列表"""
        # 1. 确定本地文件路径
        # (此处简化处理，实际应更健壮)
        video_path = os.path.join(self.data_path, 'video_data', 'video', f"{video.video_id}.mp4")

        # 2. 检查本地文件是否存在
        if not os.path.exists(video_path):
            logging.warning(f"  -> 本地视频文件不存在: {video_path}，跳过上传。")
            # TODO: 记录到 missing_media.txt
            return None

        # 3. 检查飞书是否已有附件
        if feishu_record and feishu_record['fields'].get('media_files'):
            feishu_files = feishu_record['fields']['media_files']
            if feishu_files and len(feishu_files) > 0:
                # 4. 智能比对文件大小
                local_size = os.path.getsize(video_path)
                feishu_size = feishu_files.get('size')
                if local_size == feishu_size:
                    logging.info("  -> 飞书已存在相同大小的附件，跳过上传。")
                    return None # 返回None表示无需更新附件
                else:
                    logging.info(f"  -> 文件大小不一致 (本地: {local_size}, 飞书: {feishu_size})，准备重新上传。")

        # 5. 执行上传
        logging.info(f"  -> 准备上传媒体文件: {video_path}")
        file_token = self.feishu_api.upload_media(video_path, self.material_base_token)

        if file_token:
            logging.info(f"  -> ✓ 上传成功，File Token: {file_token}")
            return [{"file_token": file_token}]
        else:
            logging.error("  -> ✗ 上传失败。")
            return [] # 返回空列表表示上传失败

    def _sync_materials(self, standard_videos: dict):
        """第二阶段：同步所有素材信息"""
        logging.info(f"--- [阶段2/2] 开始同步 {len(standard_videos)} 个素材信息 ---")

        for video_id, video_data in standard_videos.items():
            logging.info(f"正在处理视频: {video_data.title[:30]}... ({video_id})")

            author_record_id = self.author_record_id_cache.get(video_data.author_id)
            if not author_record_id:
                logging.error(f"  -> 无法找到作者 {video_data.author_id} 的飞书Record ID，跳过此视频。")
                continue

            # 1. 在飞书中查找素材
            filter_query = f"CurrentValue.[video_id]=\"{video_id}\""
            feishu_record = self.feishu_api.find_record_by_filter(
                self.material_base_token, self.material_table_id, filter_query
            )

            # 2. 处理媒体文件上传
            file_tokens = self._handle_media_upload(video_data, feishu_record)

            # 3. 构造本地数据
            local_fields = self._build_material_fields(video_data, author_record_id, file_tokens)

            if not feishu_record:
                # 4. 创建新记录
                logging.info("  -> 飞书记录不存在，准备创建...")
                self.feishu_api.create_record(
                    self.material_base_token, self.material_table_id, local_fields
                )
            else:
                # 5. 比较并更新已有记录
                record_id = feishu_record['record_id']

                # 只有在需要更新元数据或需要更新附件时才执行更新操作
                needs_meta_update = self._compare_material_data(local_fields, feishu_record['fields'])
                needs_attachment_update = file_tokens is not None

                if needs_meta_update or needs_attachment_update:
                    logging.info(f"  -> 飞书记录已存在，需要更新 (元数据: {needs_meta_update}, 附件: {needs_attachment_update})")
                    # 我们只更新需要更新的字段，而不是全部
                    update_payload = {}
                    if needs_meta_update:
                        update_payload.update({k: v for k, v in local_fields.items() if k != 'media_files'})
                    if needs_attachment_update:
                        update_payload['media_files'] = file_tokens

                    self.feishu_api.update_record(
                        self.material_base_token, self.material_table_id, record_id, update_payload
                    )
                else:
                    logging.info("  -> 飞书记录已存在且数据一致，跳过更新。")

        logging.info("--- [阶段2/2] 素材信息同步完成 ---")
