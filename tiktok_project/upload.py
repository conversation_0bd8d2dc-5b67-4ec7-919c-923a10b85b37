# upload.py
import logging
import argparse
import os
import sys
import subprocess
from datetime import datetime

# --- 核心服务导入 ---
# (假设所有模块都遵循了新的架构)
from src.core.config import load_config
from src.destinations.feishu_api import FeishuAPI
from src.mapping.douyin_mapper import DouyinMapper
from src.mapping.tiktok_mapper import TikTokMapper
from src.upload_tasks.sync_task import SyncTask
from src.upload_tasks.reupload_task import ReuploadTask
from src.core.utils import select_from_list, UserQuitInterrupt

def setup_logging(config):
    """设置日志系统"""
    # (此函数与crawl.py中的实现类似，此处简化)
    log_level = config.get('upload_settings', {}).get('LOG_LEVEL', 'INFO')
    logging.basicConfig(level=log_level, format='[%(asctime)s] %(levelname)s: %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    logging.info("Upload-Log-System Initialized.")

def interactive_mode(config, services):
    """上传程序的交互式模式"""
    print("--- 欢迎使用V3.0数据同步上传器 (交互模式) ---")
    
    task_map = {
        "常规同步 (从data/同步到飞书)": services["sync_task"],
        "智能补传 (从input/文件列表恢复)": services["reupload_task"],
    }
    
    try:
        _, selected_task_obj = select_from_list(list(task_map.keys()), "请选择要执行的任务:", show_back=False)
        task_to_run = task_map[selected_task_obj]
        
        # 对于不同任务，未来可以在这里获取额外参数
        # 例如：补传任务需要选择输入文件
        
        task_to_run.run()

    except UserQuitInterrupt:
        print("\n操作已取消，程序退出。")
    except Exception as e:
        logging.error(f"交互模式出现意外错误: {e}", exc_info=True)
        print(f"\n发生错误，请检查日志。程序退出。")

def main():
    parser = argparse.ArgumentParser(description="Data Processor & Synchronizer V3.0")
    parser.add_argument('-i', '--interactive', action='store_true', help="启动交互模式")
    parser.add_argument('--task', type=str, default='sync', choices=['sync', 'reupload'], help="直接执行指定任务 (非交互模式)")
    # 可以为非交互模式添加更多参数，如--input-file

    args = parser.parse_args()

    # 加载配置
    try:
        config = load_config()
    except Exception as e:
        # load_config内部已处理日志和退出，这里捕获以防万一
        sys.exit(1)

    # 初始化日志
    setup_logging(config)

    # 初始化所有服务
    try:
        feishu_api = FeishuAPI(config)
        mappers = {
            "douyin": DouyinMapper,
            "tiktok": TikTokMapper,
        }
        services = {
            "sync_task": SyncTask(config, feishu_api, mappers),
            "reupload_task": ReuploadTask(config, feishu_api, mappers),
        }
        logging.info("所有服务已成功初始化。")
    except Exception as e:
        logging.error(f"初始化服务时发生致命错误: {e}", exc_info=True)
        sys.exit(1)

    # 根据参数选择执行模式
    if args.interactive:
        interactive_mode(config, services)
    else:
        # 非交互模式，直接执行任务
        if args.task == 'sync':
            logging.info("在非交互模式下执行常规同步任务...")
            services["sync_task"].run()
        elif args.task == 'reupload':
            logging.info("在非交互模式下执行智能补传任务...")
            services["reupload_task"].run()
        logging.info("非交互模式任务执行完毕。")

if __name__ == "__main__":
    main()